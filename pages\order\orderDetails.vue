<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="订单详情"></u-navbar>

		<view class="content">
			<view v-if="detail.orderGoods&&detail.orderGoods.length>0" class="box">
				<view class="title">商品信息</view>
				<view class="goods-container martop">
					<view class="goods-item" v-for="(item,index) in detail.orderGoods" :key="item.id">
						<view class="flex align-center">
							<view class="spxx-image">
								<u--image radius="4" width="130rpx" height="130rpx" :src="item.goodsImgUrl"
									mode="aspectFit" :lazy-lord="true"></u--image>
							</view>
							<view class="flex flex-direction justify-between goods-msg" style="width:600rpx;">
								<view class="flex justify-between">
									<view style="width:360rpx;" class="goods-name">
										{{item.goodsName}}*{{item.totalNumber}}
									</view>
								</view>
								<view class="flex align-center justify-between">
									<view class="goods-price">
										单价：￥{{(Number(item.totalMoney)/Number(item.totalNumber))/100}}</view>
									<!--<view>应收：￥2.00</view>
											<view>实收：￥2.00</view> -->
									<!-- <view class="text" style="color: red;text-align: center;"
										v-if="item.refundMoney&&item.refundMoney>0">
										已退款：￥{{$xy.delMoney(item.refundMoney)}}
									</view> -->
								</view>
								<!-- <view>优惠活动：暂无</view> -->
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="box">
				<view class="title">订单信息</view>
				<view class="orderinformation-container martop">
					<view class="flex align-center">
						<view class="oc-name">设备：</view>{{detail.deviceId||'-'}}
					</view>
					<view class="flex align-center">
						<view>
							<view class="oc-name">订单编号：</view>{{detail.id||'-'}}<text class="under-line-text"
								@tap="copy(detail.id)">复制</text>
						</view>
					</view>
					<view class="flex align-center">
						<view>
							<view class="oc-name">商户单号：</view>{{detail.payQueryOrderId||'-'}}<text
								class="under-line-text" @tap="copy(detail.payQueryOrderId)">复制</text>
						</view>
					</view>
					<view class="flex align-center">
						<view>
							<view class="oc-name">交易单号：</view>{{detail.payOrderId||'-'}}<text class="under-line-text"
								@tap="copy(detail.payOrderId)">复制</text>
						</view>
					</view>
					<view class="flex align-center">
						<view>
							<view class="oc-name">识别编号：</view>{{detail.recognitionTaskId||'-'}}<text
								class="under-line-text" @tap="copy(detail.recognitionTaskId)"
								v-if="detail.recognitionTaskId">复制</text>
						</view>
					</view>
					<view class="flex align-center">
						<view class="oc-name">会员卡：</view>{{detail.plusMealName||'普通会员'}}
					</view>
					<view class="flex align-center">
						<view class="oc-name">交易时间：</view>{{detail.payTime||'-'}}
					</view>
					<view class="flex align-center" style="color: red;">
						<view class="oc-name">创建时间：</view><text>{{detail.createTime||'-'}}</text>
					</view>

					<view class="flex align-center justify-between">
						<!-- 	<view class="phone">
							<view class="oc-name">手机号：</view>{{detail.memberTel||'-'}}<text class="under-line-text"
								@click="$tab.navigateTo(`/pages/order/userInfo?id=${detail.memberId}`)">会员信息</text><text
								v-if="detail.memberIsBlacklist&&detail.memberTel">已拉黑</text>
						</view>
						<view class="block"
							v-if="detail.memberTel&&!detail.memberIsBlacklist&&checkPermi(['order:black'])"
							@click="block">
							拉黑
						</view> -->

						<view class="oc-name">联系电话：</view>{{$xy.delPhone(detail.memberTel)||'-'}}
						<view class="phone" @click="call(detail.memberTel)" v-if="detail.memberTel">
							<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/order/phone.png"
								mode="widthFix"></image>拨打
						</view>
						<view class="phone" @click="$tab.navigateTo(`/pages/order/userInfo?id=${detail.memberId}`)">
							会员信息
						</view>
					</view>
					<view class="flex align-center">
						<view class="oc-name">订单状态：</view>{{detail.statusName||'-'}}
					</view>
					<view class="flex align-center">
						<view class="oc-name">商品总价：</view>￥{{$xy.delMoney(detail.orderTotalMoney)}}
					</view>
					<view>
						<view class="oc-name">优惠金额：</view>￥{{$xy.delMoney(detail.discountMoney)}}
					</view>
					<view>
						<view class="oc-name">订单总金额：</view>￥{{$xy.delMoney(detail.orderTotalMoney)}}
					</view>
					<view class="flex align-center justify-start" style="margin-top: 26rpx;">
						<view v-if="checkPermi(['order:log'])">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="showlogs">交易日志</xbutton>
						</view>
						<view class="marleft" v-if="checkPermi(['order:radio'])">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="showVideoView">交易视频</xbutton>
						</view>
					</view>
					<view class="flex align-center justify-end" style="margin-top: 26rpx;">
						<view class="marleft" v-if="checkPermi(['order:oldOrder'])&&detail.oldOrderId">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="goOldOrder">原订单
							</xbutton>
						</view>
						<view class="marleft"
							v-if="checkPermi(['order:rePay'])&&detail.payStatus==8&&detail.status==7&&detail.deviceType==5&&!detail.oldOrderId">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="rePay">重试扣款
							</xbutton>
						</view>
						<view class="marleft"
							v-if="checkPermi(['order:closeOrder'])&&detail.payStatus==8&&detail.status==7">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="closeOrder">关闭订单
							</xbutton>
						</view>
						<view class="marleft" v-if="checkPermi(['order:repl'])&&(detail.status==4||detail.status==5)">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="repl">补单</xbutton>
						</view>
						<!-- 	<view class="marleft">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3">通知付款</xbutton>
						</view> -->
						<view class="marleft" payStatus
							v-if="checkPermi(['order:refund'])&&detail.payStatus!=6&&(detail.status==4||detail.status==6)">
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="open">发起退款
							</xbutton>
						</view>
					</view>

				</view>
			</view>

			<view class="box">
				<view class="title">支付信息</view>
				<view class="payments-container martop">
					<view class="flex align-center">
						<view class="oc-name">支付时间：</view>{{detail.payTime||'-'}}
					</view>
					<view>
						<view class="oc-name">支付方式：</view>
						<text v-if="detail.balancePayAmount&&detail.balancePayAmount>0">余额支付</text><text
							v-else>{{$xy.getPayType(detail.payType)}}</text>
					</view>
					<view>
						<view class="oc-name">支付金额：</view>￥{{$xy.delMoney(detail.payMoney)}}
					</view>
					<view class="flex align-center">
						<view>
							<view class="oc-name">支付订单号：</view>{{detail.payOrderId||'-'}}<text class="under-line-text"
								@tap="copy(detail.payOrderId)">复制</text>
						</view>
					</view>
					<view>
						<view class="oc-name">支付状态：</view>{{detail.payStatusName||'-'}}
					</view>
				</view>
			</view>

			<view class="box" v-if="refundDetail.id">
				<view class="title">用户退款记录</view>
				<view class="userrefundrecord-container martop">
					<view class="title1 martop">客户申请</view>
					<view>
						<view class="oc-name">申请时间：</view>{{refundDetail.createTime||'-'}}
					</view>
					<!-- <view>退款金额：￥{{refundDetail.createTime}}</view> -->
					<view>
						<view class="oc-name">用户备注：</view>{{refundDetail.description||'-'}}
					</view>
					<view>
						<view class="oc-name">退款原因：</view>{{refundDetail.reason||'-'}}
					</view>
					<view class="title1 martop">退款产品</view>
					<block v-for="(item,index) in refundDetail.goodsList" :key="item.id">
						<view class="flex align-center" style="margin-bottom:18rpx;">
							<u--image radius="4" width="130rpx" height="110rpx" :src="item.goodsImgUrl" mode="widthFix"
								:lazy-lord="true"></u--image>
							<view style="margin-left: 20rpx;">{{item.goodsName}} *{{item.totalNumber}}</view>
							<!-- 	<view class="flex align-center justify-between">
								<view>商品原价：￥{{item.totalNumber}}</view>
								<view>商品卖价：￥{{item.sellPrice}}</view>
								<view>商品总价：￥{{item.totalMoney}}</view>
							</view> -->
						</view>
					</block>
					<view style="color:red;">
						<view class="oc-name">退款金额：</view>￥{{$xy.delMoney(refundDetail.refundMoney)}}
					</view>
					<view>
						<view class="oc-name">优惠活动：</view>无
					</view>
					<view class="title1 martop">商家处理</view>
					<view>
						<view class="oc-name">处理结果：</view>{{refundDetail.refundStatusDesc||'-'}}
					</view>
					<view>
						<view class="oc-name">处理说明：</view>{{refundDetail.mercRemark||'-'}}
					</view>
					<view v-if="refundDetail.refundStatus==1">
						<view class="oc-name">系统说明：</view>{{refundDetail.remark||'-'}}
					</view>
				</view>
			</view>
		</view>

		<xpopup :show="show" @close="close" @confirm="submit" :showBtn="true" title="拉黑">
			<!-- 拉黑 -->
			<view class="pop-content">
				是否确定拉黑该用户?
			</view>
		</xpopup>

		<!-- 退款弹框 -->
		<u-popup :show="refundShow" mode="center" round="12" zIndex="9999" :overlayStyle="{zIndex:9995}"
			:safeAreaInsetBottom="false" @close="refundShow=false">
			<view class="refund-content">
				<view class="refund-radio">
					<u-radio-group placement="row" v-model="radioType" @change="radioChange">
						<u-radio :customStyle="{marginRight: '24rpx'}" activeColor="#2C6FF3" label="商品退款" name="2">
						</u-radio>
						<u-radio activeColor="#2C6FF3" label="金额退款" name="1"></u-radio>
					</u-radio-group>
				</view>
				<view class="refund-scroll-box">
					<scroll-view class="refund-scroll" :scroll-with-animation="true" scroll-y
						style="max-height:800rpx;">
						<view v-if="radioType=='2'">
							<view class="martop" v-for="(item,index) in orderGoods" :key="item.id">
								<view class="flex align-center" @tap="checked(item)">
									<view class="checked">
										<image v-if="item.checked"
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/selected.png"
											mode="widthFix">
										</image>
										<image v-else
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/select.png"
											mode="widthFix"></image>
									</view>
									<view>{{item.goodsName}}</view>
								</view>
								<view class="flex align-center" style="margin-top: 24rpx;">
									<u-number-box button-size="30" v-model="item.totalNumber"
										class='martop'></u-number-box>
									<view style="margin-left: 30rpx;">
										￥{{(item.totalNumber*item.price).toFixed(2)}}
										<!-- 	<u--input placeholder="退款金额" @blur="refundGoodsChange" type="digit" disabled
												v-model="item.totalMoney" border="surround">
											</u--input> -->
									</view>
								</view>
							</view>
						</view>
						<view class="flex align-center" style="margin-top: 24rpx;" v-else>
							<view style="width:200rpx">
								退款金额
							</view>
							<view class='marleft' style="width: 100%;">
								<u--input placeholder="退款金额" @blur="refundGoodsChange" type="digit"
									v-model="refundMoney" border="surround">
								</u--input>
							</view>
						</view>
						<view class='' style="margin-top: 24rpx;" @click="actionsheetChange">
							<u--input clearable readonly suffixIcon="arrow-down" v-model="refundReasonName"
								border="surround" suffixIconStyle="color: #909399" placeholder="退款理由">
							</u--input>
						</view>
						<view class='' style="margin-top: 24rpx;">
							<u--textarea v-model="description" placeholder="备注"></u--textarea>
						</view>
						<view style="margin-top: 24rpx;" v-if="radioType=='2'">总退款金额：￥{{refundTotalMoney}}</view>
					</scroll-view>
				</view>

				<view class="flex align-center justify-end refund-btn">
					<view>
						<xbutton width='200rpx' @click='refundShow=false'>取消</xbutton>
					</view>
					<view class='marleft'>
						<xbutton width='200rpx' @click="refundSure">确定</xbutton>
					</view>
				</view>
			</view>
		</u-popup>
		<u-action-sheet :show="actionSheetShow" :actions="actions" :title="title" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>

		<!-- 关闭订单弹框 -->
		<u-popup :show="closeOrderShow" mode="center" :safeAreaInsetBottom="false" @close="closeOrderShow=false">
			<view class="refund-container">
				<view style="font-size: 28rpx;font-weight: bold;padding-bottom:20rpx;">
					关闭订单
				</view>
				<view class='' style="margin-top: 24rpx;">
					<u--textarea v-model="closeReason" placeholder="备注"></u--textarea>
				</view>
				<view class="flex align-center justify-end" style="margin-top: 24rpx;">
					<view>
						<xbutton width='200rpx' @click='closeOrderShow=false'>取消</xbutton>
					</view>
					<view class='marleft'>
						<xbutton width='200rpx' @click="closeOrderSubmit">确定</xbutton>
					</view>
				</view>
			</view>
		</u-popup>

		<!-- 查看视频 -->
		<!-- 支付宝柜子支持视频捞取 -->
		<xvideoPopup :show="videoShow" :isAli="detail.deviceType==5" :orderId='id' :deviceId="detail.deviceId" :createTime="detail.createTime" :activityId="detail.activityId" :showMuteBtn="false"
			:urls="videoUrls" @timeupdate="timeupdate" @close="videoClose" @open="videoOpen">
		</xvideoPopup>
	</view>
</template>

<script>
	import {
		byId,
		refundDetail,
		setBlacklist,
		refundByMerc,
		cancelOrder,
		tradeRetry
	} from "@/api/order/order.js"
	import getDict from "@/utils/getDict.js"
	export default {
		data() {
			return {
				id: null,
				videoShow: false,
				selOrder: undefined,
				videoUrls: [],
				detail: {},
				refundDetail: {},
				show: false,

				refundShow: false,
				radioType: '2',
				remark: '',

				orderGoods: null,
				createTime: null,
				description: null,
				refundReason: null,
				refundReasonName: null,
				actions: [],
				actionSheetShow: false,
				refundMoney: '',

				closeOrderShow: false,
				closeReason: '', //关闭订单原因
			}
		},

		computed: {
			refundTotalMoney() {
				let num = 0;
				if (this.orderGoods) {
					this.orderGoods.forEach(i => {
						if (i.checked) {
							num += Number(i.totalNumber) * i.price
						}
					})
				}
				return num.toFixed(2)
			}
		},

		onLoad(o) {
			this.id = o.id;
			// this.createTime = o.createTime

		},

		async onShow() {
			await this.getRefundReason()
			this.getbyId()
			this.getRefundDetail()
		},

		methods: {
			/**
			 * 重试扣款
			 */
			rePay() {
				this.$modal.oldConfirm('确定要重新扣款吗?').then(res => {
					tradeRetry({
						payQueryOrderId: this.detail.payQueryOrderId
					}).then(res => {
						uni.showToast({
							title: '成功~'
						})
					})
				})

			},

			copy(text) {
				uni.setClipboardData({
					data: text,
					success: (data) => {
						uni.showToast({
							title: '复制成功'
						})
					},
					fail: function(err) {

					},
					complete: function(res) {

					}
				})
			},
			getbyId() {
				byId({
					id: this.id
				}).then(res => {
					if (res.code == 200) {
						this.detail = res.data
					}
				})
			},

			getRefundDetail() {
				refundDetail({
					orderId: this.id
				}).then(res => {
					if (res.code == 200) {
						let data = res.data
						data.reason = this.actions.find(i => i.type == data.reason)?.name
						this.refundDetail = data
					}
				})
			},

			/**
			 * 查看视频
			 */
			showVideoView() {
				this.videoShow = true;
				var urls = this.detail.video.indexOf(',') != -1 ? this.detail.video.split(',') : [this.detail.video,
					undefined
				];
				this.videoUrls = [
					this.$xy.cdnUrl(urls[0]),
					this.$xy.cdnUrl(urls[1])
				]
			},

			timeupdate(e) {

			},

			videoClose() {
				this.videoShow = false
			},

			videoOpen() {
				this.videoShow = true
			},

			block() {
				this.show = true
			},

			// 关闭弹框
			close(e) {
				this.show = false
			},

			// 弹框确定
			submit() {
				setBlacklist({
					memberId: this.detail.memberId
				}).then(res => {
					this.$modal.msg('拉黑成功~')
					this.getbyId()
				}).catch(err => {

				})
				this.close()
			},

			showlogs() {
				this.$tab.navigateTo('/pages/order/orderLogs?id=' + this.detail.activityId);
			},

			manualPayment() {
				this.$tab.navigateTo('/pages/order/orderDel?id=' + this.detail.id + '&deviceId=' + this.detail
					.deviceId)
			},

			open() {
				this.refundShow = true
				this.orderGoods = JSON.parse(JSON.stringify(this.detail.orderGoods))
				this.orderGoods.forEach(i => {
					i.price = this.$xy.delMoney(i.totalMoney) / i.totalNumber
					i.totalMoney = this.$xy.delMoney(i.totalMoney)
				})
			},

			getRefundReason() {
				return new Promise((resolve, reject) => {
					getDict('order_refund_refund_reason').then(res => {
						let actions = res.map(i => {
							return {
								type: i.value,
								name: i.msg
							}
						})
						this.actions = actions
						resolve(action)
					}).catch(err => {
						resolve(err)
					})
				})
			},

			//退款选项
			radioChange(e) {},

			//选中商品
			checked(item) {
				this.orderGoods.forEach(i => {
					if (i.id == item.id) {
						i.checked = !i.checked
					}
				})
				this.orderGoods = JSON.parse(JSON.stringify(this.orderGoods))
			},

			refundGoodsChange(e) {
				if (!this.$xy.testPrice(e)) {
					this.$modal.msg('输入价格最低为分！')
				}
			},

			// 退款处理确认按钮
			refundSure() {
				let params = {}
				if (this.radioType == 2) { //订单商品退款
					let ordersGoods = [];
					for (var i = 0; i < this.orderGoods.length; i++) {
						let item = this.orderGoods[i]
						if (item.checked) {
							if (!this.$xy.testPrice(item.totalMoney)) {
								this.$modal.msg('输入价格最低为分！')
								return
							}
							ordersGoods.push({
								orderGoodsId: item.id,
								goodsNum: item.totalNumber,
								price: this.$xy.delMoneyL(item.price)
							})
						}
					}
					if (ordersGoods && ordersGoods.length == 0) {
						this.$modal.msg('请选择退款商品！')
						return
					}
					params = {
						refundWay: this.radioType,
						orderId: this.id,
						createTime: this.detail.createTime,
						refundGoods: ordersGoods,
						description: this.description,
						reason: this.refundReason
					}

					console.log('params', params)

				} else { //金额退款
					if (!this.refundMoney) {
						this.$modal.msg('请选择输入退款金额！')
						return
					}
					params = {
						refundWay: this.radioType,
						orderId: this.id,
						createTime: this.detail.createTime,
						description: this.description,
						reason: this.refundReason,
						refundMoney: this.$xy.delMoneyL(this.refundMoney)
					}
				}

				refundByMerc(params).then(res => {
					if (res.code == 200) {
						this.getbyId()
						this.getRefundDetail()
						setTimeout(() => {
							this.$modal.showToast('订单处理成功~')
						}, 1000)
					} else {
						this.$modal.showToast('订单处理失败~')
					}
					this.orderGoods = null;
					this.refundShow = false;
				})
			},

			actionsheetChange() {
				this.actionSheetShow = true;
			},

			actionsheetSelect(e) {
				this.refundReason = e.type
				this.refundReasonName = e.name
			},

			// 发起补单
			repl() {
				uni.setStorageSync('riskGoodsTotal', '')
				this.$tab.navigateTo(`/pages/order/riskOrderDel?id=${this.id}&deviceId=${this.detail.deviceId}&type=3`)
			},

			/**
			 * 关闭订单按钮点击
			 */
			closeOrder() {
				this.closeOrderShow = true
			},

			/**
			 * 关闭订单确定   
			 */
			closeOrderSubmit() {
				cancelOrder({
					"id": this.detail.id,
					"payQueryOrderId": this.detail.payQueryOrderId,
					"reason": this.closeReason
				}).then(res => {
					this.$modal.showToast('关闭成功')
					this.closeOrderShow = false
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000)
				})
			},

			/**
			 * 前往原订单
			 */
			goOldOrder() {
				this.$tab.navigateTo(`/pages/order/orderDetails?id=${this.detail.oldOrderId}`)
			},

			shareVideo() {
				this.$xy.shareVideo(this.videoUrl)
			},

			call(tell) {
				uni.makePhoneCall({
					phoneNumber: tell
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.container {
		padding: 24rpx;
		line-height: 50rpx;

		.content {
			overflow: hidden;
		}

		.text {
			color: #2C6FF3;
		}

		.martop {
			margin-top: 12rpx;
		}

		.marleft {
			margin-left: 10rpx;
		}



		.xian {
			border-bottom: 1px solid #5b5b5b;
			margin: 20rpx 0;
		}

		.title {
			font-size: 32rpx;
			line-height: 32rpx;
			font-weight: 800;
			color: #333;
		}

		.title1 {
			font-size: 28rpx;
			font-weight: 700;
			color: #333;
			line-height: 50rpx;
			position: relative;
			padding-left: 16rpx;

			&::before {
				content: '';
				width: 5rpx;
				height: 24rpx;
				background: #2C6FF3;
				position: absolute;
				left: 4rpx;
				top: 50%;
				transform: translateY(-60%);
			}
		}

		.under-line-text {
			font-size: 26rpx !important;
			font-weight: 500;
			font-style: italic;
			text-decoration: underline;
			color: #2C6FF3 !important;
			margin-left: 24rpx;
			background-color: #fff !important;
		}

		.oc-name {
			display: inline-block;
			width: 170rpx;
		}

		.oc-name {
			display: inline-block;
			width: 170rpx;
		}

		.phone {
			display: inline-block;
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #2C6FF3;
			padding: 0 16rpx;
			line-height: 38rpx;
			background: #F4F8FF;
			border-radius: 8rpx;
			margin-left: 24rpx;

			>image {
				width: 20rpx;
				height: 20rpx;
				margin-right: 13rpx;
			}
		}

		.payments-container,
		.userrefundrecord-container,
		.orderinformation-container {
			background-color: #fff;
			color: #777777;
			padding: 20rpx 0;
			border-radius: 15rpx;
		}

		.orderinformation-container {
			.phone {
				>text {
					display: inline-block;
					background-color: #5b5b5b;
					color: #fff;
					font-size: 22rpx;
					padding: 0 12rpx;
					border-radius: 6rpx;
					margin-left: 12rpx;
					line-height: 36rpx;
				}
			}

			.block {
				width: 92rpx;
				height: 38rpx;
				background: #F4F8FF;
				border-radius: 8rpx;
				font-size: 26rpx;
				line-height: 38rpx;
				font-weight: 500;
				color: #2C6FF3;
				text-align: center;
			}
		}

		.box {
			background-color: #fff;
			padding: 28rpx 25rpx;
			border-radius: 14rpx;
			margin-top: 20rpx;

			.goods-container {
				background-color: #fff;
				border-radius: 15rpx;
				margin-top: 35rpx;

				.goods-item {
					background: #F6F7FA;
					border-radius: 14rpx;
					padding: 8rpx;
					margin-top: 20rpx;

					.goods-msg {
						margin-left: 27rpx;

						.goods-name {
							font-size: 26rpx;
							font-weight: 800;
							color: #333333;
						}

						.goods-price {
							font-size: 24rpx;
							font-weight: 500;
							color: #555555;
						}

						.text {
							font-size: 24rpx;
							padding-right: 12rpx;
						}
					}
				}

				.spxx-image {
					height: 130rpx;
				}
			}
		}
	}

	.popup_overlay {
		position: fixed;
		top: 0%;
		left: 0%;
		width: 100%;
		height: 100%;
		background-color: black;
		z-index: 1001;
		-moz-opacity: 0.8;
		opacity: .80;
		filter: alpha(opacity=88);
	}

	.popup_content {
		position: fixed;
		top: 50%;
		left: 50%;
		width: 700rpx;
		height: 500rpx;
		margin-left: -350rpx;
		margin-top: -250rpx;
		border: 10px solid white;
		background-color: white;
		z-index: 1002;
		overflow: auto;
	}

	.pop-content {
		padding: 24rpx;
	}

	.refund-container {
		background-color: #fff;
		width: 600rpx;
		padding: 20rpx 30rpx;
		height: 700rpx;
		position: relative;
		overflow: hidden;
		border-radius: 12rpx;

		.checked {
			width: 36rpx;
			height: 36rpx;
			margin-right: 12rpx;

			image {
				width: 36rpx;
				height: 36rpx;
			}
		}
	}

	.refund-content {
		background-color: #fff;
		width: 600rpx;
		padding: 20rpx 30rpx;

		position: relative;
		overflow: hidden;
		border-radius: 12rpx;

		.checked {
			width: 36rpx;
			height: 36rpx;
			margin-right: 12rpx;

			image {
				width: 36rpx;
				height: 36rpx;
			}
		}

		.refund-radio {
			padding: 20rpx 0;
		}

		.refund-scroll-box {
			padding-bottom: 100rpx;

			.refund-scroll {
				height: 700rpx;
			}
		}

		.refund-btn {
			position: absolute;
			right: 30rpx;
			bottom: 24rpx;
		}
	}
</style>