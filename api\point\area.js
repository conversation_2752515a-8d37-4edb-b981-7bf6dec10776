import request from '@/utils/request'

// 新增区域
export function areaSave(data) {
  return request({
    url: '/merc/mini/mercRegion/save',
    method: 'post',
	data:data
  })
}

// 区域树
export function areaTree() {
  return request({
    'url': '/merc/mini/mercRegion/tree',
    'method': 'post'
  })
}

// 区域删除
export function areaDel(data) {
  return request({
    url: '/merc/mini/mercRegion/del',
    method: 'post',
    data:data
  })
}

// 新区域线路接口
export function allLineWithRegion(data) {
  return request({
    url: '/merc/mini/mercLine/allLineWithRegion',
    method: 'post',
    data:data
  })
}


