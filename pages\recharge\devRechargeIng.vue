<template>
	<!-- 设备缴费 -->
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="设备缴费"></u-navbar>
		<view class="content">
			<view class="ali-tips flex justify-between align-center" v-if="showBindAli">
				<view class="bind-tip">
					您还没绑定支付宝
				</view>
				<xbutton round="50rpx" width="160rpx" @click="bindAliAcc">立即绑定</xbutton>
			</view>
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y :style="{height:fullHeight}">
				<view class="scroll-content">
					<view class="section" v-if="deviceInfo&&deviceInfo.length>0">
						<view class="title">
							设备管理费<text style="font-size: 24rpx;color: red;font-weight: normal;">（点击展开详情）</text>
						</view>
						<view class="xy-card" v-for="(item,key) in deviceInfo" :key="item.deviceId"
							:class="[!item.show?'section-content section-content-noshow':'section-content']"
							@click="showItem(item)">
							<view class="top-msg flex justify-between align-center">
								<view class="item-name" v-if="item.deviceName">
									{{item.deviceName}}
								</view>
								<view class="item-name" v-else>
									{{item.deviceId}}
								</view>
								<view class="item-num">
									×1
								</view>
								<view class="item-money">
									￥{{$xy.delMoney(item.money)}}
								</view>
							</view>
							<view class="flex justify-between align-center">
								<view class="row-left flex align-center">
									<view class="row-item">
										<view class="row-name">
											设备类型：
										</view>
										<view class="row-val">
											{{item.deviceTypeName}}
										</view>
									</view>
									<view class="online-status"
										:class="[item.busyState==1?'online-status':'online-status online-status-no']">
										{{item.busyState==1?'在线':'离线'}}
									</view>
								</view>
							</view>

							<view class="row-item">
								<view class="row-name">
									计费标准：
								</view>
								<view class="row-val">
									{{item.standard}}
								</view>
							</view>
							<view class="row-item">
								<view class="row-name">
									激活时间：
								</view>
								<view class="row-val">
									(未激活)
								</view>
							</view>
						</view>
					</view>

					<view class="section" v-if="(infCharge&&infCharge.length>0)||(moonCharge&&moonCharge.length>0)">
						<view class="title">
							设备算法服务费预充值<text style="font-size: 24rpx;color: red;font-weight: normal;">（点击展开详情）</text>
						</view>
						<view class="tab-wrap">
							<view class="tab">
								<u-tabs :list="tabList"
									:activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'30rpx'}"
									:inactiveStyle="{fontSize:'28rpx'}" :scrollable="false" :current="tabsIndex"
									@click="tabschange" lineColor="#2C6FF3">
								</u-tabs>
							</view>
						</view>
						<view v-if="tabsIndex==0">
							<view class="tips">
								说明：每台设备须预充值算法服务费才可以使用，
								预充算法服务费使用完后，将从商户余额中扣除。
							</view>
							<view class="xy-card"
								:class="[!item.show?'section-content section-content-noshow':'section-content']"
								v-for="(item,key) in infCharge" :key="item.deviceId" @click="showItem(item)">
								<view class="top-msg flex justify-between align-center">
									<view class="item-name" v-if="item.deviceName">
										{{item.deviceName}}
									</view>
									<view class="item-name" v-else>
										{{item.deviceId}}
									</view>
									<view class="item-num">
										×1
									</view>
									<view class="item-money">
										￥{{$xy.delMoney(item.money)}}
									</view>
								</view>
								<view class="flex justify-between align-center">
									<view class="row-left flex align-center">
										<view class="row-item">
											<view class="row-name">
												设备类型：
											</view>
											<view class="row-val">
												{{item.deviceTypeName}}
											</view>
										</view>
										<view class="online-status"
											:class="[item.busyState==1?'online-status':'online-status online-status-no']">
											{{item.busyState==1?'在线':'离线'}}
										</view>
									</view>
								</view>

								<view class="row-item">
									<view class="row-name">
										计费标准：
									</view>
									<view class="row-val">
										{{item.standard}}
									</view>
								</view>

							</view>
						</view>

						<view v-if="tabsIndex==1">
							<view class="tips">
								说明：超出月套餐数量的笔数，将从商户余额中扣除
							</view>
							<view class="xy-card"
								:class="[!item.show?'section-content section-content-noshow':'section-content']"
								v-for="(item,key) in moonCharge" :key="item.deviceId" @click="showItem(item)">
								<view class="top-msg flex justify-between align-center">
									<view class="item-name" v-if="item.deviceName">
										{{item.deviceName}}
									</view>
									<view class="item-name" v-else>
										{{item.deviceId}}
									</view>
									<view class="item-num">
										×1
									</view>
									<view class="item-money">
										￥{{$xy.delMoney(item.money)}}
									</view>
								</view>
								<view class="flex justify-between align-center">
									<view class="row-left flex align-center">
										<view class="row-item">
											<view class="row-name">
												设备类型：
											</view>
											<view class="row-val">
												{{item.deviceTypeName}}
											</view>
										</view>
										<view class="online-status"
											:class="[item.busyState==1?'online-status':'online-status online-status-no']">
											{{item.busyState==1?'在线':'离线'}}
										</view>
									</view>
								</view>

								<view class="row-item">
									<view class="row-name">
										计费标准：
									</view>
									<view class="row-val">
										{{item.standard}}
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>

			<view class="btn flex justify-end align-center">
				<view class="pay flex align-center">
					<view>
						应付：￥<text>{{$xy.delMoney(totalMoney)}}</text>
					</view>
					<view class="pay-btn">
						<xbutton @click="sure" delay="2000" fontSize="34rpx" size="large" width="180rpx" height="100rpx"
							round="100rpx">去结算</xbutton>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		payCheck,
	} from "@/api/recharge.js"

	import {
		userInfoBySelf,
	} from "@/api/device/device.js"

	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
				btnShow: false,
				fullHeight: 0,

				tabList: [{
						name: '按笔充值计费'
					},
					{
						name: '按月充值计费'
					}
				],

				tabsIndex: 0,
				deviceInfo: null,
				infCharge: null,
				moonCharge: null,
				algorithmId: null,
				code: null,

				devices: [],
				name: '激活',

				deviceType: null,
				showBindAli: false
			}
		},

		computed: {
			/**
			 * 统计支付金额
			 */
			totalMoney() {
				let total = 0
				if (this.deviceInfo) {
					if (this.deviceInfo && this.deviceInfo.length > 0) { //管理费
						for (var i = 0; i < this.deviceInfo.length; i++) {
							let item = this.deviceInfo[i];
							total += Number(item.money)
						}
					}
					if (this.tabsIndex == 0 && this.infCharge && this.infCharge.length > 0) { //管理费
						for (var i = 0; i < this.infCharge.length; i++) {
							let item = this.infCharge[i];
							total += Number(item.money)
						}
					}
					if (this.tabsIndex == 1 && this.moonCharge && this.moonCharge.length > 0) { //管理费
						for (var i = 0; i < this.moonCharge.length; i++) {
							let item = this.moonCharge[i];
							total += Number(item.money)
						}
					}
				}
				return total
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview',70)

			this.deviceType = o.deviceType
			if(this.deviceType==5){
				this.getUserMsg()
			}
			
			let devices = JSON.parse(o.devices)
			this.devices = devices //设备缴费状态信息
			this.deviceIds = devices.map(i => i.deviceId)
			// this.deviceIds = ["2023123456"]
			this.detail()
		},
		methods: {
			detail() {
				payCheck({
					deviceId: this.deviceIds
				}).then(res => {
					let data = res.data;
					this.delData(data, this.devices)
				})
			},

			/**
			 * 处理显示数据
			 */
			delData(data, devices) {
				let deviceInfo = [];
				let infCharge = [];
				let moonCharge = [];
				if (data.constructor === Object) {
					for (let key in data) {
						let item = data[key];
						for (let key1 in devices) {
							let item1 = devices[key1];
							if (key == item1.deviceId) {
								item.infDeviceAlgorithmCharging.busyState = item.deviceInfo.busyState
								item.moonDeviceAlgorithmCharging.busyState = item.deviceInfo.busyState
								if (item1.code == 202) {
									deviceInfo.push(item.deviceInfo)
								}
								if (item1.code == 203) {
									infCharge.push(item.infDeviceAlgorithmCharging)
									moonCharge.push(item.moonDeviceAlgorithmCharging)
								}
								if (item1.code == 204) {
									deviceInfo.push(item.deviceInfo)
									infCharge.push(item.infDeviceAlgorithmCharging)
									moonCharge.push(item.moonDeviceAlgorithmCharging)
								}
							}
						}
					}
				}
				this.deviceInfo = deviceInfo
				this.infCharge = infCharge
				this.moonCharge = moonCharge
			},

			getUserMsg() {
				userInfoBySelf().then(res => {
					let data = res.data
					this.showBindAli = data.aliUserId ? false : true;
				})
			},
			
			bindAliAcc() {
				this.$tab.navigateTo('/pages/activeDevice/bindAliAcc')
			},

			tabschange(e) {
				this.tabsIndex = e.index
			},

			showItem(item) {
				item.show = !item.show
				this.$forceUpdate()
			},

			sure() {
				//组装参数
				let type = [];
				let chargeObj = {
					deviceInfo: [],
					infCharge: [],
					moonCharge: [],
				}
				if (this.deviceInfo && this.deviceInfo.length > 0) { //type:1
					type.push(1)
					chargeObj.deviceInfo = this.deviceInfo.map(i => ({
						deviceId: i.deviceId,
						num: 1,
					}))
				}

				if (this.infCharge && this.infCharge.length > 0) {
					if (this.tabsIndex == 0) { //算法按笔
						type.push(2)
						chargeObj.infCharge = this.infCharge.map(i => ({
							name: this.name,
							algorithmId: i.algorithmId,
							deviceId: i.deviceId,
							chargingSize: 1,
							valueType: 'inf' //moon包月，inf永久
						}))
					} else { //算法按月
						type.push(3)
						chargeObj.moonCharge = this.moonCharge.map(i => ({
							name: this.name,
							algorithmId: i.algorithmId,
							deviceId: i.deviceId,
							chargingSize: 1,
							valueType: 'moon' //moon包月，inf永久
						}))
					}
				}
				let url =
					`/pages/recharge/rechargeIng?type=${JSON.stringify(type)}&chargeObj=${JSON.stringify(chargeObj)}&money=${this.totalMoney}`
				this.$tab.navigateTo(url)
			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			overflow: hidden;
			
			.ali-tips {
				border-top-left-radius: 14rpx;
				border-top-right-radius: 14rpx;
				line-height: 80rpx;
				font-size: 26rpx;
				height: 80rpx;
				padding: 0 40rpx;
				background: linear-gradient(180deg, #DEE9FF, rgba(217, 231, 255, 1));
			}

			.device-name {
				height: 84rpx;
				width: 100%;
				text-align: center;
				font-size: 32rpx;
				line-height: 84rpx;
				background: #2C6FF3;
				color: #fff;
				border-radius: 0px 0px 50rpx 50rpx;
			}

			.scrollview {
				.scroll-content {
					padding: 0 13rpx;
					overflow: hidden;

					.section {
						margin-top: 24rpx;
						margin-bottom:50rpx;

						.title {
							font-size: 32rpx;
							font-weight: 800;
							color: #333;
							line-height: 48rpx;
							padding: 12rpx 8rpx;
						}

						.tab-wrap {
							// padding-left: 30rpx;
							padding-bottom: 24rpx;

							.tab {
								width: 68%;
							}
						}

						.xy-card {}

						.section-content {
							padding: 12rpx 38rpx 12rpx;
							overflow: hidden;
							margin-bottom: 12rpx;
							position: relative;

							&.section-content-noshow {
								height: 76rpx;
							}

							.top-msg {
								line-height: 60rpx;

								.item-name {
									font-size: 26rpx;
								}

								.item-num {}

								.item-money {
									color: red;
								}
							}



							.row-left {}

							.row-right {}

							.row-item {
								font-size: 28rpx;
								color: #555555;
								line-height: 56rpx;
								display: flex;
								align-items: center;

								.row-name {
									min-width: 140rpx;
								}

								.row-val {}
							}

							.online-status {
								width: 92rpx;
								height: 38rpx;
								line-height: 38rpx;
								background: #F4F8FF;
								border-radius: 8rpx;
								font-size: 26rpx;
								color: #2C6FF3;
								text-align: center;
								margin-left: 38rpx;

								&.online-status-no {
									background: #F3F3F7;
									color: #999999;
								}
							}

							.section-total {
								border-top: 1rpx solid #E2E2E2;
								text-align: right;
								font-size: 28rpx;
								font-weight: 500;
								color: #555555;
								line-height: 80rpx;
								margin-top: 34rpx;

								>text {
									font-weight: bold;
									font-size: 32rpx;
								}
							}
						}

						.tips {
							min-height: 88rpx;
							background: #F6F7FA;
							border-radius: 8rpx;
							line-height: 56rpx;
							font-size: 24rpx;
							font-weight: 500;
							color: #2C6FF3;
							padding: 16rpx 18rpx;
							margin-bottom: 12rpx;
						}
					}
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 0;
			background-color: #fff;
			height: 130rpx;
			padding: 0 13rpx;

			.pay {
				.pay-btn {
					// width: 214rpx;
					// height: 100rpx;
					// background: #2C6FF3;
					// border-radius: 50rpx;
					// line-height: 100rpx;
					// text-align: center;
					// font-size: 36rpx;
					// color: #FFFFFF;
					margin-left: 40rpx;
				}
			}
		}
	}
</style>