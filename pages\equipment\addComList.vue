<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="增加商品"></u-navbar>
		<view class="content">
			<view class="xy-card" v-for="(item, index) in commList" :key="item.id">
				<view class="comm-item">
					<view class="image">
						<u--image radius="4" width="130rpx" height="130rpx" :src="item.cover" mode="widthFix"
							:lazy-load="true">
						</u--image>
					</view>
					<view class="item-content">
						<view class="item-top">
							{{ item.name }}
						</view>
						<view class="item-input-wrap">
							<view class="item-input" v-if="type != 1">
								<view class="input-label require"> 条形码： </view>
								<view class="input-box" style="
                    height: auto;
                    word-break: break-all;
                    line-height: 40rpx;
                  ">
									<text>{{ item.barcode }}</text>
								</view>
							</view>

							<view class="item-input" v-if="type != 1">
								<view class="input-label require"> 成本价 </view>
								<view class="input-box">
									<input type="digit" class="input" placeholder="请输入" border="surround"
										v-model="item.priceCost" />
								</view>
							</view>
							<view class="item-input require">
								<view class="input-label"> 零售价 </view>
								<view class="input-box">
									<input type="digit" class="input" placeholder="请输入" border="surround"
										v-model="item.price" />
								</view>
							</view>
							<!-- <view class="item-input">
								<view class="input-label">
									商品容量
								</view>
								<view class="input-box">
									<input type="number" class="input" placeholder="请输入" border="surround" v-model="item.capacity" />
								</view>
							</view> -->
							<view class="item-input">
								<view class="input-label"> 库存预警 </view>
								<view class="input-box">
									<input type="number" class="input" placeholder="请输入" border="surround"
										v-model="item.warning" />
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="btn safe-bottom">
			<xbutton delay="1500" size="large" round="88rpx" @click="sure">完成</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		bindMerc
	} from "@/api/commodity/mercGoods.js";
	import {
		bindDeviceByGoods
	} from "@/api/commodity/goods.js";
	export default {
		data() {
			return {
				commList: [],
				type: null,
				storeName: null,
				id: null,
				fromRisk: false,
			};
		},
		onLoad(o) {
			if (o.fromRisk) {
				this.fromRisk = true;
			}
			if (o.type == 0) {
				//公库添加到私库
				this.type = 0;
			} else if (o.type == 1) {
				//私库添加到设备
				this.type = 1;
				this.id = o.id;
			} else if (o.type == 2) {
				//公库添加到设备
				this.type = 2;
				this.id = o.id;
			}
			this.storeName = o.storeName;
			let commStor = JSON.parse(uni.getStorageSync(o.storeName));
			this.commList = commStor.map((i) => {
				i.capacity = 100;
				i.warning = 5;
				return i;
			});
		},
		methods: {
			sure() {
				if (this.type == 0) {
					//添加到私库
					let params = this.delParams();
					if (!params) return;
					bindMerc(params).then((res) => {
						if (res.code == 200) {
							this.$modal.msg("添加成功~");
							uni.setStorageSync(this.storeName, ""); //清空购物车
							uni.$emit('refreshData')
							this.$tab.navigateBack(3)
						}
					});
				} else {
					//添加到设备
					let params = this.delParams();
					if (!params) return;
					let assignParams = {
						deviceIds: [this.id],
						goodsList: params,
					};

					//添加前，判断是否为风险订单补扣添加商品
					if (this.fromRisk) {
						this.addRiskGoods(params);
					}

					//绑定机器
					bindDeviceByGoods(assignParams).then((res) => {
						if (res.code == 200) {
							this.$modal.msg("添加成功~");
							uni.setStorageSync(this.storeName, ""); //清空购物车
							uni.$emit('refresh')
							setTimeout(() => {
								// this.$tab.redirectTo(`/pages/equipment/comManage?id=${this.id}`)
								let pageList = getCurrentPages();
								let delta = 2;
								pageList.forEach((i) => {
									if (i.route == "pages/globalPages/allGoodsSearch") {
										delta = 3;
									}
								});
								uni.navigateBack({
									delta: delta,
								});
							}, 1000);
						}
					});
				}
			},

			/**
			 * 风险订单添加补扣商品
			 */
			addRiskGoods(params) {
				if (
					uni.getStorageSync(this.storeName) &&
					JSON.parse(uni.getStorageSync(this.storeName)).length > 0
				) {
					let commList = JSON.parse(uni.getStorageSync(this.storeName));
					let goodsIdList = params.map((i) => i.goodsId);
					if (uni.getStorageSync("riskNewGoodIds")) {
						let riskNewGoodIds = JSON.parse(uni.getStorageSync("riskNewGoodIds"));
						riskNewGoodIds = riskNewGoodIds.concat(goodsIdList);
						uni.setStorageSync("riskNewGoodIds", JSON.stringify(riskNewGoodIds));
					} else {
						uni.setStorageSync("riskNewGoodIds", JSON.stringify(goodsIdList));
					}
				}
			},

			delParams() {
				let params = [];
				for (let i = 0; i < this.commList.length; i++) {
					let item = this.commList[i];
					let obj = {};
					obj.goodsId = this.type == 1 ? item.goodsId : item.id;
					if (this.type != 1) {
						//非私库添加需要输入成本价格
						if (item.priceCost != undefined && item.priceCost != null) {
							obj.priceCost = this.$xy.delMoneyL(item.priceCost);
						} else {
							this.$modal.msg("请输入成本价！");
							return false;
						}
					}

					if (item.price != undefined && item.price != null) {
						obj.price = this.$xy.delMoneyL(item.price);
					} else {
						this.$modal.msg("请输入零售价格！");
						return false;
					}

					if (
						item.price != undefined &&
						item.price != null &&
						item.priceCost != undefined &&
						item.priceCost != null
					) {
						obj.price = this.$xy.delMoneyL(item.price);
						if (obj.price < obj.priceCost) {
							this.$modal.msg("零售价不能低于成本价！");
							return false;
						}
					}

					if (item.capacity != undefined && item.capacity != null) {
						obj.capacity = item.capacity;
					} else {
						// this.$modal.msg('请输入商品容量！')
						// return false
					}
					if (item.warning != undefined && item.warning != null) {
						obj.warning = item.warning;
					} else {
						// this.$modal.msg('请输入预警值！')
						// return false
					}
					params.push(obj);
				}
				return params;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.container {
		position: relative;

		.content {
			padding: 12rpx 24rpx 148rpx;

			.xy-card+.xy-card {
				margin-top: 18rpx;
			}

			.tag {
				position: absolute;
				right: 12rpx;
				top: 12rpx;
			}

			.comm-item {
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				align-items: center;
				background-color: #fff;
				border-radius: 12rpx;
				box-sizing: border-box;
				padding: 12rpx;

				.image {
					width: 130rpx;
				}

				.item-content {
					padding-left: 24rpx;
					color: #666;
					width: 530rpx;

					.item-top {
						font-size: 32rpx;
						font-weight: bold;
						color: #333;
						padding-bottom: 20rpx;
					}

					.item-input-wrap {
						display: flex;
						flex-flow: row wrap;
						justify-content: space-between;

						.item-input {
							width: 100%;
							display: flex;
							flex-flow: row nowrap;
							align-items: center;
							justify-content: flex-start;
							margin-top: 12rpx;

							.input-label {
								width: 160rpx;
								font-size: 30rpx;
								line-height: 40rpx;
							}

							.input-box {
								width: 320rpx;
								height: 70rpx;
								line-height: 70rpx;

								.input {
									height: 70rpx;
									border: 1rpx solid #eee;
									box-sizing: border-box;
									padding: 0 12rpx;
								}
							}
						}
					}
				}
			}
		}

		.btn {
			position: fixed;
			width: 100%;
			padding: 24rpx;
			left: 0;
			bottom: 0;
		}
	}
</style>