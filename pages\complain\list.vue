<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="客诉处理"></u-navbar>

		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
					:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
					lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>
		<!-- 
		<view class="search">
			<u-search animation placeholder="请输入" :clearabled="true" v-model="keyword" :showAction="false"
				@search="search"></u-search>
		</view> -->

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="item flex align-center justify-between" @click.stop="detail(item)">
						<view>
							<view class="title">
								{{item.problemDescription}}
							</view>
							<view class="rows">
								投诉单号：{{item.id}}<text class="under-line-text" @click.stop="$xy.copy(item.id)">复制</text>
							</view>
							<view class="rows">
								会员手机：{{item.memberTel||'-'}}<text class="under-line-text"
									@click.stop="$xy.copy(item.memberTel)">复制</text>
							</view>
							<view class="rows">
								投诉时间：{{item.complaintTime||''}}
							</view>
							<view class="rows">
								用户留言：<text style="color:red;">{{item.userDetail}}</text>
							</view>
						</view>
						<view>
							<u-icon name="arrow-right" size="16"></u-icon>
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>10" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>


		<u-action-sheet :show="actionSheetShow" :actions="actions" title="套餐类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		page,
		count
	} from "@/api/complain.js"
	import config from "@/config.js"

	export default {
		data() {
			return {
				keyword: null,
				list: [],
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				title: '',
				fullHeight: 0,
				current: 1,

				tabList: [{
						name: '全部',
						badge: {
							value: 0
						}
					},
					{
						name: '待处理',
						badge: {
							value: 0
						}
					},
					{
						name: '已处理',
						badge: {
							value: 0
						}
					},
				],

			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			this.getTotal()
			this.search()
			uni.$on('refresh', res => {
				this.getTotal()
				this.search()
			})
		},

		methods: {
			tabClick(e) {
				this.current = e.index
				this.reset()
				this.getpage()
			},

			getTotal() {
				count({}).then(res => {
					let data = res.data
					this.tabList[1].badge.value = data.untreatedCount
				})
			},

			//获取订单列表
			getpage() {
				page({
					page: {
						current: this.page,
						size: this.size
					},
					title: this.keyword,
					stateQuery: Number(this.current) + 1
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			detail(item) {
				this.$tab.navigateTo(`/pages/complain/detail?id=${item.id}&&title=${item.problemDescription}`)
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		},

		onUnload() {
			uni.$off('refresh')
		}
	}
</script>
<style scoped lang="scss">
	.container {
		background-color: #fff;

		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.scrollview {
			padding: 0 12rpx;
		}

		.item {
			background-color: #e2e7f0;
			padding: 20rpx;
			border-radius: 12rpx;
			margin-top: 20rpx;

			.title {
				font-size: 30rpx;
				font-weight: bold;
			}

			.rows {
				margin-top: 16rpx;
				padding-left: 12rpx;
			}

			.under-line-text {
				font-size: 26rpx !important;
				font-weight: 500;
				font-style: italic;
				text-decoration: underline;
				color: #2C6FF3 !important;
				margin-left: 24rpx;
			}
		}
	}
</style>