/**
 * @Description: 
 * @Author: 孙旺 <EMAIL>
 * @Date: 2024-04-16 10:01:47
 * @LastEditors: 孙旺 <EMAIL>
 * @LastEditTime: 2024-04-16 10:03:49
 */
import request from '@/utils/request'
/**
 * @description: 商户列表
 * @param {*} data
 * @return {*}
 * @author: 孙旺
 */
export function mercList(data) {
	return request({
		url: '/merc/merc-account-ratio/mercList',
		method: 'post',
		data: data
	})
}

/**
 * @description: 新增or修改
 * @return {*}
 * @author: 孙旺
 */
export function saveOrUpdate(data) {
	return request({
		url: '/merc/merc-account-ratio/saveOrUpdate',
		method: 'post',
		data: data
	})
}

/**
 * @description: 身份证号码查询分账人
 * @param {*} data
 * @return {*}
 * @author: 孙旺
 */
export function idCardQuery(data) {
	return request({
		url: '/merc/merc-account-ratio/idCardQuery',
		method: 'post',
		data: data
	})
}

/**
 * @description: 导出分账账单
 * @return {*}
 * @author: 孙旺
 */
export function download(data) {
	return request({
		url: '/merc/merc-account-ratio/download',
		method: 'post',
		data: data
	})
}

