{
  "easycom": {
    "^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    {
      "path": "pages/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/webView",
      "style": {
        "navigationBarTitleText": "webview",
        "navigationStyle": "custom"
      }
    }
  ],

  "subPackages": [
    //分包
    {
      "root": "pages/globalPages", //首页
      "pages": [
        {
          "path": "home",
          "style": {
            "navigationBarTitleText": "首页",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "statistics",
          "style": {
            "navigationBarTitleText": "销售统计",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "statisticsMore",
          "style": {
            "navigationBarTitleText": "销售统计",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "moreData",
          "style": {
            "navigationBarTitleText": "更多数据",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "logs",
          "style": {
            "navigationBarTitleText": "日志",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "errCode",
          "style": {
            "navigationBarTitleText": "常见错误码",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "notice",
          "style": {
            "navigationBarTitleText": "通知",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "noticeDetail",
          "style": {
            "navigationBarTitleText": "通知详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "agreement",
          "style": {
            "navigationBarTitleText": "协议",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting",
          "style": {
            "navigationBarTitleText": "设置",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "webView",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "richView",
          "style": {
            "navigationBarTitleText": "每日一图",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "incomeStatistics",
          "style": {
            "navigationBarTitleText": "会员收入统计",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "cashPrize",
          "style": {
            "navigationBarTitleText": "兑奖",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseDevice",
          "style": {
            "navigationBarTitleText": "选择设备",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/equipment", //设备
      "pages": [
        {
          "path": "detail/index",
          "style": {
            "navigationBarTitleText": "设备详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "comManage",
          "style": {
            "navigationBarTitleText": "商品管理",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addCom",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addComList",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "statistics",
          "style": {
            "navigationBarTitleText": "设备销售统计",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "statisticsMore",
          "style": {
            "navigationBarTitleText": "商品销售统计",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "calibration",
          "style": {
            "navigationBarTitleText": "电子秤校准",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/commodity", //商品
      "pages": [
        {
          "path": "comEdit",
          "style": {
            "navigationBarTitleText": "商品编辑",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "search",
          "style": {
            "navigationBarTitleText": "商品搜索",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "publicCom",
          "style": {
            "navigationBarTitleText": "商品公库",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addCom",
          "style": {
            "navigationBarTitleText": "新品建模",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "auditList",
          "style": {
            "navigationBarTitleText": "审核列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "publicSearch",
          "style": {
            "navigationBarTitleText": "公库搜索",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "commoditylist",
          "style": {
            "navigationBarTitleText": "商品清单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "comListEdit",
          "style": {
            "navigationBarTitleText": "关联商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "application",
          "style": {
            "navigationBarTitleText": "应用",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "allGoodsSearch",
          "style": {
            "navigationBarTitleText": "所有商品列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseDevice",
          "style": {
            "navigationBarTitleText": "选择设备",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "conflictList",
          "style": {
            "navigationBarTitleText": "冲突商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "conflictEdit",
          "style": {
            "navigationBarTitleText": "冲突商品编辑",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/order", //订单
      "pages": [
        {
          "path": "userInfo",
          "style": {
            "navigationBarTitleText": "用户信息",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orderQuery",
          "style": {
            "navigationBarTitleText": "订单查询",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "refundList",
          "style": {
            "navigationBarTitleText": "退款列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "refund",
          "style": {
            "navigationBarTitleText": "退款",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "refundChGoods",
          "style": {
            "navigationBarTitleText": "更换商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "refundAddGoods",
          "style": {
            "navigationBarTitleText": "更换商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orderDetails",
          "style": {
            "navigationBarTitleText": "订单详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "riskOrder",
          "style": {
            "navigationBarTitleText": "风险订单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "riskOrderDel",
          "style": {
            "navigationBarTitleText": "风险订单补扣",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orderLogs",
          "style": {
            "navigationBarTitleText": "交易日志",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "orderDel",
          "style": {
            "navigationBarTitleText": "订单补扣",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "deviceOrderList",
          "style": {
            "navigationBarTitleText": "交易明细",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "mercOrderList",
          "style": {
            "navigationBarTitleText": "交易明细",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/replenish", //补货
      "pages": [
        {
          "path": "replenishmentManagement",
          "style": {
            "navigationBarTitleText": "补货管理首页",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "replenishmentGra",
          "style": {
            "navigationBarTitleText": "补货管理首页-重力柜",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "calibration",
          "style": {
            "navigationBarTitleText": "秤校准",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "addCom",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "replenishmentHomePage",
          "style": {
            "navigationBarTitleText": "补货首页",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "replenishmentRecordDetails",
          "style": {
            "navigationBarTitleText": "补货记录详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "replenishmentRecord",
          "style": {
            "navigationBarTitleText": "补货记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "replenishImg",
          "style": {
            "navigationBarTitleText": "补货图片",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "replenishUpload",
          "style": {
            "navigationBarTitleText": "补货图片上传",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "invSearch",
          "style": {
            "navigationBarTitleText": "库存管理",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "invSearchDetail",
          "style": {
            "navigationBarTitleText": "库存管理详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "physicalRecord",
          "style": {
            "navigationBarTitleText": "盘点记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "physicalRecordDevice",
          "style": {
            "navigationBarTitleText": "盘点记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "physicalRecordDetail",
          "style": {
            "navigationBarTitleText": "盘点记录详情",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "repRecord",
          "style": {
            "navigationBarTitleText": "补货记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "stockRecord",
          "style": {
            "navigationBarTitleText": "库存变化记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "channelSet",
          "style": {
            "navigationBarTitleText": "货道设置",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/point", //区域点位线路
      "pages": [
        {
          "path": "point",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "lineDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "employee",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/employee", //人员系统配置
      "pages": [
        {
          "path": "employee",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addEmployee",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "empDetail",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "deviceManage",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/activeDevice", //设备激活
      "pages": [
        {
          "path": "bindDevice",
          "style": {
            "navigationBarTitleText": "设备激活",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "deviceManage",
          "style": {
            "navigationBarTitleText": "设备管理",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "bindAliAcc",
          "style": {
            "navigationBarTitleText": "绑定支付宝",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "bindAliDev",
          "style": {
            "navigationBarTitleText": "设备激活",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/report", //数据报表
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "数据报表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "listDetail",
          "style": {
            "navigationBarTitleText": "报表明细",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "createReport",
          "style": {
            "navigationBarTitleText": "报表生成",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseDevice",
          "style": {
            "navigationBarTitleText": "选择设备",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseGoods",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/merchant", //商户列表
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "商户列表",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/customer", //顾客信息
      "pages": [
        {
          "path": "blackList",
          "style": {
            "navigationBarTitleText": "黑名单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "wantEat",
          "style": {
            "navigationBarTitleText": "用户想吃",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "wantEatDetail",
          "style": {
            "navigationBarTitleText": "用户想吃",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/recharge", //设备管理费
      "pages": [
        {
          "path": "devManaFee",
          "style": {
            "navigationBarTitleText": "设备管理费",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "devYearFee",
          "style": {
            "navigationBarTitleText": "设备年费",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "devSimFee",
          "style": {
            "navigationBarTitleText": "流量卡缴费",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "rechargeRecord",
          "style": {
            "navigationBarTitleText": "设备管理费记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "costDevDetail",
          "style": {
            "navigationBarTitleText": "设备管理费明细",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "costSimDetail",
          "style": {
            "navigationBarTitleText": "流量卡续费明细",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "costAlgoDetail",
          "style": {
            "navigationBarTitleText": "算法扣费明细",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "costPackDetail",
          "style": {
            "navigationBarTitleText": "设备卡包",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "devRechargeIng",
          "style": {
            "navigationBarTitleText": "设备缴费",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "rechargeIng",
          "style": {
            "navigationBarTitleText": "结算",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "balanceRech",
          "style": {
            "navigationBarTitleText": "余额充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "payRecord",
          "style": {
            "navigationBarTitleText": "费用账单",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "costDetail",
          "style": {
            "navigationBarTitleText": "费用明细",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "cardPack",
          "style": {
            "navigationBarTitleText": "卡包",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "rechargeSuccess",
          "style": {
            "navigationBarTitleText": "重置成功",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "smsRecord",
          "style": {
            "navigationBarTitleText": "短信充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "smsRech",
          "style": {
            "navigationBarTitleText": "短信充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "telRecord",
          "style": {
            "navigationBarTitleText": "会员手机号充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "telRech",
          "style": {
            "navigationBarTitleText": "会员手机号充值",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "autoFee",
          "style": {
            "navigationBarTitleText": "手机号自动续费",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "dataCardBuy",
          "style": {
            "navigationBarTitleText": "流量卡购买",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "dataCardList",
          "style": {
            "navigationBarTitleText": "流量卡购买记录",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/market", //营销
      "pages": [
        {
          "path": "home",
          "style": {
            "navigationBarTitleText": "营销管理",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addPro",
          "style": {
            "navigationBarTitleText": "添加活动",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addSpe",
          "style": {
            "navigationBarTitleText": "添加活动",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addSend",
          "style": {
            "navigationBarTitleText": "添加活动",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseDevice",
          "style": {
            "navigationBarTitleText": "添加设备",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseGoods",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/invoice", //开票
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "开票管理",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "invoiceEdit",
          "style": {
            "navigationBarTitleText": "开票信息",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "titleList",
          "style": {
            "navigationBarTitleText": "我的发票抬头列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "editTitle",
          "style": {
            "navigationBarTitleText": "我的发票抬头编辑",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/vip", //vip设置
      "pages": [
        {
          "path": "vipSetting",
          "style": {
            "navigationBarTitleText": "会员设置",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "editVip",
          "style": {
            "navigationBarTitleText": "编辑套餐",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseDevice",
          "style": {
            "navigationBarTitleText": "添加设备",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseGoods",
          "style": {
            "navigationBarTitleText": "添加商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "viperMan",
          "style": {
            "navigationBarTitleText": "用户管理",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "viperRec",
          "style": {
            "navigationBarTitleText": "套餐记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "sendCoupon",
          "style": {
            "navigationBarTitleText": "赠送优惠券",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/integral",
      "pages": [
        {
          "path": "integralSet",
          "style": {
            "navigationBarTitleText": "积分设置",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "userIntegralList",
          "style": {
            "navigationBarTitleText": "会员积分",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "userIntegralRecord",
          "style": {
            "navigationBarTitleText": "积分记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "integralExcSet",
          "style": {
            "navigationBarTitleText": "积分兑换商品设置",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseDevice",
          "style": {
            "navigationBarTitleText": "选择设备",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseGoods",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/help", //帮助手册
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "帮助手册",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "detail",
          "style": {
            "navigationBarTitleText": "帮助",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    // {
    // 	"root": "pages/accountRatio", //分账管理
    // 	"pages": [{
    // 		"path": "list",
    // 		"style": {
    // 			"navigationBarTitleText": "分账管理",
    // 			"navigationStyle": "custom"
    // 		}
    // 	}, {
    // 		"path": "add",
    // 		"style": {
    // 			"navigationBarTitleText": "新增分账人",
    // 			"navigationStyle": "custom"
    // 		}
    // 	}, {
    // 		"path": "detail",
    // 		"style": {
    // 			"navigationBarTitleText": "分账明细",
    // 			"navigationStyle": "custom"
    // 		}
    // 	}]
    // },
    {
      "root": "pages/rechargeAct", //储值套餐
      "pages": [
        {
          "path": "recPackageList",
          "style": {
            "navigationBarTitleText": "储值套餐列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "add",
          "style": {
            "navigationBarTitleText": "新增储值套餐",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setting",
          "style": {
            "navigationBarTitleText": "配置",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "oldVsNewRec",
          "style": {
            "navigationBarTitleText": "老带新记录",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "recOrderList",
          "style": {
            "navigationBarTitleText": "充值订单列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "member",
          "style": {
            "navigationBarTitleText": "储值会员",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "restList",
          "style": {
            "navigationBarTitleText": "余额明细",
            "navigationStyle": "custom"
          }
        }
      ]
    },

    {
      "root": "pages/complain", //客诉处理
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "detail",
          "style": {
            "navigationBarTitleText": "详情",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/channel", //渠道推广
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "add",
          "style": {
            "navigationBarTitleText": "新建推广",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "type",
          "style": {
            "navigationBarTitleText": "渠道类型管理",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/coupon", //渠道推广
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "add",
          "style": {
            "navigationBarTitleText": "新建推广",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseDevice",
          "style": {
            "navigationBarTitleText": "选择设备",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseGoods",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/lottery", //渠道推广
      "pages": [
        {
          "path": "list",
          "style": {
            "navigationBarTitleText": "活动列表",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "add",
          "style": {
            "navigationBarTitleText": "添加活动",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "setPrize",
          "style": {
            "navigationBarTitleText": "设置奖品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "addPrize",
          "style": {
            "navigationBarTitleText": "添加奖品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "winnerRec",
          "style": {
            "navigationBarTitleText": "中奖记录",
            "navigationStyle": "custom"
          }
        }
      ]
    },
    {
      "root": "pages/collection", //商品采集
      "pages": [
        {
          "path": "home",
          "style": {
            "navigationBarTitleText": "商品数据",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "comInfo",
          "style": {
            "navigationBarTitleText": "商品信息",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "wifiConnect",
          "style": {
            "navigationBarTitleText": "wif连接",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "chooseGoods",
          "style": {
            "navigationBarTitleText": "选择商品",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "ready",
          "style": {
            "navigationBarTitleText": "准备采集",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "collecting",
          "style": {
            "navigationBarTitleText": "开始采集",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "result",
          "style": {
            "navigationBarTitleText": "采集结果",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "collectionList",
          "style": {
            "navigationBarTitleText": "已采集商品",
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/equipment/comManage": {
      "network": "all",
      "packages": ["pages/commodity"]
    },
    "pages/login": {
      "network": "all",
      "packages": ["pages/globalPages"]
    }
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "", //启动页面，必选
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
