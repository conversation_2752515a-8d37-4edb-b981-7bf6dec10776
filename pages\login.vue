<!-- 顶部蓝色 -->
<template>
	<view class="contaier">
		<view class="top">
			<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/head-bg.png" mode="widthFix">
			</image>
			<view class="top-content">
				<view class="text-white text-bold">{{appName}}</view>
				<view class="margin-top-xs text-white">欢迎使用，请先登录</view>
			</view>
		</view>

		<view class="input-box padding-lr">
			<form>
				<view class="cu-form-group margin-top">
					<view class="title">账号</view>
					<u--input placeholder="请输入账号" border="none" v-model="loginForm.username"></u--input>
				</view>
				<view class="cu-form-group" style="border-bottom: 1rpx solid #eee;">
					<view class="title">密码</view>
					<u--input placeholder="请输入密码" type="password" border="none" v-model="loginForm.password"
						:clearable="true" @clear="clearPassWord"></u--input>
				</view>
			</form>
		</view>

		<view class="remember">
			<u-checkbox-group v-model="isRemember" @change="remember">
				<u-checkbox size="16" labelSize="12" labelColor="#666" name="1" label="记住密码"></u-checkbox>
			</u-checkbox-group>
		</view>

		<view class="padding margin-top-xs">
			<button @click="handleLogin" class="cu-btn block round bg-login-zl margin-tb-sm lg">立即登录</button>
		</view>
	</view>
</template>
<script>
	import config from '@/config.js'
	import storage from '@/utils/storage'
	import constant from '@/utils/constant'
	export default {
		data() {
			return {
				codeUrl: "",
				captchaEnabled: true,
				globalConfig: getApp().globalData.config,
				loginForm: {
					username: "",
					password: "",
					code: "",
					uuid: ''
				},
				isRemember: [],
				appName: null
			}
		},

		onLoad() {
			console.log(uni.getAccountInfoSync().miniProgram.appId)
			switch (uni.getAccountInfoSync().miniProgram.appId) {
				case 'wxe875ab8368fdbe57':
					this.appName = '小白商家助手'
					break;
				case 'wxde6edfb4b2c1e7db':
					this.appName = '喵星人商家助手'
					break;
				default:
					this.appName = '商家助手'
					break;
			}
			if (uni.getStorageSync('account')) {
				this.isRemember = ['1']
				let account = JSON.parse(uni.getStorageSync('account'))
				this.loginForm.username = account.username
				this.loginForm.password = account.password
			}
		},
		methods: {
			remember(e) {
				this.isRemember = e
			},

			clearPassWord() {
				this.loginForm.password = ""
			},

			// 登录方法
			async handleLogin() {
				if (!this.loginForm.username) {
					this.$modal.msg('请输入账号~')
					return
				}
				if (!this.loginForm.password) {
					this.$modal.msg('请输入密码~')
					return
				}
				this.pwdLogin()
			},

			// 密码登录
			async pwdLogin(params) {
				await this.$store.dispatch('Login', this.loginForm).then((res) => {
					getApp().globalData.defaultPwd = res.defaultPwd //是否默认密码，用来首页是否修改密码弹框
					if (this.isRemember.length > 0 && this.isRemember[0]) { //记住密码
						let account = {
							username: this.loginForm.username,
							password: this.loginForm.password
						}
						uni.setStorageSync('account', JSON.stringify(account))
					} else {
						uni.setStorageSync('account', '')
					}
				})

				await this.$store.dispatch('GetPermis').then(res => {
					//判断用户是否有任一菜单权限
					if (res && res.length > 0) {
						this.$tab.reLaunch('/pages/globalPages/home')
					} else {
						this.$modal.msg('该用户无权限~')
					}
				}).catch(err => {

				})
			},
		}
	};
</script>
<style lang="scss" scoped>
	.contaier {
		height: 100vh;
		background-color: #ffffff;
	}

	.top {
		width: 750rpx;
		height: 480rpx;
		background-size: 100%;
		background-repeat: no-repeat;
		text-align: center;
		position: relative;

		image {
			width: 100%;
		}

		.top-content {
			width: 100%;
			position: absolute;
			top: 170rpx;

			>view:nth-child(1) {
				font-size: 48rpx;
			}
		}
	}

	.remember {
		margin-left: 60rpx;
		margin-top: 24rpx;
	}

	.bg-login-zl {
		background-image: linear-gradient(45deg, #727CFB, #46D0ED);
		color: #ffffff;
	}

	.wx-login {
		width: 100%;
		padding: 0 24rpx;
		position: fixed;
		bottom: 24rpx;
		left: 0;
		z-index: 9999;
	}
</style>