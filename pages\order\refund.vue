<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="退款"></u-navbar>
		<!-- <scroll-view   scroll-y="true" scroll-with-animation="true"
			lower-threshold="100" @scrolltolower="loadMore">
			<view class="" v-for="(item,index) in logList" :key="item.id">
				<view class="flex">
					<view class="" style="min-width: 200rpx;color: gray;">{{ item.activityTime.substring(5)}}</view>
					<view class="">{{item.msg}}</view>			
				</view>

			</view>
		</scroll-view> -->
		<view class="content">
			<view class="flex justify-end">
				<xbutton @click="addGoods">新增商品</xbutton>
			</view>
			<view class="com-box">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="com-item">
						<view class="select-img" @click="changeSelect(item)">
							<image
								src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/select.png"
								mode="widthFix" v-show="!item.selected"></image>
							<image
								src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/selected.png"
								mode="widthFix" v-show="item.selected"></image>
						</view>
						<view class="com-detail">
							<view class="name-btn flex justify-between">
								<view class="com-name">
									{{item.goodsName}}
								</view>
								<view class="change-btn" @click="changeGoods(index)">
									更换商品
								</view>
							</view>
							<view class="num-price flex">
								<view class="num">
									数量：{{item.totalNumber}}
								</view>
								<view class="price">
									单价：￥{{$xy.delMoney(item.sellPrice)}}
								</view>
							</view>
							<view class="active">
								促销活动：-
							</view>
							<view class="refund-type-tab">
								<u-radio-group v-model="list[index].type" placement="row">
									<u-radio :customStyle="{marginRight: '40rpx'}" label="按数量退款" name="0">
									</u-radio>
									<u-radio label="按金额退款" name="1">
									</u-radio>
								</u-radio-group>
							</view>
							<view style="margin-top: 30rpx;">
								<view class="" v-if="list[index].type==0">
									<view class="refund-num">
										<u-number-box v-model="list[index].totalNumber"></u-number-box>
									</view>
									<view class="refund-price-item">
										退款金额：<text
											style="color: #FF0000;">¥{{$xy.delMoney(item.sellPrice*item.totalNumber)}}</text>
									</view>
								</view>
								<view class="refund-price flex align-center" v-else>
									退款金额：￥<view style="width:200rpx;margin-left: 20rpx;"><u--input border="surround"
											v-model="value"></u--input></view>
								</view>
							</view>

						</view>
					</view>
				</block>
			</view>
			<view class="total">
				退款总金额：<text>¥3.00</text>
			</view>
			<view class="remark">
				<view class="remark-label">
					备注：
				</view>
				<view class="remark-content">
					<u--textarea v-model="value1" placeholder="请输入内容"></u--textarea>
				</view>
			</view>
		</view>

		<view class="btn flex justify-end align-center">
			<xbutton bgColor="#fff" borderColor="#CCCCCC" color="#555555" round='33rpx' width="160rpx">取消</xbutton>
			<xbutton style="padding:0 20rpx;" bgColor="#fff" borderColor="#2C6FF3" color="#2C6FF3" round='33rpx'
				width="160rpx">拒绝退款</xbutton>
			<xbutton bgColor="#fff" borderColor="#2C6FF3" color="#2C6FF3" round='33rpx' width="160rpx">确定退款</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		byId,
		orderLogs,
		refundDetail
	} from "@/api/order/order.js"
	export default {
		data() {
			return {
				id: null,
				list: [],
				changeIndex: null,
			}
		},
		onLoad(o) {
			this.id = o.id
			let tempList = JSON.parse(uni.getStorageSync('refundList'))
			for (var i = 0; i < tempList.length; i++) {
				let item = tempList[i]
				item.selected = true
				item.type = '0'
			}
			this.list = tempList

			uni.$on('chooseGood', res => {
				this.list.splice(this.changeIndex, 1, res)
				uni.setStorageSync('refundList',JSON.stringify(this.list))
				this.$forceUpdate()
			})

			uni.$on('addGoods', res => {
				for (var i = 0; i < res.length; i++) {
					let item = res[i]
					if (this.list.every(j => j.id != item.id)) {
						this.list.push(item)
					}
				}
				uni.setStorageSync('refundList',JSON.stringify(this.list))
				this.$forceUpdate()
			})
		},

		methods: {
			queryLogs() {
				orderLogs({
					activityId: this.activityId
				}).then(res => {
					this.logList = res.data;
				})
			},

			changeSelect(item) {
				item.selected = !item.selected
				this.$forceUpdate()
			},

			changeGoods(index) {
				this.changeIndex = index
				this.$tab.navigateTo('/pages/order/refundChGoods')
			},
			addGoods() {
				this.$tab.navigateTo('/pages/order/refundAddGoods')
			},
		},
		onUnload() {
			uni.$off('chooseGood')
			uni.$off('addGoods')
		}
	}
</script>

<style scoped lang="scss">
	.container {
		padding: 24rpx 14rpx;
		line-height: 50rpx;
		min-height: 100vh;
		background-color: #fff;

		.content {
			padding-bottom: 200rpx;

			.com-box {
				.com-item {
					padding: 24rpx;
					border-radius: 14rpx;
					background-color: #fff;
					box-shadow: 0px 1px 7rpx 0px rgba(29, 76, 63, 0.17);
					margin-top: 20rpx;
					padding-left: 105rpx;
					position: relative;

					.select-img {
						position: absolute;
						left: 24rpx;
						top: 43rpx;
						width: 34rpx;
						height: 200rpx;

						>image {
							width: 34rpx;
							height: 34rpx;
						}
					}

					.com-detail {
						.name-btn {
							.com-name {
								width: 400rpx;
								font-size: 32rpx;
								font-weight: 800;
								color: #333333;
								line-height: 42rpx;
							}

							.change-btn {
								font-size: 32rpx;
								font-weight: 500;
								color: #2C6FF3;
							}
						}

						.num-price,
						.active {
							font-size: 26rpx;
							font-weight: 500;
							color: #555555;
							line-height: 62rpx;

							.num {
								width: 176rpx;
							}
						}

						.refund-type-tab {
							margin-top: 16rpx;
						}

						.refund-price-item {
							font-size: 22rpx;
							font-weight: 500;
							color: #333333;
							margin-top: 24rpx;
						}
					}
				}
			}

			.total {
				text-align: right;
				font-size: 28rpx;
				color: #333333;
				padding: 40rpx 12rpx;

				text {
					font-size: 38rpx;
					font-weight: bold;
					color: #FF0000;
				}
			}

			.remark {
				padding: 0 22rpx;

				.remark-label {
					line-height: 66rpx;
					color: #333;
				}

				.remark-content {}
			}
		}

		.btn {
			padding: 0 14rpx;
			width: 100%;
			height: 130rpx;
			position: fixed;
			left: 0;
			bottom: calc(env(safe-area-inset-bottom) / 2);
			box-shadow: 0px 1px 7rpx 0px rgba(29, 76, 63, 0.17);
			background-color: #fff;
			z-index: 999;
		}
	}
</style>