<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="激活管理"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search animation placeholder="搜索设备" :clearabled="false" v-model="keyword" :showAction="false"
					@search="search"></u-search>
				<view class="scan-icon" @click="scan">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>
			</view>
			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>

			<view class="tab-list flex flex-wrap flex-start">
				<block v-for="(item,index) in typeList" :key="item.name">
					<view :class="[deviceType==item.value?'tab-item tab-show':'tab-item']"
						@click="typeChange(item.value)">
						{{item.name}}
					</view>
				</block>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">

				<view class="list" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="index">
						<view class="thumb-box" @click.stop="selectedItem(item)">
							<view class="select-line-img" v-if="current==0">
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
									mode="widthFix" v-show="item.checked"></image>
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
									mode="widthFix" v-show="!item.checked"></image>
							</view>
							<view class="check-content">
								<view class="comm-main">
									<view v-if="item.deviceName">
										{{item.deviceName}}
										<text style="font-weight: normal;font-size: 24rpx;">({{item.deviceId}})</text>
									</view>
									<view v-else>
										{{item.deviceId}}
									</view> 
									<view v-if="item.activeState==2 && item.deviceType=='5'">
										商户前缀：{{item.mercPrefix}}
										<!-- <text class="under-line-text" @click.stop="copy(item.mercPrefix)">复制</text> -->
									</view>
									<view>
										设备sn：{{item.deviceSn||'-'}}
										<!-- 	<text class="under-line-text"
											@click.stop="copy(item.deviceSn)">复制</text> -->
									</view>
									<view v-if="item.activeState==2 && item.nfcSn">
										NFC SN：{{item.nfcSn}}
									</view>
									<view v-if="item.activeState==1">
										激活时间：{{item.activeTime}}
									</view>

								</view>

							</view>

							<view class="edit" @click.stop="edit(item)">
								<xbutton round='60rpx' padding='0rpx 20rpx' bgColor='#2C6FF3'>
									设置
								</xbutton>
							</view>
						</view>
					</block>
					<view class="more" style="overflow: hidden;">
						<u-loadmore :status="status" v-if="list.length>=1" />
					</view>
				</view>
				<view class="empty" v-if="list.length==0">
					<u-empty mode="list" text="没有设备!"></u-empty>
				</view>
			</scroll-view>
		</view>

		<view class="btn" v-if="current==0&&checkPermi(['activeMan:activeNow'])">
			<view class="choose-pay flex justify-between align-center">
				<view class="all-select flex align-center" @click="selectAll">
					<view class="select-line-img">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
							mode="widthFix" v-show="allChecked"></image>
						<image
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
							mode="widthFix" v-show="!allChecked"></image>
					</view>
					<view>
						全选
					</view>
				</view>
				<view class="pay flex align-center">
					<view class="pay-btn">
						<xbutton @click="submit" delay="2000" fontSize="34rpx" size="large" width="180rpx"
							height="100rpx" round="100rpx" v-if="btnShow">去激活</xbutton>
						<xbutton delay="2000" bgColor="#c1c1c1" color="#333" fontSize="34rpx" size="large"
							width="180rpx" height="100rpx" round="100rpx" v-else>去激活</xbutton>
					</view>
				</view>
			</view>
		</view>

		<xpopup :show="show" @close="close" @confirm="editSubmit" :showBtn="true" title="编辑">
			<!-- 设备编辑 -->
			<view class="popup-content">
				<view class="point-name">
					<view class="pop-item flex align-center">
						<view class="pop-label">
							名称：
						</view>
						<view class="pop-content">
							<u-input v-model="editForm.name" placeholder="请输入点位名称" border="none">
							</u-input>
						</view>
					</view>
					<view class="pop-item flex align-center">
						<view class="pop-label">
							点位：
						</view>
						<view class="pop-content" @click="$tab.navigateTo(`/pages/point/point?type=1`)">
							<u-input v-model="editForm.placeName" placeholder="请选择点位" disabled disabledColor="#fff"
								border="none" suffixIcon="arrow-right" suffixIconStyle="color: #555555"
								:suffixIconStyle="{fontSize:24}">
							</u-input>
						</view>
					</view>
				</view>

				<view class="point-msg">
					<view class="pop-item flex align-center">
						<view class="pop-label">
							点位名称：
						</view>
						<view class="pop-content">
							{{pointDetail.placeName||'-'}}
						</view>
					</view>
					<view class="pop-item flex align-center">
						<view class="pop-label">
							场景：
						</view>
						<view class="pop-content">
							{{pointDetail.sceneNames||'-'}}
						</view>
					</view>
					<view class="pop-item flex align-center">
						<view class="pop-label">
							管理员：
						</view>
						<view class="pop-content">
							{{pointDetail.adminName||'-'}}
						</view>
					</view>
					<view class="pop-item flex align-center">
						<view class="pop-label">
							区域/线路：
						</view>
						<view class="pop-content">
							{{pointDetail.regionName||'-'}}
						</view>
					</view>
					<view class="pop-item flex align-center">
						<view class="pop-label">
							地址：
						</view>
						<view class="pop-content">
							{{pointDetail.address||'-'}}
						</view>
					</view>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		aliDeviceActivePage,
		deviceActive,
		detail,
		updateInfo
	} from "@/api/device/device.js"
	import {
		placeDetail
	} from "@/api/point/point"
	import getDict from '@/utils/getDict.js'
	export default {
		data() {
			return {
				page: 1, //商品分页
				size: 10,

				status: 'loadmore', //加载更多

				tabList: [{
						name: '未激活'
					},
					{
						name: '已激活'
					}
				],
				current: 0,
				fullHeight: 0,

				list: [],
				keyword: '',

				typeList: [],

				deviceType: null,
				allChecked: false,
				show: false,
				editForm: {
					placeId: null,
					name: '',
					placeName: null,
				},
				pointDetail: {
					placeName: '',
					sceneNames: '',
					adminName: '',
					regionName: '',
					address: '',
				},
				deviceDetail: {},
				item: {}
			}
		},

		computed: {
			activeState() {
				let val = 1;
				if (this.current == 0) {
					val = 2
				} else {
					val = 1
				}
				return val
			},

			btnShow() {
				if (this.list.length > 0) {
					return this.list.find(i => i.checked) != undefined
				} else {
					return false
				}
			},

			deviceIds() {
				let deviceIds = []
				if (this.list.length > 0) {
					this.list.forEach(i => {
						if (i.checked) {
							deviceIds.push(i.deviceId)
						}
					})
				}
				return deviceIds
			},
		},

		async onLoad(o) {
			await this.getDeviceType()
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 60)
			this.search()

			uni.$on('selectPoint', res => {
				this.choosePoint(res)
			})
		},

		onShow() {

		},

		methods: {
			choosePoint(e) {
				this.pointDetail = e
				this.editForm.name = this.deviceDetail.deviceName ? this.deviceDetail.deviceName : e
					.placeName //默认设置点位名称为设备名称
				this.editForm.placeName = e.placeName
				this.editForm.placeId = e.id
				this.$forceUpdate()
			},
			getDeviceType() {
				return new Promise((resolve, reject) => {
					getDict('device_type').then(res => {
						let typeList = res.map(i => ({
							name: i.msg,
							value: i.code
						}))
						this.typeList = JSON.parse(JSON.stringify(typeList))
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			search(val) {
				this.reset();
				this.getList()
			},

			async tabClick(e) {
				let _this = this
				_this.current = e.index
				if (this.current == 1) {
					_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)
				} else {
					_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 60)
				}
				_this.search()
			},

			typeChange(e) {
				this.deviceType = this.deviceType == e ? null : e
				this.search()
			},

			//扫码
			scan() {
				this.$xy.scanDevice().then(res => {
					console.log(res)
					this.keyword = res
					this.search()
				})
			},

			//根据类目获取商品列表
			getList(id) {
				aliDeviceActivePage({
					page: {
						current: this.page,
						size: this.size
					},
					activeState: this.activeState,
					deviceId: this.keyword,
					deviceType: this.deviceType
				}).then(res => {
					let newData = res.data.records;
					for (var i = 0; i < newData.length; i++) {
						let item = newData[i]
						item.checked = false
					}

					if (newData.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.list = this.list.concat(newData)
				})
			},

			//触底加载更多
			scrolltolower() {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
				this.allChecked = false;
			},

			copy(text) {
				uni.setClipboardData({
					data: toString(text),
					success: (data) => {
						uni.showToast({
							title: '复制成功'
						})
					},
					fail: function(err) {
						console.log(err)
					},
					complete: function(res) {
						console.log(res)
					}
				})
			},

			selectedItem(item) {
				if (this.current != 0) return
				//米源必要绑定点位
				if(this.$xy.appId()=='wxde6edfb4b2c1e7db'){
					//设备激活前必须绑定点位信息
					if(!item.placeId){
						this.$modal.oldConfirm('当前设备未绑定点位，请先绑定点位才能激活,立即前往?').then(res => {
							//前往绑定
							this.edit(item)
						})
						return
					}
				}
				
				item.checked = !item.checked
				let selectAll = true
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i]
					if (!item.checked) {
						selectAll = false
					}
				}
				this.allChecked = selectAll
				this.$forceUpdate()
			},

			selectAll() {
				this.allChecked = !this.allChecked
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i]
					item.checked = this.allChecked
				}
				this.$forceUpdate()
			},

			submit() {
				this.$modal.oldConfirm('不需要缴费的设备将直接激活，其他设备缴费后激活，确认激活？').then(res => {
					deviceActive({
						deviceId: this.deviceIds
					}).then(res => {
						let data = res.data;
						if (data && data.length) { //有值则需要缴费
							this.$tab.navigateTo(
								`/pages/recharge/devRechargeIng?devices=${JSON.stringify(data)}&deviceType=${this.deviceType}`
							)
						} else {
							this.$modal.msg('激活成功~')
							setTimeout(() => {
								this.search()
							}, 1000)
						}
					}).catch(err => {
						this.err = '获取失败'
					})
				})
			},

			detail(item) {
				this.$tab.navigateTo(`/pages/activeDevice/bindAliDev?id=${item.deviceId}`)
			},

			async edit(item) {
				this.show = true
				this.item = item
				this.deviceDetail = await this.getDetail(item.deviceId)
				this.editForm = {
					name: this.deviceDetail.deviceName ? this.deviceDetail.deviceName : this.deviceDetail.deviceId,
					placeId: this.deviceDetail.placeId,
					placeName: this.deviceDetail.placeName,
				}

				if (this.deviceDetail.placeId) {
					this.getPlaceDetail(this.deviceDetail.placeId)
				}
			},

			getPlaceDetail(id) {
				placeDetail({
					id: id
				}).then(res => {
					let data = res.data
					this.pointDetail = data
				})
			},

			// 关闭弹框
			close(e) {
				this.show = false
			},

			// 弹框确定
			async editSubmit() {
				if (!this.editForm.name) {
					this.$modal.showToast('设备名称必填！')
					return
				}
				if (!this.editForm.placeId) {
					this.$modal.showToast('请先设置点位！')
					return
				}

				await this.updateDevice()
				this.item.deviceName = this.editForm.name
				this.close()
				this.$forceUpdate()
			},

			getDetail() {
				return new Promise((resolve, reject) => {
					detail({
						deviceId: this.item.deviceId,
						isSysinfo: true,
						isStatus: false,
						isRegister: false
					}).then(res => {
						this.deviceDetail = res.data;
						resolve(res.data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//修改设备信息
			updateDevice() {
				let params = {
					deviceId: Number(this.deviceDetail.deviceId),
					deviceName: this.editForm.name,
					placeId: this.editForm.placeId,
				}
				return new Promise((resolve, reject) => {
					updateInfo(params).then(res => {
						this.$modal.msg('修改成功~')
						setTimeout(() => {
							resolve(res)
						}, 1000)
					}).catch(err => {
						reject(err)
					})
				})
			},
		},

		onUnload() {
			uni.$off('selectPoint')
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {

			.search {
				padding: 24rpx 24rpx 12rpx;
				background-color: #fff;
				position: relative;

				.scan-icon {
					position: absolute;
					right: 36rpx;
					top: 38rpx;
					z-index: 2;
				}

				.search-history {

					.history-item {
						margin-right: 24rpx;
						padding: 0 12rpx;
						background-color: #f2f2f2;
						color: #333;
						font-size: 24rpx;
						line-height: 40rpx;
						border-radius: 40rpx;
						margin-top: 24rpx;
					}
				}
			}

			.tab-wrap {
				background-color: #fff;

				.tab {
					width: 50%;
				}
			}

			.tab-list {
				width: 100%;
				background-color: #fff;
				padding: 24rpx 26rpx 0;

				.tab-item {
					padding: 0 20rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					line-height: 62rpx;
					margin-bottom: 14rpx;
					text-align: center;
					margin-right: 12rpx;

					&.tab-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}
			}

			.list {
				width: 100%;
				padding: 0rpx 13rpx 12rpx;
				padding-bottom: calc(110rpx + env(safe-area-inset-bottom) / 2);
				overflow: hidden;

				.thumb-box {
					border-bottom: 1rpx solid #f4f4f4;
					display: flex;
					flex-flow: row nowrap;
					padding: 20rpx 30rpx;
					align-items: center;
					background-color: #fff;
					border-radius: 12rpx;
					margin-top: 12rpx;
					position: relative;

					.edit {
						position: absolute;
						right: 24rpx;
						bottom: 24rpx;
					}
				}

				.select-line-img {
					margin-right: 28rpx;

					>image {
						width: 34rpx;
						height: 34rpx;
					}
				}

				.check-content {
					display: flex;
					flex-direction: row;
					align-items: center;

					.comm-img {
						width: 130rpx;
						height: 130rpx;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-around;

						image {
							width: 100%;
						}
					}

					.comm-main {
						box-sizing: border-box;
						color: #333;
						font-size: 28rpx;

						.under-line-text {
							font-size: 26rpx !important;
							font-weight: 500;
							font-style: italic;
							text-decoration: underline;
							color: #2C6FF3 !important;
							margin-left: 24rpx;
							background-color: #fff !important;
						}

						>view {
							padding: 4rpx 0;
							line-height: 50rpx;
						}

						>view:nth-child(1) {
							font-size: 30rpx;
							font-weight: bold;
							color: #333;
						}

						>view:nth-child(2) {
							// font-size: 28rpx;
						}

						>view:nth-child(3) {
							// font-size: 28rpx;
						}

						>view:nth-child(4) {
							// font-size: 28rpx;

							text {
								font-weight: bold;
								color: red;
							}
						}
					}
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			background-color: #fff;
			bottom: 0;
			left: 0;

			.all-select {
				padding: 20rpx;

				.select-line-img {
					width: 34rpx;
					height: 34rpx;
					margin-right: 28rpx;

					>image {
						width: 34rpx;
						height: 34rpx;
					}
				}
			}

			.choose-pay {
				height: 130rpx;
				padding: 0 13rpx;

				.choose {
					height: 60rpx;
					background: #E0E0E0;
					border-radius: 30rpx;
					line-height: 60rpx;
					font-size: 26rpx;
					color: #333;
					padding: 0 20rpx;

					>view {
						padding-right: 10rpx;
					}

					>image {
						width: 25rpx;
						height: 24rpx;
					}
				}

				.pay {
					.pay-btn {
						// width: 214rpx;
						// height: 100rpx;
						// background: #2C6FF3;
						// border-radius: 50rpx;
						// line-height: 100rpx;
						// text-align: center;
						// font-size: 36rpx;
						// color: #FFFFFF;
						margin-left: 40rpx;
					}
				}
			}

		}

		.empty {
			position: absolute;
			left: 50%;
			top: 40%;
			transform: translate(-50%, -50%);
		}

		.popup-content {
			padding: 0 24rpx;

			.point-name {
				padding: 0 20rpx;

				.pop-item {
					line-height: 66rpx;
					font-size: 28rpx;
					color: #555;

					.pop-label {
						width: 220rpx;
					}

					.pop-content {
						width: 480rpx;
					}
				}
			}

			.point-msg {
				background: #F6F7FA;
				border-radius: 14rpx;
				padding: 20rpx 36rpx;
				margin-top: 20rpx;

				.pop-item {
					line-height: 56rpx;
					font-size: 28rpx;
					color: #777777;

					.pop-label {
						width: 220rpx;
					}

					.pop-content {
						width: 480rpx;
					}
				}
			}

			.not-tit {
				font-size: 28rpx;
				font-weight: bold;
				text-align: center;
				line-height: 60rpx;
			}
		}
	}
</style>