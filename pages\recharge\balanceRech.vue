<template>
	<!-- 余额充值 -->
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="余额充值"></u-navbar>
		<view class="content">
			<view class="section1 xy-card flex justify-between align-center">
				<view>
					余额（元）
				</view>
				<view>{{$xy.delMoney(restMoney)}}</view>
			</view>

			<view class="section2 xy-card" :style="{height:fullHeight}">
				<view class="title">
					充值金额
				</view>
				<view class="recharge-list flex justify-between flex-wrap">
					<block v-for="item in rechargeList" :key="item.value">
						<view :class="[money==item.value?'list-item list-item-select':'list-item']"
							@click="rechargeClick(item)">
							{{item.label}}
						</view>
					</block>
				</view>

				<view class="recharge-input">
					<u-input placeholder="点击输入充值金额" type="digit" v-model="money" border="none" max maxlength="8"
						placeholderClass="input-class" clearable fontSize="22" :customStyle="{height:'50px'}">
						<template slot="prefix">
							<text style="color: #333;font-size: 42rpx;font-weight: bold;padding-right: 30rpx;">¥</text>
						</template>
					</u-input>
				</view>

				<view class="btn">
					<xbutton size="large" width="672rpx" round="130rpx" @click="submit" v-if="money">立即充值</xbutton>
					<xbutton size="large" width="672rpx" round="130rpx" bgColor="#c1c1c1" @click="submit" color="#333"
						v-else>立即充值</xbutton>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		account
	} from "@/api/recharge.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
				rechargeList: [{
						value: '100',
						label: '100元'
					},
					{
						value: '200',
						label: '200元'
					},
					{
						value: '300',
						label: '300元'
					},
					{
						value: '500',
						label: '500元'
					},
					{
						value: '1000',
						label: '1000元'
					},
					{
						value: '2000',
						label: '2000元'
					},
				],
				fullHeight: 0,
				money: '',
				restMoney: 0,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.section2', 20)
		},

		onShow() {
			this.detail()
		},

		methods: {
			detail() {
				account({}).then(res => {
					this.restMoney = res.data.balance
				})
			},
			rechargeClick(e) {
				this.money = Number(e.value)
			},

			submit() {
				if (this.money) {
					this.$tab.navigateTo(
						`/pages/recharge/rechargeIng?type=${JSON.stringify([5])}&money=${this.$xy.delMoneyL(this.money)}`
						)
				} else {
					this.$modal.msg('请选择充值金额！')
				}
			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			padding: 0 13rpx;
			overflow: hidden;

			.section1 {
				height: 160rpx;
				margin-top: 20rpx;

				>view:nth-child(1) {
					font-size: 28rpx;
					font-weight: 500;
					color: #333333;
				}

				>view:nth-child(2) {
					font-size: 48rpx;
					font-weight: 800;
					color: #333333;
				}
			}

			.section2 {
				margin-top: 20rpx;
				position: relative;

				.title {
					font-size: 28rpx;
					font-weight: 800;
					color: #333333;
					padding: 30rpx 0;
				}

				.recharge-list {
					.list-item {
						width: 215rpx;
						height: 126rpx;
						background: #F7F7F7;
						border-radius: 10rpx;
						font-size: 32rpx;
						font-weight: 500;
						color: #333333;
						text-align: center;
						line-height: 126rpx;
						margin-bottom: 28rpx;

						&.list-item-select {
							background: #2C6FF3;
							color: #fff;
						}
					}
				}

				.recharge-input {
					margin-top: 40rpx;
					padding-bottom: 14rpx;
					border-bottom: 1rpx solid #ececec;
				}
				
				::v-deep .input-class{
					font-size: 36rpx;
				}

				.btn {
					position: absolute;
					left: 24rpx;
					bottom: 24rpx;
				}
			}
		}
	}
</style>