<template>
	<u-popup :show="show" @close="close" :round="round" :overlayStyle="{zIndex:zIndex-5}" :zIndex="zIndex"
		:safeAreaInsetBottom="safeAreaInsetBottom&&mode=='bottom'" @open="open" :mode="mode">
		<view class="pop-content">
			<slot name="title">
				<view class="head flex justify-between align-center">
					<view class="popup-title" v-if="title">
						{{title}}
					</view>
					<view class="popup-clear" v-if="clear" @click="clearClick">
						{{clear}}
					</view>
				</view>
			</slot>
			<slot />
			<slot name="botton">
				<view class="popup-btn" v-if="showBtn">
					<xbutton width="200rpx" round="88rpx" size="large" bgColor="#fff" color="#2C6FF3" borderColor="#2C6FF3"
						@click="close">取消</xbutton>
					<xbutton delay="1500" round="88rpx" width="200rpx" size="large" @click="submit">确定</xbutton>
				</view>
			</slot>
		</view>
	</u-popup>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		props: {
			mode: {
				type: String,
				require: false,
				default: 'bottom'
			},
			show: {
				type: Boolean,
				require: true,
				default: false
			},
			showBtn: {
				type: Boolean,
				require: true,
				default: true
			},
			title: {
				type: String,
				require: true,
				default: ''
			},
			clear: {
				type: String,
				require: false
			},
			safeAreaInsetBottom: {
				type: Boolean,
				require: false,
				default: true
			},
			round: {
				type: String,
				require: false
			},
			zIndex: {
				type: Number,
				require: false,
				default: 10075
			}
		},

		model: {
			prop: 'value',
			event: 'change'
		},

		watch: {
			value: {
				handler(newVal, oldVal) {
					this.fileList = newVal
				},
				immediate: true,
				deep: true
			}
		},

		methods: {
			close() {
				this.$emit('close', false)
			},
			open() {
				this.$emit('open', true)
			},

			submit() {
				this.$emit('confirm')
			},

			clearClick() {
				this.$emit('clearClick')
			},
		}
	}
</script>

<style lang="scss" scoped>
	.pop-content {
		box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		padding: 24rpx;
	}

	.popup-clear {
		padding: 24rpx;
		color: #2C6FF3;
	}

	.popup-btn {
		display: flex;
		flex-flow: row nowrap;
		justify-content: space-around;
		width: 100%;
		padding: 50rpx 24rpx 24rpx;

		.cu-btn {
			background-color: #2C6FF3;
			color: #fff;
			width: 200rpx;
		}

		.cu-btn1 {
			background-color: green;
		}

	}
</style>