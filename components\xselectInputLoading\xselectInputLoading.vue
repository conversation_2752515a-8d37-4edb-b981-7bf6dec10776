<template>
	<!-- 下拉搜索框 -->
	<view class="container">
		<view class="uni-bg" v-if="active" @click="bgClick"></view>
		<view class="uni-select-lay" :style="{'z-index':zindex}">
			<input type="text" :name="name" v-model="value" class="uni-select-input">
			<view class="uni-select-lay-select" :class="{'active':active}">
				<!-- 禁用mask -->
				<view class="uni-disabled" v-if="disabled"></view>
				<!-- 清空 -->
				<input type="text" class="uni-select-lay-input"
					:class="{active:changeValue!=''&&changeValue!=placeholder}" v-model="changeValue"
					:disabled="disabled" :placeholder="placeholder" @focus="unifocus" @blur="uniblur"
					@input="intchange">
				<view class="clear" @click="clearInput" v-if="changeValue!=''">
					<u-icon name="close-circle-fill" size="40rpx" color="#d5d5d5"></u-icon>
				</view>
				<!-- <view class="uni-select-lay-icon" @click="select"><text></text></view> -->
			</view>
			<view class="uni-select-lay-options"
				:class="optionsDirection === 'top' ? 'uni-select-lay-options-top' :'uni-select-lay-options-bottom' "
				v-show="active">
				<!-- 搜索时显示内容 -->
				<template>
					<template v-if="options.length>0 ">
						<scroll-view style="height:100%;" :scroll-with-animation="true" scroll-y lower-threshold="20"
							@scrolltolower="scrolltolower">
							<view class="uni-select-lay-item" :class="{active:value==item[svalue]}"
								v-for="(item,index) in options" :key="item[svalue]" @click="selectitem(index,item)">
								{{item[slabel]||item[svalue]}}
							</view>
						</scroll-view>
					</template>
					<template v-else>
						<view class="nosearch">无匹配内容！</view>
					</template>
				</template>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * 自定义搜索下拉框
	 */
	export default {
		name: "xy-selectInput",
		props: {
			// 是否禁用
			disabled: {
				type: Boolean,
				default: false
			},
			// 层级
			zindex: {
				type: Number,
				default: 999
			},
			// 数据列表
			options: {
				type: Array,
				default () {
					return []
				}
			},
			// 数据列表的位置
			optionsDirection: {
				type: String,
				default: 'bottom'
			},
			// input字段名
			name: {
				type: String,
				default: ''
			},
			// 默认展示value值
			value: {
				type: String,
				default: ''
			},
			// 无选项时展示文字
			placeholder: {
				type: String,
				default: '请选择'
			},
			// 自定义列表中键值对应关系label
			slabel: {
				type: String,
				default: 'label'
			},
			// 自定义列表中键值对应关系 value
			svalue: {
				type: String,
				default: 'value'
			}
		},
		data() {
			return {
				active: false, //组件是	否激活，
				oldvalue: "", //数据回滚
				changes: false, //正在搜索
				changeValue: ""
			};
		},
		watch: {
			value: {
				handler(newVal, oldVal) {
					this.changeValue = newVal
				},
			},
		},
		methods: {
			/**
			 * 触底加载
			 */
			scrolltolower() {
				this.$emit('scrolltolower')
			},

			/**
			 * 判断数组及当前active值
			 */
			itemcheck() {
				// 此处判断是否有初始value,存在则判断显示文字
				if (this.value != "") {
					// 展示plachhoder
					//判断数组
					if (this.options.length > 0) {
						this.options.forEach(item => {
							if (this.value == item[this.svalue]) {
								this.oldvalue = this.value = item[this.slabel];
								return;
							}
						})
					}
				} else {
					this.oldvalue = this.value = "";
				}
			},
			/**
			 * 点击组件
			 */
			select() {
				if (this.disabled) {
					return;
				}
				this.active = !this.active;
				if (this.active) {
					this.changes = false;
				} else {
					this.value = this.oldvalue;
				}
			},
			/**
			 * 获得焦点
			 */
			unifocus() {
				if (this.disabled) {
					return
				};
				this.active = true;
				this.changes = false;
			},

			/**
			 * 失去焦点
			 */
			uniblur() {
				// this.active = false;
				// this.changes = false;
			},

			/**
			 * value值改变
			 */
			intchange() {
				this.changes = true;
				this.$emit("input", this.changeValue)
			},
			/** 点击组件列
			 * @param {Object} index 索引
			 * @param {Object} item	值
			 */
			selectitem(index, item) {
				this.active = false;
				this.$emit("select", index, item)
			},

			clearInput() {
				this.$emit("clear")
				this.$emit("input", '')
			},

			/**
			 * 点击其他区域关闭
			 */
			bgClick() {
				this.active = false
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
	}

	.uni-bg {
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, .6);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 998;
	}

	.uni-select-lay {
		position: relative;
		z-index: 999;
		background-color: #fff;
		border-radius: 4px;

		.uni-select-input {
			opacity: 0;
			position: absolute;
			z-index: -111;
		}

		// select部分 
		.uni-select-lay-select {
			user-select: none;
			position: relative;
			z-index: 3;
			height: 36px;
			padding: 0 30px 0 10px;
			box-sizing: border-box;
			border-radius: 4px;
			border: 1px solid rgb(229, 229, 229);
			display: flex;
			align-items: center;
			font-size: 14px;
			color: #999;

			.uni-disabled {
				position: absolute;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 19;
				cursor: no-drop;
			}

			// input 框的清除按钮
			.uni-select-lay-input-close {
				position: absolute;
				right: 35px;
				top: 0;
				height: 100%;
				width: 15px;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 3;
				cursor: pointer;

				text {
					position: relative;
					background: #fff;
					width: 13px;
					height: 13px;
					border-radius: 50%;
					border: 1px solid #bbb;

					&::before,
					&::after {
						content: "";
						position: absolute;
						left: 20%;
						top: 50%;
						height: 1px;
						width: 60%;
						transform: rotate(45deg);
						background-color: #bbb;
					}

					&::after {
						transform: rotate(-45deg);
					}
				}
			}

			.uni-select-lay-input {
				font-size: 14px;
				color: #999;
				display: block;
				width: 98%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				line-height: 30px;

				&.active {
					color: #333
				}
			}

			.clear {
				position: absolute;
				right: 10rpx;
			}

			.uni-select-lay-icon {
				cursor: pointer;
				position: absolute;
				right: 0;
				top: 0;
				height: 100%;
				width: 30px;
				display: flex;
				align-items: center;
				justify-content: center;

				&::before {
					content: "";
					width: 1px;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					background-color: #e5e5e5;
				}

				text {
					display: block;
					width: 0;
					height: 0;
					border-width: 12rpx 12rpx 0;
					border-style: solid;
					border-color: #bbb transparent transparent;
					transition: .3s;
				}
			}

			&.active .uni-select-lay-icon {
				text {
					transform: rotate(180deg);
				}
			}
		}

		// options部分
		.uni-select-lay-options {
			user-select: none;
			position: absolute;
			left: 0;
			width: 100%;
			height: 500rpx;
			overflow-y: auto;
			border-radius: 4px;
			border: 1px solid rgb(229, 229, 229);
			background: #fff;
			padding: 5px 0;
			box-sizing: border-box;
			z-index: 9;

			.uni-select-lay-item {
				padding: 0 10px;
				box-sizing: border-box;
				cursor: pointer;
				line-height: 2.5;
				transition: .3s;
				font-size: 14px;

				&.active {
					background: #007AFF;
					color: #fff;

					&:hover {
						background: #007AFF;
						color: #fff;
					}
				}

				&:hover {
					background-color: #f5f5f5;
				}
			}

			.nosearch {
				font-size: 16px;
				line-height: 3;
				text-align: center;
				color: #666;
			}
		}

		.uni-select-lay-options-top {
			bottom: calc(100% + 5px);
		}

		.uni-select-lay-options-bottom {
			top: calc(100% + 5px);
		}
	}
</style>