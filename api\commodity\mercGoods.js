import request from '@/utils/request'

// 商品分页
export function ownerGoodsList(data) {
	return request({
		url: '/goods/merc-mini/goodsMerc/page',
		method: 'post',
		data: data
	})
}

// 商品类目
export function goodsCategory(data) {
	return request({
		url: '/goods/merc-mini/goodsMerc/categoryList',
		method: 'post',
		data: data
	})
}

// 对象查询
export function searchObj(data) {
	return request({
		url: '/goods/merc-mini/goodsMerc/obj',
		method: 'post',
		data: data
	})
}

// 商品添加到私库
export function bindMerc(data) {
	return request({
		url: '/goods/merc-mini/goodsMerc/bindMerc',
		method: 'post',
		data: data
	})
}

// 商品修改
export function update(data) {
	return request({
		url: '/goods/merc-mini/goodsMerc/update',
		method: 'post',
		data: data
	})
}
//根据商品ID修改私库商品信息
export function updateByGoodsId(data) {
	return request({
		url: '/goods/merc-mini/goodsMerc/updateByGoodsId',
		method: 'post',
		data: data
	})
}
