<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="加盟商"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search animation placeholder="请输入商户名称" :clearabled="false" v-model="keyword" :showAction="false"
					@search="getList"></u-search>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y
				lower-threshold="100" :style="{height:fullHeight}">

				<view class="list-warp" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="item.id">
						<view class="item-wrap xy-card" @click="loginSubMerc(item.id)">
							<view class="item-head flex justify-between">
								<view class="title">
									{{item.name}}
								</view>
								<view class="status">
									<u-icon name="arrow-right" color="#333" size="18"></u-icon>
								</view>
							</view>
							<view class="item-body">
								<view class="item-body-sec flex justify-between">
									<view>
										机器总数量：{{item.deviceCount}}台
									</view>
									<view>
										在线机器数量：{{item.deviceOnlineCount}}台
									</view>
								</view>

								<view class="item-body-report flex justify-between">
									<view class="report-part">
										<view>今日总收益</view>
										<view><text>¥</text>{{$xy.delMoney(item.daySalesMoney)}}</view>
									</view>
									<view class="report-part">
										<view>今日订单笔数</view>
										<view>{{item.daySalesCount}}<text>笔</text></view>
									</view>
									<view class="report-part">
										<view>本月总收益</view>
										<view><text>¥</text>{{$xy.delMoney(item.monthSalesMoney)}}</view>
									</view>
								</view>

								<view class="item-body-conn">
									<view>
										<view>联系人：</view>{{item.contact}}
									</view>
									<view>
										<view>联系方式：</view>{{item.contactPhone}}
									</view>
								</view>
							</view>
							<view class="item-foot flex justify-end" v-if="item.status==2">
								<!-- <xbutton padding="15rpx 40rpx" bgColor="#F4F8FF" color="#2C6FF3"
									@click="downLoad(item.fileUrl)">下载</xbutton> -->
								<view class="view flex align-center justify-end">
									<xbutton padding="15rpx 40rpx" bgColor="#F4F8FF" color="#2C6FF3"
										@click="downLoad(item.fileUrl)">下载</xbutton>
									<xbutton padding="15rpx 40rpx" bgColor="#fff" borderColor="#2C6FF3" color="#2C6FF3"
										style="margin-left: 24rpx;" @click="shareUrl(item.fileUrl)">
										分享
									</xbutton>
									<!-- <xbutton padding="15rpx 40rpx">发送</xbutton> -->
								</view>
							</view>
						</view>
					</block>
				</view>

				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

		</view>

		<!-- 区域选择弹框 -->
		<xpopup :show="shareShow" @close="shareShow=false" :showBtn="false" mode="center">
			<view class="popup-content">
				<view class="txt txt1">
					{{fileUrl}}
				</view>
				<view class="txt txt2">
					文件路径已复制，请前往微信粘贴！
				</view>
				<view slot="botton" class="button flex">
					<view @click="shareShow=false">
						不分享了
					</view>
					<navigator target="miniProgram" open-type="exit">去微信粘贴</navigator>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		subMerc,
		changeSubAuth
	} from '@/api/merchant.js'

	import {
		setToken,
		setMercId
	} from '@/utils/auth'
	export default {
		data() {
			return {
				list: [],
				keyword: '',
				fullHeight: 0,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview',0)
			
			this.getList()
		},
		methods: {
			getList() {
				subMerc({
					keyword: this.keyword
				}).then(res => {
					this.list = res.data;
				})
			},

			loginSubMerc(subMercId) {
				if (!this.checkPermis(['merchant:child'])) return
				changeSubAuth({
					subMercId: subMercId
				}).then(res => {
					setToken(res.data.satoken)
					setMercId(res.data.mercId)
					uni.setStorageSync('parentKey', res.data.parentKey)
					this.$store.commit('SET_TOKEN', res.data.satoken)
					this.$store.commit('SET_NAME', res.data.mercName)
					this.$tab.reLaunch('/pages/globalPages/home?type=1')
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;

		.content {
			overflow: hidden;
			padding: 0 13rpx;

			.search {
				padding: 24rpx 0;
			}

			.btn {
				margin-top: 20rpx;
				padding-bottom: 24rpx;
			}

			.list-warp {
				.item-wrap {
					padding: 24rpx 32rpx;

					+.item-wrap {
						margin-top: 20rpx;
					}

					.item-head {
						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}

						.status {
							font-size: 28rpx;
						}
					}

					.item-body {
						margin-top: 22rpx;

						.item-body-sec {
							>view {
								flex: 1;
							}
						}

						.item-body-report {
							background: #F6F7FA;
							border-radius: 14rpx;
							padding: 20rpx;
							margin-top: 20rpx;

							.report-part {
								flex: 1;

								>view:nth-child(1) {
									font-size: 26rpx;
									color: #555555;
								}

								>view:nth-child(2) {
									font-size: 36rpx;
									color: #333;
									font-weight: bold;
									margin-top: 20rpx;

									>text {
										font-size: 28rpx;
									}
								}
							}
						}

						.item-body-conn {
							font-size: 28rpx;
							color: #777777;
							line-height: 52rpx;
							margin-top: 14rpx;

							>view {
								>view {
									display: inline-block;
									width: 160rpx;
								}
							}
						}
					}

					.item-foot {
						margin-top: 16rpx;

						.view {
							width: 440rpx;
						}
					}
				}
			}

			.empty {
				padding-top: 40%;
			}
		}

		.popup-content {
			width: 600rpx;
			border-radius: 12rpx;
			overflow: hidden;

			.txt {
				padding: 24rpx;
				text-align: center;
				word-break: break-all;
				line-height: 60rpx;
			}

			.txt1 {
				padding: 24rpx;
				text-align: left;
				word-break: break-all;
			}

			.button {
				text-align: center;
				line-height: 80rpx;
				border-top: 1rpx solid #dfdfdf;

				>view {
					flex: 1;
					border-right: 1rpx solid #dfdfdf;
				}

				>navigator {
					flex: 1;
					color: #2C6FF3;
				}
			}
		}
	}
</style>