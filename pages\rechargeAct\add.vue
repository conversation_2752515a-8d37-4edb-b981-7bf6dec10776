<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="新增储值套餐"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap" style="padding:0 30rpx;">

					<u-form-item label="套餐名称" borderBottom prop="rechargeName">
						<u--input inputAlign="right" v-model="form.rechargeName" placeholder="请输入套餐名称"
							border="none"></u--input>
					</u-form-item>

					<u-form-item label="套餐类型" borderBottom>
						<u-radio-group v-model="form.type" placement="row" class="radio-group flex justify-end">
							<u-radio :label="item.label" :name="item.value" :customStyle="{marginLeft: '10px'}"
								v-for="(item,index) in typeOptions" :key="item.name">
							</u-radio>
							<!--<view class="tips">备注：只能存在一个长期套餐</view>-->
						</u-radio-group>
					</u-form-item>


					<u-form-item label="有效日期" borderBottom @click="chooseDate" v-if="form.type==2">
						<view style="text-align: right;" v-if="form.startTime">{{form.startTime}}~{{form.endTime}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择有效日期</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<u-form-item label="选择设备" borderBottom>
						<u-radio-group v-model="form.isAllDevice" placement="row" @change="actionAllDeviceRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
							</u-radio>
							<u-radio label="指定设备" name="0">
							</u-radio>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="指定设备" @click="chooseDev" borderBottom v-if="form.isAllDevice==0">
						<view class="choose-list" v-if="form.deviceList&&form.deviceList.length>0">
							<text v-for="(item,index) in form.deviceList" :key="item.deviceId">
								{{item.deviceName||item.deviceId}}
								<text v-if="index!=form.deviceList.length-1">，</text>
							</text>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择设备</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<view class="mj-content">
						<view class="mj-head flex justify-between align-center">
							<view class="mj-title">
								套餐内容
							</view>
							<view class="edit" @click="addMj">
								增加
							</view>
						</view>
						<view class="mj-body">
							<block v-for="(item,index) in form.itemList" :key="item.rad">
								<view class="mj-item flex align-center justify-between">
									<view class="left-input flex align-center">
										<text>充值</text><u-input v-model="item.amount" type="digit" placeholder="请输入"
											border="none"></u-input>
									</view>
									<view class="left-input flex align-center">
										<text>到账</text><u-input v-model="item.receiptAmount" type="digit"
											placeholder="请输入" border="none"></u-input>
									</view>
									<view class="edit del" @click="delMj(index)">
										删除
									</view>
								</view>
							</block>
						</view>
					</view>
				</view>
			</u--form>

			<view class="img-upload flex align-center">
				<view>
					<view>弹窗活动图片</view>
					<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：690*800</view>
				</view>
				<ximgUpload :maxCount="1" v-model="popImg" />
			</view>

			<view class="img-upload flex align-center">
				<view>
					<view>横幅推广图片</view>
					<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：750*280</view>
				</view>
				<ximgUpload :maxCount="1" v-model="bannerImg" />
			</view>

		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>

		<u-calendar :show="calendarShow" allowSameDay closeOnClickOverlay @close="calendarShow=false"
			:monthNum="calendarMonthNum" mode="range" color="#2C6FF3" @confirm="calendarConfirm"></u-calendar>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		save,
		obj
	} from "@/api/rechargeAct.js"

	export default {
		data() {
			return {
				form: {
					type: '',
					itemList: [{
						rad: Math.random(),
						amount: null,
						receiptAmount: null,
					}],
					endTime: null,
					startTime: null,
					rechargeName: null,
					isAllDevice: '0',
					deviceList: [],
				},
				calendarShow: false,
				typeOptions: [],
				calendarMonthNum: 10,
				popImg: [],
				bannerImg: [],
			}
		},

		async onLoad(o) {
			await getDict('member_recharge_type').then(res => {
				console.log(res)
				this.typeOptions = res.map(i => ({
					label: i.msg,
					value: i.value
				}))

				if (o.id) {
					this.detail(o.id)
				} else {
					this.form.type = this.typeOptions[0].value
				}
			})

			uni.$on('updateDev', res => {
				if (res && res.length > 0) {
					this.form.deviceList = res
				} else {
					this.form = {
						isAllDevice: '1',
						deviceList: [],
					}
				}
			})
		},

		methods: {
			detail(id) {
				obj({
					id: id
				}).then(res => {
					let data = res.data
					for (let item of data.itemList) {
						item.amount = this.$xy.delMoney(item.amount)
						item.receiptAmount = this.$xy.delMoney(item.receiptAmount)
						item.rad = Math.random()
					}
					data.startTime = data.startTime ? data.startTime.split(' ')[0] : ''
					data.endTime = data.endTime ? data.endTime.split(' ')[0] : ''
					data.isAllDevice = data.isAllDevice ? '1' : '0'
					this.popImg = data.popImg ? [data.popImg] : []
					this.bannerImg = [data.bannerImg] || []
					this.form = res.data
					console.log(this.form)
				})
			},

			/**
			 * @param {Object} e
			 * 选择设备切换
			 */
			actionAllDeviceRadio(e) {
				this.form.isAllDevice = e
			},

			/**
			 * 选择设备
			 */
			chooseDev() {
				let ids = []
				if (this.form.deviceList && this.form.deviceList.length > 0) {
					ids = this.form.deviceList.map(i => i.deviceId.toString())
				}
				this.$tab.navigateTo(`/pages/vip/chooseDevice?ids=${JSON.stringify(ids)}`)
			},

			submit() {
				let params = JSON.parse(JSON.stringify(this.form))
				for (let item of params.itemList) {
					item.amount = this.$xy.delMoneyL(item.amount)
					item.receiptAmount = this.$xy.delMoneyL(item.receiptAmount)
				}
				if (params.type == 2) {
					params.startTime = params.startTime + ' 00:00:00'
					params.endTime = params.endTime + ' 23:59:59'
				} else {
					params.startTime = null
					params.endTime = null
				}
				params.bannerImg = this.bannerImg[0]
				params.popImg = this.popImg[0]
				params.isAllDevice = this.form.isAllDevice == '0' ? false : true
				params.deviceIds = this.form.isAllDevice == '0' ? this.form.deviceList.map(i => i.deviceId) : []

				if (this.checkParams(params)) return
				save(params).then(res => {
					let msg = params.id ? '修改成功~' : '新建成功~'
					this.$modal.msg(msg)
					setTimeout(() => {
						uni.$emit('refresh')
						this.$tab.navigateBack()
					}, 1000)
				})
			},

			checkParams(params) {
				return false
			},

			actionStatusRadio() {
				this.form.type = e
			},
			chooseDate() {
				this.calendarShow = true
			},

			addMj() {
				let list = JSON.parse(JSON.stringify(this.form.itemList))
				list.push({
					rad: Math.random(),
					limit: null,
					price: null,
				})
				this.form.itemList = list
				this.$forceUpdate()
			},

			delMj(index) {
				this.$modal.oldConfirm('确定删除吗？').then(res => {
					if (this.form.itemList.length == 1) {
						this.$modal.msg('最少保留一项！')
						return
					}
					this.form.itemList.splice(index, 1)
				})
			},

			calendarConfirm(e) {
				console.log(e)
				this.form.startTime = e[0];
				this.form.endTime = e.at(-1)
				this.calendarShow = false
			},
		},
		onUnload() {
			uni.$off(['updateDev'])
		}
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding: 20rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
				border-radius: 12rpx;

				::v-deep .u-radio-group {
					justify-content: flex-end !important;
				}

				.radio-group {
					position: relative;

					.tips {
						font-size: 24rpx;
						color: red;
						line-height: 50rpx;
						padding-bottom: 10rpx;
					}
				}

				.choose-list {
					>text {
						word-break: break-all;
					}
				}
			}

			.mj-content {
				padding-bottom: 30rpx;

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 250rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.img-upload {
				height: 200rpx;
				font-size: 26rpx;
				color: #333;
				background-color: #fff;
				padding: 0 14rpx 0 30rpx;
				margin-top: 20rpx;
				border-radius: 12rpx;

				>view {
					margin-right: 41rpx;
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>