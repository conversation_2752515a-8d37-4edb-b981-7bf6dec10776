<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="订单管理"></u-navbar>
		<view class="time-choose">
			<view class="flex align-center justify-between top">
				<view class="con-btn" @tap="onByDay(-1)">
					前一天 </view>
				<view class="flex align-center justify-between date" @tap="timeShow=true">
					<view class="">{{searchQuery.orderDate||'选择时间'}}</view>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/date.png" mode="widthFix">
					</image>
				</view>
				<view class="con-btn" @tap="onByDay(1)">后一天</view>
			</view>
		</view>

		<xhelpPopup guideId="DJ0001" />

		<view class="flex align-center justify-between screen-container">
			<view>{{Number(orderCount.orderNum)}}个订单，合计:￥{{Number(orderCount.orderTotalAmount/100)}}</view>
			<view class="flex align-center">
				<view class="show-zero flex justify-end align-center">
					<view>零元单：</view>
					<view>
						<u-switch activeColor="#2C6FF3" size="14" v-model="searchQuery.showZero"
							@change="showZeroChange"></u-switch>
					</view>
				</view>
				<view class="flex align-center justify-center" @tap="screen">
					<view class="" style="font-size: 28rpx;font-weight: 500;color: #333333;">筛选</view>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png"
						style="width: 32rpx;height: 32rpx;margin-left: 12rpx;" mode="widthFix"></image>
				</view>
			</view>
		</view>

		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
					:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
					lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>
		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="orderList.length>0">
				<block v-for="(item,index) in orderList" :key="item.id">
					<view class="equipment-container" @tap="details(item)">
						<view class="flex align-center justify-between">
							<view class="title" v-if="item.deviceName">
								{{item.deviceName}}<text>({{item.deviceId}})</text>
							</view>
							<view class="title" v-else>{{item.deviceId}}</view>
							<view>
								<u-icon name="arrow-right" size="14"></u-icon>
							</view>
						</view>

						<view class="order-detail-item">
							<view>订单号：</view>{{item.id}}
						</view>

						<view class="order-detail-item">
							<view>支付方式：</view><text
								v-if="item.balancePayAmount>0">余额支付</text><text v-else>{{$xy.getPayType(item.payType)}}</text>
						</view>

						<view class="order-detail-item">
							<view>设备类型：</view>{{item.deviceTypeName||'-'}}
						</view>

						<view class="order-detail-item">
							<view>会员卡：</view>{{item.plusMealName||'普通会员'}}
						</view>

						<view class="order-detail-item">
							<view>时间：</view>{{item.createTime}}
						</view>

						<view class="order-detail-item"
							v-if="$xy.delMoney(item.payMoney)==0&&item.status==4&&!item.orderGoods">
							<view>零元单提示：</view>
							<view style="color: red;">未拿商品</view>
						</view>

						<view class="goods-container sb-box" v-for="(item1,index1) in item.orderGoods" :key="item1.id">
							<view class="flex align-center justify-center image-container">
								<u--image radius="4" width="130rpx" height="130rpx" :src="item1.goodsImgUrl"
									mode="aspectFit" :lazy-lord="true"></u--image>
							</view>
							<view class="details-container">
								<view class="flex align-center goods-name-num justify-between">
									<view class="goods-name">{{item1.goodsName}}</view>
									<view class="goods-num">×{{item1.totalNumber}}</view>
								</view>
								<view class="gp-item">
									单价：￥{{(Number(item1.totalMoney)/Number(item1.totalNumber))/100}}
								</view>
								<view class="gp-item" v-if="item1.refundMoney&&item1.refundMoney!=0">
									已退款：<text style="color:red;">￥{{$xy.delMoney(item1.refundMoney)}}</text>
								</view>
								<view class="goods-price flex">
									<!-- <view class="gp-item">
										应收：￥{{(Number(item1.totalMoney)-Number(item1.discountMoney))/100}}
									</view> -->
									<!-- <view class="gp-item">
										实收：￥{{item1.totalMoney-item1.discountMoney}}
									</view> -->
								</view>
								<!-- <view class="good-act">优惠活动：无</view> -->
							</view>
						</view>
						<view v-if="current==0" class="martop" style="text-align: right;">
							共计{{Number(item.goodsNumber)}}件，实付款<text
								style="color: red;font-weight: bold;">￥{{$xy.delMoney(item.orderTotalMoney)}}</text>
						</view>
						<view v-else class="martop" style="text-align: right;">共计{{Number(item.goodsNumber)}}件，应付款<text
								style="color: red;font-weight: bold;">￥{{$xy.delMoney(item.orderTotalMoney)}}</text>
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="orderList.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<xpopup :show="screenShow" @close="close" @confirm="sure" :showBtn="true" clear="清空" @clearClick="clear"
			title="筛选" zIndex="10000">
			<view class="popup-container">
				<view class='martop'>
					<u--input placeholder="订单编号" v-model="searchQuery.id" border="surround"></u--input>
				</view>
				<view class='martop' @click="chooseDevice">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="searchQuery.chooseDeviceIds"
						suffixIconStyle="color: #909399" placeholder="设备选择" border="surround"></u--input>
				</view>
				<view class='martop' @click="actionsheetChange('sfvip')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.isPayVip" border="surround"
						suffixIconStyle="color: #909399" placeholder="是否vip">
					</u--input>
				</view>
				<view class='martop'>
					<u--input placeholder="手机号码" v-model="searchQuery.phone" border="surround"></u--input>
				</view>
				<view class='martop'>
					<u--input placeholder="交易单号" v-model="searchQuery.payBuyOrderId" border="surround"></u--input>
				</view>
				<view class='martop'>
					<u--input placeholder="支付单号" v-model="searchQuery.payOrderId" border="surround"></u--input>
				</view>
				<view class='martop'>
					<u--input placeholder="支付订单号" v-model="searchQuery.payOrderNo" border="surround"></u--input>
				</view>
				<view class='martop' @click="actionsheetChange('sblx')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.deviceType"
						suffixIconStyle="color: #909399" placeholder="设备类型" border="surround"></u--input>
				</view>
				<view class='martop' @click="actionsheetChange('xzqy')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="searchQuery.regionName"
						border="surround" suffixIconStyle="color: #909399" placeholder="行政区域">
					</u--input>
				</view>
				<view class='martop' @click="actionsheetChange('lx')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.merLineId" border="surround"
						suffixIconStyle="color: #909399" placeholder="路线"></u--input>
				</view>
				<!-- 	<view class='martop' @click="actionsheetChange('ddzt')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.payStatus" border="surround"
						suffixIconStyle="color: #909399" placeholder="订单状态">
					</u--input>
				</view> -->
				<view class='martop' @click="actionsheetChange('yczt')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.riskType" border="surround"
						suffixIconStyle="color: #909399" placeholder="异常状态">
					</u--input>
				</view>
				<!-- <view class='martop'>

					<u--input clearable suffixIcon="arrow-down" v-model="searchQuery.regionName" border="surround"
						suffixIconStyle="color: #909399" placeholder="拉黑操作" @focus="actionsheetChange('lhcz')">
					</u--input>
				</view>
				<view class='martop'>
					<u--input clearable suffixIcon="arrow-down" v-model="searchQuery.regionName" border="surround"
						suffixIconStyle="color: #909399" placeholder="促销活动" @focus="actionsheetChange('cxhd')">
					</u--input>
				</view> -->
			</view>
		</xpopup>
		<u-action-sheet :show="actionSheetShow" :actions="actions" :title="title" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>

		<!-- 设备选择弹框 -->
		<xpopup :show="deviceShow" @close="deviceClose" @confirm="deviceConfirm" :showBtn="true" title="设备选择">
			<view class="popup-container">
				<view class="search">
					<u-search animation placeholder="输入设备号/设备名称" :clearabled="true" v-model="searchKey"
						:showAction="false" @search="getDeviceList"></u-search>
				</view>

				<scroll-view style="height: 600rpx;" scroll-y scroll-with-animation>
					<view class='device-scroll'>
						<u-checkbox-group v-model="searchQuery.chooseDeviceIds" @change="deviceChange"
							activeColor="#2C6FF3">
							<u-checkbox :customStyle="{marginBottom: '14px',marginRight: '24px',width:'300rpx'}"
								v-for="(item, index) in deviceList" :key="item.deviceId"
								:label="item.deviceName||item.deviceId" :name="item.deviceId">
							</u-checkbox>
						</u-checkbox-group>
					</view>
				</scroll-view>
			</view>
		</xpopup>

		<!-- 区域选择弹框 -->
		<xpopup :show="areaShow" @close="areaClose" :showBtn="false" title="选择区域">
			<!-- 类目选择 -->
			<scroll-view style="height: 600rpx;" scroll-y scroll-with-animation>
				<view class="popup-content">
					<tki-tree style="width:100%;" :range="areaList" :foldAll="false" rangeKey="name" idKey="name"
						buttonName="选中" @btnClick="areaSubmit">
					</tki-tree>
				</view>
			</scroll-view>
		</xpopup>

		<!-- 时间选择 -->
		<u-datetime-picker :show="timeShow" :closeOnClickOverlay="true" @close="timeShow=false" @confirm="timeSubmit"
			@cancel="timeShow=false" v-model="timeStamp" mode="date" :maxDate="maxDate"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		orderPage,
		orderPageCount,
		page
	} from "@/api/order/order.js"
	import {
		areaTree
	} from "@/api/point/area"
	import {
		linePage,
	} from "@/api/point/line"
	import {
		simpleDeviceSearchPage
	} from '@/api/replenishment/replenishment.js'

	export default {
		data() {
			return {
				keyword: '',
				orderListlength: '',
				orderList: [],
				orderCount: {},
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				mode: 'single',
				screenShow: false,
				actionSheetShow: false,
				currentDate: '',
				nextDate: '',
				date: '',
				actions: [],
				title: '',
				zeroOrderName: true,
				zeroOrderList: [{
					type: false,
					name: '否'
				}, {
					type: true,
					name: '是'
				}],
				equipmentTypename: '',
				equipmentTypeList: [{
					type: '',
					name: '全部'
				}, {
					type: 1,
					name: '开门柜'
				}, {
					type: 2,
					name: '重力柜'
				}],
				administrativeDivisionname: '',
				administrativeDivisionList: [{
					type: 'xzqy',
					name: '全部'
				}, {
					type: 'xzqy',
					name: '省市区三级联动'
				}],
				routename: '',
				routeList: [{
					type: 'lx',
					name: '全部'
				}, {
					type: 'lx',
					name: '路线1'
				}, {
					type: 'lx',
					name: '路线2'
				}],
				orderStatusname: '',
				orderStatusList: [{
						type: '',
						name: '全部'
					}, {
						type: 1,
						name: '交易中'
					}, {
						type: 2,
						name: '交易异常'
					},
					{
						type: 3,
						name: '交易取消'
					},
					{
						type: 4,
						name: '交易完成'
					},
					{
						type: 5,
						name: '交易关闭'
					},
					{
						type: 6,
						name: '下次补单'
					},
					{
						type: 7,
						name: '支付中'
					}
				],
				abnormalStatename: '',
				abnormalStateList: [{
						type: '',
						name: '全部'
					}, {
						type: 2,
						name: '发起识别失败'
					}, {
						type: 3,
						name: '未拿商品'
					}, {
						type: 4,
						name: '无法识别'
					},
					{
						type: 5,
						name: '故意遮挡'
					}, {
						type: 6,
						name: '柜中无此sku'
					}, {
						type: 9,
						name: '异物拿放'
					}, {
						type: 10,
						name: '疑似设备故障'
					},
					{
						type: 11,
						name: '支付失败'
					}, {
						type: 12,
						name: '超时'
					},
					{
						type: 8,
						name: '其他告警'
					}
				],
				pullBlackOperationname: '',
				pullBlackOperationList: [{
					type: 'lhcz',
					name: '全部'
				}, {
					type: 'lhcz',
					name: '是'
				}, {
					type: 'lhcz',
					name: '否'
				}],
				salesPromotionname: '',
				salesPromotionList: [{
					type: 'cxhd',
					name: '全部'
				}, {
					type: 'cxhd',
					name: '双十一活动'
				}],

				searchQuery: {
					orderDate: '', //日期
					deviceSearch: '', //搜索关键字
					id: '', //订单编号
					phone: '', //手机号码
					payOrderNo: '', //支付单号
					deviceType: '', //设备类型
					regionName: '', //区域
					merLineId: '', //线路
					payStatus: '', //订单状态
					riskType: '', //异常订单类型
					showZero: false, //零元单
					deviceId: '', //机器编号
					supplement: false, //补单中
					payOrderId: '', //支付单号
					payBuyOrderId: '', //交易单号
					status: '', //订单状态
					chooseDeviceIds: [], //设备集合
					isPayVip: ''
				},

				showQuery: {
					deviceType: '',
					merLineId: '',
					payStatus: '',
					riskType: '',
					showZero: '',
					isPayVip: ''
				},

				pickerType: null,

				// 编辑弹框
				areaShow: false,
				areaList: [], //区域数据

				timeShow: false, //时间选择弹框
				timeStamp: new Date(), //时间picker显示时间

				noLine: false, //是否有线路

				fullHeight: 0,

				tabList: [{
						name: '全部'
					},
					{
						name: '支付中'
					},
					{
						name: '补单中'
					},
				],
				current: 0,
				maxDate: '',
				deviceShow: false,
				deviceList: [],
				searchKey: ''
			}
		},

		async onLoad(o) {
			this.currentDate = new Date();
			this.maxDate = new Date().getTime()

			if (o.type == 1) { //今日数据
				this.searchQuery.orderDate = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd');
			}

			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			// this.searchQuery.orderDate = uni.$u.timeFormat(this.currentDate, 'yyyy-mm-dd')
			//改为分页接口获取 this.getCountData()
		},

		onShow() {
			this.search()
		},

		methods: {
			// 时间选择
			timeSubmit(e) {
				this.searchQuery.orderDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.timeShow = false
				this.reset()
				this.getpage()
				//改为分页接口获取 this.getCountData()
			},

			tabClick(e) {
				this.current = e.index
				this.searchQuery.orderDate = ''
				switch (this.current) {
					case 0:
						this.searchQuery.status = ''
						this.searchQuery.payStatus = ''
						this.searchQuery.supplement = false
						break;
					case 1:
						// this.searchQuery.payStatus = 8
						this.searchQuery.status = 7
						this.searchQuery.supplement = false
						break;
					case 2:
						this.searchQuery.status = ''
						this.searchQuery.payStatus = ''
						this.searchQuery.supplement = true
						break;
					default:
						break;
				}
				this.reset()
				this.getpage()
			},

			chooseDevice() {
				this.deviceShow = true
				this.getDeviceList()
			},

			/**
			 * 设备列表
			 */
			getDeviceList() {
				simpleDeviceSearchPage({
					page: {
						size: -1
					},
					searchKey: this.searchKey
				}).then(res => {
					this.deviceList = res.data.records
				})
			},

			deviceChange(e) {
				console.log(e)
				this.searchQuery.chooseDeviceIds = e
			},

			deviceClose() {
				this.deviceShow = false
				this.searchQuery.chooseDeviceIds = []
			},

			deviceConfirm() {
				this.deviceShow = false
			},

			//获取区域树
			getAreaTree() {
				areaTree().then(res => {
					this.areaList = res.data
				})
			},

			areaClose() {
				this.areaShow = false
			},

			//区域选择提交
			areaSubmit(res) {
				this.searchQuery.regionName = res.name;
				this.searchQuery.merLineId = null;
				this.showQuery.merLineId = null;
				this.getLineOption(res.name)
				this.areaClose()
			},

			//获取线路options
			getLineOption(regionName) {
				linePage({
					page: {
						current: 1,
						size: 1000,
					},
					regionName: regionName
				}).then(res => {
					let data = res.data.records;
					if (data.length > 0) {
						this.noLine = false;
						let newData = data.map(i => {
							return {
								type: i.id,
								name: i.lineName
							}
						})
						this.actions = newData;
					} else {
						this.noLine = true; //没有线路
						this.actions = [];
					}
				})
			},

			actionsheetChange(type) {
				this.pickerType = type;
				if (type == 'sblx') {
					this.actions = this.equipmentTypeList
					this.title = '请选择设备类型'
					this.actionSheetShow = true
				}
				if (type == 'xzqy') {
					this.areaShow = true;
				}
				if (type == 'lx') {
					if (this.searchQuery.regionName) {
						if (!this.noLine) {
							this.title = '请选择路线'
							this.actionSheetShow = true
						} else {
							this.$modal.msg('当前区域未建立线路~')
						}
					} else {
						this.$modal.msg('请先选择区域~')
					}
				}
				if (type == 'ddzt') {
					this.actions = this.orderStatusList
					this.title = '请选择订单状态'
					this.actionSheetShow = true
				}
				if (type == 'yczt') {
					this.actions = this.abnormalStateList
					this.title = '请选择异常状态'
					this.actionSheetShow = true
				}
				if (type == 'lhcz') {
					this.actions = this.pullBlackOperationList
					this.title = '请选择拉黑操作'
					this.actionSheetShow = true
				}
				if (type == 'cxhd') {
					this.actions = this.salesPromotionList
					this.title = '请选择促销活动'
					this.actionSheetShow = true
				}

				if (type == 'sfvip') {
					this.actions = [{
						type: '',
						name: '全部会员'
					}, {
						type: 1,
						name: 'vip会员'
					}]
					this.title = '是否vip'
					this.actionSheetShow = true
				}
			},
			details(item) {
				this.$tab.navigateTo(
					`/pages/order/orderDetails?id=${item.id}&createTime=${item.createTime}&type=${this.current}`)
			},
			actionsheetSelect(e) {
				switch (this.pickerType) {
					case 'sblx':
						this.searchQuery.deviceType = e.type
						this.showQuery.deviceType = e.name
						break;
					case 'xzqy':
						this.searchQuery.regionName = e.name
						break;
					case 'lx':
						this.searchQuery.merLineId = e.type
						this.showQuery.merLineId = e.name
						break;
					case 'ddzt':
						this.searchQuery.payStatus = e.type
						this.showQuery.payStatus = e.name
						break;
					case 'yczt':
						this.searchQuery.riskType = e.type
						this.showQuery.riskType = e.name
						break;
					case 'sfvip':
						this.searchQuery.isPayVip = e.type ? true : false
						this.showQuery.isPayVip = e.name
						break;
						// case 'lhcz':
						// 	this.pullBlackOperationname = e.name
						// 	break;
						// case 'cxhd':
						// 	this.salesPromotionname = e.name
						// 	break;
					default:
						break;
				}
			},
			close() {
				this.screenShow = false
			},
			confirm(e) {
				this.searchQuery.orderDate = e[0]
				this.show = false
				this.search()
				//改为分页接口获取 this.getCountData()
			},

			clear() {
				let obj1 = {
					id: '', //订单编号
					phone: '', //手机号码
					payOrderNo: '', //支付单号
					deviceType: '', //设备类型
					regionName: '', //区域
					merLineId: '', //线路
					riskType: '', //异常订单类型
					deviceId: '', //设备编号
					payOrderId: '', //支付定单号
					payBuyOrderId: '', //交易单号
					status: '',
					chooseDeviceIds: [],
					isPayVip: ''
				}
				for (let key in obj1) {
					this.searchQuery[key] = obj1.key
				}

				let obj2 = {
					deviceType: '',
					merLineId: '',
					riskType: '',
				}
				for (let key in obj2) {
					this.showQuery[key] = obj2.key
				}
				this.screenShow = false
				this.search()
			},

			//是否展示零元单
			showZeroChange() {
				this.search()
			},

			onByDay(e) {
				if (e == 1 && this.searchQuery.orderDate) { //后一天不能超过当前时间
					if (this.searchQuery.orderDate == uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')) {
						this.$modal.msg('别点了，没有数据啦~')
						return
					}
				}
				if (!this.searchQuery.orderDate) { //没有时间设置当前时间
					this.searchQuery.orderDate = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd');
				} else {
					let newDate = new Date(new Date(this.searchQuery.orderDate).getTime() + e * (24 * 60 * 60 *
						1000));
					this.searchQuery.orderDate = uni.$u.timeFormat(newDate, 'yyyy-mm-dd')
				}
				this.search()
			},

			//点击筛选
			screen() {
				this.screenShow = true
				this.getAreaTree()
			},
			//统计数据
			// getCountData() {
			// 	let params = {
			// 		orderDate: this.searchQuery.orderDate
			// 	}
			// 	Object.assign(params)
			// 	orderPageCount(params).then(res => {
			// 		let data = res.data;
			//
			// 		this.orderCount = data
			// 	})
			// },
			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					}
				}
				Object.assign(params, this.searchQuery)
				page(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.orderCount.orderNum = res.data.orderNum;
					this.orderCount.orderTotalAmount = res.data.orderTotalAmount;
					this.orderList = this.orderList.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.orderList = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			sure() {
				this.reset()
				this.getpage()
				this.screenShow = false;
			}
		}
	}
</script>
<style scoped lang="scss">
	.container {
		::v-deep .u-checkbox-group {
			flex-flow: row wrap !important;
			justify-content: space-between !important;
		}

		::v-deep .u-checkbox {
			min-width: 260rpx !important;
		}

		::v-deep .u-checkbox__icon-wrap {
			margin-left: 20rpx !important;
		}

		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;

			.tab {
				// width: 40%;
			}
		}

		.marleft {
			margin-left: 10rpx;
		}

		.scrollview {
			overflow: hidden;
		}

		.time-choose {
			background-color: #fff;

			.top {
				height: 80rpx;
				color: #fff;
				padding: 0 20rpx;
				background-color: #2C6FF3;
				border-radius: 0px 0px 50rpx 50rpx;

				.con-btn {
					padding: 0 36rpx;
				}

				.date {
					height: 50rpx;
					width: 300rpx;
					border-radius: 10rpx;
					background-color: #fff;
					color: #000;
					padding: 0 20rpx;

					image {
						width: 30rpx;
						height: 30rpx;
					}
				}


			}
		}

		.screen-container {
			background-color: #fff;
			padding: 20rpx 13rpx 0;

			>view:nth-child(1) {
				font-size: 28rpx;
				font-weight: 500;
				color: #777777;
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 20rpx 24rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.goods-container {
				min-height: 154rpx;
				margin-top: 14rpx;
				padding: 12rpx 12rpx 12rpx 164rpx;
				box-sizing: border-box;
				position: relative;

				.image-container {
					height: 130rpx;
					width: 130rpx;
					position: absolute;
					left: 12rpx;
					top: 50%;
					transform: translateY(-50%);
				}

				.details-container {
					position: relative;
					padding: 12rpx;

					.goods-name-num {
						.goods-name {
							font-size: 26rpx;
							color: #333;
							font-weight: bold;
						}

						.goods-num {
							font-size: 26rpx;
							color: red;
							margin-right: 40rpx;
							font-weight: bold;
						}
					}

					.goods-price {
						font-size: 28rpx;
					}

					.good-act {
						margin-top: 12rpx;
					}

					.gp-item {
						width: 50%;
						margin-top: 12rpx;
					}

					.goodf-act {
						font-size: 28rpx;
						color: #333;
						margin-top: 12rpx;
					}

					.goods-btn {
						margin-top: 12rpx;
					}

					.refund {
						position: absolute;
						right: 0;
						top: 0;
						background-color: red;
						color: #fff;
						border-radius: 4rpx;
						padding: 0 12rpx;
						line-height: 40rpx;
						font-size: 24rpx;
					}
				}

			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;

				>view {
					display: inline-block;
					width: 170rpx;
				}
			}
		}

		.popup-container {
			padding: 20rpx;
		}

		.popup-content {
			padding: 0 24rpx;
		}
	}
</style>