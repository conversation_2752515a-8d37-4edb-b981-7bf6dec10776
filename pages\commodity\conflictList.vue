<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="deviceName"></u-navbar>
		<view class="title">冲突商品</view>
		<scroll-view class="scrollview" scroll-y="true" scroll-with-animation="true" lower-threshold="100"
			@scrolltolower="loadMore" :style="{height:fullHeight}">
			<view class="scroll-content" v-if="conflictList&&conflictList.length>0">
				<block v-for="(item,index) in conflictList" :key="item.modelId">
					<view class="conflictlist-container xy-card">
						<view class="conflictlist-content flex flex-direction justify-between">
							<view class="flex-sub list-name">{{item.conflictName}}</view>
							<view class="flex-sub">关联商品：{{item.goodsIds.length}}件</view>
						</view>
						<view class="flex align-center justify-end btn-box martop">
							<view class='marleft' v-if="checkPermi(['comList:del'])">
								<xbutton size="medium" bgColor="red" round='25rpx' padding='0rpx 20rpx'
									@click='deleted(item)'>删除</xbutton>
							</view>
							<view class='marleft' v-if="checkPermi(['comList:update'])">
								<xbutton size="medium" round='25rpx' padding='0rpx 20rpx' @click='modify(item)'>修改
								</xbutton>
							</view>
							<view class='marleft' v-if="checkPermi(['comList:addCom'])">
								<xbutton size="medium" round='25rpx' padding='0rpx 20rpx' @click="relatedgoods(item)">
									关联商品
								</xbutton>
							</view>
						</view>
					</view>
				</block>
				<u-loadmore :status="loadmoreStatus" />
			</view>

			<view style="margin-top: 120rpx;" v-else>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<view class="btn safe-bottom">
			<xbutton round="88rpx" size="large" @click="add">新增冲突组
			</xbutton>
		</view>

		<xpopup :show="addShow" @open="open" @close="cancel" @confirm="submit" :title="popTitle">
			<view class="add-container">

				<!-- 	<u--input class='martop' clearable suffixIcon="arrow-down" v-model="equipmentTypename"
					suffixIconStyle="color: #909399" placeholder="设备类型" border="surround" @focus="actionSheetShow=true">
				</u--input> -->
				<view class='martop'>
					<u--input placeholder="请输入冲突组名称" v-model="name" border="surround"></u--input>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		goodsConflictList,
		goodsConflictSave,
		goodsConflictUpdate,
		goodsConflictDel
	} from "@/api/commodity/goods.js"
	import {
		detail
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				fullHeight: 0,
				conflictList: [], //冲突组
				name: '',
				popTitle: '新增冲突组',
				type: '',
				equipmentTypename: '',
				deviceType: '',
				actionSheetShow: false,
				addShow: false,

				deviceId: null,
				conflictName: '',
				conflictItem: null,

				deviceName: '',
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 70)
			this.deviceId = o.id
			this.deviceName = o.deviceName
			this.getpage()

			uni.$on('refresh', res => {
				this.getpage()
			})
		},

		methods: {
			getpage() {
				goodsConflictList({
					deviceId: this.deviceId,
					conflictName: this.conflictName
				}).then(res => {
					this.conflictList = res.data
				})
			},

			open() {
				this.addShow = true
			},

			//新增冲突组
			add() {
				this.popTitle = '新增冲突组'
				this.addShow = true
			},

			actionsheetSelect(e) {
				this.equipmentTypename = e.name
				this.deviceType = e.deviceType
			},
			cancel() {
				this.addShow = false
			},
			deleted(item) {
				uni.showModal({
					title: '提示',
					content: '是否确认删除',
					success: res => {
						if (res.confirm) {
							goodsConflictDel({
								ids: [item.id]
							}).then(res => {
								if (res.code == 200) {
									this.getpage()
									setTimeout(() => {
										this.$modal.msg('删除成功~')
									}, 500)
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},

			modify(item) {
				this.popTitle = '修改冲突组'
				this.conflictItem = item
				this.name = item.conflictName
				this.id = item.id
				this.addShow = true
			},

			saveDevComList() {
				backUpDeviceGoods({
					deviceId: this.deviceId
				}).then(res => {
					this.$modal.msg('保存成功~')
					setTimeout(() => {
						this.reset()
						this.getpage()
					}, 1000)
				})
			},

			// 新增冲突组
			submit() {
				if (!this.name) {
					uni.$u.toast('请填写冲突组名称')
					return;
				}
				if (this.popTitle == '新增冲突组') {
					goodsConflictSave({
						conflictName: this.name, //冲突组名称
						deviceId: this.deviceId, //设备类型
					}).then(res => {
						if (res.code == 200) {
							this.addShow = false
							this.getpage()
							setTimeout(() => {
								this.$modal.msg('新增成功~')
							}, 500)
						}
					})
				}
				if (this.popTitle == '修改冲突组') {
					goodsConflictUpdate({
						id: this.id,
						deviceId: this.deviceId,
						goodsIds: this.conflictItem.goodsIds,
						conflictName: this.name
					}).then(res => {
						if (res.code == 200) {
							this.addShow = false
							this.getpage()
							setTimeout(() => {
								this.$modal.msg('修改成功~')
							}, 500)
						}
					})
				}

			},

			//冲突组关联商品
			relatedgoods(item) {
				this.$tab.navigateTo(
					`conflictEdit?deviceId=${this.deviceId}&&title=${item.conflictName}`)
			},

			//冲突组应用到设备
			application(item) {
				this.modelId = item.modelId
				this.doorShow = true
			},

			// 应用到其他设备
			left() {
				this.doorShow = false
				this.$tab.navigateTo(`chooseDevice?modelId=${this.modelId}&algorithmId=${this.algorithmId}`)
			},

			// 应用到当前设备
			right() {
				saveGoodsDevice({
					deviceIds: [this.deviceId], //设备信息id
					modelId: this.modelId //冲突组ID
				}).then(res => {
					this.$modal.msg('应用成功!')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000)
				}).catch(err => {
					this.doorShow = false
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
	.container {
		.martop {
			margin-top: 20rpx;
		}

		.marleft {
			margin-left: 20rpx;
		}

		.title {
			margin: 20rpx;
		}

		.scroll-content {
			overflow: hidden;
			padding-bottom: calc(120rpx + env(safe-area-inset-bottom) / 2);
		}

		.conflictlist-container {
			margin: 0 13rpx;

			&+.conflictlist-container {
				margin-top: 20rpx;
			}

			.conflictlist-content {
				padding: 0 20rpx 20rpx;

				>view {
					line-height: 50rpx;
					color: #555;
				}

				.list-name {
					font-size: 32rpx;
					line-height: 60rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 20rpx;
				}
			}

			.btn-box {}
		}

		.btn {
			width: 100%;
			position: fixed;
			bottom: 24rpx;
			left: 0;
			padding: 24rpx 24rpx 0;
		}

		.add-container {
			padding: 20rpx 30rpx 40rpx;
		}
	}
</style>