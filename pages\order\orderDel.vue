<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="手动扣款"></u-navbar>
		<view class="video-container">
			<xvideo :src="videoUrl" :showMuteBtn="false"/>
			<view class="flex align-center justify-between" style="padding:0 24rpx;">
				<view class="video-tab">
					<u-subsection :list="videoType" activeColor="#2C6FF3" :current="videoCurrent"
						@change="sectionChange">
					</u-subsection>
				</view>
				<view class="flex">
					<view>
						<xbutton size="mini" padding="0 20rpx" @tap='addCom(deviceId)'>添加商品</xbutton>
					</view>
					<view style="margin-left: 24rpx;">
						<xbutton size="mini" width='180rpx' @tap='submit'>提交补扣申请</xbutton>
					</view>
				</view>
			</view>
		</view>
		<view class="goods-select" v-if="goodsList.length>0">
			<u-scroll-list :indicator="indicator" indicatorColor="#fff0f0" indicatorActiveColor="#f56c6c">
				<view class="flex justify-around">
					<view class="goods-item" v-for="(item,index) in goodsList" :key="index">
						<view class="flex flex-direction align-center justify-center image-dele-container goodContainer"
							@tap="clean(index,item)">
							<!-- <view class="flex align-center justify-center numberContainer">
								{{value}}
							</view> -->
							<view class="image">
								<u--image radius="4" width="100rpx" height="100rpx" :src="item.goodsImg" mode="aspectFit"
									:lazy-lord="true"></u--image>
							</view>
							<view class="goods-select-name">{{item.name}}</view>
						</view>
						<view class="flex align-center justify-center" style="margin-top: 8rpx;">
							<view class="flex align-center justify-center">
								<view class="minus" @tap="reduce(item,index)">
									<u-icon name="minus" size="12"></u-icon>
								</view>
								<text style="width: 50rpx;text-align: center;" class="input">{{item.number}}</text>
								<view class="plus" @tap="add(item)">
									<u-icon name="plus" color="#FFFFFF" size="12"></u-icon>
								</view>
							</view>
						</view>
					</view>
				</view>
			</u-scroll-list>
		</view>

		<view class="goods-select-empty" v-else>
			添加需要补扣的商品~
		</view>

		<view class="classify-wrap">
			<classify :status="status" :commList="commList"
				@comClick='detail' :height="fullHeight" :leftShow="false" />
		</view>
	</view>
</template>

<script>
	import {
		ownerGoodsList,
		list
	} from "@/api/commodity/goods.js"
	import {
		apply
	} from "@/api/order/riskorder.js"
	import {
		categoryList
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				scrollintoview: '',
				fullHeight: '0',
				tabList: [], //商品类目
				commList: [], //商品列表
				goodsList: [], //商品id列表
				status: 'nomore', //加载更多
				riskId: '',
				deviceId: '',
				video: {
					url1: '',
					url2: ''
				},
				videoUrl: null,
				videoType: ['主视频', '副视频'],
				videoCurrent: 0,
				statGoodsList: [], //平台识别补扣商品
			}
		},

		async onLoad(e) {
			this.riskId = e.id
			this.deviceId = e.deviceId
			this.initOrder()
			
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.classify-wrap')
		},
		
		onShow() {
			this.getCommList()
		},

		methods: {
			//初始化页面信息
			initOrder() {
				let orderDetail = JSON.parse(uni.getStorageSync('riskOrder'));
				if (orderDetail.video) {
					this.video = {
						url1: orderDetail.video.split(',')[0],
						url2: orderDetail.video.split(',')[1]
					}
					this.videoUrl = this.video.url1
				} else {
					this.video = {
						url1: '',
						url2: ''
					}
				}


				let tempGoodsList = orderDetail.orderGoods.map(i => {
					return {
						goodsImg: i.goodsImgUrl,
						spid: i.goodsId,
						name: i.goodsName,
						price: Number(i.totalMoney) / 100,
						number: Number(i.totalNumber),
						initNum: Number(i.totalNumber)
					}
				})

				this.statGoodsList = JSON.parse(JSON.stringify(tempGoodsList))
				this.goodsList = JSON.parse(JSON.stringify(tempGoodsList))
			},

			sectionChange(e) {
				this.videoCurrent = e;
				if (e == 0) {
					this.videoUrl = this.video.url1
				} else {
					this.videoUrl = this.video.url2
				}
			},

			//根据类目获取商品列表
			getCommList() {
				list({
					deviceId: this.deviceId,
				}).then(res => {
					let data = res.data;
					let newData = data.map(i => {
						i.price = (Number(i.price) / 100).toFixed(2)
						return i
					})
					this.commList = newData
				})
			},

			statGoodsTips() {
				this.$modal.msg('该商品为平台算法识别异常商品，为防止您的无辜损失，无法再删减！')
			},

			clean(e, item) { //点击移除商品
				//校验是否算法识别商品
				if (this.isStatGoods(item)) {
					this.statGoodsTips()
					return
				}
				this.goodsList.splice(e, 1)
			},

			// 校验是否算法识别商品
			isStatGoods(e) {
				console.log(e)
				console.log(this.statGoodsList)
				for (let i = 0; i < this.statGoodsList.length; i++) {
					let item = this.statGoodsList[i];
					if (item.spid == e.spid) {
						return true
					}
				}
				return false
			},

			//校验是否减少了算法识别商品
			isDelStatGoods(e) {
				for (let i = 0; i < this.statGoodsList.length; i++) {
					let item = this.statGoodsList[i];
					if (item.spid == e.spid && item.number == e.number) {
						return true
					}
				}
				return false
			},

			detail(e) {
				this.goodsList.forEach(item => {
					if (e.goodsId == item.spid) {
						this.add(item)
					}
				})

				for (var i = 0; i < this.goodsList.length; i++) {
					if (this.goodsList[i].spid == e.goodsId) {
						return;
					}
				}

				this.goodsList.push({
					goodsImg: e.cover,
					spid: e.goodsId,
					name: e.name,
					price: e.price,
					number: 1,
					initNum: 0
				})

			},
			//新增
			add(e) {
				e.number++
			},
			//减少
			reduce(e, index) {
				if (this.isDelStatGoods(e)) { //校验是否减少了算法识别商品
					this.statGoodsTips()
					return
				}
				e.number--
				if (e.number == 0) {
					this.goodsList.splice(index, 1)
				}
			},
			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},
			
			// 添加商品至机器
			addCom(deviceId){
				this.$tab.navigateTo('/pages/equipment/addCom?id='+deviceId)
			},
			
			//提交补扣申请
			submit() {
				if (!this.goodsList.length) {
					uni.$u.toast('请选择商品')
					return;
				}

				var goodsId = []
				this.goodsList.forEach(item => {
					if (item.number - item.initNum > 0) { //除去算法识别商品
						goodsId.push({
							goodsId: item.spid,
							goodsName: item.name,
							price: (Number(item.price)) * 100,
							totalNumber: item.number - item.initNum
						})
					}
				})
				apply({
					"cutGoods": goodsId,
					"riskId": this.riskId
				}).then(res => {
					this.$modal.msg('提交申请成功~')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 800)
				})
			}
		},

		onUnload() {
			uni.setStorage('riskOrder', '')
		},
	}
</script>

<style lang="scss" scoped>
	.container {
		.martop {
			margin-top: 20rpx;
		}

		.margin {
			margin: 10rpx 20rpx;
		}

		.margintop {
			margin-top: 10rpx;
		}

		.video-container {
			.video-tab {
				width: 300rpx;
			}
		}

		.box {
			padding: 20rpx 24rpx;
		}

		.goods-item+.goods-item {
			margin-left: 12rpx;
		}

		.image-dele-container {
			position: relative;
			width: 140rpx;
			height: 140rpx;
			background-color: #f6f6f6;
			border-radius: 15rpx;
			padding: 12rpx;

			view {
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
				width: 100rpx;
			}

			.image {
				width: 100rpx;
				height: 100rpx;
			}

			.goods-select-name {
				font-size: 24rpx;
			}
		}

		.goods-select {
			margin: 0 24rpx;
			height: 218rpx;
			padding-top: 10rpx;
		}

		.goods-select-empty {
			height: 218rpx;
			text-align: center;
			line-height: 218rpx;
		}

		.goodContainer {
			position: relative;

			.numberContainer {
				width: 35rpx;
				height: 35rpx;
				border-radius: 100%;
				color: #fff;
				background-color: red;
				position: absolute;
				right: -5rpx;
				top: -5rpx;
			}
		}

		.minus {
			width: 22px;
			height: 22px;
			border-width: 1px;
			border-color: #E6E6E6;
			border-style: solid;
			border-top-left-radius: 100px;
			border-top-right-radius: 100px;
			border-bottom-left-radius: 100px;
			border-bottom-right-radius: 100px;
			@include flex;
			justify-content: center;
			align-items: center;
		}



		.plus {
			width: 18px;
			height: 18px;
			background-color: #FF0000;
			border-radius: 50%;
			/* #ifndef APP-NVUE */
			display: flex;
			/* #endif */
			justify-content: center;
			align-items: center;
		}
	}
</style>