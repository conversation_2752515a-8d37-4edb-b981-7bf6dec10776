<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="我的发票抬头"></u-navbar>
		<view class="content" v-if="list&&list.length>0">
			<block v-for="(item,index) in list" :key="item.titleId">
				<view class="list-item flex justify-between align-center" @click="detail(item)">
					<view>
						<view class="name">
							{{item.companyName}}
						</view>
						<view class="code" v-if="item.type==1">
							{{item.taxIdNumber}}
						</view>
					</view>
					<view>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</block>
		</view>
		<view class="empty">
			<u-empty text="没有内容!"></u-empty>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				添加发票抬头
			</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		titleList
	} from '@/api/invoice.js'

	export default {
		data() {
			return {
				list: []
			}
		},

		onLoad(o) {
			this.getTitles()
		},

		methods: {
			detail(item){
				this.$tab.navigateTo(`/pages/invoice/editTitle?id=${item.titleId}&&obj=${JSON.stringify(item)}`)
			},
			getTitles() {
				titleList({}).then(res => {
					this.list = res.data
				})
			},
			submit() {
				this.$tab.navigateTo('/pages/invoice/editTitle')
			},
		},
	};
</script>

<style scoped lang="scss">
	.container {
		.content {
			padding-bottom: 200rpx;

			.list-item {
				height: 120rpx;
				padding: 0 30rpx;
				background-color: #fff;
				margin-top: 24rpx;
				line-height: 50rpx;

				.name {
					font-size: 30rpx;
					font-weight: bold;
				}

				.code {
					font-size: 26rpx;
				}
			}
		}
		
		.empty{
			padding-top: 40%;
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 9;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}
	}
</style>