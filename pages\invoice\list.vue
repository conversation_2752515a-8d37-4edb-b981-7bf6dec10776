<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="开发票"></u-navbar>
		<view class="content">
			<xhelpPopup guideId="FP0001" />
			
			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">

				<view class="scroll-content" v-if="list&&list.length>0">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="xy-card section flex" @click="lineSelect(item)">
							<view class="select-line-img" v-if="current==0">
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
									mode="widthFix" v-show="item.checked"></image>
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
									mode="widthFix" v-show="!item.checked"></image>
							</view>

							<view>
								<view class="flex align-center justify-between section-top">
									{{item.note||'其他'}}
								</view>

								<view class="section-bot">
									<view class="row-item flex align-center">
										<view class="row-name">
											订单号：
										</view>
										<view class="row-val">
											{{item.payOrderId}}
										</view>
									</view>

									<view class="row-item flex align-center">
										<view class="row-name">
											总金额：
										</view>
										<view class="row-val">
											￥{{$xy.delMoney(item.sumMoney)}}
										</view>
									</view>

									<view class="row-item flex align-center">
										<view class="row-name">
											支付时间：
										</view>
										<view class="row-val">
											{{item.payTime||'-'}}
										</view>
									</view>
								</view>
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view class="empty" v-else>
					<u-empty text="没有内容~"></u-empty>
				</view>
			</scroll-view>
		</view>

		<view class="btn" v-if="current==0">

			<view class="choose-pay flex justify-between align-center">
				<view class="all-select flex align-center" @click="selectAll">
					<view class="select-line-img">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
							mode="widthFix" v-show="allChecked"></image>
						<image
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
							mode="widthFix" v-show="!allChecked"></image>
					</view>
					<view>
						全选
					</view>
				</view>
				<view class="pay flex align-center">
					<view>
						应付：￥<text>{{$xy.delMoney(totalMoney)}}</text>
					</view>
					<view class="pay-btn">
						<xbutton @click="sure" delay="2000" fontSize="34rpx" size="large" width="180rpx" height="100rpx"
							round="100rpx" v-if="btnShow">去开票</xbutton>
						<xbutton delay="2000" bgColor="#c1c1c1" color="#333" fontSize="34rpx" size="large"
							width="180rpx" height="100rpx" round="100rpx" v-else>去开票</xbutton>
					</view>
				</view>
			</view>
		</view>

		<u-picker :show="timeShow" :columns="timeActions" confirmColor="#2C6FF3" :closeOnClickOverlay="true"
			keyName="name" @close="timeClose" @confirm="timeSubmit" @cancel="timeClose">
		</u-picker>
	</view>
</template>

<script>
	import {
		invoiceList
	} from '@/api/invoice.js'
	export default {
		data() {
			return {
				list: [],
				tabList: [{
						code: 0,
						name: '可开票订单',
					},
					{
						code: 1,
						name: '其他订单',
					}
				],
				current: 0,
				fullHeight: 0,
				allChecked: false,

				page: 1,
				size: 10,
				loadmoreStatus: 'loadmore',
			}
		},

		onLoad(o) {
			this.allChecked = false
			this.search()

			uni.$on('update', res => {
				this.current = 1
				this.search()
			})
		},

		computed: {
			totalMoney() {
				let listTotal = 0
				if (this.list.length > 0) {
					for (var i = 0; i < this.list.length; i++) {
						let item = this.list[i];
						if (item.checked) {
							listTotal += item.sumMoney
						}
					}
				}
				return listTotal
			},

			btnShow() {
				if (this.list.length > 0) {
					return this.list.find(i => i.checked) != undefined
				} else {
					return false
				}
			}
		},

		watch: {
			current: {
				async handler(newVal, oldVal) {
					if (this.current == 0) {
						this.fullHeight = await this.$xy.scrollHeight(this, '.scrollview', 74)
					} else {
						this.fullHeight = await this.$xy.scrollHeight(this, '.scrollview')
					}
				},
				immediate: true
			},
		},

		methods: {
			tabClick(e) {
				console.log(e)
				this.current = e.index
				this.chargingStatus = e.code
				this.allChecked = false
				this.search()
			},

			selectAll() {
				this.allChecked = !this.allChecked
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i]
					item.checked = this.allChecked
				}
				this.$forceUpdate()
			},

			search() {
				this.reset()
				this.getList()
			},

			getList(id) {
				invoiceList({
					page: {
						current: this.page,
						size: this.size
					},
					isInvoice: this.current == 1
				}).then(res => {
					let data = res.data.data.records;
					for (var i = 0; i < data.length; i++) {
						let item = data[i]
						item.checked = false
					}

					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}

					if (data.length > 0) {
						this.allChecked = false
					}

					this.list = this.list.concat(data)

				})
			},

			reset() {
				this.page = 1;
				this.list = []
				this.loadmoreStatus = 'loadmore'
			},

			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getList()
			},

			lineSelect(item) {
				if (this.current == 1) return
				item.checked = !item.checked
				let selectAll = true
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i]
					if (!item.checked) {
						selectAll = false
					}
				}
				this.allChecked = selectAll
				this.$forceUpdate()
			},

			sure() {
				let ids = []
				let notes = []
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i];
					if (item.checked) {
						ids.push(item.id)
						notes.push(item.note)
					}
				}
				//费用类型去重
				notes = [...new Set(notes)]
				let url = `/pages/invoice/invoiceEdit?totalMoney=${this.totalMoney}&&ids=${ids}&&notes=${notes}`
				this.$tab.navigateTo(url)
			},

			checkParams() {
				if (!this.list.find(i => i.checked)) {
					this.$modal.msg('请选择开票订单~')
					return true
				}
				return false
			},
		},

		onUnload() {
			uni.$off('update')
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			overflow: hidden;

			.tab-wrap {
				padding: 12rpx 0;
				background-color: #fff;

				.tab {
					width: 80%;
				}
			}

			.scrollview {
				.scroll-content {
					padding: 0 13rpx;
					overflow: hidden;

					.section {
						margin-top: 20rpx;

						.select-line-img {
							margin-right: 28rpx;
							margin-top: 8rpx;

							>image {
								width: 34rpx;
								height: 34rpx;
							}
						}

						.section-top {
							margin-bottom: 20rpx;
							font-size: 32rpx;
							font-weight: bold;
							word-break: break-all;
						}

						.section-bot {
							margin-top: 20rpx;
							position: relative;

							.row-item {
								font-size: 26rpx;
								color: #555555;
								line-height: 52rpx;

								.row-name {
									min-width: 140rpx;
								}

								.row-val {}
							}

							.recharge-detail {
								position: absolute;
								right: 0rpx;
								bottom: 0rpx;
							}
						}

						.device-item {
							background: #F6F7FA;
							border-radius: 14rpx;
							margin-top: 10rpx;
							padding: 24rpx 18rpx;

							.select-img {
								margin-right: 18rpx;
								margin-top: 6rpx;

								>image {
									width: 34rpx;
									height: 34rpx;
								}
							}

						}
					}
				}
			}


		}

		.btn {
			width: 100%;
			position: fixed;
			background-color: #fff;
			bottom: 0;
			left: 0;



			.choose-pay {
				height: 130rpx;
				padding: 0 13rpx;

				.all-select {
					padding: 20rpx 38rpx 20rpx 20rpx;

					.select-line-img {
						width: 34rpx;
						height: 34rpx;
						margin-right: 28rpx;

						>image {
							width: 34rpx;
							height: 34rpx;
						}
					}
				}

				.pay {
					.pay-btn {
						// width: 214rpx;
						// height: 100rpx;
						// background: #2C6FF3;
						// border-radius: 50rpx;
						// line-height: 100rpx;
						// text-align: center;
						// font-size: 36rpx;
						// color: #FFFFFF;
						margin-left: 40rpx;
					}
				}
			}

		}

		.empty {
			padding-top: 40%;
		}
	}
</style>