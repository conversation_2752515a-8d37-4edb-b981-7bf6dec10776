<!-- 冲突商品编辑 -->
<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="title"></u-navbar>
		<view class="flex align-center justify-around margintop">
			<view class="">共关联({{comList.length}})件商品 </view>
			<view class="color">点击图片可以移除</view>
			<view class="">
				<xbutton size="size" @tap='save'>保存</xbutton>
			</view>
		</view>
		<view class="margintop">
			<view style="height: 230rpx;overflow: hidden;" v-if="comList&&comList.length>0">
				<u-scroll-list :indicator="indicator" indicatorColor="#fff0f0" indicatorActiveColor="#f56c6c">
					<view class="flex justify-around">
						<view class="" v-for="(item,index) in comList" :key="item.id">
							<view class="flex flex-direction align-center justify-center image-dele-container"
								@tap="clean(item)">
								<view class="image">
									<u--image radius="4" width="120rpx" height="120rpx" :src="item.img" mode="aspectFit"
										:lazy-lord="true"></u--image>
								</view>
								<view class="">{{item.name}}</view>
							</view>
						</view>
					</view>
				</u-scroll-list>
			</view>
			<view class="no-com" v-else style="height: 230rpx;overflow: hidden;">
				请添加商品
			</view>
		</view>
		<view class="search" @click="searchComm">
			<view class="search-input">
				<u-search placeholder="商品搜索" actionText="取消" :actionStyle="{color:'#2C6FF3'}" :showAction="!leftShow"
					:clearabled="false" v-model="keyword" @search="search" @custom="cancle"></u-search>
				<view @click="scan" :class="[leftShow?'scan-icon scan-left-show':'scan-icon scan-left-hidden']">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>
			</view>

			<view class="search-history flex flex-wrap flex-start">
				<view class="history-item" v-for="(item,index) in historyList" :key="index" @click="searchFast(item)">
					{{item}}
				</view>
			</view>
		</view>
		<view class="content">
			<view class="swiperitem-content">
				<view class="classify-wrap">
					<classify :status="status" :commList="commList" :height="fullHeight" @comClick="comClick"
						:leftShow="false" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		list,
		goodsConflictList,
		goodsConflictUpdate
	} from "@/api/commodity/goods.js"
	import {
		saveKeyWord
	} from "@/utils/common.js"
	export default {
		data() {
			return {
				modelId: '', //清单id

				goodsId: [], //商品id列表

				fullHeight: "0",

				commList: [],

				id: null, //设备id
				categoryCode: null,

				leftShow: true,
				historyList: [],
				keyword: '',

				oldList: [],
				newList: [],

				title: null,
				deviceId: null,
				algorithmId: null,
				status: 'nomore',
				conflictObj: null
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.classify-wrap', 0)

			this.title = o.title
			this.deviceId = o.deviceId

			await this.getrefGoods()

			this.getCommList().then(res => {
				if (this.conflictObj.goodsIds.length > 0) {
					for (let item1 of this.conflictObj.goodsIds) {
						for (let item2 of res) {
							if (item1 == item2.goodsId) {
								this.oldList.push({
									name: item2.name,
									id: item2.goodsId,
									img: item2.cover
								})
							}
						}
					}
				}
			})
		},

		computed: {
			comList() {
				let list = []
				list = [...this.oldList, ...this.newList]
				return list
			},
		},

		methods: {
			//反显
			getrefGoods() {
				return new Promise((resolve, reject) => {
					goodsConflictList({
						deviceId: this.deviceId,
						conflictName: this.title
					}).then(res => {
						this.conflictObj = res.data[0]
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			searchComm() {
				this.leftShow = false
			},

			search(val) {
				saveKeyWord('goods', this.keyword)
				this.getCommList()
			},

			cancle(val) {
				this.keyword = ''
				this.leftShow = true
				this.search()
			},

			searchFast(e) {
				this.keyword = e
				this.search()
			},

			//扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.keyword = res.result;
						this.search()
					}
				});
			},

			//根据类目获取商品列表
			getCommList() {
				return new Promise((resolve, reject) => {
					list({
						deviceId: this.deviceId,
					}).then(res => {
						let data = res.data;
						let newData = data.map(i => {
							i.price = this.$xy.delMoney(i.price)
							return i
						})
						this.commList = data
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},
			//点击商品
			comClick(e) {
				if (this.comList.some(i => i.id == e.goodsId)) {
					this.$modal.msg('该商品已存在~')
					return
				}
				this.newList.push({
					name: e.name,
					id: e.goodsId,
					img: e.cover
				})
			},

			clean(e) {
				for (let i = 0; i < this.oldList.length; i++) {
					let item = this.oldList[i]
					if (item.id == e.id) {
						this.oldList.splice(i, 1)
						return
					}
				}

				for (let i = 0; i < this.newList.length; i++) {
					let item = this.newList[i]
					if (item.id == e.id) {
						this.newList.splice(i, 1)
						return
					}
				}

			},

			save() {
				if (!this.comList.length) {
					uni.$u.toast('请选择商品')
					return;
				}
				var comList = []
				this.comList.forEach(item => {
					comList.push(item.id)
				})
				this.conflictObj.goodsIds = comList
				goodsConflictUpdate(this.conflictObj).then(res => {
					if (res.code == 200) {
						uni.$u.toast(res.msg)
						uni.$emit('refresh')
						setTimeout(() => {
							this.$tab.navigateBack()
						}, 1000)
					}
				})
			},
		}
	}
</script>
<style lang="scss" scoped>
	.container {
		.margin {
			margin: 10rpx 20rpx;
		}

		.margintop {
			margin-top: 10rpx;
		}

		.marginleft {
			margin-left: 10rpx;
		}

		.active {
			background-color: #ffffff;
		}

		.un-active {
			background-color: #e5e5e5;
		}

		.color {
			color: red;
		}

		.image-dele-container {
			position: relative;
			width: 200rpx;
			height: 200rpx;
			margin-left: 10rpx;
			background-color: #f6f6f6;
			border-radius: 15rpx;

			view {
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
				width: 120rpx;
			}

			.image {
				width: 120rpx;
				height: 120rpx;
			}
		}

		.image-container {
			width: 150rpx;
			height: 200rpx;
			background-color: #d9d9d9;

			image {
				width: 90rpx;
				height: 100rpx;
			}
		}

		.no-com {
			height: 228rpx;
			text-align: center;
			line-height: 228rpx;
		}

		.search {
			padding: 24rpx 24rpx;
			background-color: #fff;

			.search-input {
				position: relative;

				.scan-icon {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					z-index: 2;

					&.scan-left-show {
						right: 36rpx;
					}

					&.scan-left-hidden {
						right: 100rpx;
					}
				}
			}

			.search-history {

				.history-item {
					margin-right: 24rpx;
					padding: 0 12rpx;
					background-color: #f2f2f2;
					color: #333;
					font-size: 24rpx;
					line-height: 40rpx;
					border-radius: 40rpx;
					margin-top: 24rpx;
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
		}
	}
</style>