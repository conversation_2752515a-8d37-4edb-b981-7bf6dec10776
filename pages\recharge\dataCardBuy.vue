<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="流量卡购买"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap" style="padding:0 30rpx;">
					<view class="title">收件信息</view>
					<u-form-item label="收件人" borderBottom prop="name">
						<u--input inputAlign="right" v-model="form.name" placeholder="填写收件人姓名" border="none"></u--input>
					</u-form-item>

					<u-form-item label="手机号" borderBottom prop="tel">
						<u--input inputAlign="right" v-model="form.tel" placeholder="填写手机号" border="none"></u--input>
					</u-form-item>

					<u-form-item label="收件地址" borderBottom prop="address">
						<u--input inputAlign="right" v-model="form.address" placeholder="填写收件地址"
							border="none"></u--input>
					</u-form-item>

					<u-form-item label="快递选择" @click="show=true">
						<view class="" style="text-align: right;" v-if="form.expressValue">{{form.expressValue}}</view>
						<view v-else style="color: #c0c4cc;text-align: right;">快递选择</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<view class="title">选择流量卡</view>
					<view class="card-box">
						<block v-for="(item,index) in cardOptions" :key="index">
							<view class="card-item">
								<view class="flex justify-between">
									<view class="card-name">
										{{item.name}}
									</view>
								</view>
								<view class="card-val flex">
									<view class="val">流量：{{item.flow}}</view>
									<view class="time">有效期：{{item.month}}</view>
									<view class="price">价格：￥{{$xy.delMoney(item.money)}}</view>
								</view>
								<view class="card-num flex justify-end">
									<u-number-box color="#333" v-model="item.num" min="0">
									</u-number-box>
								</view>
							</view>
						</block>
					</view>
					<view class="total">
						流量卡总计：￥{{$xy.delMoney(totalMoney)}}
					</view>
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<view class="title" style="padding-bottom: 30rpx;">备注</view>
					<view class="remark">
						<u--textarea v-model="form.mercRemark" placeholder="请输入内容"></u--textarea>
					</view>
				</view>
			</u--form>
		</view>

		<view class="btn flex justify-end align-center">
			<view class="btn-total">共计：￥{{$xy.delMoney(totalMoney+expressMoney)}}</view>
			<view class="pay-btn">
				<xbutton @click="submit" delay="2000" fontSize="34rpx" size="large" width="180rpx" height="100rpx"
					round="100rpx">确认支付</xbutton>
			</view>
		</view>

		<u-action-sheet :show="show" :actions="expressOptions" title="快递选择" @cancle="show=false" @close="show = false"
			@select="actionSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		save,
		obj
	} from "@/api/rechargeAct.js"

	export default {
		data() {
			return {
				form: {
					name: '',
					tel: '',
					address: '',
					expressCode: '',
					expressValue: '',
					mercRemark: ''
				},
				cardOptions: [],
				expressOptions: [],
				show: false
			}
		},

		computed: {
			totalMoney() {
				let total = 0
				for (let item of this.cardOptions) {
					if (item.num > 0) {
						total += (item.money * item.num)
					}
				}
				return total
			},
			expressMoney() {
				let total = 0
				if (this.form.expressCode) {
					total = Number(this.form.expressCode.split('_')[1])
				}
				return total
			}
		},

		async onLoad(o) {
			await getDict(['sim_goods', 'sim_express_type']).then(res => {
				let cardOptions = []
				let expressOptions = []
				for (let item of res) {
					if (item.paterCode == 'sim_goods') { //流量卡
						let itemObj = JSON.parse(item.value)
						itemObj.num = 0
						itemObj.code = item.code
						cardOptions.push(itemObj)
					} else { //快递
						expressOptions.push({
							name: item.msg,
							value: item.value
						})
					}
				}
				this.cardOptions = cardOptions;
				this.expressOptions = expressOptions
			})
		},

		methods: {
			submit() {
				let goodsList = []
				for (let item of this.cardOptions) {
					if (item.num > 0) {
						goodsList.push({
							code: item.code,
							name: item.name,
							num: item.num
						})
					}
				}
				let params = {
					goodsList: goodsList,
					expressCode: this.form.expressCode,
					mercRemark: this.form.mercRemark,
					address: this.form.address,
					name: this.form.name,
					tel: this.form.tel
				}
				if (this.checkParams(params)) return

				this.$tab.navigateTo(
					`/pages/recharge/rechargeIng?type=${JSON.stringify([10])}&money=${this.totalMoney+this.expressMoney}&chargeObj=${JSON.stringify(params)}`
				)
			},

			checkParams(params) {
				if (!params.name) {
					this.$modal.msg('请输入收件人姓名！')
					return true
				}
				if (!params.tel) {
					this.$modal.msg('请输入手机号！')
					return true
				}
				if (!params.address) {
					this.$modal.msg('请输入收件地址！')
					return true
				}
				if (!params.expressCode) {
					this.$modal.msg('请选择快递！')
					return true
				}
				if (params.goodsList && params.goodsList.length == 0) {
					this.$modal.msg('请选择至少一张流量卡！')
					return true
				}
				return false
			},

			actionSelect(e) {
				console.log(e)
				this.form.expressCode = e.value;
				this.form.expressValue = e.name;
				this.show = false
			},
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding: 20rpx 20rpx 160rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
				border-radius: 12rpx;

				.title {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					padding-top: 30rpx;
					margin-left: -10rpx;
				}

				.card-box {
					margin-top: 30rpx;

					.card-item {
						background: #F6F7FA;
						border-radius: 14rpx;
						margin-bottom: 10rpx;
						padding: 25rpx 32rpx 25rpx 36rpx;

						.card-name {
							font-weight: 800;
							font-size: 28rpx;
						}

						.card-val {
							font-size: 24rpx;
							color: #555;
							margin-top: 20rpx;

							.time,
							.price {
								margin-left: 24rpx;
							}
						}

						.card-num {
							margin-top: 16rpx;
						}

						&:nth-child(2) {
							background-color: #E2E9FB;
						}
					}
				}

				.total {
					text-align: right;
					font-weight: bold;
					font-size: 28rpx;
					color: #333333;
					margin-top: 20rpx;
					padding-bottom: 30rpx;
					padding-right: 16rpx;
				}

				.remark {
					padding-bottom: 30rpx;
				}
			}

			.mj-content {
				padding-bottom: 30rpx;

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 250rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.img-upload {
				height: 200rpx;
				font-size: 26rpx;
				color: #333;
				background-color: #fff;
				padding: 0 14rpx 0 30rpx;
				margin-top: 20rpx;
				border-radius: 12rpx;

				>view {
					margin-right: 41rpx;
				}
			}
		}

		.btn {
			width: 100%;
			height: 130rpx;
			background-color: #fff;
			position: fixed;
			left: 0;
			bottom: 0;
			z-index: 99;
			padding-right: 38rpx;

			.btn-total {
				margin-right: 80rpx;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>