<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="配置"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" :rules="rules" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap" style="padding:0 30rpx;">

					<u-form-item label="推荐人赠送金额" borderBottom prop="goodsName">
						<u--input inputAlign="right" type="digit" v-model="form.name" placeholder="请输入" border="none"></u--input>
					</u-form-item>
					
					<u-form-item label="被推荐人赠送金额" borderBottom prop="goodsName">
						<u--input inputAlign="right" type="digit" v-model="form.name" placeholder="请输入" border="none"></u--input>
					</u-form-item>
					
					<u-form-item label="赠送次数限制" borderBottom prop="goodsName">
						<u--input inputAlign="right" type="number" v-model="form.name" placeholder="请输入" border="none"></u--input>
					</u-form-item>

					<u-form-item label="拉新条件" borderBottom>
						<u-radio-group v-model="form.enable_status" placement="col" @change="actionStatusRadio" class="radio-group">
							<u-radio label="拉新即送" :customStyle="{marginBottom: '40rpx'}" name="拉新即送">
							</u-radio>
							<u-radio label="拉新且购买即送" name="拉新且购买即送">
							</u-radio>
						</u-radio-group>
					</u-form-item>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		addSpe,
		speDetail,
		speEdit
	} from "@/api/market/promotion.js"
	//验证规则
	let rulesObj = {
		type: 'string',
		required: true,
		message: '必填项',
		trigger: ['change']
	}

	export default {
		data() {
			return {
				form: {
					name: null,
					goods_ids: [],
					device_ids: [],
					goods_list: [],
					device_list: [],
					start_date: null,
					end_date: null,
					is_all_day: '全天',
					day_time: [{
						id: Math.random(),
						start_time: "8:00",
						end_time: "20:00"
					}],
					enable_status: '拉新即送',
					weight: null,
					is_all_device: '1',
					is_all_goods: '0',
					// user_limit_num: null
					plus_can_type: '活动优惠优先',
				},

				rules: {
					'unitName': {
						type: 'string',
						required: true,
						message: '请选择商品规格~',
						trigger: ['change']
					},
				},

				calendarShow: false,
				calendarMonthNum: '3',
				tabList: [{
						name: '满减红包'
					},
					{
						name: '折扣红包'
					},
				],
				current: 0,
				timeIndex: 0,

				comList: [{
					name: '奥利奥',
					oldPrice: 6,
					price: 5,
					num: 10
				}],

				actions: [{
						name: '会员折扣优先'
					},
					{
						name: '活动优惠优先'
					},
					{
						name: '会员折扣叠加'
					}
				],
				actionsShow: false,
			}
		},
		
		computed:{
			timeVal(){
				let str=''
				if(this.form.start_date&&this.form.end_date){
					str=this.form.start_date+'~'+this.form.end_date
				}
				return str
			}
		},

		onLoad(o) {
			this.getMonthNum()
			if (o.id) {
				this.getDetail(o.id)
			}
		},

		onShow() {
			if (uni.getStorageSync('market')) {
				console.log('showon')
				this.form = JSON.parse(uni.getStorageSync('market'))
			} else {
				uni.setStorageSync('market', JSON.stringify(this.form))
			}
		},

		watch: {
			form: {
				handler(newVal, oldVal) {
					console.log(newVal)
					uni.setStorageSync('market', JSON.stringify(newVal))
				},
				deep: true
			}
		},

		methods: {
			getDetail(id) {
				speDetail({
					id: id
				}).then(res => {
					let data = res.data
					let goods_ids = []
					let goods_list = []
					if (!data.is_all_goods && data.goods_list && data.goods_list.length > 0) {
						for (let key in data.goods_list) {
							let item = data.goods_list[key]
							goods_ids.push(item.goods_id)
							goods_list.push({
								cover: item.cover,
								goods_id: item.goods_id,
								goods_name: item.goods_name,
								price_old: item.price_old,
								price_onsale: this.$xy.delMoney(item.price_onsale),
								total_num: item.total_num
							})
						}
					}
					let dayTime = [];
					if (!data.is_all_day && data.day_time) {
						for (let key in data.day_time) {
							let item = data.day_time[key]
							let obj = {
								id: Math.random(),
								end_time: item.end_time,
								start_time: item.start_time
							}
							dayTime.push(obj)
						}
					} else {
						dayTime = [{
							id: Math.random(),
							start_time: "8:00",
							end_time: "20:00"
						}]
					}

					let device_ids = []
					if (!data.is_all_device && data.device_list && data.device_list.length > 0) {
						for (let key in data.device_list) {
							let item = data.device_list[key]
							device_ids.push(item.device_id)
						}
					}

					let params = {
						id: data.id,
						name: data.name,
						goods_ids: goods_ids,
						goods_list: goods_list,
						device_ids: device_ids,
						start_date: data.start_date,
						end_date: data.end_date,
						is_all_day: data.is_all_day ? '全天' : '指定时间',
						day_time: dayTime,
						enable_status: data.enable_status,
						type: data.type,
						weight: data.weight,
						device_list: data.device_list,
						is_all_device: data.is_all_device ? '1' : '0',
						is_all_goods: data.is_all_goods ? '1' : '0',
						plus_can_type: data.plus_can_type
					}
					this.current = data.type == '满减' ? '0' : '1'
					this.form = JSON.parse(JSON.stringify(params))
					console.log('反显数据=======' + JSON.stringify(this.form))
				})
			},

			chooseDev() {
				this.$tab.navigateTo('/pages/market/chooseDevice?type=1')
			},

			chooseCom() {
				this.$tab.navigateTo('/pages/market/chooseGoods?type=1')
			},

			chooseDate() {
				this.calendarShow = true
			},

			calendarConfirm(e) {
				console.log(e)
				this.form.start_date = e[0];
				this.form.end_date = e.at(-1)
				this.calendarShow = false
			},

			getMonthNum() {
				getDict('calendar_range').then(res => {
					this.calendarMonthNum = Number(res[0].value)
					console.log(this.calendarMonthNum)
				})
			},

			addTime() {
				if (this.form.day_time.length == 3) {
					this.$modal.msg('最多设置三个时段！')
					return
				}
				let list = JSON.parse(JSON.stringify(this.form.day_time))
				list.push({
					id: Math.random(),
					start_time: "8:00",
					end_time: "20:00"
				})
				this.form.day_time = list
				this.$forceUpdate()
			},

			delTime(index) {
				if (this.form.day_time.length == 1) {
					this.$modal.msg('最少保留一项！')
					return
				}
				this.form.day_time.splice(index, 1)
			},

			delCom(index) {
				this.form.goods_list.splice(index, 1)
				this.form.goods_ids.splice(index, 1)
				this.form.is_all_goods = '0'
				this.$forceUpdate()
			},

			actionAllDeviceRadio(e) {
				this.form.is_all_device = e
			},

			actionTimeRadio(e) {
				this.form.is_all_day = e
			},

			actionStatusRadio(e) {
				this.form.enable_status = e
			},

			timeChange(index) {
				this.timeIndex = index
				this.$refs.timeslot.open()
			},

			confirmTime(e) {
				this.form.day_time[this.timeIndex].start_time = e.start.hour + ':' + e.start.min
				this.form.day_time[this.timeIndex].end_time = e.end.hour + ':' + e.end.min
			},

			priceSet(value, oldPrice) {
				if (value != null) {
					if (value <= 0 || value * 100 >= oldPrice) {
						this.$modal.msg('特价价格需低于原价，特价不能为0')
					}
				}
			},

			plusCanChoose() {
				this.actionsShow = true
			},

			actionsSelect(e) {
				this.form.plus_can_type = e.name
			},

			//表单提交
			submit() {
				console.log(this.form)
				if (this.checkParams(this.form)) return
				this.$refs.form.validate().then(res => {
					let goods_list = this.form.goods_list.map(item => ({
						cover: item.cover,
						goods_id: item.goods_id,
						goods_name: item.goods_name,
						price_old: item.price_old,
						price_onsale: this.$xy.delMoneyL(item.price_onsale),
						total_num: item.total_num
					}))
					let params = {
						name: this.form.name,
						device_ids: this.form.is_all_device == 0 ? this.form.device_ids : null,
						goods_list: goods_list,
						start_date: this.form.start_date,
						end_date: this.form.end_date,
						is_all_day: this.form.is_all_day == '全天' ? 1 : 0,
						day_time: this.form.day_time,
						enable_status: this.form.enable_status,
						weight: this.form.weight,
						is_all_device: this.form.is_all_device,
						is_all_goods: this.form.is_all_goods,
						type: '特价',
						plus_can_type: this.form.plus_can_type
						// user_limit_num: this.form.user_limit_num
					}
					if (this.form.id) {
						params.id = this.form.id
						speEdit(params).then(res => {
							uni.setStorageSync('market', '') //清空临时存储
							this.$modal.msg('修改成功~')
							setTimeout(() => {
								this.$tab.navigateBack()
							}, 1000)
						}).catch(err => {

						})
					} else {
						addSpe(params).then(res => {
							uni.setStorageSync('market', '') //清空临时存储
							this.$modal.msg('新建成功~')
							setTimeout(() => {
								this.$tab.navigateBack()
							}, 1000)
						}).catch(err => {

						})
					}
				}).catch(errors => {
					console.log(errors)
				})
			},

			checkParams(params) {
				let bflag = false
				for (let item of params.goods_list) {
					if (item.price_onsale != 0) {
						if (item.price_onsale <= 0 || item.price_onsale * 100 >= item.price_old) {
							this.$modal.msg('特价价格需低于原价，特价不能为0')
							bflag = true
							break
						}
					}
				}
				return bflag
			},
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding: 20rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
				border-radius: 12rpx;
				.radio-group{
					position: relative;
					.tips{
						font-size: 24rpx;
						color: red;
						line-height: 50rpx;
						padding-bottom: 10rpx;
					}
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>