<template>
	<!-- 设备缴费 -->
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="结算"></u-navbar>
		<view class="content">
			<view class="title">
				{{userName}}
			</view>
			<view class="types">
				{{typesName}}
			</view>
			<view class="num">
				￥{{$xy.delMoney(money)}}
			</view>
			<view class="pay-type">
				<view class="pay-type-item flex justify-between align-center" v-if="!type.includes(5)">
					<view class="left flex align-center">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/ye.png"
							mode="widthFix"></image>
						<text>余额抵扣</text>
					</view>
					<view class="rest right flex align-center" @click="inputRestMoney">
						<text v-if="Number(restMoney)>0">{{payMoney?'￥'+$xy.delMoney(payMoney):'选余额可抵扣'}}</text>
						<text v-else>余额不足</text>
						<u-icon name="arrow-right" color="#fff" size="14"></u-icon>
					</view>
				</view>
				<view class="pay-type-item flex justify-between align-center">
					<view class="left flex align-center">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/yhq.png"
							mode="widthFix"></image>
						<text>代金券</text>
					</view>
					<view class="right flex align-center">
						<text>暂无优惠券</text>
						<u-icon name="arrow-right" color="#999999" size="14"></u-icon>
					</view>
				</view>
			</view>
			<view class="total-num flex align-center justify-end">
				合计<text>￥{{$xy.delMoney(money-payMoney)}}</text>
			</view>

			<view>
				<xbutton size="large" width="664rpx" height="100rpx" round="100rpx" @click="submit">立即支付</xbutton>
			</view>
			<view style="margin-top: 35rpx;" v-if="payMoney<money">
				<xbutton size="large" bgColor="#E6EEFF" color="#2C6FF3" width="664rpx" height="100rpx" round="100rpx"
					@click="qrCodePay">
					扫码支付（支持他人代付）</xbutton>
			</view>
		</view>

		<xpopup :show="qrcodeShow" round="12" @close="qrcodeClose" :safeAreaInsetBottom="false" :showBtn="false"
			mode="center">
			<view class="qrcode-content">
				<view class="num">
					￥{{$xy.delMoney(money-payMoney)}}
				</view>
				<view class="qrcode" @click="reflesh">
					<image style="width: 185px; height:185px;" :src="qrImg" mode="widthFix"
						:show-menu-by-longpress="true"></image>
				</view>
				<view class="pay-type flex align-item justify-between">
					<view class="pay-type-item wx" @click="changeQrcode(3)">
						微信支付
					</view>
					<view class="line">
						|
					</view>
					<view class="pay-type-item ali" @click="changeQrcode(2)">
						支付宝支付
					</view>
				</view>
				<view class="tips flex flex-start">
					<view class="tips-before">提示1：</view>
					<view class="tips-content">如需他人代付，请长按图片或截屏转发给他人付款</view>
				</view>
				<view class="tips flex flex-start">
					<view class="tips-before">提示2：</view>
					<view class="tips-content">界面退出不会影响付款结算，可放心关闭；</view>
				</view>
				<view class="tips flex flex-start">
					<view class="tips-before">提示3：</view>
					<view class="tips-content">请在<text style="color:red;">5分钟</text>内完成支付，超时后如不能支付，请点击付款码<text
							@click="reflesh">刷新</text>。</view>
				</view>
			</view>
		</xpopup>

		<xpopup :show="restShow" round="12" @close="restClose" :safeAreaInsetBottom="false" :showBtn="false"
			mode="center">
			<view class="rest-content">
				<view class="title">
					余额抵扣
				</view>
				<view class="rest-money flex align-center">
					<view class="label">账户余额：</view><text>￥{{$xy.delMoney(restMoney)}} </text>
				</view>
				<view class="pay-money flex align-center">
					<view class="label">抵扣余额：</view>
					<view class="m-input"><u--input :customStyle="{width:'100%',height:'100%'}" placeholder="请输入抵扣余额"
							type="digit" v-model="inputMoney" border="none" clearable @change="inputChange"></u--input>
					</view>
				</view>

				<view class="flex align-center justify-between" style="margin-top: 68rpx;">
					<view style="width:48%;">
						<xbutton size="large" borderColor="#2C6FF3" height="80rpx" bgColor="#fff" color="#2C6FF3"
							round="80rpx" @click="restShow=false">取消</xbutton>
					</view>
					<view style="width:48%;">
						<xbutton size="large" height="80rpx" round="80rpx" @click="restSubmit">确定</xbutton>
					</view>
				</view>
			</view>
		</xpopup>，


		<view class="canvas-box">
			<canvas style="width: 185px; height:185px;" canvas-id="myQrcode"></canvas>
		</view>
	</view>
</template>

<script>
	import {
		payCheck,
		rechargeCreate,
		account,
		wxAppPay,
		elseRechargeCreate
	} from "@/api/recharge.js"

	import drawQrcode from '@/static/js/weapp.qrcode.esm.js'
	import {
		getCode
	} from '@/utils/common.js'
	import {
		getMercId
	} from '@/utils/auth'

	export default {
		data() {
			return {
				qrcodeShow: false,
				restShow: false,
				inputMoney: '',
				payMoney: 0,
				money: 0,
				type: [],
				deviceIds: [],
				restMoney: 0,
				payType: 3, //支付类型2=支付宝 3=微信 100=赠送。默认微信支付
				qrImg: null,
				isPay: false,

				chargeObj: null,

				paying: false, //是否支付中
			}
		},

		async onLoad(o) {
			this.rechargeType(o) //存储上个页面传入参数
			await this.detail()
			//初始化余额抵扣
			if (!this.type.includes(5)) { //排除余额充值
				this.initPayMoney()
			}
		},

		computed: {
			//用户名
			userName() {
				return this.$store.state.user.name
			},
			//缴费项目名称
			typesName() {
				let typeNames = {
					'1': '设备管理费',
					'2': '算法服务费(按笔)',
					'3': '算法服务费(包月)',
					'4': '流量卡缴费',
					'5': '余额充值',
					'6': '会员功能激活',
					'7': '积分功能激活',
					'8': '短信充值',
					'9': '会员手机号充值',
					'10': '流量卡购买'
				}
				let typesNameArr = []
				if (this.type.length > 0) {
					for (var i = 0; i < this.type.length; i++) {
						let item = this.type[i];
						let typeName = typeNames[item];
						typesNameArr.push(typeName)
					}
				}
				return typesNameArr.join(',')
			}
		},

		methods: {
			//存储上个页面传入参数
			rechargeType(o) {
				if (o.type) {
					//type为数组，1设备管理费，2算法服务费(按笔)，3算法服务费(包月)，4流量卡缴费，5余额充值，6会员功能激活，7积分功能激活
					this.type = JSON.parse(o.type)
				}
				if (o.chargeObj) {
					this.chargeObj = JSON.parse(o.chargeObj)
				}
				if (o.money) {
					this.money = o.money
				}
			},

			//获取余额
			detail() {
				return new Promise((resolve, reject) => {
					account({}).then(res => {
						this.restMoney = res.data.balance
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			initPayMoney() {
				if (this.restMoney > this.money || this.restMoney == this.money) {
					this.payMoney = this.money
				} else if (this.restMoney < this.money && this.restMoney >= 0) {
					this.payMoney = this.restMoney
				} else if (this.restMoney < 0) {
					this.payMoney = 0
				}
				console.log(this.payMoney)
			},

			// 弹框
			qrcodeClose() {
				this.qrcodeShow = false
			},
			restClose() {
				this.restShow = false
			},
			restSubmit() {
				this.payMoney = this.$xy.delMoneyL(this.inputMoney)
				this.inputMoney = ""
				this.restClose()
			},

			inputRestMoney() {
				if (Number(this.restMoney) > 0) {
					this.restShow = true
				} else {
					this.$modal.msg('余额不足')
				}
			},

			//输入抵扣余额监听事件
			inputChange(e) {
				if (e.split(',')[1]?.length > 2) {
					this.inputMoney = e.toFixed(2)
				}
				if (this.$xy.delMoneyL(this.inputMoney) > this.restMoney || this.$xy.delMoneyL(this.inputMoney) > this
					.money) {
					this.inputMoney = this.restMoney < this.money ? this.$xy.delMoney(this.restMoney) : this.$xy.delMoney(
						this.money)
				}
			},

			//二维码支付生成二维码
			qrCodePay() {
				this.createOrder(this.payType).then(res => {
					let data = res.data
					this.drawQrcode(data)
					this.qrcodeShow = true
				})
			},

			//切换二维码
			changeQrcode(type) {
				if (this.isPay) return
				this.payType = type
				this.isPay = true
				this.createOrder(this.payType).then(res => {
					let data = res.data
					this.drawQrcode(data)
				})
			},

			reflesh() {
				this.changeQrcode(this.payType)
			},

			//绘制二维码
			drawQrcode(url) {
				drawQrcode({
					width: 185,
					height: 185,
					canvasId: 'myQrcode',
					// ctx: wx.createCanvasContext('myQrcode'),
					text: `${url}`,
				})

				// 绘制结束,生成本地图片链接
				setTimeout(() => {
					this.isPay = false
					this.saveImg()
				}, 1000)
			},

			//二维码保存转发
			saveImg() {
				console.log('转换图片')
				uni.canvasToTempFilePath({
					x: 0,
					y: 0,
					width: 185,
					height: 185,
					destWidth: 185, //画布宽高*dpr 以iphone6为准
					destHeight: 185,
					canvasId: 'myQrcode',
					success: res => {
						//console.log(res.tempFilePath) //生成的临时图片路径
						this.qrImg = res.tempFilePath
						console.log('临时地址======' + this.qrImg)
					},
				})
			},

			//充值提交
			submit() {
				if (this.paying) {
					this.$modal.msg('支付中~')
				} else {
					this.paying = true
					this.createOrder(3).then(async res => {
						if (res.data) {
							let order = this.$xy.urlToOrderId(res.data)
							let code = await getCode()
							wxAppPay({
								outTradeNo: order,
								appid: uni.getAccountInfoSync().miniProgram.appId,
								type: 2,
								code: code
							}).then(res => {
								let params = JSON.parse(res.data)
								uni.requestPayment({
									provider: 'wxpay',
									timeStamp: params.timeStamp,
									nonceStr: params.nonceStr,
									package: params.package,
									signType: params.signType,
									paySign: params.paySign,
									success: res => {
										this.paying = false
										this.$tab.navigateTo(
											`/pages/recharge/rechargeSuccess?money=${this.money}&type=${this.type}`
										)
									},
									fail: err => {
										this.paying = false
										console.log('fail:' + JSON.stringify(err));
									}
								});
							}).catch(err => {
								this.paying = false
							})
						} else {
							this.paying = false
							this.$tab.navigateTo(
								`/pages/recharge/rechargeSuccess?money=${this.money}&type=${this.type}`)
						}
					}).catch(err => {
						this.paying = false
					})
				}
			},

			createOrder(payType) {
				return new Promise(async (resolve, reject) => {
					try {
						let res = null
						if (!this.type.includes(10)) {
							let code = await getCode()
							res = await rechargeCreate(Object.assign(this.delParams(payType), {
								code: code
							}))
						} else { //流量卡购买调用其他创建订单逻辑
							res = await elseRechargeCreate(this.delParams(payType))
						}
						resolve(res)
					} catch (e) {
						reject(e)
					}
				})
			},

			/**
			 * 处理参数
			 * @param {Object} type 2=支付宝 3=微信 100=赠送
			 */
			delParams(type) {
				if (!this.type.includes(10)) { //正常支付逻辑
					let params = {
						deductionMercAccountMoney: this.payMoney ? this.payMoney : 0,
						deviceAlgorithmChargings: null,
						deviceChargings: null,
						payType: type, //2=支付宝 3=微信 100=赠送
						mercAccountMoney: null, //充值余额
						appid: uni.getAccountInfoSync().miniProgram.appId,
					}
					if (this.type.includes(1)) { //设备管理费
						params.deviceChargings = this.chargeObj.deviceInfo
					}
					if (this.type.includes(2)) { //算法服务费（按笔）
						params.deviceAlgorithmChargings = this.chargeObj.infCharge
					}
					if (this.type.includes(3)) { //算法服务费（按月）
						params.deviceAlgorithmChargings = this.chargeObj.moonCharge
					}
					if (this.type.includes(4)) { //流量卡缴费
						params.deviceSimCharges = this.chargeObj.deviceSimCharges
					}
					if (this.type.includes(5)) { //余额充值
						params.deductionMercAccountMoney = 0
						params.mercAccountMoney = this.money
					}
					if (this.type.includes(6)) { //会员功能激活
						params.mercPlusConfig = true
					}
					if (this.type.includes(7)) { //积分功能激活
						params.mercPointsConfig = true
					}
					if (this.type.includes(8)) { //短信充值
						params.mercSms = this.chargeObj
					}
					if (this.type.includes(9)) { //短信充值
						params.mercMemberTelPackage = this.chargeObj
					}
					return params
				} else { //流量卡购买调用其他创建订单逻辑
					let params = {
						goodsList: this.chargeObj.goodsList,
						expressCode: this.chargeObj.expressCode,
						mercRemark: this.chargeObj.mercRemark,
						address: this.chargeObj.address,
						name: this.chargeObj.name,
						tel: this.chargeObj.tel,
						payType: type,
						mercId: getMercId(),
						deductionMercAccountMoney: this.payMoney ? this.payMoney : 0
					}
					return params
				}
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;
		overflow: hidden;

		.content {
			background-color: #fff;
			width: 724rpx;
			margin-top: 24rpx;
			border-radius: 12rpx;
			padding: 24rpx 30rpx;
			margin-left: 13rpx;
			overflow: hidden;
			min-height: 100vh;

			.title {
				font-size: 42rpx;
				text-align: center;
				margin-top: 50rpx;
			}

			.types {
				font-size: 28rpx;
				color: #999;
				line-height: 50rpx;
				text-align: center;
			}

			.num {
				text-align: center;
				font-size: 54rpx;
				font-weight: bold;
				color: #333;
				margin-top: 72rpx;
			}

			.pay-type {
				margin-top: 180rpx;

				.pay-type-item {
					line-height: 50rpx;
					padding-bottom: 36rpx;

					+.pay-type-item {
						border-top: 1rpx solid #F7F7F7;
						padding-top: 36rpx;
					}

					.left {
						>image {
							width: 40rpx;
							height: 40rpx;
							margin-right: 33rpx;
						}

						>text {
							font-size: 28rpx;
							color: #333;
						}
					}

					.right {
						color: #999;
						font-size: 24rpx;

						>text {
							margin-right: 12rpx;
						}

						&.rest {
							padding: 0 18rpx 0 25rpx;
							background: linear-gradient(0deg, #FF7750, #FF5F42);
							border-radius: 25rpx;
							color: #fff;
						}
					}
				}
			}

			.total-num {
				font-size: 24rpx;
				color: #333333;
				margin-top: 24rpx;
				margin-bottom: 100rpx;

				>text {
					font-weight: bold;
					font-size: 42rpx;
					margin-left: 30rpx;
				}
			}
		}

		.qrcode-content {
			width: 616rpx;
			border-radius: 18rpx;
			padding: 50rpx 110rpx;
			text-align: center;

			.num {
				text-align: center;
				font-size: 46rpx;
				font-weight: 800;
				color: #333333;
			}

			.qrcode {
				width: 370rpx;
				height: 370rpx;
				margin-top: 43rpx;
				position: relative;
				left: 50%;
				transform: translateX(-50%);
			}

			.pay-type {
				padding: 40rpx 12rpx;

				>.pay-type-item {
					width: 160rpx;
					text-decoration: underline;
					font-size: 26rpx;
					font-weight: 800;
					color: #2C6FF3;
				}

				.line {
					color: #2C6FF3;
				}
			}

			.tips {
				font-size: 22rpx;
				font-weight: 500;
				color: #999999;
				line-height: 30rpx;
				text-align: left;
				margin-bottom: 12rpx;

				.tips-before {
					width: 110rpx;
				}

				.tips-content {
					width: 390rpx;

					>text {
						color: #2C6FF3;
						font-style: italic;
						text-decoration: underline;
					}
				}
			}

			.tips1 {
				font-size: 22rpx;
				color: #999999;
				line-height: 30rpx;
				// margin-top: 65rpx;
				margin-top: 24rpx;
			}
		}

		.rest-content {
			width: 616rpx;
			border-radius: 18rpx;
			padding: 40rpx 58rpx;

			.title {
				text-align: center;
				font-size: 28rpx;
				font-weight: 800;
				color: #333333;
			}

			.rest-money {
				font-size: 28rpx;
				color: #333333;

				>.label {
					width: 150rpx;
				}

				>text {
					font-weight: 800;
				}

				margin-top: 90rpx;
			}

			.pay-money {
				font-size: 28rpx;
				color: #333333;
				margin-top: 44rpx;

				>.label {
					width: 150rpx;
				}

				>.m-input {
					width: 337rpx;
					height: 66rpx;
					background: #F1F3F7;
					border-radius: 10rpx;
					padding: 0 24rpx;
				}
			}
		}

		.canvas-box {
			position: relative;
			left: -2000rpx;
		}
	}
</style>