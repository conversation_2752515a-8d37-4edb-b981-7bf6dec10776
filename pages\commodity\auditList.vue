<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="商品建模"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search animation placeholder="商品搜索" :clearabled="false" v-model="keyword" :showAction="false"
					@search="search"></u-search>
				<view class="scan-icon" @click="scan">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>
			</view>
			<view class="tab-list">
				<u-tabs :list="tabList" :scrollable="false" :current="pos" @click="tabClick" lineColor="#2C6FF3">
				</u-tabs>
			</view>

			<view class="tab-wrap flex flex-wrap flex-start">
				<block v-for="(item,index) in typeList" :key="item.name">
					<view :class="[algorithmId==item.value?'tab-item tab-show':'tab-item']"
						@click="typeChange(item.value)">
						{{item.name}}
					</view>
				</block>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				:style="{height:fullHeight}">
				<view class="list" v-if="commList&&commList.length>0">
					<block v-for="(item,index) in commList" :key="item.id">
						<view class="list-item" @click.native="goDetail(item)">
							<view class="comm-item">
								<view class="image">
									<u--image width="130rpx" height="130rpx" :src="item.cover" mode="widthFix"
										:lazy-load="true"></u--image>
								</view>
								<view class="item-content">
									<view>
										{{item.goodsName}}
									</view>
									<view>
										{{item.brandName}}
									</view>
									<view style="color: red;">
										算法类型：{{item.algorithmAlias}}
									</view>
									<view>
										创建时间：{{item.createTime}}
									</view>
									<view class="failedMsg" v-if="item.status==-1">
										驳回原因：{{item.failedMsg}}
									</view>
								</view>
								<view class="tag">
									<u-tag text="已通过" plain size="mini" type="success" v-if="item.status==1"></u-tag>
									<u-tag text="已驳回" plain size="mini" type="error" v-if="item.status==-1"></u-tag>
									<u-tag text="审核中" plain size="mini" type="warning" v-if="item.status==0"></u-tag>
								</view>
							</view>
						</view>
					</block>
					<u-loadmore :status="status" v-if="commList.length>=1" />
				</view>
				<view class="empty" v-else>
					<u-empty></u-empty>
				</view>
			</scroll-view>

		</view>
		<view class="btn safe-bottom" v-if="checkPermi(['comModel:add'])">
			<xbutton size="large" width="702rpx" round="130rpx" @click="$tab.navigateTo('/pages/commodity/addCom')">新品建模
			</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		goodsPage
	} from "@/api/commodity/goodsMode.js"
	import {
		mercAiList
	} from "@/api/commodity/goods.js"
	export default {
		data() {
			return {
				keyword: '',
				tabList: [{
						name: '已通过',
						id: 1
					},
					{
						name: '审核中',
						id: 0
					},
					{
						name: '已驳回',
						id: -1
					}
				],

				page: 1,
				size: 10,
				keywords: '',
				typeList: [],

				pos: 1,
				current: 0,
				status: 'loadmore',
				commList: [],
				fullHeight: 0,
				algorithmId: ''
			}
		},
		async onLoad() {
			await this.getAlgoType()
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 70)

			this.getList()
		},
		methods: {
			getAlgoType() {
				return new Promise((resolve, reject) => {
					mercAiList({}).then(res => {
						let data = res.data
						let newData = data.map(i => {
							return {
								value: i.id,
								name: i.alias
							}
						})
						newData.unshift({
							value: '',
							name: '全部'
						})
						this.typeList = JSON.parse(JSON.stringify(newData))
						resolve(res)
					})
				})
			},

			typeChange(e) {
				this.algorithmId = e
				this.search()
			},

			tabClick(e) {
				console.log(e)
				this.pos = e.index;
				this.current = e.id;
				this.reset();
				this.getList();
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},

			getList() {
				goodsPage({
					page: {
						current: this.page,
						size: this.size
					},
					keywords: this.keyword,
					status: this.current,
					algorithmId: this.algorithmId
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.commList = this.commList.concat(data)
				})
			},

			scrolltolower() {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			search(val) {
				this.reset()
				this.getList()
			},

			scan() {
				uni.scanCode({
					success: function(res) {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
					}
				});
			},

			// 详情
			goDetail(e) {
				this.$tab.navigateTo('/pages/commodity/addCom?id=' + e.id)
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		height: 100% !important;
	}

	.container {
		height: 100% !important;

		.content {
			// padding-bottom: 88rpx;

			.search {
				padding: 24rpx 24rpx;
				background-color: #fff;
				position: relative;

				.scan-icon {
					position: absolute;
					right: 36rpx;
					top: 50%;
					transform: translateY(-50%);
					z-index: 2;
				}
			}

			.tab-list {
				width: 100%;
				padding: 0 24rpx;
				background-color: #fff;
				margin-bottosearchm: 12rpx;
			}

			.tab-wrap {
				width: 100%;
				background-color: #fff;
				padding: 24rpx 26rpx 0;

				.tab-item {
					padding: 0 20rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					line-height: 62rpx;
					margin-bottom: 14rpx;
					text-align: center;
					margin-right: 12rpx;

					&.tab-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}
			}

			.empty {
				padding-top: 40%;
			}

			.list {
				width: 100%;
				padding: 0 24rpx;
				margin-top: 16rpx;

				.comm-item {
					display: flex;
					flex-direction: row;
					justify-content: flex-start;
					align-items: center;
					background-color: #fff;
					margin-bottom: 12rpx;
					border-radius: 12rpx;
					box-sizing: border-box;
					padding: 12rpx;
					position: relative;

					.image {
						width: 130rpx;
						height: 130rpx;
					}

					.item-content {
						padding-left: 24rpx;
						color: #999;

						>view:nth-child(1) {
							font-size: 28rpx;
							font-weight: bold;
							color: #333;

						}

						>view:nth-child(2) {
							font-size: 24rpx;
							padding: 12rpx 0;
						}

						>view:nth-child(3) {
							font-size: 24rpx;
							padding-bottom: 12rpx;
						}

						.failedMsg {
							color: red;
							font-size: 24rpx;
							line-height: 30rpx;
							margin-top: 10rpx;
						}
					}

					.tag {
						position: absolute;
						right: 12rpx;
						top: 12rpx;
					}
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
		}
	}
</style>