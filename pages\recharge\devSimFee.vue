<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="流量卡续费"></u-navbar>
		<view class="content">
			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>

				</view>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				
				<view class="tips">
					流量卡费：流量卡套餐费+数据流量使用费（视频数据存储费，视频数据识别查看流量费）。
					商家自行换卡，每台机器将收取数据流量使用费150元/年。
				</view>

				<view class="scroll-content" v-if="list&&list.length>0">
					<block v-for="(item,index) in list" :key="item.sim.id">
						<view class="xy-card section" @click.stop="lineSelect(item)">
							<view class="flex align-center justify-between section-top">
								<view class="flex align-center">
									<view class="select-line-img">
										<block v-if="item.sim.activateTime">
											<image
												src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
												mode="widthFix" v-show="item.checked"></image>
											<image
												src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
												mode="widthFix" v-show="!item.checked"></image>
										</block>
										<block v-else>
											<image
												src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-stopslect.png"
												mode="widthFix"></image>
										</block>
									</view>
									<view class="title flex justify-between align-center">
										<view class="title-left flex align-center">
											<view class="device-name">
												{{item.sim.id}}
											</view>
											<view class="online-status"
												:class="[item.deviceInfo.deviceStatus.netState==1?'online-status':'online-status online-status-no']">
												<text v-if="item.deviceInfo.deviceStatus.netState==1">在线</text>
												<text v-else>离线</text>
											</view>
										</view>

									</view>
								</view>

								<view class="title-right">
									{{item.sim.timeoutStatus}}
								</view>
							</view>

							<view class="section-bot">
								<view class="row-item flex align-center">
									<view class="row-name">
										设备：
									</view>
									<view class="row-val">
										<text
											v-if="item.deviceInfo.deviceName">{{item.deviceInfo.deviceName}}({{item.deviceInfo.deviceId}})</text>
										<text v-else>{{item.deviceInfo.deviceId}}</text>
									</view>
								</view>
								<!-- <view class="row-item flex align-center">
									<view class="row-name">
										机器类型：
									</view>
									<view class="row-val">
										{{deviceType[item.deviceInfo.deviceType]}}
									</view>
								</view> -->
								<view class="row-item flex align-center">
									<view class="row-name">
										套餐：
									</view>
									<view class="row-val">
										{{item.chargingName}}
									</view>
								</view>
								<!-- <view class="row-item flex align-center">
									<view class="row-name">
										设备状态：
									</view>
									<view class="row-val">
										在线
									</view>
								</view> -->
								<view class="row-item flex align-center">
									<view class="row-name">
										计费标准：
									</view>
									<view class="row-val">
										{{$xy.delMoney(item.chargingMoney)}}元/年
									</view>
								</view>
								<view class="row-item flex align-center" v-if="item.sim.lastRenewalTime">
									<view class="row-name">
										续费时间：
									</view>
									<view class="row-val">
										{{item.sim.lastRenewalTime||'-'}}
									</view>
								</view>
								<view class="row-item flex align-center" v-else>
									<view class="row-name">
										激活时间：
									</view>
									<view class="row-val">
										{{item.sim.activateTime||'-'}}
									</view>
								</view>

								<view class="row-item flex align-center">
									<view class="row-name">
										到期时间：
									</view>
									<view class="row-val">
										{{item.sim.timeout||'-'}}
									</view>
								</view>
								<!-- <view class="row-item flex align-center">
									<view class="row-name">
										说明：
									</view>
									<view class="row-val">
										{{item.chargingName}}
									</view>
								</view> -->


								<view class="recharge-detail">
									<xbutton width="140rpx" round="70rpx" @click.native.stop="copy(item.sim.id)">复制卡号
									</xbutton>
								</view>
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view class="empty" v-else>
					<u-empty text="没有设备!"></u-empty>
				</view>
			</scroll-view>
		</view>

		<view class="btn">
			<view class="all-select flex align-center" @click="selectAll">
				<view class="select-line-img">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
						mode="widthFix" v-show="allChecked"></image>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
						mode="widthFix" v-show="!allChecked"></image>
				</view>
				<view>
					全选
				</view>
			</view>
			<view class="choose-pay flex justify-between align-center">
				<view class="choose flex align-center" @click="selectTime">
					<view v-if="payTime">
						{{payTime}}年
					</view>
					<view v-else>
						{{'续期'}}
					</view>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/arrow-bot.png"
						mode="widthFix"></image>
				</view>
				<view class="pay flex align-center">
					<view>
						应付：￥<text>{{$xy.delMoney(totalMoney)}}</text>
					</view>
					<view class="pay-btn">
						<xbutton @click="sure" delay="2000" fontSize="34rpx" size="large" width="180rpx" height="100rpx"
							round="100rpx" v-if="btnShow">去结算</xbutton>
						<xbutton delay="2000" bgColor="#c1c1c1" color="#333" fontSize="34rpx" size="large"
							width="180rpx" height="100rpx" round="100rpx" v-else @click="$modal.msg('请选择至少一张流量卡！')">去结算
						</xbutton>
					</view>
				</view>
			</view>
		</view>

		<u-picker :show="timeShow" :columns="timeActions" confirmColor="#2C6FF3" :closeOnClickOverlay="true"
			keyName="name" @close="timeClose" @confirm="timeSubmit" @cancel="timeClose">
		</u-picker>
	</view>
</template>

<script>
	import {
		simFeeList
	} from "@/api/recharge.js"
	import {
		pageCount
	} from "@/api/device/device.js"
	import getDict from '@/utils/getDict.js'
	import {
		getMercId
	} from '@/utils/auth'
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],

				tabList: [],
				current: 0,
				fullHeight: 0,
				allChecked: false,

				timeShow: false,
				timeActions: [],

				page: 1,
				size: 10,
				loadmoreStatus: 'loadmore',

				chargingStatus: '',
				deviceType: [],
				payTime: ''
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 112)
		},

		async onShow() {
			await getDict(['device_charging_query_type', 'device_type', 'device_manage_change_time']).then(res => {
				let tabList = [{
					code: '',
					name: '全部',
					badge: {
						value: 0,
					}
				}]
				let deviceType = {}
				let timeActions = []
				for (var i = 0; i < res.length; i++) {
					let item = res[i];
					//tab类型字典
					if (item.paterCode == 'device_charging_query_type') {
						let obj = {
							code: item.code,
							name: item.msg,
							badge: {
								value: 0,
							}
						}
						tabList.push(obj)
					}
					//设备类型字典
					if (item.paterCode == 'device_type') {
						deviceType[item.code] = item.msg
					}
					//充值时长类型字典
					if (item.paterCode == 'device_manage_change_time') {
						let obj = {
							code: item.code,
							name: item.msg
						}
						timeActions.push(obj)
					}
				}
				this.tabList = tabList
				this.deviceType = deviceType
				this.timeActions = [timeActions]
				this.search()
				this.allChecked = false
				this.payTime = this.timeActions[0][0].code
			})

			await this.getTabNum().then(res => {
				let data = res.data;
				this.tabList.forEach(i => {
					switch (i.name) {
						case '全部':
							i.badge.value = data.allCount
							break;
						case '即将到期':
							i.badge.value = data.beTimeoutCount
							break;
						case '欠费':
							i.badge.value = data.timeoutCount
							break;
						default:
							break;
					}
				})
			})
		},

		computed: {
			totalMoney() {
				let listTotal = 0
				if (this.list && this.list.length > 0) {
					for (var i = 0; i < this.list.length; i++) {
						let item = this.list[i];
						if (item.checked) {
							listTotal += item.chargingMoney
						}
					}
				}
				return listTotal * this.payTime
			},

			chargeObj() {
				let chargeObj = {
					deviceSimCharges: []
				}
				if (this.list && this.list.length > 0) {
					for (var i = 0; i < this.list.length; i++) {
						let item = this.list[i];
						if (item.checked) {
							let obj = {
								simId: item.sim.id,
								size: this.payTime,
							}
							chargeObj.deviceSimCharges.push(obj)
						}
					}
				}
				return chargeObj
			},

			btnShow() {
				return this.payTime && this.list.find(i => i.checked) != undefined
			}
		},

		methods: {
			getTabNum() {
				return new Promise((resolve, reject) => {
					pageCount({
						isBind: true
					}).then(res => {
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},
			tabClick(e) {
				console.log(e)
				this.current = e.index
				this.chargingStatus = e.code
				this.allChecked = false
				this.search()
			},

			selectAll() {
				this.allChecked = !this.allChecked
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i]
					if (item.sim.activateTime) {
						item.checked = this.allChecked
					}
				}
				this.$forceUpdate()
			},

			selectTime() {
				this.timeShow = true
			},

			timeSubmit(e) {
				this.payTime = e.value[0].code
				this.timeShow = false
			},

			timeClose() {
				this.timeShow = false
			},

			search() {
				this.reset()
				this.getList()
			},

			goDetail(deviceId) {
				this.$tab.navigateTo(`/pages/recharge/rechargeDetail?deviceId=${deviceId}`)
			},

			getList(id) {
				simFeeList({
					chargingStatus: this.chargingStatus,
					isBind: true,
					mercId: getMercId(),
					page: {
						current: this.page,
						size: this.size
					}
				}).then(res => {
					let total = res.data.total;
					let data = res.data.records;
					for (var i = 0; i < data.length; i++) {
						let item = data[i]
						item.checked = false
					}

					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					if (data.length > 0) {
						this.allChecked = false
					}
					this.list = this.list.concat(data)
				})
			},

			reset() {
				this.page = 1;
				this.list = []
				this.loadmoreStatus = 'loadmore'
			},

			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getList()
			},

			lineSelect(item) {
				if (!item.sim.activateTime) {
					this.$modal.msg('不可选择！')
					return
				}
				item.checked = !item.checked
				let selectAll = true
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i]
					if (!item.checked) {
						selectAll = false
					}
				}
				this.allChecked = selectAll
				this.$forceUpdate()
			},

			sure() {
				let type = [4]
				let url =
					`/pages/recharge/rechargeIng?type=${JSON.stringify(type)}&chargeObj=${JSON.stringify(this.chargeObj)}&money=${this.totalMoney}`
				this.$tab.navigateTo(url)
			},

			checkParams() {
				if (!this.list.find(i => i.checked)) {
					this.$modal.msg('请选择需要充值的设备~')
					return true
				}
				if (!this.payTime) {
					this.$modal.msg('请选择充值时长~')
					return true
				}
				return false
			},

			copy(text) {
				console.log(text)
				uni.setClipboardData({
					data: text,
					success: (data) => {
						uni.showToast({
							title: '复制成功'
						})
					},
					fail: function(err) {

					},
					complete: function(res) {

					}
				})
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			overflow: hidden;

			.tab-wrap {
				padding: 12rpx 0;
				background-color: #fff;

				.tab {
					width: 80%;
				}
			}

			.scrollview {
				
				.tips{
					padding:20rpx 13rpx 0;
					color:red;
				}
				
				.scroll-content {
					padding: 0 13rpx;

					.section {
						margin-top: 30rpx;

						.section-top {
							height: 62rpx;
							margin-bottom: 20rpx;

							.select-line-img {
								margin-right: 16rpx;

								>image {
									width: 34rpx;
									height: 34rpx;
								}
							}

							.title {

								.title-left {
									.device-name {
										font-size: 32rpx;
										font-weight: bold;
										width: 370rpx;
										word-break: break-all;
									}

									.online-status {
										width: 92rpx;
										height: 38rpx;
										line-height: 38rpx;
										background: #F4F8FF;
										border-radius: 8rpx;
										font-size: 26rpx;
										color: #2C6FF3;
										text-align: center;
										margin-left: 8rpx;

										&.online-status-no {
											background: #F3F3F7;
											color: #999999;
										}
									}
								}


							}

							.title-right {
								font-size: 26rpx;
								color: #F00E0E;
							}
						}

						.section-bot {
							margin-top: 20rpx;
							padding-left: 48rpx;
							position: relative;

							.row-item {
								font-size: 28rpx;
								color: #555555;
								line-height: 56rpx;

								.row-name {
									min-width: 140rpx;
								}

								.row-val {}
							}

							.recharge-detail {
								position: absolute;
								right: 0rpx;
								bottom: 0rpx;
							}
						}

						.device-item {
							background: #F6F7FA;
							border-radius: 14rpx;
							margin-top: 10rpx;
							padding: 24rpx 18rpx;

							.select-img {
								margin-right: 18rpx;
								margin-top: 6rpx;

								>image {
									width: 34rpx;
									height: 34rpx;
								}
							}

						}
					}
				}
			}


		}

		.btn {
			width: 100%;
			position: fixed;
			background-color: #fff;
			bottom: 0;
			left: 0;

			.all-select {
				background-color: #F4F4F4;
				padding: 20rpx 38rpx;

				.select-line-img {
					width: 34rpx;
					height: 34rpx;
					margin-right: 28rpx;

					>image {
						width: 34rpx;
						height: 34rpx;
					}
				}
			}

			.choose-pay {
				height: 130rpx;
				padding: 0 13rpx;

				.choose {
					height: 60rpx;
					background: #E0E0E0;
					border-radius: 30rpx;
					line-height: 60rpx;
					font-size: 26rpx;
					color: #333;
					padding: 0 20rpx;

					>view {
						padding-right: 10rpx;
					}

					>image {
						width: 25rpx;
						height: 24rpx;
					}
				}

				.pay {
					.pay-btn {
						// width: 214rpx;
						// height: 100rpx;
						// background: #2C6FF3;
						// border-radius: 50rpx;
						// line-height: 100rpx;
						// text-align: center;
						// font-size: 36rpx;
						// color: #FFFFFF;
						margin-left: 40rpx;
					}
				}
			}

		}

		.empty {
			padding-top: 40%;
		}
	}
</style>