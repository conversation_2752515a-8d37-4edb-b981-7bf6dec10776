<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff"
			:placeholder="true" title="采集商品信息">
		</u-navbar>

		<view>
			<view class="form-item" @click="showGoodsSelect">
				<text class="label">商品名称:</text>
				<view class="input-wrapper">
					<input class="input with-arrow" v-model="goodsInfo.name" placeholder="请选择商品" disabled />
					<u-icon name="arrow-right" size="18" color="#999"></u-icon>
				</view>
			</view>
		</view>

		<!-- 固定在底部的操作按钮 -->
		<view class="fixed-bottom-bar">
			<button class="save-btn" @click="submitForm">
				继续采集
			</button>
		</view>

		<!-- 商品选择弹窗 -->
		<xpopup :show="showGoodsPopup" :mode="'center'" :title="'选择商品'" :showBtn="false" :round="'12'"
			@close="closeGoodsPopup">
			<view class="goods-popup">
				<!-- 搜索框 -->
				<view class="search-box">
					<u-search v-model="searchKeyword" placeholder="请输入商品名称搜索" :show-action="false" :clearabled="true"
						@input="handleSearch" @clear="clearSearch" height="70rpx">
					</u-search>
				</view>

				<!-- 商品列表容器 -->
				<view class="goods-list-container">
					<scroll-view scroll-y class="goods-list">
						<view class="goods-item" v-for="(item, index) in goodsList" :key="index"
							@click="selectGoods(item)">
							<image class="goods-image" :src="item.mainImg || '/static/images/default-goods.png'"
								mode="aspectFill" />
							<view class="goods-info">
								<text class="goods-name">{{ item.name }}</text>
								<text class="goods-spec">规格：{{ item.specs }}</text>
								<text class="goods-brand">品牌：{{ item.brandName }}</text>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		waitGatherProduct
	} from "@/api/collection.js";
	export default {
		data() {
			return {
				showGoodsPopup: false,
				searchKeyword: "",
				goodsList: [],
				selectedGoods: null,
				goodsInfo: {
					"productId": null,
					"name": null,
					"brandName": null,
					"specS": null,
					"category": null
				},
				deviceId: null
			};
		},

		onLoad(o) {
			this.deviceId = o.deviceId;
		},

		methods: {
			/**
			 * 处理搜索输入，使用防抖优化
			 * @param {string} value - 搜索关键词
			 */
			handleSearch(value) {
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				this.searchTimer = setTimeout(() => {
					this.searchGoods();
				}, 300);
			},

			/**
			 * 显示商品选择弹窗
			 */
			showGoodsSelect() {
				this.showGoodsPopup = true;
				this.handleSearch()
			},

			/**
			 * 根据关键词搜索商品
			 */
			searchGoods() {
				waitGatherProduct({
					name: this.searchKeyword,
				}).then((res) => {
					if (res.code === 200) {
						this.goodsList = res.data || [];
					}
				});
			},

			/**
			 * 清空搜索内容和结果
			 */
			clearSearch() {
				this.searchKeyword = "";
				this.goodsList = [];
			},

			/**
			 * 关闭商品选择弹窗
			 */
			closeGoodsPopup() {
				this.showGoodsPopup = false;
				this.searchKeyword = "";
				this.goodsList = [];
			},

			/**
			 * 选择商品并填充表单
			 * @param {Object} item - 选中的商品信息
			 */
			selectGoods(item) {
				this.goodsInfo = item
				this.closeGoodsPopup();
				uni.showToast({
					title: "已选择商品",
					icon: "success",
				});
			},

			submitForm() {
				if (!this.goodsInfo.productId) return this.$modal.msg('请选择商品')
				this.$tab.navigateTo(`/pages/collection/ready?goodsInfo=${JSON.stringify(this.goodsInfo)}&deviceId=${this.deviceId}`);
			}
		},
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #fff;
		position: relative;
		padding: 0 20rpx;

		.form-item {
			display: flex;
			align-items: center;
			min-height: 88rpx;
			margin-top: 50rpx;

			&:last-child {
				border-bottom: none;
			}

			.label {
				width: 130rpx;
				font-size: 28rpx;
				color: #333;

				&.required::before {
					content: "*";
					color: #ff3b30;
					margin-right: 4rpx;
				}
			}

			.input-wrapper {
				flex: 1;
				display: flex;
				align-items: center;
				border: 1rpx solid #f5f5f5;
				padding: 0 20rpx;
				border-radius: 12rpx;

				.input {
					flex: 1;
				}
			}

			.input {
				height: 88rpx;
				font-size: 28rpx;
				color: #333;

				&.with-arrow {
					padding-right: 40rpx;
				}

				&::placeholder {
					color: #999;
				}
			}
		}

		.fixed-bottom-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			background-color: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.save-btn {
				height: 88rpx;
				background-color: #4177fb;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0;
			}
		}

		// 商品选择弹窗样式
		.goods-popup {
			width: 600rpx;
			height: 800rpx;
			background-color: #fff;
			display: flex;
			flex-direction: column;

			.search-box {
				padding: 20rpx;
				background-color: #fff;
				border-bottom: 1rpx solid #f5f5f5;
				flex-shrink: 0;
			}

			.goods-list-container {
				flex: 1;
				overflow: hidden;

				.goods-list {
					height: 100%;

					.goods-item {
						display: flex;
						padding: 20rpx;
						border-bottom: 1rpx solid #f5f5f5;
						background-color: #fff;

						&:active {
							background-color: #f5f5f5;
						}

						.goods-image {
							width: 120rpx;
							height: 120rpx;
							border-radius: 8rpx;
							margin-right: 20rpx;
							flex-shrink: 0;
						}

						.goods-info {
							flex: 1;
							display: flex;
							flex-direction: column;
							justify-content: space-around;

							.goods-name {
								font-size: 28rpx;
								color: #333;
								font-weight: 500;
							}

							.goods-spec,
							.goods-brand {
								font-size: 24rpx;
								color: #666;
							}
						}
					}
				}
			}
		}

		.fixed-bottom-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); // 适配底部安全区域
			background-color: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.save-btn {
				height: 88rpx;
				background-color: #4177fb;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0;
			}
		}
	}

	// 修改弹窗内容区域的样式
	:deep(.pop-content) {
		height: 800rpx;
		display: flex;
		flex-direction: column;

		.head {
			flex-shrink: 0;
		}
	}
</style>