import request from '@/utils/request'

/**
 * 设备管理费列表
 * @param {Object} data
 */
export function rechargeList(data) {
    return request({
        url: '/merc/mini/mercMemberRecharge/list',
        method: 'post',
        data: data
    })
}

/**
 * 商户会员余额变化记录分页
 * @param data
 * @returns {*}
 */
export function balanceRecordsPage(data) {
    return request({
        url: '/merc/mini/memberBalanceRecords/page',
        method: 'post',
        data: data
    })
}

/**
 * 商户会员推荐关系分页
 * @param data
 * @returns {*}
 */
export function refRecordsPage(data) {
    return request({
        url: '/merc/mini/memberMercRefRecords/page',
        method: 'post',
        data: data
    })
}

export function save(data) {
    return request({
        url: '/merc/mini/mercMemberRecharge/save',
        method: 'post',
        data: data
    })
}

/**
 * @param {Object} data
 * 删除套餐
 */
export function remove(data) {
    return request({
        url: '/merc/mini/mercMemberRecharge/remove',
        method: 'post',
        data: data
    })
}

/**
 * @param {Object} data
 * 反显
 */
export function obj(data) {
    return request({
        url: '/merc/mini/mercMemberRecharge/obj',
        method: 'post',
        data: data
    })
}

/**
 * @param {Object} data
 * 充值记录
 */
export function recOrderList(data) {
    return request({
        url: '/order/order-member-money-mini/page',
        method: 'post',
        data: data
    })
}

/**
 * @param {Object} data
 * 商户储值套餐配置
 */
export function merObj(data) {
    return request({
        url: '/merc/merc-config/merObj',
        method: 'post',
        data: data
    })
}
