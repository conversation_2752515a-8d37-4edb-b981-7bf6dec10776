<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="补货图片"></u-navbar>
		<view class="content">

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view class="list" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="index">
						<view class="comm-main flex justify-between align-center"
							@click.stop="$xy.previewImg(item.imgsUrlList)">
							<view class="images">
								<u--image radius="4" width="130rpx" height="130rpx" :src="item.imgsUrlList[0]"
									mode="aspectFit" :lazy-lord="true"></u--image>
							</view>
							<view>
								<view class="flex align-center">
									<view>
										{{item.createUserName}}
									</view>
								</view>
								<view class="phone-user flex align-center">
									<view class="tel">
										{{item.createTime}}
									</view>
								</view>
							</view>
							<view class="total">共{{item.imgsUrlList.length}}张图片</view>
						</view>
					</block>
				</view>
				<view class="empty" v-if="list.length==0">
					<u-empty mode="list" text="没有图片!"></u-empty>
				</view>
			</scroll-view>


			<view class="btn" @click="upload">
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/camera.png"
					mode="widthFix"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		imgList
	} from "@/api/replenishment/replenishment.js"
	export default {
		data() {
			return {
				list: [], //列表
				fullHeight: 0,
				page: 1,
				loadmoreStatus: 'loadmore',
				keyword: '',
				deviceId: null
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')

			this.deviceId = o.deviceId
			this.getList()

			uni.$on('refreshData', res => {
				this.search()
			})
		},

		methods: {
			//获取分页
			getList() {
				imgList({
					page: {
						current: this.page,
						size: 10
					},
					deviceId: this.deviceId
				}).then(res => {
					let data = res.data.records;
					console.log(res)
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getList()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getList()
			},

			//上传照片
			upload() {
				this.$tab.navigateTo('/pages/replenish/replenishUpload?deviceId=' + this.deviceId)
			},
		},
		onUnload() {
			uni.$off('refreshData')
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.content {

			.list {
				width: 100%;
				padding: 0rpx 13rpx 12rpx;
				padding-bottom: calc(110rpx + env(safe-area-inset-bottom) / 2);
				overflow: hidden;

				.comm-img {
					width: 130rpx;
					height: 130rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-around;

					image {
						width: 130rpx;
						height: 130rpx;
					}
				}

				.comm-main {
					padding: 20rpx 30rpx;
					background-color: #fff;
					border-radius: 12rpx;
					margin-top: 12rpx;
					box-sizing: border-box;
					color: #999;
					line-height: 60rpx;
					position: relative;

					.image {
						width: 23rpx;
						height: 23rpx;
						margin-right: 12rpx;
					}

					>view:nth-child(1) {
						width: 580rpx;

						>view {
							padding: 4rpx 0;
						}

						>view:nth-child(1) {

							>view:nth-child(1) {
								font-size: 30rpx;
								font-weight: bold;
								color: #333;
							}

							>view:nth-child(2) {
								font-size: 28rpx;
								margin-left: 24rpx;
							}
						}

						>view:nth-child(2) {
							font-size: 28rpx;
						}
					}

					.phone-user {
						width:500rpx;
					}

					.stop {
						// position: absolute;
						// right: 30rpx;
						// top: 26rpx;
					}
					
					.total{
						position: absolute;
						right:20rpx;
						top:20rpx;
					}
				}
			}
		}
	}

	.empty {
		padding-top: 40%;
	}

	.btn {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		position: fixed;
		right: 24rpx;
		bottom: 200rpx;

		image {
			width: 120rpx;
			height: 120rpx;
			box-shadow: 3.2px 2.4px 3.4px -30px rgba(0, 0, 0, 0.128),
				8.9px 6.5px 9.3px -30px rgba(0, 0, 0, 0.164),
				21.4px 15.7px 22.3px -30px rgba(0, 0, 0, 0.195),
				71px 52px 74px -30px rgba(0, 0, 0, 0.29);
		}
	}
</style>