import request from '@/utils/request'

// 获取用户菜单
export function save(data) {
  return request({
    url: '/merc/mini/mercUser/save',
    method: 'post',
	data:data
  })
}

// 获取用户列表
export function list(data) {
  return request({
    url: '/merc/mini/mercUser/list',
    method: 'post',
	data:data
  })
}

// 角色列表
export function roleList(data) {
  return request({
    url: '/authorize/sysRole/page',
    method: 'post',
	data:data
  })
}

// 用户详情
export function objByUserId(data) {
  return request({
    url: '/merc/mini/mercUser/objByUserId',
    method: 'post',
	data:data
  })
}

// 更新用户
export function update(data) {
  return request({
    url: '/merc/mini/mercUser/update',
    method: 'post',
	data:data
  })
}

// 绑定微信
export function bindWx(data) {
  return request({
    url: '/merc/wechatUser/bind',
    method: 'post',
	data:data
  })
}

// 是否绑定微信、支付宝
export function isBind(data) {
  return request({
    url: '/merc/wechatUser/isBind',
    method: 'post',
	data:data
  })
}

// 绑定公众号
export function bindWxMp(data) {
  return request({
    url: '/merc/wechatUser/bindWxMp',
    method: 'post',
	data:data
  })
}

// 删除团队成员
export function delMercUser(data) {
  return request({
    url: '/merc/mini/mercUser/del',
    method: 'post',
	data:data
  })
}

/**
 * @param {Object} data
 * 部门树
 */
export function deptList(data) {
  return request({
    url: '/authorize/sysDept/tree/list',
    method: 'post',
	data:data
  })
}

/**
 * @param {Object} data
 * 部门角色
 */
export function getRefRoleIds(data) {
  return request({
    url: '/authorize/sysDept/getRefRoleIds',
    method: 'post',
	data:data
  })
}
















