<template>
	<!-- 手机号码获取套餐 -->
	<view class="container">
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="rgba(0,0,0,0)"
			:placeholder="false" title="手机号套餐"></u-navbar>
		<view class="content">
			<view class="balance-top">
				<view class="top-title">
					余额（条）
				</view>
				<view class="top-content flex justify-between align-center">
					<view class="flex align-center">
						<view class="balance-num">
							{{usableSize}}
						</view>
						<view @click="$tab.navigateTo('/pages/recharge/autoFee')">
							<view class="auto-fee auto-fee-on" v-if="autoFeeStatus">(自动续费已开通)</view>
							<view class="auto-fee auto-fee-off" v-else>
								开通自动续费
							</view>
						</view>
					</view>
					<xbutton width="154rpx" height="58rpx" round="58rpx"
						@click="$tab.navigateTo(`/pages/recharge/telRech?num=${usableSize}`)">立即充值</xbutton>
				</view>
				<view class="tips">每个用户只会获取一次手机号，服务费由微信收取。</view>
			</view>
			<view class="tab flex">
				<view :class="[current==0?'':'show']" @click="tabClick(0)">
					充值记录
				</view>
				<view :class="[current==1?'':'show']" @click="tabClick(1)">
					使用记录
				</view>
			</view>
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y :style="{height:fullHeight}"
				@scrolltolower="loadMore">
				<view class="scroll-content" v-if="list&&list.length>0">
					<block v-for="item in list" :key="item.id">
						<view class="list-item flex justify-between">
							<view class="flex list-left">
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/cz.png"
									mode="widthFix" v-if="current==0"></image>
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/kf.png"
									mode="widthFix" v-else></image>
								<view class="list-msg">
									<view class="order-no">
										订单号：{{item.id}}
									</view>
									<view class="order-type" v-if="current==0">
										类型：{{item.note}}
									</view>
									<view class="order-no" v-if="current==0">
										价格：<text style="color: red;">¥{{$xy.delMoney(item.money)}}</text>
									</view>
									<view class="order-no" v-else>
										剩余:<text style="color: red;">{{item.newSize}}条</text>
									</view>
									<view class="order-time">
										{{item.createTime}}
									</view>
								</view>
							</view>
							<view class="list-num">
								<view v-if="current==0">
									+<text>{{item.size}}条</text>
								</view>
								<view class="black" v-else>
									<text>{{item.deductSize}}条</text>
								</view>
								<!-- <view class="order-balance">
									余额：¥{{$xy.delMoney(item.balance)}}
								</view> -->
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else style="padding-top: 40%;">
					<u-empty text="没有数据!"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		telNum,
		telBuy,
		telUse,
		merObj
	} from "@/api/recharge.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: [],
				deviceIds: [],
				current: 0,
				page: 1,
				size: 10,
				loadmoreStatus: "loadmore",
				usableSize: 0,
				fullHeight: 0,
				autoFeeStatus: false
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			uni.$on('refresh', res => {
				this.getAutoFeeStatus()
			})
		},
		onUnload() {
			uni.$off('refresh')
		},
		onShow() {
			this.detail()
			this.getAutoFeeStatus()
			this.reset()
			this.getList()
		},
		methods: {
			tabClick(type) {
				this.current = type
				this.reset()
				this.getList()
			},

			detail() {
				telNum({}).then(res => {
					this.usableSize = res.data.usableSize
				})
			},

			getAutoFeeStatus() {
				merObj({}).then(res => {
					if (res.data.memberTelAudit) {
						this.autoFeeStatus = res.data.memberTelAudit.enable
					}
				})
			},

			getList() {
				if (this.current == 0) {
					telBuy({
						page: {
							current: this.page,
							size: this.size,
						},
					}).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.list = this.list.concat(data)
					})
				} else {
					telUse({
						page: {
							current: this.page,
							size: this.size,
						},
					}).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.list = this.list.concat(data)
					})
				}
			},

			loadMore(e) {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getList()
			},

			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			overflow: hidden;

			.balance-top {
				height: 448rpx;
				background: linear-gradient(0deg, #FFFFFF, #E2ECFF);
				overflow: hidden;
				padding: 220rpx 68rpx 0rpx 40rpx;

				.top-title {
					font-size: 28rpx;
				}

				.top-content {
					margin-top: 21rpx;

					.balance-num {
						font-size: 48rpx;
						font-weight: 800;
						color: #333333;
					}

					.auto-fee {
						font-size: 24rpx;
						margin-left: 20rpx;

						&.auto-fee-on {
							color: green;
						}

						&.auto-fee-off {
							color: #FF0000;
							text-decoration: underline;
						}
					}
				}

				.tips {
					font-size: 22rpx;
					color: #666;
					line-height: 50rpx;
					margin-top: 30rpx;
				}
			}

			.tab {
				line-height: 88rpx;

				>view {
					width: 50%;
					background-color: #fff;
					text-align: center;
					border-top: 1rpx solid #F6F7FA;

					&.show {
						background-color: #F6F7FA;
					}
				}
			}

			.scrollview {
				margin-top: 20rpx;

				.scroll-content {
					background-color: #fff;
					padding: 20rpx 24rpx;

					.list-item {
						padding: 30rpx 0;

						+.list-item {
							border-top: 1rpx solid #F3F3F3;
						}

						.list-left {
							>image {
								width: 39rpx;
								height: 39rpx;
								margin-top: 20rpx;
							}

							.list-msg {
								width: 438rpx;
								line-height: 46rpx;
								margin-left: 38rpx;

								>view:nth-child(1) {
									font-size: 28rpx;
									color: #333333;
								}

								>view:nth-child(2) {
									font-size: 26rpx;
									color: #999999;
								}

								>view:nth-child(3) {
									font-size: 24rpx;
									color: #999999;
								}
							}
						}

						.list-num {
							text-align: right;
							line-height: 46rpx;

							>view:nth-child(1) {
								font-size: 28rpx;
								font-weight: 800;
								color: #FF0000;

								&.black {
									color: #333;
								}

								>text {
									margin-left: 8rpx;
								}
							}

							>view:nth-child(2) {
								font-size: 24rpx;
								font-weight: 500;
								color: #999999;
							}


						}
					}
				}
			}
		}
	}
</style>