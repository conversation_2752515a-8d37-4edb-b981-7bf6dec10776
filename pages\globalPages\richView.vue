<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="每日一图"></u-navbar>
		<view class="content safe-bottom">
			<rich-text :nodes="nodes"></rich-text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				nodes: ''
			}
		},
		onLoad(o) {
			this.nodes = uni.getStorageSync('nodes')
		},
		onUnload() {
			uni.setStorageSync('nodes', '')
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 24rpx;
	}
</style>