<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="销售统计"></u-navbar>
		<view class="content">
			<view class="goods-table">
				<view class="table">
					<view class="table-title flex justify-between align-center">
						<view class="title">
							商品销售排行
						</view>
						<view class="sort-type flex">
							<view :class="[goodsSortType==0?'sort-type-item sort-type-show':'sort-type-item']"
								@click="goodsSort(0)">
								销售额
							</view>
							<view :class="[goodsSortType==1?'sort-type-item sort-type-show':'sort-type-item']"
								@click="goodsSort(1)">
								销量
							</view>
						</view>
					</view>
					<uni-table :border="false" :stripe="false" emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th align="center" width="52">排名</uni-th>
							<uni-th align="center" width="110">上榜商品</uni-th>
							<uni-th align="center" width="92">销售额</uni-th>
							<uni-th align="center" width="92">销量</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(item,index) in list2" :key="item.goodsId">
							<uni-td v-if="index==0">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/first-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else-if="index==1">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/second-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else-if="index==2">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/third-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else>
								<view class="table-td">
									{{index+1}}
								</view>
							</uni-td>
							<uni-td>
								<view class="table-td table-td-name">{{item.goodsName}}</view>
							</uni-td>
							<uni-td>
								<view class="table-td">￥{{$xy.delMoney(item.salesMoney)}}</view>
							</uni-td>
							<uni-td>
								<view class="table-td">{{item.goodsCount}}件</view>
							</uni-td>
						</uni-tr>
					</uni-table>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		sumPage
	} from "@/api/commodity/goods.js"

	export default {
		data() {
			return {
				timeStart: '',
				timeEnd: '',

				typeColumns: [
					['销售额', '订单笔数']
				],
				status: 'loadmore', //加载更多
				list1: [], //设备列表
				list2: [], //商品列表
				deviceSortType: 0,
				goodsSortType: 0,
				id: null
			}
		},

		onLoad(o) {
			this.id = o.id
			this.timeStart = o.timeStart
			this.timeEnd = o.timeEnd
			this.goodsSortType = o.sortType
		},

		onShow() {
			this.getData()
		},



		methods: {
			getData() {
				this.getList(this.goodsSortType)
			},

			goodsSort(type) {
				this.goodsSortType = type
				this.getList(type)
			},

			getParams(type) {
				let orderByKey = "";
				let orderBy = "";
				switch (type) {
					case '销售额':
						orderBy = 'desc';
						orderByKey = 'sales_money';
						break;
					case '销量':
						orderBy = 'desc';
						orderByKey = 'goods_count';
						break;
					default:
						break;
				}

				let params = {
					type: 'day',
					orderByKey: orderByKey,
					orderBy: orderBy,
					beginDate: this.timeStart,
					endDate: this.timeEnd,
					deviceId: this.id
				}
				return params
			},

			//获取商品排行
			getList(e) {
				let type = e == 0 ? '销售额' : '销量';
				let dataParams = this.getParams(type)
				let pageParams = {
					page: {
						current: 1,
						size: 1000
					}
				}
				let params = Object.assign(dataParams, pageParams)
				sumPage(params).then(res => {
					if (res.data) {
						this.list2 = res.data.records;
					} else {
						this.list2 = []
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {
			.select {
				.time-tab {
					padding: 0 36rpx;

					.time-tab-item {
						padding: 0 24rpx;
						background-color: #fff;
						border: 1rpx solid #e0b4a2;
						text-align: center;
						line-height: 50rpx;
						color: #e0b4a2;
						border-radius: 40rpx;

						&.time-tab-show {
							background-color: #eab09a;
							color: #d98a6d;
						}
					}
				}

				.time-select {
					padding: 24rpx 36rpx;
					color: rgb(144, 144, 144);
					font-size: 28rpx;

					>view:nth-child(2) {
						width: 160rpx;
						text-align: right;
					}

					>view:nth-child(3) {
						width: 80rpx;
						padding: 0 24rpx;
					}
				}
			}

			.total {
				padding: 12rpx 0;
				background-color: #fff;

				.total-name {
					color: #333;
					font-size: 28rpx;
					line-height: 50rpx;
				}

				.total-num {
					color: #eab09a;
					font-size: 32rpx;
					font-weight: bold;
					line-height: 50rpx;
				}
			}

			.search {
				padding: 24rpx 24rpx;
				background-color: #fff;
				position: relative;
				font-size: 26rpx;

				.time-type {
					width: 180rpx;
				}

				.time-select {
					width: 340rpx;
					line-height: 64rpx;
					background-color: #eeeeef;
					border-radius: 6rpx;
				}


			}

			.table {
				width: 100%;
				background-color: #fff;
				color: #333;

				.table-title {
					line-height: 50rpx;
					padding: 24rpx;
					border-bottom: 1rpx solid #ebeef5;
				}

				.table-td {
					width: 100%;
					text-align: center;

					&.table-td-name {
						width: 270rpx;
						white-space: wrap;
					}
				}

				.table-img {
					width: 40rpx;
					height: 40rpx;
					position: relative;
				}

				.sort-type {
					width: 250rpx;
					height: 50rpx;
					line-height: 48rpx;
					text-align: center;
					font-weight: normal;

					.sort-type-item {
						width: 50%;
						font-size: 26rpx;
						color: #555555;
						background-color: #fff;

						&.sort-type-item:nth-child(1) {
							border-top-left-radius: 6rpx;
							border-bottom-left-radius: 6rpx;
							border-top: 1rpx solid #CCCCCC;
							border-left: 1rpx solid #CCCCCC;
							border-bottom: 1rpx solid #CCCCCC;

							&.sort-type-show {
								color: #fff;
								border-top: 1rpx solid #2C6FF3;
								border-left: 1rpx solid #2C6FF3;
								border-bottom: 1rpx solid #2C6FF3;
								background-color: #2C6FF3;
							}
						}

						&.sort-type-item:nth-child(2) {
							border-top-right-radius: 6rpx;
							border-bottom-right-radius: 6rpx;
							border-top: 1rpx solid #CCCCCC;
							border-right: 1rpx solid #CCCCCC;
							border-bottom: 1rpx solid #CCCCCC;

							&.sort-type-show {
								color: #fff;
								border-top: 1rpx solid #2C6FF3;
								border-right: 1rpx solid #2C6FF3;
								border-bottom: 1rpx solid #2C6FF3;
								background-color: #2C6FF3;
							}
						}
					}

				}

				::v-deep.uni-table-th {
					color: #333;
					font-weight: normal;
				}

				::v-deep.uni-table-td {
					vertical-align: middle;
				}
			}

			.more {
				text-align: right;
				padding-right: 40rpx;
				position: relative;
				color: #333;
				line-height: 80rpx;
				background-color: #fff;

				>view {
					position: absolute;
					right: 12rpx;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
	}
</style>