<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="活动创建"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="100">
				<view class="section-wrap">
					<u-form-item label="活动名称">
						<u-input inputAlign="right" v-model="form.title" placeholder="请输入活动名称" border="none"></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="选择设备" borderBottom>
						<u-radio-group v-model="form.is_all_device" placement="row" @change="actionAllDeviceRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
							</u-radio>
							<u-radio label="指定设备" name="0">
							</u-radio>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="指定设备" borderBottom @click="chooseDev" v-if="form.is_all_device==0">
						<view class="choose-list" v-if="form.deviceIds&&form.deviceIds.length>0">
							<text v-for="(item,index) in devices" :key="index">
								{{item.deviceName}}
								<text v-if="index!=devices.length-1">，</text>
							</text>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择设备</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>


					<u-form-item label="模式" @click="plusCanChoose">
						<view style="text-align: right;">{{form.lotteryType==1?'九宫格':'大转盘'}}
						</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<view class="rule-tips">单人抽奖次数规则(0表示不限次数)</view>
					<u-form-item label="最多抽" borderBottom>
						<u-input inputAlign="right" type="number" v-model="form.limitNum" border="none">
							<template slot="suffix">
								<text>次</text>
							</template>
						</u-input>
					</u-form-item>
					<u-form-item label="每天抽">
						<u-input inputAlign="right" type="number" v-model="form.limitDayNum" border="none">
							<template slot="suffix">
								<text>次</text>
							</template>
						</u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="活动日期" borderBottom @click="chooseDate">
						<view style="text-align: right;" v-if="form.startTime">{{form.startTime}}~{{form.endTime}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择活动日期</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<view class="action-time">
						<u-form-item label="抽奖时间">
							<u-radio-group v-model="form.timeType" placement="row" @change="actionTimeRadio">
								<u-radio :customStyle="{marginRight: '12rpx'}" label="全天" name="1">
								</u-radio>
								<u-radio :customStyle="{marginRight: '12rpx'}" label="分时" name="2">
								</u-radio>
								<u-radio label="时间段" name="3">
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<view class="edit action-time-edit" @click="addTime" v-if="form.timeType=='3'">
							增加
						</view>
					</view>

					<view class="time-wrap flex align-center" v-if="form.timeType=='2'">
						每隔<view class="time-input"><u-input v-model="timeConfig1.everyTime" type="digit"
								placeholder="请输入"></u-input></view>分钟开放抽奖，开放<view class="time-input"><u-input
								v-model="timeConfig1.timeLong" type="digit" placeholder="请输入"></u-input></view>分钟
					</view>

					<view class="time-wrap" v-if="form.timeType=='3'">
						<view class="time-item flex justify-between align-center" v-for="(item,index) in timeConfig2"
							:key="item.id">
							<view class="time-tip">
								时间段{{index+1}}
							</view>
							<view class="time-select" @click="timeChange(index)">
								{{item.startTime}}~{{item.endTime}}
							</view>
							<view class="edit action-time-del" @click="delTime(index)">
								删除
							</view>
						</view>
					</view>
				</view>

				<view class="section-wrap">
					<view class="rule-name flex justify-between">
						<view>
							活动规则
						</view>
						<view @click="addRule" class="rule-add">添加</view>
					</view>

					<view class="rules">
						<view v-for="(item,index) in ruleData" :key="item.id" class="flex align-center rule-item">
							<view class="rule-sort">规则{{index+1}}</view><u--textarea v-model="item.content" autoHeight
								placeholder="请输入内容"></u--textarea>
							<view class="rule-del" @click="delRule(item,index)">删除</view>
						</view>
					</view>
				</view>

				<view class="section-wrap">
					<view class="img-upload flex align-center">
						<view>
							<view>banner图</view>
							<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：750*280</view>
						</view>
						<ximgUpload :maxCount="1" v-model="imgBanner" />
					</view>
				</view>

				<view class="section-wrap">
					<view class="img-upload flex align-center">
						<view>
							<view>抽奖背景图</view>
							<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：750*1625</view>
						</view>
						<ximgUpload :maxCount="1" v-model="imgTheme.bg" />
					</view>
				</view>

				<view class="section-wrap">
					<view class="img-upload flex align-center">
						<view>
							<view>抽奖标题</view>
							<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：580*257</view>
						</view>
						<ximgUpload :maxCount="1" v-model="imgTheme.title" />
					</view>
				</view>

				<view class="section-wrap">
					<view class="img-upload flex align-center">
						<view>
							<view>奖盘背景图</view>
							<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：500*500</view>
						</view>
						<ximgUpload :maxCount="1" v-model="imgTheme.boxBg" />
					</view>
				</view>

				<view class="section-wrap">
					<view class="img-upload flex align-center">
						<view>
							<view>开始按钮</view>
							<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：<text
									v-if="form.lotteryType==1">150*150</text><text v-else>150*182</text></view>
						</view>
						<ximgUpload :maxCount="1" v-model="imgTheme.btn" />
					</view>
				</view>

			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存前往编辑奖品
			</xbutton>
		</view>

		<u-calendar :show="calendarShow" allowSameDay closeOnClickOverlay @close="calendarShow=false"
			:monthNum="calendarMonthNum" mode="range" color="#2C6FF3" @confirm="calendarConfirm"></u-calendar>

		<xtimeslot ref="timeslot" :title="'选择时间段'" @confirm="confirmTime">
		</xtimeslot>

		<u-action-sheet :show="actionsShow" :actions="actions" title="模式选择" @close="actionsShow = false"
			@select="actionsSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		saveOrUpdate,
		obj
	} from "@/api/lottery.js"

	export default {
		data() {
			return {
				form: {
					"id": "",
					"title": "",
					"is_all_device": "1",
					"deviceIds": [],
					"lotteryType": "1",
					"startTime": "",
					"endTime": "",
					"content": "",
					"limitNum": 0,
					"limitDayNum": 0,
					"prize": "",
					"timeType": "1",
					"status": true,
					"timeConfig": "",
					"imgTheme": ""
				},

				imgTheme: {
					bg: [],
					title: [],
					boxBg: [],
					btn: [],
				},

				imgBanner: [],

				devices: [],
				timeConfig1: {
					"everyTime": '',
					"timeLong": ''
				},

				timeConfig2: [{
					id: Math.random(),
					startTime: "8:00",
					endTime: "20:00"
				}],

				calendarShow: false,
				calendarMonthNum: '3',
				timeIndex: 0,

				actions: [{
						id: 1,
						name: '九宫格'
					},
					{
						id: 2,
						name: '大转盘'
					}
				],
				actionsShow: false,
				ruleData: [{
					id: Math.random(),
					content: ''
				}]
			}
		},

		onLoad(o) {
			// this.getMonthNum()
			if (o.id) {
				this.getDetail(o.id)
			}

			uni.$on('updateDevice', (data) => {
				// data: {
				// 	checkDev:{deviceId: 1, deviceName: "测试设备1"},
				// 	allChecked:false
				// }	
				if (data.allChecked) {
					this.form.is_all_device = '1'
					this.form.deviceIds = []
				} else {
					let deviceIds = []
					deviceIds = data.checkDev.map(i => i.deviceId)
					this.form.deviceIds = deviceIds;
					this.devices = data.checkDev
				}
			})
		},

		methods: {
			getDetail(id) {
				obj({
					id: id
				}).then(res => {
					let data = res.data
					this.form = data

					if (this.form.startTime) {
						this.form.startTime = this.form.startTime.split(' ')[0]
					}

					if (this.form.endTime) {
						this.form.endTime = this.form.endTime.split(' ')[0]
					}

					if (data.deviceIds && data.deviceIds > 0) {
						this.form.is_all_device = '0'
						this.devices = this.form?.deviceList
					} else {
						this.form.is_all_device = '1'
					}

					if (this.form.content) {
						let ruleData = this.form.content.split('\n')
						this.ruleData = ruleData.map(i => ({
							id: Math.random(),
							content: i
						}))
					}

					if (this.form.timeType == 2 && data.timeConfig) {
						this.timeConfig1 = JSON.parse(data.timeConfig)
					} else if (this.form.timeType == 3 && data.timeConfig) {
						let tempArr = JSON.parse(data.timeConfig)
						for (let i = 0; i < tempArr.length; i++) {
							tempArr[i].id = Math.random()
						}
						this.timeConfig2 = tempArr
					}

					if (this.form.imgBanner) {
						this.imgBanner = [this.form.imgBanner]
					}

					if (this.form.imgTheme) {
						this.imgTheme = JSON.parse(this.form.imgTheme)
					}
				})
			},
			chooseDev() {
				this.$tab.navigateTo(`/pages/globalPages/chooseDevice?deviceIds=${this.form.deviceIds.join(',')}`)
			},

			chooseDate() {
				this.calendarShow = true
			},

			calendarConfirm(e) {
				console.log(e)
				this.form.startTime = e[0];
				this.form.endTime = e.at(-1)
				this.calendarShow = false
			},

			getMonthNum() {
				getDict('calendar_range').then(res => {
					this.calendarMonthNum = Number(res[0].value)
					console.log(this.calendarMonthNum)
				})
			},

			addTime() {
				if (this.timeConfig2.length == 3) {
					this.$modal.msg('最多设置三个时段！')
					return
				}
				this.timeConfig2.push({
					id: Math.random(),
					startTime: "8:00",
					endTime: "20:00"
				})
				this.$forceUpdate()
			},

			delTime(index) {
				if (this.timeConfig2.length == 1) {
					this.$modal.msg('最少保留一项！')
					return
				}
				this.timeConfig2.splice(index, 1)
			},

			actionAllDeviceRadio(e) {
				this.form.is_all_device = e
			},

			actionTimeRadio(e) {
				console.log(e)
				switch (e) {
					case 1:
						this.form.timeConfig = null
						break;
					case 2:
						this.timeConfig1 = {
							"everyTime": '',
							"timeLong": ''
						}
						break;
					case 3:
						this.timeConfig2 = [{
							id: Math.random(),
							startTime: "8:00",
							endTime: "20:00"
						}]
						break;
					default:
						break;
				}
				this.form.is_all_day = e
			},

			actionStatusRadio(e) {
				console.log(e)
				this.form.enable_status = e.type
			},

			plusCanChoose() {
				this.actionsShow = true
			},

			actionsSelect(e) {
				console.log(e)
				this.form.lotteryType = e.id
			},

			timeChange(index) {
				this.timeIndex = index
				this.$refs.timeslot.open()
			},

			confirmTime(e) {
				this.timeConfig2[this.timeIndex].startTime = e.start.hour + ':' + e.start.min
				this.timeConfig2[this.timeIndex].endTime = e.end.hour + ':' + e.end.min
			},

			addRule() {
				this.ruleData.push({
					id: Math.random(),
					content: ''
				})
			},

			delRule(item, index) {
				if (this.ruleData.length == 1) {
					this.$modal.msg('最少保留一项！')
					return
				}
				this.ruleData.splice(index, 1)
			},

			//表单提交
			submit() {
				console.log(this.form)
				let timeConfig = null;
				if (this.form.timeType == 2) {
					timeConfig = JSON.stringify(this.timeConfig1);
				} else if (this.form.timeType == 3) {
					timeConfig = JSON.stringify(this.timeConfig2);
				}

				if (this.form.is_all_device == '1') {
					this.form.deviceIds = []
				}

				this.form.content = this.ruleData.map(item => item.content).join('\n')
				this.form.imgTheme = JSON.stringify(this.imgTheme)
				this.form.imgBanner = this.imgBanner.length > 0 ? this.imgBanner[0] : ''
				let params = {
					"id": this.form.id,
					"title": this.form.title,
					"deviceIds": this.form.deviceIds,
					"lotteryType": this.form.lotteryType,
					"startTime": this.form.startTime + ' 00:00:00',
					"endTime": this.form.endTime + ' 23:59:59',
					"content": this.form.content,
					"limitNum": this.form.limitNum,
					"limitDayNum": this.form.limitDayNum,
					"prize": null,
					"timeType": this.form.timeType,
					"status": this.form.status,
					"timeConfig": timeConfig,
					"imgTheme": this.form.imgTheme,
					"imgBanner": this.form.imgBanner,
				}

				console.log('params', params)
				// if (this.checkParams(params)) return

				saveOrUpdate(params).then(res => {
					let data = res.data;
					this.$modal.msg('成功~')
					this.$tab.navigateTo(`/pages/lottery/setPrize?id=${data.id}`)
				}).catch(err => {

				})
			},

			checkParams(params) {
				if (this.current == 1) {
					if (params.price.scalar > 1) {
						this.$modal.msg('折扣必须是0-1之间的小数')
						return true
					}
				}
				return false
			},
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	::v-deep .u-textarea {
		white-space: pre-wrap;
		word-wrap: break-word;
	}

	.container {
		.content {
			padding-bottom: 280rpx;

			.section-wrap {
				padding: 0 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
			}

			.input-text {
				text-align: right !important;
			}

			.tab-wrap {
				padding: 12rpx 0;
			}

			.choose-list {
				width: 418rpx;

				>text {
					word-break: break-all;
				}
			}

			.rule-tips {
				padding-top: 20rpx;
				color: red;
			}

			.img-upload {
				height: 200rpx;
				font-size: 26rpx;
				color: #333;
				background-color: #fff;
				margin-top: 20rpx;

				>view {
					margin-right: 41rpx;
				}
			}

			.form-item {
				position: relative;

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.action-time {
			position: relative;

			.edit {
				color: #2C6FF3;
				text-decoration: underline;
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);

				&.del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.time-wrap {
			padding-bottom: 20rpx;

			.time-item {
				line-height: 84rpx;

				.time-select {}

				.action-time-del {
					color: red;
					text-decoration: underline;
				}
			}

			.time-input {
				width: 160rpx;
				padding: 0 12rpx;
			}
		}

		.rule-name {
			padding: 20rpx 0 14rpx;

			>view:nth-child(1) {
				font-size: 28rpx;
			}

			.rule-add {
				color: #2C6FF3;
				text-decoration: underline;
			}
		}

		.rules {
			padding-bottom: 20rpx;

			.rule-item {
				padding-top: 20rpx;

				.rule-sort {
					margin-right: 12rpx;
				}

				.rule-del {
					margin-left: 12rpx;
					color: #2C6FF3;
					text-decoration: underline;
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 9;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>