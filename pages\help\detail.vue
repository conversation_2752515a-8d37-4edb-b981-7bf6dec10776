<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="title"></u-navbar>
		<view class="content safe-bottom">
			<rich-text :nodes="nodes"></rich-text>
			<view class="down-box">
				<block v-for="(item,index) in downLoadArr" :key="item">
					<view class="btn" @click="downLoad(item)">
						下载{{index+1}}
					</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		detail
	} from "@/api/help.js"
	export default {
		data() {
			return {
				nodes: '',
				title: '帮助',
				downLoadArr: []
			}
		},
		onLoad(o) {
			this.title = o.title
			this.getDetail(o.id)
		},
		methods: {
			getDetail(id) {
				detail({
					id: id
				}).then(res => {
					this.nodes = res.data.content

					// 解析文档中下载链接
					const str = this.nodes
					const regex = /<a href="(.*?).xlsx/g;
					const hrefs = str.match(regex);
					let arr = []
					hrefs.forEach((href) => {
						arr.push(href.substr(9))
					});
					this.downLoadArr = arr
				})
			},

			downLoad(url) {
				this.$xy.exportDoc(url)
			},
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 24rpx;

		.down-box {
			position: fixed;
			right: 40rpx;
			bottom: 100rpx;

			.btn {
				width: 100rpx;
				height: 100rpx;
				border-radius: 100rpx;
				background-color: skyblue;
				text-align: center;
				line-height: 100rpx;
				margin-top: 20rpx;
			}
		}
	}
</style>