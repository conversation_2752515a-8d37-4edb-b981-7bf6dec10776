<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="我的发票抬头"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap">
					<u-form-item label="抬头类型" required borderBottom>
						<u-radio-group v-model="form.type" placement="row" @change="actionRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="企业单位" name="1">
							</u-radio>
							<u-radio label="个人/非企业单位" name="2">
							</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="公司名称" required :borderBottom="form.type==1">
						<view class="flex align-center">
							<u-input inputAlign="left" v-model="form.companyName" placeholder="请输入" border="none"
								clearable></u-input>
						</view>
					</u-form-item>

					<u-form-item label="公司税号" required borderBottom v-if="form.type==1">
						<u-input inputAlign="left" v-model="form.taxIdNumber" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>

					<u-form-item label="公司地址" required borderBottom v-if="form.type==1">
						<u-input inputAlign="left" v-model="form.address" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>

					<u-form-item label="公司电话" required borderBottom v-if="form.type==1">
						<u-input inputAlign="left" v-model="form.tel" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>

					<u-form-item label="开户行" required borderBottom v-if="form.type==1">
						<u-input inputAlign="left" v-model="form.bank" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>

					<u-form-item label="开户行账号" required v-if="form.type==1">
						<u-input inputAlign="left" v-model="form.accountWithBank" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="联系人" required borderBottom>
						<u-input inputAlign="left" v-model="form.contactName" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>
					<u-form-item label="电话" required borderBottom>
						<u-input inputAlign="left" v-model="form.contactTel" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>
					<u-form-item label="地址" required>
						<u-input inputAlign="left" v-model="form.contactAddress" placeholder="请输入" border="none"
							clearable></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap default-set flex justify-between align-center">
					<view class="left">
						<view>
							设为默认抬头
						</view>
						<view>
							每次开票会默认填写该抬头信息
						</view>
					</view>
					<view class="right">
						<u-switch activeColor="#2C6FF3" size="20" v-model="form.defaultTitle"></u-switch>
					</view>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				提交
			</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		titleEdit,
		titleAdd,
	} from '@/api/invoice.js'

	export default {
		data() {
			return {
				form: {
					"companyName": "",
					"taxIdNumber": "",
					"accountWithBank": "",
					"address": "",
					"tel": "",
					"contactName": "",
					"contactTel": "",
					"contactAddress": "",
					"bank": "",
					"type": '1',
					"defaultTitle": false,
					"contactName": "",
					"contactTel": "",
					"contactAddress": "",
				},
				id: null,
			}
		},

		onLoad(o) {
			this.id = o.id

			let obj = JSON.parse(o.obj)
			this.form = {
				"companyName": obj.companyName,
				"taxIdNumber": obj.taxIdNumber,
				"accountWithBank": obj.accountWithBank,
				"address": obj.address,
				"tel": obj.tel,
				"contactName": obj.contactName,
				"contactTel": obj.contactTel,
				"contactAddress": obj.contactAddress,
				"bank": obj.bank,
				"type": obj.type.toString(),
				"defaultTitle": obj.defaultTitle,
				"contactName": obj.contactName,
				"contactTel": obj.contactTel,
				"contactAddress": obj.contactAddress,
			}
		},

		methods: {
			actionRadio(e) {
				this.form.type = e
			},

			//表单提交
			submit() {
				if (this.checkParams(this.form)) return
				let params = {}
				if (this.form.type == 1) {
					params = this.form
				} else {
					params = {
						"companyName": this.form.companyName,
						"type": this.form.type,
						"defaultTitle": this.form.defaultTitle,
						"contactName": this.form.contactName,
						"contactTel": this.form.contactTel,
						"contactAddress": this.form.contactAddress,
					}
				}

				if (this.id) {
					params.titleId = this.id
					titleEdit(params).then(res => {
						this.$modal.msg('修改成功~')
						uni.$emit('refreshData')
						
						var pages = getCurrentPages()
						let pageIndex=pages.findIndex(i=>i.route=='pages/invoice/invoiceEdit')
						setTimeout(() => {
							uni.navigateBack({
								delta: pages.length-pageIndex-1
							})
						}, 1000)
					})
				} else {
					titleAdd(params).then(res => {
						this.$modal.msg('新建成功~')
						uni.$emit('refreshData')
						
						var pages = getCurrentPages()
						let pageIndex=pages.findIndex(i=>i.route=='pages/invoice/invoiceEdit')
						setTimeout(() => {
							uni.navigateBack({
								delta: pages.length-pageIndex-1
							})
						}, 1000)
					})
				}
			},

			checkParams(params) {
				if (this.form.type == 1) {
					if (!this.form.companyName) {
						this.$modal.msg('请填写名称~')
						return true
					}
					if (!this.form.taxIdNumber) {
						this.$modal.msg('请填写公司税号~')
						return true
					}
					if (!this.form.address) {
						this.$modal.msg('请填写公司地址~')
						return true
					}
					if (!this.form.tel) {
						this.$modal.msg('请填写公司电话~')
						return true
					}
					if (!this.form.bank) {
						this.$modal.msg('请填写开户行~')
						return true
					}
					if (!this.form.accountWithBank ) {
						this.$modal.msg('请填写开户行账号~')
						return true
					}
					if (!this.form.contactName) {
						this.$modal.msg('请填写联系人~')
						return true
					}
					if (!this.form.contactTel) {
						this.$modal.msg('请填写电话~')
						return true
					}
					if (!this.form.contactAddress) {
						this.$modal.msg('请填写地址~')
						return true
					}
				}else{
					if (!this.form.companyName) {
						this.$modal.msg('请填写名称~')
						return true
					}
					if (!this.form.contactName) {
						this.$modal.msg('请填写联系人~')
						return true
					}
					if (!this.form.contactTel) {
						this.$modal.msg('请填写电话~')
						return true
					}
					if (!this.form.contactAddress) {
						this.$modal.msg('请填写地址~')
						return true
					}
				}
				return false
			},
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx;
				background-color: #fff;
				margin-top: 24rpx;
			}

			.default-set {
				line-height: 50rpx;
				padding: 12rpx 30rpx;

				.left {
					>view:nth-child(2) {
						font-size: 20rpx;
						line-height: 40rpx;
						color: #5d5d5d;
					}
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 9;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>