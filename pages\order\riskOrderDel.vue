<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="title"></u-navbar>
		<view class="video-container">
			<xvideo :src="videoUrl" :showMuteBtn="false" />
			<view class="flex align-center justify-between" style="padding:0 24rpx;">
				<view class="video-tab">
					<u-subsection :list="videoType" activeColor="#2C6FF3" :current="videoCurrent" fontSize="24rpx"
						@change="sectionChange">
					</u-subsection>
				</view>
				<view class="flex">
					<view>
						<xbutton size="mini" padding="0 20rpx" @tap='addCom(deviceId)'>添加商品</xbutton>
					</view>
					<view style="margin-left: 24rpx;">
						<xbutton size="mini" padding="0 20rpx" @tap='submit'>提交{{type==3?'补单':'扣款'}}
						</xbutton>
					</view>
				</view>
			</view>
		</view>
		<view class="goods-select" v-if="goodsList&&goodsList.length>0">
			<u-scroll-list :indicator="indicator" indicatorColor="#fff0f0" indicatorActiveColor="#f56c6c">
				<view class="flex justify-around">
					<view class="goods-item" v-for="(item,index) in goodsList" :key="index">
						<view class="flex">
							<view
								class="flex flex-direction align-center justify-center image-dele-container goodContainer"
								@tap="clean(index,item)">
								<!-- <view class="flex align-center justify-center numberContainer">
									{{value}}
								</view> -->
								<view class="image">
									<u--image radius="4" width="100rpx" height="100rpx" :src="item.goodsImg"
										mode="aspectFit" :lazy-lord="true"></u--image>
								</view>
								<view class="goods-select-name">{{item.name}}</view>
							</view>
							<!-- <view class="chang-price" @click="changePrice(item)">
								修改价格
							</view> -->
						</view>

						<view class="flex align-center justify-center" style="margin-top: 8rpx;">
							<view class="flex align-center justify-center">
								<view class="minus" @tap="reduce(item,index)">
									<u-icon name="minus" size="12"></u-icon>
								</view>
								<text style="width: 50rpx;text-align: center;" class="input">{{item.number}}</text>
								<view class="plus" @tap="add(item)">
									<u-icon name="plus" color="#FFFFFF" size="12"></u-icon>
								</view>
							</view>
						</view>
					</view>
				</view>
			</u-scroll-list>
		</view>

		<view class="goods-select-empty" v-else>
			添加需要补扣的商品~
		</view>

		<view class="search-btn flex justify-between align-center">
			<view class="search">
				<u-search animation placeholder="请输入商品名称" :clearabled="false" v-model="keyword" :showAction="false"
					@change="inputChange"></u-search>
			</view>
			<view @click="menuShow=!menuShow" class="menu-btn">
				<image :src="menuShow?'../../static/images/global/menu.png':'../../static/images/global/list.png'"
					mode="widthFix"></image>
			</view>
		</view>

		<view class="classify-wrap">
			<ComList :status="status" :commList="commList" @comClick='detail' :height="fullHeight"
				:menuShow="menuShow" />
		</view>

		<!-- 修改价格 -->
		<xpopup :show="priceShow" @close="priceClose" zIndex="10085" :showBtn="true" title="修改价格"
			@confirm="changeSubmit">
			<view class="pop-content">
				<view class="old-price">
					原价：<text style="color:red;font-weight: bold;">￥{{goodDetail.oldPrice}}</text>
				</view>
				<view class="new-price">
					<u--input placeholder="请输入新价格" type="digit" v-model="goodDetail.newPrice" border="surround">
					</u--input>
				</view>
			</view>
		</xpopup>

		<!-- 批量修改价格 -->
		<xpopup :show="batchPriceShow" @close="batchPriceClose" :showBtn="true" title="确认价格"
			@confirm="batchPriceSubmit">
			<view class="batch-pop-content">
				<!-- <view class="batch-pop-tips">
					提示：如需调整价格，请在修改栏填写，【修改后的价格将直接设置到机器，即后续交易也将使用这个价格】。
				</view> -->
				<scroll-view style="height: 700rpx;" scroll-y="true" scroll-with-animation="true" lower-threshold="100">
					<block v-for="(item,index) in goodsList" :key="item.id">
						<view class="goods-list">
							<view class="flex align-center">
								<view class="image">
									<u--image radius="4" width="100rpx" height="100rpx" :src="item.goodsImg"
										mode="aspectFit" :lazy-lord="true"></u--image>
								</view>
								<view class="goods-content">
									<view class="flex align-center justify-between">
										<view class="goods-name">
											{{item.name}}
										</view>
										<view class="goods-item-num">
											×{{item.number}}
										</view>
									</view>

									<view class="goods-price flex align-center justify-between">
										<view class="goods-item-price flex align-center">
											单价：￥{{item.price}}
											<view @click="changePrice(item)">修改价格</view>
										</view>
										<view class="goods-item-total" style="color: red;font-weight: bold;">
											合计：￥{{(item.price*item.number).toFixed(2)}}
										</view>
									</view>
								</view>
							</view>
						</view>
					</block>
				</scroll-view>
				<view class="pop-content-total">
					总计：<text>￥{{totalMoney}}</text>
				</view>
			</view>
		</xpopup>


		<!-- 补单理由 -->
		<xpopup :show="reasonShow" @close="reasonClose" :showBtn="true" title="补单备注信息" @confirm="reasonSubmit">
			<view class="pop-content">
				<view class="old-price">
					<text style="color:red;">
						提示：
						1）客户可以在我的订单->待支付中进行支付。
						2）客户扫码后会弹出订单，提示客户支付。
					</text>
				</view>
				<view class="new-price">
					<u--textarea v-model="reason" placeholder="备注"></u--textarea>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import ComList from './components/comList.vue'
	import {
		ownerGoodsList,
		list,
		updateBySupply
	} from "@/api/commodity/goods.js"
	import {
		apply,
		page
	} from "@/api/order/riskorder.js"
	import {
		byId,
		nextPayOrders
	} from "@/api/order/order.js"
	import {
		categoryList,
		sendCommand
	} from "@/api/device/device.js"
	import {
		changePrice
	} from '@/api/commodity/goodsMode.js'
	export default {
		components: {
			ComList
		},
		data() {
			return {
				scrollintoview: '',
				fullHeight: '0',
				tabList: [], //商品类目
				commList: [], //商品列表
				goodsList: [], //商品id列表
				status: 'nomore', //加载更多
				id: '',
				deviceId: '',
				video: {
					url1: '',
					url2: ''
				},
				videoUrl: '',
				videoType: ['主视频', '副视频'],
				videoCurrent: 0,
				statGoodsList: [], //平台识别补扣商品

				priceShow: false,
				oldPrice: null,
				newPrice: null,
				goodDetail: {
					oldPrice: null,
					newPrice: null,
					id: null
				},
				batchPriceShow: false,
				orderDetail: null,
				tempList: [], //临时商品存储

				reason: '',
				reasonShow: false,

				keyword: '',
				type: '',
				menuShow: true
			}
		},

		computed: {
			totalMoney() {
				let total = 0
				if (this.goodsList && this.goodsList.length > 0) {
					for (var i = 0; i < this.goodsList.length; i++) {
						let item = this.goodsList[i]
						total += (Number(item.price) * Number(item.number))
					}
				}
				return total.toFixed(2)
			},

			title() {
				let msg = this.type == 3 ? '补单' : '风险订单补扣'
				return msg
			}
		},

		async onLoad(e) {
			this.id = e.id
			this.deviceId = e.deviceId
			this.type = e.type // 1风险订单跳转扣款 2风险订单跳转补单 3订单详情跳转补单
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.classify-wrap')
		},

		async onShow() {
			await this.getCommList()
			await this.getDetail()
			this.init()
		},

		methods: {
			/**
			 * 详情
			 */
			getDetail() {
				return new Promise((resolve, reject) => {
					if (this.type == 1 || this.type == 2) { //风险订单
						page({
							page: {
								current: 1,
								size: 10,
							},
							id: this.id
						}).then(res => {
							let data = res.data.records[0];
							this.orderDetail = data
							resolve(data)
						}).catch(err => {
							reject(err)
						})
					} else { //订单详情
						byId({
							id: this.id
						}).then(res => {
							this.orderDetail = res.data
							resolve(res)
						}).catch(err => {
							reject(err)
						})
					}
				})
			},

			//初始化页面信息
			init() {
				//反显商品数量
				if (uni.getStorageSync('riskGoodsTotal')) {
					this.goodsList = JSON.parse(uni.getStorageSync('riskGoodsTotal'))
				} else {
					//算法识别商品
					let riskOrderVideo = this.orderDetail.video;

					if (this.orderDetail.riskType == '18') {
						this.videoType = ['主视频', '副视频', '超时视频']
					} else {
						this.videoType = ['主视频', '副视频']
					}

					if (riskOrderVideo) {
						this.video = {
							url1: riskOrderVideo.split(',')[0],
							url2: riskOrderVideo.split(',')[1]
						}
						this.videoUrl = this.video.url1
					} else {
						this.video = {
							url1: '',
							url2: ''
						}
					}

					let riskOrderGoods = this.type != 3 ? this.orderDetail.orderGoods : [];
					if (riskOrderGoods && riskOrderGoods.length > 0) {
						let tempGoodsList = riskOrderGoods.map(i => {
							return {
								id: i.id,
								goodsImg: i.goodsImgUrl,
								goodsId: i.goodsId,
								name: i.goodsName,
								price: (Number(i.totalMoney) / i.totalNumber) / 100,
								number: Number(i.totalNumber),
								initNum: Number(i.totalNumber)
							}
						})
						this.statGoodsList = JSON.parse(JSON.stringify(tempGoodsList)) //算法识别商品
						this.goodsList = JSON.parse(JSON.stringify(tempGoodsList))
					}
				}

				//商品库添加商品
				if (uni.getStorageSync('riskNewGoodIds') && JSON.parse(uni.getStorageSync('riskNewGoodIds')).length > 0) {
					let riskNewGoodIds = JSON.parse(uni.getStorageSync('riskNewGoodIds'))
					console.log('this.commList', this.commList)
					console.log('riskNewGoodIds', riskNewGoodIds)
					for (let goodsId of riskNewGoodIds) {
						let newRiskGood = this.commList.find(i => i.goodsId == goodsId)
						console.log('newRiskGood', newRiskGood)
						if (newRiskGood) {
							this.detail(newRiskGood)
						}
					}
					// 添加完毕清空
					// uni.setStorageSync('riskNewGoodIds', '')
				}
				console.log('this.goodsList', this.goodsList)
			},

			sectionChange(e) {
				this.videoCurrent = e;
				if (e == 0) {
					this.videoUrl = this.video.url1
				} else if (e == 1) {
					this.videoUrl = this.video.url2
				} else if (e == 2) {
					var url = this.video.url1.replace("sjgFile", "uploadFiles");
					var idx = url.lastIndexOf("/") + 1;
					var name = this.orderDetail.orderCreateTime.substring(0, 10) + "_" + url.substring(idx).replace(
						"T_c00",
						"T_c01");
					console.log(name);
					this.videoUrl = url.substring(0, idx) + name;
					console.log(this.videoUrl);
				}
			},

			async onVideoError(o) {
				//console.log('----------',o);
				if (this.videoUrl.indexOf("T_c01") >= 0) {
					var templet = {};
					var idx = this.videoUrl.lastIndexOf("/") + 1;
					// var name = this.selOrder.orderCreateTime.substring(0,10)+"_"+this.videoUrl.substring(idx);
					var name = this.videoUrl.substring(idx);
					var time = this.orderDetail.orderCreateTime.substring(0, 10);
					if (!name.startsWith(time)) {
						name = this.orderDetail.orderCreateTime.substring(0, 10) + "_" + this.videoUrl.substring(idx);
					}
					//console.log("/storage/emulated/0/aiShj/videos/old_backs/"+name)
					await this.getDict('mqtt_cmd_templet_task', 'file').then(res => {
						console.log(res);
						templet = JSON.parse(res[0].value);
						templet.data.task = "upload";
						templet.data.file = "/storage/emulated/0/aiShj/videos/old_backs/" + name;
					});
					console.log(templet);
					await sendCommand([{
						deviceId: this.deviceId,
						templet: templet
					}]).then(res => {
						this.$modal.showToast('正在捞取机器视频，请稍后再重新打开查看~')
						reSn = res.data[0].v1;
					});
				}
			},

			/**
			 * 输入框值改变时触发
			 */
			inputChange(e) {
				console.log(e)
				let comArr = []
				comArr = this.tempList.filter(i => i.name.indexOf(e) != -1)
				this.commList = comArr
			},

			//根据类目获取商品列表
			getCommList() {
				return new Promise((resolve, reject) => {
					list({
						deviceId: this.deviceId,
					}).then(res => {
						let data = res.data;
						let newData = data.map(i => {
							i.price = this.$xy.delMoney(i.price)
							return i
						})
						this.tempList = data
						this.commList = data
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			statGoodsTips() {
				this.$modal.oldConfirm('该商品为平台算法识别订单异常商品，以防损失，无法再删减！')
			},

			clean(e, item) { //点击移除商品
				//校验是否算法识别商品
				if (this.type != 3 && this.isStatGoods(item)) {
					this.statGoodsTips()
					return
				}
				this.goodsList.splice(e, 1)
			},

			// 校验是否算法识别商品
			isStatGoods(e) {
				console.log(e)
				console.log(this.statGoodsList)
				for (let i = 0; i < this.statGoodsList.length; i++) {
					let item = this.statGoodsList[i];
					if (item.id == e.id) {
						return true
					}
				}
				return false
			},

			//校验是否减少了算法识别商品
			isDelStatGoods(e) {
				for (let i = 0; i < this.statGoodsList.length; i++) {
					let item = this.statGoodsList[i];
					if (item.id == e.id && item.number == e.number) {
						return true
					}
				}
				return false
			},

			detail(e) {
				this.goodsList.forEach(item => {
					if (e.id == item.id) {
						this.add(item)
					}
				})

				for (var i = 0; i < this.goodsList.length; i++) {
					if (this.goodsList[i].id == e.id) {
						return;
					}
				}

				this.goodsList.push({
					id: e.id,
					goodsImg: e.cover,
					goodsId: e.goodsId,
					name: e.name,
					price: e.price,
					number: 1,
					initNum: 0
				})
			},
			//新增
			add(e) {
				e.number++
			},
			//减少
			reduce(e, index) {
				if (this.type != 3 && this.isDelStatGoods(e)) { //校验是否减少了算法识别商品
					this.statGoodsTips()
					return
				}
				e.number--
				if (e.number == 0) {
					this.goodsList.splice(index, 1)
				}
			},
			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},

			// 添加商品至机器
			addCom(deviceId) {
				//清空之前的数据
				uni.setStorageSync('riskNewGoodIds', '')
				// 保存商品
				uni.setStorageSync('riskGoodsTotal', JSON.stringify(this.goodsList))
				//添加商品
				this.$tab.navigateTo(`/pages/equipment/addCom?id=${deviceId}&fromRisk=fromRisk`)
			},

			changePrice(item) {
				this.goodDetail = {
					oldPrice: item.price,
					newPrice: '',
					id: item.id
				}
				this.priceShow = true
			},

			priceClose() {
				this.priceShow = false
			},

			changeSubmit() {
				if (this.goodDetail.newPrice) {
					// changePrice({
					// 	id: this.goodDetail.id,
					// 	price: this.$xy.delMoneyL(this.goodDetail.newPrice)
					// }).then(res => {
					// 	if (res.code == 200) {
					// 		this.priceClose()
					// 		this.reset()
					// 		this.getCommList()
					// 		//同步商品价格
					// 		this.goodsList.find(i => i.id == this.goodDetail.id).price = this.goodDetail.newPrice
					// 		setTimeout(() => {
					// 			this.$modal.msg('修改成功~')
					// 		}, 1000)
					// 	}
					// }).catch(err => {})
					this.priceClose()
					//同步商品价格
					this.goodsList.find(i => i.id == this.goodDetail.id).price = Number(this.goodDetail.newPrice)
					this.$modal.msg('修改成功~')
				} else {
					this.$modal.msg('商品价格不能为空！')
				}
			},

			batchPriceClose() {
				this.batchPriceShow = false
			},

			//确认价格
			batchPriceSubmit() {
				this.batchPriceClose()
				if (this.type != 2) { //补单填入备注
					this.reasonShow = true
				} else { //扣款
					this.repairSubmit()
				}
			},

			//修改价格
			submit() {
				if (!this.goodsList.length) {
					uni.$u.toast('请选择商品')
					return;
				}
				this.batchPriceShow = true
			},

			reasonClose() {
				this.reasonShow = false
			},

			reasonSubmit() {
				this.repairSubmit()
			},

			//提交补单/补扣
			repairSubmit() {
				var goodsId = []
				let totalMoney = 0
				this.goodsList.forEach(item => {
					if (item.number - item.initNum > 0) { //除去算法识别商品
						goodsId.push({
							id: item.id,
							goodsId: item.goodsId,
							goodsName: item.name,
							price: this.$xy.delMoneyL(item.price),
							totalNumber: item.number - item.initNum,
						})
					}
					totalMoney += item.price * item.number
				})

				if (this.type != 2) { //扣款
					if (!this.reason) {
						this.$modal.msg('请输入补单备注！')
						return
					}
					nextPayOrders({
						"goods": goodsId,
						"id": this.id,
						"msg": this.reason
					}).then(res => {
						this.$modal.msg('提交申请成功~')
						uni.$emit('refreshData') //刷新风险订单列表
						setTimeout(() => {
							this.$tab.navigateBack()
						}, 1000)

					})
				} else { //补单
					apply({
						"cutGoods": goodsId,
						"riskId": this.id
					}).then(res => {
						this.$modal.msg('提交申请成功~')
						uni.$emit('refreshData') //刷新风险订单列表
						setTimeout(() => {
							this.$tab.navigateBack()
						}, 1000)

					})
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.container {
		.martop {
			margin-top: 20rpx;
		}

		.margin {
			margin: 10rpx 20rpx;
		}

		.margintop {
			margin-top: 10rpx;
		}

		.video-container {
			.video-tab {
				width: 380rpx;
			}
		}

		.box {
			padding: 20rpx 24rpx;
		}

		.goods-item+.goods-item {
			margin-left: 24rpx;
		}

		.image-dele-container {
			position: relative;
			width: 140rpx;
			height: 140rpx;
			background-color: #f6f6f6;
			border-radius: 15rpx;
			padding: 12rpx;

			view {
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
				width: 100rpx;
			}

			.image {
				width: 100rpx;
				height: 100rpx;
			}

			.goods-select-name {
				font-size: 24rpx;
			}
		}

		.goods-select {
			margin: 0 24rpx;
			height: 218rpx;
			padding-top: 10rpx;
		}

		.goods-select-empty {
			height: 218rpx;
			text-align: center;
			line-height: 218rpx;
		}

		.search-btn {
			padding-right: 24rpx;
		}

		.search {
			padding: 0 12rpx 12rpx;
			width: 680rpx;
		}

		.menu-btn {
			>image {
				width: 36rpx;
				height: 36rpx;
			}
		}

		.goodContainer {
			position: relative;

			.numberContainer {
				width: 35rpx;
				height: 35rpx;
				border-radius: 100%;
				color: #fff;
				background-color: red;
				position: absolute;
				right: -5rpx;
				top: -5rpx;
			}
		}

		.chang-price {
			font-size: 28rpx;
			text-align: center;
			width: 30rpx;
			padding: 0 12rpx;
			color: #2C6FF3;

		}

		.minus {
			width: 22px;
			height: 22px;
			border-width: 1px;
			border-color: #E6E6E6;
			border-style: solid;
			border-top-left-radius: 100px;
			border-top-right-radius: 100px;
			border-bottom-left-radius: 100px;
			border-bottom-right-radius: 100px;
			@include flex;
			justify-content: center;
			align-items: center;
		}



		.plus {
			width: 18px;
			height: 18px;
			background-color: #FF0000;
			border-radius: 50%;
			/* #ifndef APP-NVUE */
			display: flex;
			/* #endif */
			justify-content: center;
			align-items: center;
		}

		.pop-content {
			padding: 24rpx 42rpx;

			.old-price {
				font-size: 28rpx;
				line-height: 48rpx;
				margin-bottom: 24rpx;
			}
		}

		.batch-pop-content {
			padding: 24rpx 42rpx 60rpx;
			position: relative;

			.batch-pop-tips {
				color: red;
				line-height: 50rpx;
				margin-bottom: 20rpx;
			}

			.goods-list {
				margin-bottom: 20rpx;
				padding-bottom: 24rpx;
				border-bottom: 1rpx solid #ececec;


				.goods-content {
					margin-left: 24rpx;

					.goods-name {
						font-size: 28rpx;
						font-weight: bold;
						color: #333;
						width: 480rpx;
					}

					.goods-item-num {
						color: red;
					}

					.goods-price {
						margin-top: 16rpx;
					}

					.goods-item-price,
					.goods-item-total {
						font-size: 24rpx;
						color: #999;
					}

					.goods-item-price {
						>view {
							font-style: italic;
							text-decoration: underline;
							color: #2C6FF3;
							margin-left: 20rpx;
						}
					}
				}
			}

			.pop-content-total {
				font-weight: bold;
				color: red;
				text-align: right;
				position: absolute;
				right: 40rpx;
				bottom: 0;
			}
		}
	}
</style>