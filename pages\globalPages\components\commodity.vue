<template>
	<view class="container">
		<u-navbar bgColor="#2C6FF3" :placeholder="true" :autoBack="false">
			<view slot="left" style="color:#fff;font-size: 36rpx;">
				商品私库
			</view>
		</u-navbar>
		<view class="content">
			<view class="btn-wrap flex justify-between"
				v-if="checkPermi(['upshelfStatus','addComModel','publicComList'])">
				<xbutton bgColor="#F7F7F7" color="#777777" v-if="checkPermi(['upshelfStatus'])"
					@click="$tab.navigateTo('/pages/commodity/search')">商品上下架
				</xbutton>
				<xbutton bgColor="#F7F7F7" color="#777777" v-if="checkPermi(['addComModel'])"
					@click="$tab.navigateTo('/pages/commodity/addCom')">新品建模
				</xbutton>
				<xbutton bgColor="#F7F7F7" color="#777777" v-if="checkPermi(['publicComList'])"
					@click="$tab.navigateTo('/pages/commodity/publicCom')">官方商品库
				</xbutton>
			</view>
			<view class="search" @click="searchComm">
				<view class="search-input">
					<u-search placeholder="商品搜索" actionText="取消" :actionStyle="{color:'#2C6FF3'}"
						:showAction="!leftShow" :clearabled="false" v-model="keyword" @search="search"
						@custom="cancle"></u-search>
					<view @click="scan" :class="[leftShow?'scan-icon scan-left-show':'scan-icon scan-left-hidden']">
						<u-icon name="scan" size="22" color="#909399"></u-icon>
					</view>
				</view>

				<view class="search-history flex flex-wrap flex-start" v-if="!leftShow">
					<view class="history-item" v-for="(item,index) in historyList" :key="index"
						@click="searchFast(item)">
						{{item}}
					</view>
				</view>
			</view>
			<view class="algo flex justify-end" @click="changeType">
				<view class="flex align-center filter-algo">
					<view style="margin-right: 6rpx;">{{algoTypeName}}</view>
					<view><u-icon name="arrow-down-fill" size="10" color="#6b778b"></u-icon></view>
				</view>
			</view>
			<view class="classify-wrap">
				<classify storeName="perStor" :tabList="tabList" :status="status" :commList="commList"
					@switchMenu="switchMenu" @comClick='detail' @lowerBottom="lowerBottom" :height="fullHeight"
					:leftShow="leftShow" />
			</view>
		</view>

		<u-action-sheet :show="actionSheetShow" :actions="actions" title="切换商品种类" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		goodsCategory,
		ownerGoodsList
	} from "@/api/commodity/mercGoods.js"

	import {
		mercAiList
	} from "@/api/commodity/goods.js"
	export default {
		data() {
			return {
				fullHeight: '0',
				tabList: [], //商品类目
				commList: [], //商品列表

				page: 1, //商品分页
				size: 10,
				categoryCode: null,

				status: 'loadmore', //加载更多

				leftShow: true,
				keyword: '',
				historyList: [],
				actionSheetShow: false,
				actions: [],
				algoType: '',
				algoTypeName: '切换商品库',
			}
		},
		async created() {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.classify-wrap', 50)

			if (uni.getStorageSync('goods')) {
				this.historyList = JSON.parse(uni.getStorageSync('goods'))
			}
		},

		methods: {
			/**
			 * 刷新数据
			 */
			async refreshData() {
				try {
					let getAlgoListRes = await this.getAlgoList();
					if (!getAlgoListRes.data || getAlgoListRes.data.length == 0) return
					this.getCategory()
				} catch (error) {
					console.log('error', error)
				}
			},

			changeType() {
				this.actionSheetShow = true
			},

			actionsheetSelect(e) {
				this.algoType = e.type
				this.algoTypeName = e.name
				this.search()
			},

			//商家算法列表
			getAlgoList() {
				return new Promise((resolve, reject) => {
					mercAiList({}).then(res => {
						let data = res.data
						let newData = data.map(i => {
							return {
								type: i.id,
								name: i.alias
							}
						})
						this.actions = newData
						this.algoType = data[0].id
						this.algoTypeName = data[0].alias
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			search(val) {
				this.saveKeyWord('goods', this.keyword)
				this.reset();
				this.getCommList()
			},

			cancle(val) {
				this.keyword = ''
				this.leftShow = true
				this.search()
			},

			saveKeyWord(type, val) {
				if (val) {
					let arr = []
					if (uni.getStorageSync(type)) {
						let arr = JSON.parse(uni.getStorageSync(type))
						if (arr.indexOf(val) != -1) {
							console.log('arr.indexOf(val)', arr.indexOf(val))
							arr.splice(arr.indexOf(val), 1)
						}
						arr.unshift(val)
						if (arr.length > 6) {
							arr.pop()
						}
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					} else {
						arr.unshift(val)
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					}
				} else {
					return
				}
			},

			searchFast(e) {
				this.keyword = e
				this.search()
			},

			//扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.keyword = res.result;
						this.search()
					}
				});
			},

			searchComm() {
				this.leftShow = false
			},

			//获取类目列表
			getCategory() {
				goodsCategory({
					algorithmId: this.algoType
				}).then(res => {
					this.tabList = res.data
					if (this.tabList && this.tabList.length > 0) {
						this.switchMenu(this.tabList[0])
					} else {
						this.reset()
					}
				})
			},

			//商品类目切换
			switchMenu(item) {
				this.categoryCode = item.categoryCode
				this.reset()
				this.getCommList()
			},

			//根据类目获取商品列表
			getCommList() {
				let params = {}
				if (this.leftShow) { //搜索
					params = {
						categoryCode: this.categoryCode,
						algorithmId: this.algoType,
						page: {
							current: this.page,
							size: this.size
						},
						status: '1' //0下架1上架
					}
				} else { //非搜索
					params = {
						page: {
							current: this.page,
							size: this.size
						},
						algorithmId: this.algoType,
						status: '1', //0下架1上架
						keyword: this.keyword
					}
				}
				ownerGoodsList(params).then(res => {
					let data = res.data.records;
					if (data && data.length > 0) {
						data = data.map(i => {
							i.name = i.goodsName;
							i.barcode = i.goodsBarcode;
							i.cover = i.goodsCover;
							i.price = i.price / 100;
							i.categoryName = i.capacity == null ? '未分类' : i.capacity;
							return i
						})
					}
					if (data.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.commList = this.commList.concat(data)
				})
			},

			//触底加载更多
			lowerBottom() {
				if (this.status == 'nomore') return
				this.page++
				this.getCommList()
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},

			detail(e) {
				this.$tab.navigateTo('/pages/commodity/comEdit?id=' + e.id)
			}
		},
	}
</script>

<style lang="scss" scoped>
	.btn-wrap {
		padding: 24rpx 24rpx 0;
		background-color: #fff;
	}

	.search {
		padding: 24rpx 24rpx 20rpx;
		background-color: #fff;

		.search-input {
			position: relative;

			.scan-icon {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				z-index: 2;

				&.scan-left-show {
					right: 36rpx;
				}

				&.scan-left-hidden {
					right: 100rpx;
				}
			}
		}

		.search-history {

			.history-item {
				margin-right: 24rpx;
				padding: 0 12rpx;
				background-color: #f2f2f2;
				color: #333;
				font-size: 24rpx;
				line-height: 40rpx;
				border-radius: 40rpx;
				margin-top: 24rpx;
			}
		}
	}

	.algo {
		font-size: 28rpx;
		color: #0D0D0D;
		background-color: #fff;
		padding: 0 24rpx 20rpx;

		.filter-algo {
			padding: 0 35rpx;
			line-height: 46rpx;
			background: #F2F2F2;
			border-radius: 14rpx;
		}
	}

	.classify-wrap {
		// padding-bottom: 200rpx;
	}

	.btn {
		width: 100%;
		position: fixed;
		bottom: 120rpx;
		left: 0;
		display: flex;
		flex-flow: row nowrap;
		justify-content: space-between;
		padding: 0 24rpx;

		&.safa-btn {
			bottom: 180rpx;
		}
	}
</style>