<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="人员管理"></u-navbar>
		<view class="content">
			<view class="list">
				<u-cell-group :border="false">
					<u-cell title="登录名">
						<input slot="value" placeholder="请输入登录名" type="text" v-model="params.account"
							class="u-slot-value"></input>
					</u-cell>
					<u-cell title="姓名">
						<input slot="value" placeholder="请输入姓名" type="text" v-model="params.name"
							class="u-slot-value"></input>
					</u-cell>
					<u-cell title="手机号">
						<input slot="value" placeholder="请输入手机号" type="number" v-model="params.tel"
							class="u-slot-value"></input>
					</u-cell>
					<u-cell title="邮箱">
						<input slot="value" placeholder="请输入邮箱" type="" v-model="params.mail"
							class="u-slot-value"></input>
					</u-cell>
					<u-cell title="密码">
						<input slot="value" placeholder="请输入密码" type="password" v-model="params.password"
							class="u-slot-value"></input>
					</u-cell>
					<u-cell title="部门" :isLink="true" @click="deptChoose">
						<text slot="value">{{deptNames.join(',')}}</text>
					</u-cell>
					<u-cell title="角色" :isLink="true" @click="popShow=true">
						<text slot="value">{{roleName.join(',')}}</text>
					</u-cell>
					<u-cell title="备注" :border="false">
						<input slot="value" placeholder="请输入备注" type="" v-model="params.remark"
							class="u-slot-value"></input>
					</u-cell>
				</u-cell-group>
			</view>
			<view style="color: red;line-height: 60rpx;text-align: right;padding-right: 20rpx;">
				{{err}}
			</view>
			<view class="btn safe-bottom">
				<xbutton round="82rpx" size="large" @click="submit">创建</xbutton>
			</view>
		</view>

		<xpopup :show="popShow" @close="popClose" @confirm="popSubmit" :showBtn="true" title="选择角色">
			<view class="pop-content">
				<view class="pop-tips">
					提示：“客服”角色，用于买家联系商家客服人员，处理订单及设备等事宜，客服联系电话为工作人员手机号码！
				</view>
				<!-- 多选 -->
				<view class="roles flex flex-wrap justify-between">
					<block v-for="(item, index) in roleList" :key="item.name">
						<view
							:class="[item.disabled?'role-item disabled':item.checked?'role-item checked':'role-item unchecked']"
							@click="checkboxClick(item)" v-if="item.name!='管理员'">
							{{item.name}}
						</view>
					</block>
				</view>
			</view>
		</xpopup>

		<!-- 部门选择 -->
		<xtree ref="deptRef" :multiple="false" :range="deptData" :selectParent="true" rangeKey="name"
			confirmColor="#2C6FF3" @confirm="deptSubmit"></xtree>
	</view>
</template>

<script>
	import {
		save,
		roleList,
		deptList,
		getRefRoleIds
	} from "@/api/system/employee.js"
	export default {
		data() {
			return {
				params: {
					"name": "",
					"tel": "",
					"mail": "",
					"password": "",
					"roleIds": [],
					"account": "",
					"remark": "",
					"deptIds": []
				},
				roleName: [],
				popShow: false,
				roleList: [],
				err: '',
				deptData: [],
				deptNames: [],
			}
		},

		onLoad(o) {
			this.getRoleList()
			this.getDeptData()
		},

		methods: {
			getRoleList() {
				roleList({
					page: {
						current: 1,
						size: 10000
					},
					sysId: uni.getStorageSync('sysId')
				}).then(res => {
					let data = res.data.records;
					if (data.length > 0) {
						let newData = []
						data.forEach(i => {
							if (i.name != '管理员') {
								newData.push({
									id: i.id,
									name: i.name,
									checked: false
								})
							}
							i.disabled = false //禁用
						})
						this.roleList = newData;
					} else {
						this.roleList = [];
					}
				})
			},

			//弹框提交
			submit() {
				if (this.checkParams(this.params)) return
				save(this.params).then(res => {
					this.$modal.msg('保存成功')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000)
					this.err = ''
				}).catch(err => {
					if (err.length > 15) {
						this.err = '请重试!'
					} else {
						this.err = err.substr(0, 12)
					}
				})
			},

			checkParams(params) {
				if (!/^[a-zA-Z0-9_-]{1,12}$/.test(params.account)) {
					this.$modal.msg('登录名为1-12位的英文、数字、下划线、减号组成')
					return true
				}
				if (!/^[\d\w\u4e00-\u9fa5,\.;\:"'?!\-]{2,15}$/.test(params.name)) {
					this.$modal.msg('姓名为2-15位的中文、英文、数字、下划线等组成')
					return true
				}
				if (!/^1\d{10}$/.test(params.tel)) {
					this.$modal.msg('手机号码格式不正确')
					return true
				}
				if (!/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/.test(params.mail)) {
					this.$modal.msg('邮箱格式不正确')
					return true
				}
				if (!/^[a-zA-Z0-9_-]{1,12}$/.test(params.password)) {
					this.$modal.msg('密码为1-12位的英文、数字、下划线、减号组成')
					return true
				}
				if (!params.roleIds || params.roleIds.length == 0) {
					this.$modal.msg('请为该用户分配角色')
					return true
				}
				return false
			},

			/**
			 * 部门选择
			 */
			deptChoose() {
				this.$refs.deptRef._show();
			},

			/**
			 * 部门树
			 */
			getDeptData() {
				deptList({
					sysId: uni.getStorageSync('sysId')
				}).then(res => {
					this.deptData = res.data
				})
			},

			/**
			 * 选中部门
			 */
			deptSubmit(e) {
				if (e.length > 0) {
					// 初始化部门自带角色
					for (let item of this.roleList) {
						if (item.disabled) { //部门角色标记清除
							item.checked = false
							item.disabled = false
							this.roleName.splice(this.roleName.indexOf(item.name), 1)
							this.params.roleIds.splice(this.params.roleIds.indexOf(item.id), 1)
						}
					}

					let deptNames = [];
					let deptIds = []
					for (let item of e) {
						deptNames.push(item.name)
						deptIds.push(item.id)
					}
					this.deptNames = deptNames
					this.params.deptIds = deptIds
					//加载部门自带角色
					this.deptIdGetRoleIds(deptIds)
				} else {
					this.$modal.msg('请选择部门！')
				}
			},

			/**
			 * @param {Object} deptId
			 * 部门初始角色
			 */
			deptIdGetRoleIds(deptId) {
				return new Promise((resolve, reject) => {
					getRefRoleIds({
						id: deptId[0],
						sysId: uni.getStorageSync('sysId')
					}).then(res => {
						let data = res.data;
						if (data && data.length > 0) {

							for (let roleItem of this.roleList) {
								// 部门自带角色弹框选中项配置
								if (data.indexOf(roleItem.id) != -1) {
									roleItem.disabled = true
									roleItem.checked = true

								}

								//回显角色名称，设置请求参数角色id
								for (let defaultRoleId of data) {
									if (defaultRoleId == roleItem.id) {
										if (this.params.roleIds.indexOf(roleItem.id) == -1) {
											this.params.roleIds.push(roleItem.id)
										}
										if (this.roleName.indexOf(roleItem.name) == -1) {
											this.roleName.push(roleItem.name)
										}
									}
								}
							}
						}
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//滚动到底加载更多
			onReachBottom() {
				if (this.noMore) return
				this.page++
				this.getPointList()
			},

			//禁用
			stop() {
				this.popShow = true
			},

			//弹框关闭
			popClose() {
				this.popShow = false
			},

			//弹框提交
			popSubmit() {
				let nameVal = []
				let idVal = []
				this.roleList.forEach(i => {
					if (i.checked) {
						nameVal.push(i.name)
						idVal.push(i.id)
					}
				})
				this.roleName = nameVal
				this.params.roleIds = idVal
				this.popClose()
			},

			checkboxClick(item) {
				if (item.disabled) {
					this.$modal.msg('当前为部门默认角色!')
					return
				}
				item.checked = !item.checked
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.content {
			padding: 0rpx 13rpx 12rpx;
			overflow: hidden;

			.list {
				width: 100%;
				margin-top: 24rpx;
				background-color: #fff;
				padding: 0rpx 30rpx;
				border-radius: 12rpx;

				input {
					text-align: right;
				}

				::v-deep .u-cell__body {
					padding: 30rpx 6rpx;
				}
			}
		}
	}

	.empty {
		padding-top: 40%;
	}

	.btn {
		width: 100%;
		position: fixed;
		left: 0;
		padding: 0 24rpx;
		bottom: $iphone-safe-bottom;
	}

	.popup-content {
		padding: 36rpx 24rpx;
		display: flex;
		flex-flow: row nowrap;
		justify-content: flex-start;
		align-items: center;

		input {
			border: 1rpx solid #999;
			border-radius: 6rpx;
			width: 530rpx;
			padding: 0 24rpx;
		}

		&.edit-point {
			flex-direction: column;
			align-items: flex-start;

			.edit-point-item {
				width: 100%;
				padding-left: 170rpx;
				position: relative;

				&+.edit-point-item {
					margin-top: 12rpx;
				}

				>view:nth-child(1) {
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
	}

	.del-popup-content {
		padding: 36rpx 24rpx;
		text-align: center;
		font-size: 32rpx;
	}

	.pop-content {
		padding: 24rpx;

		.pop-tips {
			margin-bottom: 44rpx;
			color: red;
		}

		.u-page__tag-item {
			margin-right: 24rpx;
		}

		.roles {
			.role-item {
				display: inline-block;
				border: 1rpx solid #2C6FF3;
				padding: 0 20rpx;
				line-height: 44rpx;
				border-radius: 6rpx;
				font-size: 26rpx;
				margin-right: 20rpx;
				margin-bottom: 20rpx;

				&.checked {
					background-color: #2C6FF3;
					color: #fff;
				}

				&.unchecked {
					color: #2C6FF3;
				}

				&.disabled {
					border: 1rpx solid #ffaa00;
					background-color: #ffaa00;
					color: #fff;
				}
			}
		}
	}
</style>