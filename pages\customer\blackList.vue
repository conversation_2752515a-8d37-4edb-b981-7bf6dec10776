<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="黑名单"></u-navbar>
		<view class="content">
			<!-- 	<view class="search">
				<u-search animation placeholder="请输入商户名称" :clearabled="false" v-model="keyword" :showAction="false"
					@search="getList"></u-search>
			</view> -->

			<scroll-view @scrolltolower="loadMore" class="scrollview" :scroll-with-animation="true" scroll-y
				lower-threshold="100" :style="{height:fullHeight}">

				<view class="list-warp" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="item.id">
						<view class="item-wrap xy-card">
							<view class="item-content">
								<view>手机号：{{item.tel}}</view>
								<view class="flex order-no" v-if="item.banOrderId">
									<view>订单号：{{item.banOrderId}}</view>
									<view @click="search(item.banOrderId)">查看</view>
								</view>
								<view class="flex order-no">
									<view>拉黑时间：{{item.banOrderCreateTime.replace(/T/g,' ')}}</view>
								</view>
								<view class="ban-reason">
									{{item.banReason||'-'}}
								</view>
							</view>
							<view class="flex justify-end" style="margin-top: 24rpx;">
								<xbutton round='30rpx' style="margin-right: 24rpx;" @click="histOrder(item.memberId)">历史订单</xbutton>
								<xbutton round='30rpx' @click="removeBlack(item)">解除黑名单</xbutton>
							</view>
						</view>
					</block>
				</view>

				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		blackList
	} from '@/api/merchant.js'
	import {
		removeBlackList
	} from "@/api/order/order.js"
	export default {
		data() {
			return {
				list: [],
				keyword: '',
				fullHeight: 0,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview',0)
			
			this.getList()
		},
		methods: {
			getList() {
				blackList({
					page: {
						current: 1,
						size: 100
					}
				}).then(res => {
					this.list = res.data.records;
				})
			},

			removeBlack(item) {
				let memberId = item.memberId
				this.$modal.oldConfirm(`是否确认解除用户${item.tel}黑名单？`).then(res => {
					removeBlackList({
						memberId: memberId
					}).then(res => {
						this.$modal.msg('解除成功~')
						this.getList()
					}).catch(err => {

					})
				})
			},
			
			histOrder(id){
				this.$tab.navigateTo('/pages/order/userInfo?id='+id)
			},
			
			search(id){
				this.$tab.navigateTo('/pages/order/orderDetails?id='+id)
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;

		.content {
			overflow: hidden;
			padding: 0 13rpx;

			.search {
				padding: 24rpx 0;
			}

			.btn {
				margin-top: 20rpx;
				padding-bottom: 24rpx;
			}

			.list-warp {
				padding-top: 24rpx;

				.item-wrap {
					padding: 24rpx 32rpx;

					+.item-wrap {
						margin-top: 20rpx;
					}
					
					.item-content{
						line-height: 60rpx;
						.order-no{
							>view:nth-child(2){
								font-style: italic;
								text-decoration: underline;
								color: #2C6FF3 !important;
								margin-left: 24rpx;
								background-color: #fff !important;
							}
						}
						
						.ban-reason{
							border-radius: 12rpx;
							margin-top: 24rpx;
							padding:12rpx;
							background-color: #dfdfdf;
						}
					}
				}
			}

			.empty {
				padding-top: 40%;
			}
		}

		.popup-content {
			width: 600rpx;
			border-radius: 12rpx;
			overflow: hidden;

			.txt {
				padding: 24rpx;
				text-align: center;
				word-break: break-all;
				line-height: 60rpx;
			}

			.txt1 {
				padding: 24rpx;
				text-align: left;
				word-break: break-all;
			}

			.button {
				text-align: center;
				line-height: 80rpx;
				border-top: 1rpx solid #dfdfdf;

				>view {
					flex: 1;
					border-right: 1rpx solid #dfdfdf;
				}

				>navigator {
					flex: 1;
					color: #2C6FF3;
				}
			}
		}
	}
</style>