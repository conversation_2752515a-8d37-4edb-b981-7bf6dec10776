<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="兑奖"></u-navbar>
		<view class="content">
			<view class="tips">
				<view>1.请先下载中奖名单模板文件</view>
				<view>2.把奖项、手机号、优惠券方案ID填入模板文件</view>
				<view>3.表格名称为唯一标识符号，同一个名称重复导入只会处理一次</view>
			</view>

			<view class="flex flex-direction">
				<view class="btn1">
					<xbutton round='88rpx' width="200rpx" @click="downLoad">
						下载模板文件
					</xbutton>
				</view>
				<view class="btn1 flex align-center">
					<xbutton round='88rpx' width="200rpx" @click="chooseFile">
						请选择文件
					</xbutton>
					<view class="file" v-if="tempPath">{{fileName}}<text class="del" @click="del">删除</text></view>
				</view>

			</view>
			<view class="btn">
				<xbutton size="large" round='88rpx' @click="upload">
					导入
				</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import config from '@/config'
	import {
		getToken
	} from '@/utils/auth'
	export default {
		data() {
			return {
				fileName: null,
				tempPath: null
			}
		},
		
		onLoad(o) {
			this.src = `${decodeURIComponent(o.src)}?id=${o.id}`
		},

		methods: {
			downLoad() {
				let url = 'https://cdn.ossfile.mxrvending.com/assets/xls_template/winner.xlsx'
				this.$xy.exportDoc(url)
			},

			chooseFile() {
				let _this = this;
				const baseUrl = config.baseUrl
				uni.chooseMessageFile({
					count: 1,
					type: 'file',
					extension: ['.xlsx', '.xls'],
					success: function(res) {
						console.log("文件上传", res);
						_this.fileName = res.tempFiles[0].name
						_this.tempPath = res.tempFiles[0].path
					},
					fail(err) {
						console.log('选择文件失败', err);
						// 处理选择文件失败后的逻辑
					}
				})
			},

			upload() {
				if (!this.tempPath) {
					this.$modal.msg('请先选择文件！！！')
					return
				}
				let _this = this
				uni.showLoading({
					title: '加载中'
				});
				uni.uploadFile({
					url: config.baseUrl + '/merc/merc-coupon/uploadSend', //文件上传的接口地址
					filePath: this.tempPath,
					name: 'file',
					header: {
						'satoken': getToken()
					},
					success: (res) => {
						console.log(res)
						let data=JSON.parse(res.data)
						uni.hideLoading();
						_this.$modal.msg(data.msg)
					},
					fail: (error) => {
						uni.hideLoading();
					},
				})
			},

			del() {
				this.tempPath = null
				this.fileName = null
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 20rpx;

		.tips {
			color: red;
			line-height: 50rpx;
		}

		.btn1 {
			margin-top: 20rpx;
		}

		.file {
			margin-left: 20rpx;
			color: green;
		}

		.del {
			color: red;
			text-decoration: underline;
			margin-left: 30rpx;
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
		}
	}
</style>