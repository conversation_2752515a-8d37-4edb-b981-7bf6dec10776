<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="会员管理"></u-navbar>

		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
					:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
					lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>

		<view class="search">
			<u-search animation placeholder="请输入手机号码" :clearabled="true" v-model="keyword" :showAction="false"
				@search="search"></u-search>
		</view>

		<view class="flex align-center justify-between screen-container">
			<view class="total-list flex">
				<view>总数:{{10||'-'}}</view>
				<view class="total-item">充值金额:{{10||'-'}}</view>
				<view>赠送金额:{{10||'-'}}</view>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="equipment-container" v-if="current==0">
						<view class="flex align-center justify-between">
							<view class="title">
								手机号：{{item.tel||'-'}}
							</view>
						</view>

						<view class="order-detail-item">
							<view>剩余总金额：</view>￥{{ ($xy.delMoney(item.balance + item.balanceGive))}}
						</view>

						<view class="order-detail-item">
							<view>剩余充值金额：</view>￥{{$xy.delMoney(item.balance)}}
						</view>
						<view class="order-detail-item">
							<view>剩余赠送金额：</view>￥{{$xy.delMoney(item.balanceGive)}}
						</view>

						<view class="flex justify-end martop">
							<view class="marleft24">
								<xbutton padding='0rpx 20rpx' bgColor="#F4F8FF" borderColor="#F4F8FF" color="#2C6FF3"
									@tap='pointsDetail(item)'>充值记录
								</xbutton>
							</view>
							<view class="marleft24">
								<xbutton padding='0rpx 20rpx' @tap='orderDetail(item)'>余额明细
								</xbutton>
							</view>
						</view>
					</view>

				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>


		<u-action-sheet :show="actionSheetShow" :actions="actions" title="套餐类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		viperPage,
		countTotalPage,
		userPage,
		listByMercId
	} from "@/api/vip.js"

	export default {
		data() {
			return {
				keyword: null,
				list: [{
					tel: 12321312
				}],
				orderCount: {},
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				mode: 'single',
				screenShow: false,
				actionSheetShow: false,
				actions: [],
				title: '',

				pickerType: null,

				fullHeight: 0,

				tabList: [{
						name: '普通会员'
					},
					{
						name: 'vip会员'
					},
					{
						name: '储值会员'
					},
				],
				current: 0,
				showBlack: false,

				countData: [],

				meal: {
					id: null,
					name: null
				},
				total: 0
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			// this.screenList()
			// this.search()
			// this.plusTotal()
		},

		methods: {
			/**
			 * @param {Object} 
			 * 会员统计数据
			 */
			plusTotal() {
				countTotalPage({}).then(res => {
					let data = res.data;
					this.countData = data
				})
			},

			/**
			 * 套餐筛选
			 */
			screenList() {
				listByMercId({}).then(res => {
					let data = res.data;
					let actions = data.map(i => ({
						name: i.mealName,
						type: i.id
					}))
					this.actions = [{
						name: '全部',
						type: ''
					}, ...actions]
				})
			},

			tabClick(e) {
				this.current = e.index
				this.reset()
				this.getpage()
			},

			actionsheetSelect(e) {
				this.meal = {
					name: e.name,
					id: e.type
				}

				this.search()
			},


			//是否展示黑名单用户
			showBlackChange() {
				this.search()
			},

			//点击筛选
			screen() {
				this.actionSheetShow = true
			},

			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					isBalance: true
				}
				if (this.current == 0) {
					let obj = {
						isBlacklistSelect: this.current == 0 ? this.showBlack : false,
						tel: this.keyword || null,
						isBalance: this.current == 2 ? true : ''
					}
					Object.assign(params, obj)
					userPage(params).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.total = res.data.total
						this.list = this.list.concat(data)
					})
				} else {
					let obj = {
						tel: this.keyword || null,
						mealId: this.meal.id
					}
					Object.assign(params, obj)
					viperPage(params).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.list = this.list.concat(data)
					})
				}

			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			orderDetail(item) {
				this.$tab.navigateTo('/pages/order/userInfo?id=' + item.memberId)
			},

			pointsDetail(item) {
				this.$tab.navigateTo('/pages/integral/userIntegralRecord?id=' + item.memberId)
			},

			record(item) {
				this.$tab.navigateTo('/pages/vip/viperRec?memberId=' + item.memberId)
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		}
	}
</script>
<style scoped lang="scss">
	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.screen-container {
			background-color: #fff;
			padding: 10rpx 13rpx 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;

			.total-list {
				font-size: 24rpx;

				.total-item {
					padding: 0 20rpx;
				}
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 24rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;
				margin-left: 6rpx;

				>view {
					display: inline-block;
					width: 200rpx;
				}
			}
		}
	}
</style>