<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="老带新记录"></u-navbar>
		<view class="content">
			<view class="section" style="border-radius: 0">
				<view class="item flex justify-between align-center">
					<view class="item-left"> 推荐人手机号 </view>
					<view class="item-right flex align-center">
						<u--input v-model="params.parentTel" inputAlign="right" placeholder="请输入"
							type="text"></u--input>
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left"> 新用户手机号 </view>
					<view class="item-right flex align-center">
						<u--input v-model="params.tel" inputAlign="right" placeholder="请输入" type="text"></u--input>
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left"> 推荐日期 </view>
					<view class="item-right flex align-center">
						<view :class="[params.startTime ? 'end-time' : 'end-time-show']"
							@click="selectTime('startTime')">
							{{ params.startTime || "开始日期" }}
						</view>
						<view>~</view>
						<view :class="[params.endTime ? 'end-time' : 'end-time-show']" @click="selectTime('endTime')">
							{{ params.endTime || "结束日期" }}
						</view>
						<u-icon name="arrow-right" color="#777" size="16px"></u-icon>
					</view>
				</view>
			</view>

			<view class="scroll-box">
				<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
					@scrolltolower="scrolltolower" :style="{height:fullHeight}">
					<view v-if="list.length>0" class="content">
						<block v-for="(item,index) in list" :key="item.id">
							<view class="equipment-container">
								<view class="list-title">时间：{{ item.createTime }}</view>
								<view class="list-item flex">
									<view class="item-name">推荐人手机号：</view>
									<view class="item-val">{{ item.parentTel }}</view>
								</view>
								<view class="list-item flex">
									<view class="item-name">新用户人手机号：</view>
									<view class="item-val">{{ item.tel }}</view>
								</view>
								<view class="list-item flex">
									<view class="item-name">推荐人赠送：</view>
									<view class="item-val">{{ item.parentMemberGive }}</view>
								</view>
								<view class="list-item flex">
									<view class="item-name">新用户赠送：</view>
									<view class="item-val">{{ item.memberGive }}</view>
								</view>
							</view>
						</block>
						<view class="load-more" style="padding:24rpx;">
							<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
						</view>
					</view>
					<view v-else class='empty'>
						<u-empty mode="data" text="数据为空"></u-empty>
					</view>
				</scroll-view>
			</view>
		</view>

		<u-datetime-picker :show="show" v-model="time" :max-date="maxTime" confirmColor="#2C6FF3" mode="date"
			:closeOnClickOverlay="true" @cancel="show = false" @confirm="confirm"
			@close="show = false"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		refRecordsPage
	} from "@/api/rechargeAct.js";

	export default {
		data() {
			return {
				show: false,
				time: new Date(),
				maxTime:new Date().getTime(),
				timeType: "startTime",
				list: [],
				page: 1, //当前分页
				size: 10, //分页数据条数
				actions: [],
				typeShow: false,
				params: {
					parentTel: null,
					tel: null,
					startTime: null,
					endTime: null,
				},

				fullHeight: 0,
				loadmoreStatus: 'loadmore',
			};
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)
			this.search()
		},

		watch: {
			params: {
				handler(newVal, oldVal) {
					this.search()
				},
				deep: true,
				immediate: false,
			}
		},

		methods: {
			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
				}
				let obj = {
					tel: this.params.tel,
					parentTel: this.params.parentTel,
					startTime: this.params.startTime ? this.params.startTime + " 00:00:00" : '',
					endTime: this.params.endTime ? this.params.endTime + " 23:59:59" : '',
				}
				Object.assign(params, obj)
				refRecordsPage(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})

			},

			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			search() {
				this.reset()
				this.getpage()
			},
			
			scrolltolower(){
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
			
			inputClear() {
				this.params.deviceId = "";
			},

			selectTime(type) {
				this.timeType = type;
				this.show = true;
			},

			confirm(e) {
				this.params[this.timeType] = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
				this.show = false;
			},

			close() {
				this.show = false;
			},

			checkParams() {
				if (!this.params.startTime) {
					return true;
				}
				if (!this.params.endTime) {
					return true;
				}
				// if (!this.params.deviceId) {
				// 	return true
				// }
				return false;
			},
		},
	};
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		::v-deep .u-border {
			border-color: transparent !important;
			text-align: right;
		}

		.content {
			.section {
				padding: 6rpx 20rpx 0;
				border-radius: 14rpx;
				background-color: #fff;

				.item {
					line-height: 28rpx;
					padding: 10rpx 17rpx 10rpx 23rpx;
					height: 88rpx;

					&:not(:last-child) {
						border-bottom: 1rpx solid #eaeaea;
					}

					.item-left {
						line-height: 50rpx;
					}

					.item-right {
						line-height: 50rpx;

						.item-val {
							color: #333;
							display: inline-block;
							max-width: 362rpx;
							text-align: right;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.start-time,
						.end-time {
							padding: 0 12rpx;
						}

						.start-time-show,
						.end-time-show {
							color: #c6c6c6;
						}
					}
				}
			}
		}

		.scroll-box {
			overflow: hidden;

			.menu-item {
				text-align: center;
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-top: 24rpx;

				>image {
					width: 57rpx;
					height: 57rpx;
				}

				>view {
					color: #333;
					font-size: 24rpx;
					line-height: 24rpx;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 16rpx;
				}
			}

			.empty {
				padding-top: 40%;
			}

			.martop {
				margin-top: 20rpx;
			}

			.tab-wrap {
				padding: 12rpx 0;

				.tab {
					// width: 40%;
					width: 70%;
				}
			}

			.tab-list {
				width: 100%;
				background-color: #fff;
				padding: 24rpx 26rpx 0;

				.tab-item {
					padding: 0 20rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					line-height: 62rpx;
					margin-bottom: 14rpx;
					text-align: center;
					margin-right: 12rpx;

					&.tab-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}
			}

			.marleft {
				margin-left: 10rpx;
			}

			.scrollview {
				.content {
					padding-bottom: 90rpx;
					overflow: hidden;
				}
			}

			.equipment-container {
				margin: 13rpx 13rpx 0;
				padding: 24rpx 30rpx 40rpx;
				border-radius: 14rpx;
				background-color: #fff;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
				position: relative;

				.list-title {
					font-size: 32rpx;
					color: #333;
					font-weight: bold;
					margin-bottom: 20rpx;
				}

				.list-item {
					font-size: 28rpx;
					line-height: 56rpx;
					color: #777777;
					padding-left: 8rpx;

					.item-name {
						width: 240rpx;
					}
				}
			}

			.btn {
				width: 724rpx;
				position: fixed;
				left: 13rpx;
				bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
				border-radius: 88rpx;
				overflow: hidden;

				.btn-wrap {
					text-align: center;
					font-size: 36rpx;
					font-weight: 500;
				}
			}
		}

		.empty {
			padding-top: 40%;
		}
	}
</style>