<template>
	<view class="container">
		<u-navbar titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff" :placeholder="true"
			title="会员收入统计"></u-navbar>
		<view class="content">
			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>

			<view class="total-content today" v-if="current==0">
				<view class="time-tab flex align-center justify-between">
					<!-- <view class="time-item" :class="[dayTimeCurrent==0?'time-item time-show':'time-item']"
						@click="dayTimeClick(0)">
						今日
					</view> -->
					<!-- <view class="time-item" :class="[dayTimeCurrent==1?'time-item time-show':'time-item']" -->
					<view class="time-item time-show" @click="dayTimeClick(0)">
						{{date}}
					</view>

					<view class="flex">
						<view class="change-day" @tap="changeDay(0)">
							<xbutton>今日</xbutton>
						</view>
						<view class="change-day" @tap="changeDay(-1)">
							<xbutton>前一天</xbutton>
						</view>
						<view class="change-day" @tap="changeDay(1)">
							<xbutton>后一天</xbutton>
						</view>
					</view>
				</view>

				<u-datetime-picker :show="timeShow" mode="date" v-model="datePicker" @confirm="confirm"
					:max-date="new Date().getTime()" oseOnClickOverlay="true" @close="close"
					@cancel="close"></u-datetime-picker>

				<view class="today-receive">
					<view>{{today}}总收益</view>
					<view>
						￥<view>{{$xy.delMoney(vipTotal.totalPayMoney+vipRecTotal.totalPayMoney)}}</view>
					</view>
					<view>
						储值收入+vip会员收入
					</view>
				</view>

				<view class="sale-refund card" style="padding:30rpx 20rpx;">
					<view class="sr-title" style="margin-left: 14rpx;">
						<image src="../../static/images/global/total-before.png" mode="widthFix"></image>
						收入金额和笔数
					</view>
					<view class="sr-content flex justify-between">
						<view class="sr-content-item sr-content-left">
							<view class="sc-top">
								储值收入
							</view>
							<view class="sc-bot flex">
								<view class="sc-item">
									<view class="sc-num">
										￥<text>{{$xy.delMoney(vipRecTotal.totalPayMoney)}}</text>
									</view>
									<view class="sc-name">
										金额
									</view>
								</view>
								<view class="sc-item">
									<view class="sc-num">
										<text>{{vipRecTotal.totalNum||0}}</text>笔
									</view>
									<view class="sc-name">
										订单数量
									</view>
								</view>
							</view>
						</view>
						<view class="sr-content-item sr-content-right">
							<view class="sc-top">
								vip会员收入
							</view>
							<view class="sc-bot flex">
								<view class="sc-item">
									<view class="sc-num">
										￥<text>{{$xy.delMoney(vipTotal.totalPayMoney)}}</text>
									</view>
									<view class="sc-name">
										金额
									</view>
								</view>
								<view class="sc-item">
									<view class="sc-num">
										<text>{{vipRecTotal.totalNum||0}}</text>笔
									</view>
									<view class="sc-name">
										订单数量
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="total-content month" v-else>
				<view class="time-tab flex align-center justify-between">
					<view class="time-item time-show" @click="monthTimeClick(0)">
						{{month}}
					</view>

					<view class="flex">
						<view class="change-day" @tap="changeMonth(0)">
							<xbutton>本月</xbutton>
						</view>
						<view class="change-day" @tap="changeMonth(-1)">
							<xbutton>上一月</xbutton>
						</view>
						<view class="change-day" @tap="changeMonth(1)">
							<xbutton>下一月</xbutton>
						</view>
					</view>
				</view>

				<u-datetime-picker :max-date="new Date().getTime()" :show="monthShow" mode="year-month"
					v-model="monthPicker" @confirm="monthConfirm" :closeOnClickOverlay="true" @close="monthClose"
					@cancel="monthClose"></u-datetime-picker>

				<view class="today-receive month-receive">
					<view class="sr-total flex justify-around">
						<view class="sr-item">
							<view class="sr-num">
								￥<text>{{$xy.delMoney(monthData.totalPayMoney)}}</text>
							</view>
							<view class="sr-name">
								总金额
							</view>
						</view>
						<view class="sr-item">
							<view class="sr-num">
								<text>{{monthData.totalNum||0}}</text>
							</view>
							<view class="sr-name">
								总笔数
							</view>

						</view>
					</view>

				</view>

				<view class="chart card">
					<view class="chart-title flex justify-between align-center">
						<view>
							{{tabCurrent==0?'会员储值收入':'vip会员收入'}}
						</view>

						<view class="sort-type flex">
							<view :class="[tabCurrent==0?'sort-type-item sort-type-show':'sort-type-item']"
								@click="chartTabChange(0)">
								会员储值收入
							</view>
							<view :class="[tabCurrent==1?'sort-type-item sort-type-show':'sort-type-item']"
								@click="chartTabChange(1)">
								vip会员收入
							</view>
						</view>
					</view>
					<view class="chart-content" v-if="monthData.itemList&&monthData.itemList.length>0">
						<block v-for="(item,index) in monthData.itemList" :key="item.day">
							<view class="chart-item flex align-center" @click="detail(item.day)">
								<view class="chart-time">
									{{item.day}}
								</view>
								<view class="chart-bar-box flex justify-between align-center">
									<view class="chart-bar" :style="{width:item.width}"></view>
									<view class="chart-num">
										￥{{$xy.delMoney(item.payMoney)}}/{{item.num}}笔
									</view>
								</view>
							</view>
						</block>
					</view>

					<view v-else class='empty'>
						<u-empty mode="data" text="数据为空"></u-empty>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		totalGroupDay,
		totalGroupDayRec
	} from "@/api/order/order.js"

	export default {
		data() {
			return {
				tabList: [{
						name: '今日统计'
					},
					{
						name: '本月统计'
					}
				],
				current: 0,
				monthData: {
					itemList: [],
					totalNum: 0,
					totalPayMoney: 0
				},
				timeShow: false,
				date: null,
				datePicker: null,
				dateDx: 0,

				monthDx: 0,
				month: null,
				monthShow: false,
				monthPicker: null,
				tabCurrent: 0,

				vipTotal: {
					itemList: [],
					totalNum: 0,
					totalPayMoney: 0
				},
				vipRecTotal: {
					itemList: [],
					totalNum: 0,
					totalPayMoney: 0
				},
			}
		},

		computed: {
			today() {
				let nowDate = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
				return this.date == nowDate ? '今日' : '当日'
			}
		},

		onLoad(o) {
			this.datePicker = new Date()
			this.monthPicker = new Date()
			this.date = uni.$u.timeFormat(this.datePicker, 'yyyy-mm-dd')
			this.month = uni.$u.timeFormat(this.datePicker, 'yyyy-mm')
			this.tabList[0].name = `今日统计`
			this.tabList[1].name = `本月统计`
		},

		onShow() {
			this.getData()
		},


		methods: {
			tabClick(e) {
				this.current = e.index
				this.getData()
			},

			getData() {
				if (this.current == 0) {
					this.getDayData()
				} else {
					this.getMonthData()
				}
			},

			dayTimeClick(e) {
				this.timeShow = true
			},

			monthTimeClick(e) {
				this.monthShow = true
			},

			confirm(e) {
				this.date = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.isToday()
				this.getData()
				this.timeShow = false;
			},

			close() {
				this.timeShow = false
			},

			monthClose() {
				this.monthShow = false
			},

			// 是否今日
			isToday(day) {
				let nowDate = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
				if (this.date == nowDate) {
					this.tabList[0].name = `今日统计`
				} else {
					this.tabList[0].name = `按日统计`
				}
			},

			isNowMonth(month) {
				let nowMonth = uni.$u.timeFormat(new Date(), 'yyyy-mm')
				if (this.month == nowMonth) {
					this.tabList[1].name = `本月统计`
				} else {
					this.tabList[1].name = `按月统计`
				}
			},

			changeDay(add) {
				if (add == 0) {
					this.dateDx = 0;
				} else if (add == -1) { //前一天
					this.dateDx += add;
				} else if (add == 1) { //后一天
					let nowDate = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
					if (new Date(this.date).getTime() < new Date(nowDate).getTime()) { //处理超过当前时间的情况
						this.dateDx += add;
					} else {
						this.$modal.msg('别点了,没有数据啦~')
						return
					}
				}

				let date = new Date().getTime();
				let newDate = date + this.dateDx * (24 * 60 * 60 * 1000);
				this.date = uni.$u.timeFormat(newDate, 'yyyy-mm-dd')
				this.isToday()
				this.getData()
			},

			changeMonth(add) {
				// if (add == 0) {
				// 	this.monthDx = 0;
				// } else {
				// 	if (add > 0) {
				// 		if (this.monthDx == 0) {
				// 			this.$modal.msg('别点了,没有数据啦~')
				// 			return
				// 		}
				// 	}
				// 	this.monthDx += add;
				// }

				if (add == 0) {
					this.monthDx = 0;
				} else if (add == -1) { //前一月
					this.monthDx += add;
				} else if (add == 1) { //后一月
					console.log(new Date(this.month).getFullYear(), new Date().getFullYear())
					if ((new Date(this.month).getFullYear() < new Date().getFullYear()) || (new Date(this.month)
							.getFullYear() == new Date().getFullYear() && new Date(this.month)
							.getMonth() < new Date().getMonth())) { //年份小于，年份等于月份小于满足条件
						this.monthDx += add;
					} else {
						this.$modal.msg('别点了,没有数据啦~')
						return
					}
				}

				let today = new Date();
				let month = new Date(today.getFullYear(), today.getMonth() + this.monthDx, today.getDate());
				this.month = uni.$u.timeFormat(month, 'yyyy-mm')
				this.isNowMonth()
				this.getData()
			},

			async getMonthData() {
				let data = this.tabCurrent == 0 ? await this.getVipRecData() : await this.getVipData()
				let max = 0;
				if (data.itemList && data.itemList.length > 0) {
					for (let i = 0; i < data.itemList.length; i++) {
						let item = data.itemList[i];
						if (item.payMoney > max) {
							max = item.payMoney
						}
					}
					for (let i = 0; i < data.itemList.length; i++) {
						let item = data.itemList[i];
						if (item.payMoney > 0) {
							item.width = (item.payMoney / (max / 0.7)) * 100 + '%'
						} else {
							item.width = '0%'
						}
					}
				}
				this.monthData = data
				console.log(this.monthData)
			},

			getDayData() {
				this.getVipRecData('day')
				this.getVipData('day')
			},

			/**
			 * 会员储值统计
			 */
			getVipRecData(type) {
				let params = {}
				if (type == 'day') {
					params = {
						startTime: this.date + ' 00:00:00',
						endTime: this.date + ' 23:59:59'
					}
				} else {
					let monthArr = this.month.split('-')
					let lastDate = new Date(monthArr[0], monthArr[1], 0).getDate();
					params = {
						startTime: this.month + '-01 00:00:00',
						endTime: `${this.month}-${lastDate} 23:59:59`
					}
				}

				return new Promise((resolve, reject) => {
					totalGroupDayRec(params).then(res => {
						let data = res.data;
						this.vipRecTotal = data
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			/**
			 * 会员统计
			 */
			getVipData(type) {
				let params = {}
				if (type == 'day') {
					params = {
						startTime: this.date + ' 00:00:00',
						endTime: this.date + ' 23:59:59'
					}
				} else {
					let monthArr = this.month.split('-')
					let lastDate = new Date(monthArr[0], monthArr[1], 0).getDate();
					params = {
						startTime: this.month + '-01 00:00:00',
						endTime: `${this.month}-${lastDate} 23:59:59`
					}
				}
				return new Promise((resolve, reject) => {
					totalGroupDay(params).then(res => {
						let data = res.data;
						this.vipRecTotal = data
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			detail(time) {
				this.current = 0;
				this.date = time;
				this.tabList[0].name = `按日统计`
				this.getData()
			},

			monthConfirm(e) {
				this.month = uni.$u.timeFormat(e.value, 'yyyy-mm')
				// this.isToday()
				// this.getData()
				this.monthShow = false;
				this.isNowMonth()
				this.getData()
			},

			chartTabChange(type) {
				this.tabCurrent = type;
				this.getMonthData()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #fff !important;

		.content {
			color: #333;

			.tab-wrap {
				background-color: #fff;

				.tab {
					width: 90%;
					margin-left: -12rpx;
				}
			}

			.time-tab {
				margin-top: 24rpx;
				padding: 0 13rpx;

				.time-item {
					padding: 0 64rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					margin-right: 20rpx;
					line-height: 62rpx;

					&.time-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}

				.change-day {
					margin-left: 12rpx;
				}
			}

			.total-content {

				.today-receive {
					width: 724rpx;
					margin-left: 13rpx;
					background: #2C6FF3;
					box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
					border-radius: 14rpx;
					color: #fff;
					padding: 28rpx 18rpx;
					margin-top: 20rpx;

					>view:nth-child(1) {
						font-size: 30rpx;
						line-height: 30rpx;
						font-weight: 500;
					}

					>view:nth-child(2) {
						font-size: 32rpx;
						line-height: 64rpx;
						margin-top: 32rpx;
						text-align: center;

						>view {
							font-size: 64rpx;
							padding-left: 12rpx;
							display: inline-block;
							font-weight: bold;
						}
					}

					>view:nth-child(3) {
						font-size: 26rpx;
						line-height: 26rpx;
						font-weight: 500;
						margin-top: 28rpx;
						text-align: center;
					}

					&.month-receive {
						color: #fff;

						.sr-head {
							>view:nth-child(1) {
								font-size: 24rpx;
								line-height: 44rpx;
								margin-top: 23rpx;
								font-weight: bold;
								text-align: center;

								>view {
									font-size: 44rpx;
									display: inline-block;
									padding-left: 12rpx;
								}
							}

							>view:nth-child(2) {
								font-size: 26rpx;
								line-height: 26rpx;
								margin-top: 22rpx;
								text-align: center;
							}
						}

						.sr-total {
							margin-top: 42rpx;

							.sr-item {
								width: 25%;
								text-align: center;

								.sr-name {
									font-size: 26rpx;
									line-height: 26rpx;
									margin-top: 24rpx;
								}

								.sr-num {
									font-size: 24rpx;
									font-weight: 800;
									line-height: 32rpx;

									>text {
										font-size: 32rpx;
									}
								}
							}
						}
					}
				}

				.risk-order {
					padding: 0 12rpx;

					.risk-item {
						width: 230rpx;
						height: 120rpx;
						background: #F1F6FF;
						border-radius: 14rpx;
						padding: 24rpx 10rpx 18rpx 12rpx;

						.risk-name {
							font-size: 26rpx;
							padding-left: 16rpx;
							position: relative;
							line-height: 26rpx;


							&::before {
								content: '';
								width: 6rpx;
								height: 20rpx;
								position: absolute;
								left: 0;
								top: 2rpx;
								background-color: #2C6FF3;
							}

							&.risk-name2::before {
								background-color: #FB360F;
							}

							&.risk-name3::before {
								background-color: #5804E2;
							}

						}

						.risk-num {
							font-size: 32rpx;
							text-align: center;
							margin-top: 26rpx;
							line-height: 32rpx;
							font-weight: bold;
						}

						&.to-month {
							width: 354rpx;
							height: 149rpx;
							background: #F1F6FF;
							border-radius: 14rpx;

							.risk-num {
								font-size: 24rpx;
								margin-top: 40rpx;
								line-height: 32rpx;
								text-align: left;
								padding-left: 12rpx;

								>text {
									font-size: 32rpx;
									font-weight: bold;
								}
							}
						}
					}
				}

				.card {
					box-shadow: 0px 0px 10px 0px rgba(174, 201, 255, 0.2);
					border-radius: 14rpx;
				}

				.sale-refund {
					width: 724rpx;
					background: #FFFFFF;
					margin-left: 12rpx;
					margin-top: 30rpx;
					padding: 34rpx 34rpx 47rpx;

					.sr-title {
						font-size: 30rpx;
						font-weight: 800;
						line-height: 30rpx;
						position: relative;
						padding-left: 24rpx;

						>image {
							width: 14rpx;
							height: 25rpx;
							position: absolute;
							left: 0;
							top: 2rpx;
						}
					}

					.sr-head {
						color: #333;

						>view:nth-child(1) {
							font-size: 24rpx;
							line-height: 44rpx;
							margin-top: 43rpx;
							font-weight: bold;
							text-align: center;

							>view {
								font-size: 44rpx;
								display: inline-block;
								padding-left: 12rpx;
							}
						}

						>view:nth-child(2) {
							font-size: 26rpx;
							line-height: 26rpx;
							margin-top: 22rpx;
							text-align: center;
						}
					}

					.sr-content {
						margin-top: 20rpx;

						.sr-content-item {
							width: 334rpx;
							background: #F1F6FF;
							border-radius: 14rpx;
							padding: 24rpx 18rpx;

							&.sr-content-right {
								background-color: #FFF7F7;
							}

							&.sr-content-left1 {
								background-color: #FFF7F7;
								padding-bottom: 58rpx;
							}

							&.sr-content-right1 {
								background-color: #F9F9F9;
								padding-bottom: 58rpx;
							}

							.sc-top {
								font-size: 28rpx;
								line-height: 28rpx;
								font-weight: 800;
							}

							.sc-bot {
								.sc-item {
									width: 228rpx;
									text-align: center;
									margin-top: 40rpx;

									.sc-name {
										font-size: 26rpx;
										color: #555555;
										line-height: 26rpx;
										margin-top: 24rpx;
									}

									.sc-num {
										font-size: 24rpx;
										font-weight: 800;
										line-height: 32rpx;

										>text {
											font-size: 32rpx;
										}
									}
								}
							}

							.sc-val {
								font-size: 32rpx;
								line-height: 32rpx;
								margin-top: 45rpx;
								font-weight: 800;
								color: #333333;
								text-align: center;
							}
						}
					}

					.sr-total {
						margin-top: 42rpx;

						.sr-item {
							width: 25%;
							text-align: center;

							.sr-name {
								font-size: 26rpx;
								color: #555555;
								line-height: 26rpx;
								margin-top: 24rpx;
							}

							.sr-num {
								font-size: 24rpx;
								font-weight: 800;
								line-height: 32rpx;

								>text {
									font-size: 32rpx;
								}
							}
						}
					}
				}

				.sub-rec-ref {
					width: 724rpx;
					margin-left: 13rpx;
					margin-top: 30rpx;

					.card {
						width: 354rpx;
						padding: 26rpx 22rpx;

						&.srr-rec {
							background-color: #2C6FF3;
							color: #fff;
						}

						.srr-title {
							font-size: 30rpx;
							line-height: 30rpx;
						}

						.srr-total {
							margin-top: 36rpx;

							.srr-item {
								width: 182rpx;

								.srr-name {
									font-size: 26rpx;
									line-height: 26rpx;
								}

								.srr-num {
									font-size: 24rpx;
									font-weight: 800;
									line-height: 32rpx;
									margin-top: 24rpx;

									>text {
										font-size: 32rpx;
									}
								}
							}
						}
					}
				}
			}

			.month {
				.chart {
					width: 724rpx;
					margin-left: 13rpx;
					margin-top: 12rpx;
					padding-bottom: 24rpx;

					.chart-title {
						padding: 30rpx 25rpx 20rpx;

						.chart-title-text {
							font-size: 28rpx;
							font-weight: 500;
							color: #333333;
						}

						.sort-type {
							width: 330rpx;
							height: 50rpx;
							line-height: 48rpx;
							text-align: center;
							font-weight: normal;

							.sort-type-item {
								width: 50%;
								font-size: 26rpx;
								color: #555555;
								background-color: #fff;

								&.sort-type-item:nth-child(1) {
									border-top-left-radius: 6rpx;
									border-bottom-left-radius: 6rpx;
									border-top: 1rpx solid #CCCCCC;
									border-left: 1rpx solid #CCCCCC;
									border-bottom: 1rpx solid #CCCCCC;

									&.sort-type-show {
										color: #fff;
										border-top: 1rpx solid #2C6FF3;
										border-left: 1rpx solid #2C6FF3;
										border-bottom: 1rpx solid #2C6FF3;
										background-color: #2C6FF3;
									}
								}

								&.sort-type-item:nth-child(2) {
									border-top-right-radius: 6rpx;
									border-bottom-right-radius: 6rpx;
									border-top: 1rpx solid #CCCCCC;
									border-right: 1rpx solid #CCCCCC;
									border-bottom: 1rpx solid #CCCCCC;

									&.sort-type-show {
										color: #fff;
										border-top: 1rpx solid #2C6FF3;
										border-right: 1rpx solid #2C6FF3;
										border-bottom: 1rpx solid #2C6FF3;
										background-color: #2C6FF3;
									}
								}
							}

						}
					}

					.chart-content {
						padding: 0 15rpx;

						.chart-item {
							height: 54rpx;
							margin-top: 10rpx;

							.chart-time {
								margin-right: 10rpx;
								width: 190rpx;
							}

							.chart-bar-box {
								background: #F6F6F6;
								border-radius: 8rpx 27rpx 27rpx 8rpx;
								width: 620rpx;

								.chart-bar {
									height: 54rpx;
									border-radius: 8rpx 27rpx 27rpx 8rpx;
									background-color: #98C0FC;
									transition: all 1s linear;
								}

								.chart-num {
									color: #df6b73;
									margin-right: 12rpx;
									font-size: 26rpx;
								}
							}
						}
					}

					.empty {
						padding: 60rpx 0;
					}
				}
			}
		}
	}
</style>