<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <u-navbar
      leftIconColor="#fff"
      titleStyle="color:#fff;fontSize:36rpx;"
      :autoBack="true"
      bgColor="#2C6FF3"
      :placeholder="true"
      title="货道设置"></u-navbar>

    <!-- 提示文本 -->
    <view class="tip-text">
      <text class="red">提示：用手指可以滑出需要添加商品的货道</text>
    </view>

    <!-- 操作按钮 -->
    <view class="action-bar flex justify-between">
      <view class="selection-counter">
        已选择 <text class="counter-num">{{ selectedCount }}</text> 个货道
      </view>
      <xbutton @click="clearAisleFun"> 货道清理 </xbutton>
    </view>

    <!-- 货道选择网格 -->
    <view class="grid-container">
      <!-- 货道网格 -->
      <view class="channel-wrapper">
        <!-- 列选择按钮 -->
        <view
          class="column-selectors"
          :style="{
            gridTemplateColumns: `40rpx repeat(${
              deviceInfo.layerAisleNum || 10
            }, 1fr)`,
          }">
          <view class="corner-placeholder" @click="toggleSelectAll">
            <text>全选</text>
          </view>
          <view
            v-for="(_, colIndex) in deviceInfo.layerAisleNum"
            :key="colIndex"
            class="column-selector"
            @click="selectColumn(colIndex)">
            <view
              class="selector-circle"
              :class="{ selected: isColumnSelected(colIndex) }"></view>
          </view>
        </view>

        <view class="grid-with-row-selectors">
          <!-- 行选择按钮 -->
          <view class="row-selectors">
            <view
              v-for="(_, rowIndex) in deviceInfo.layerNum"
              :key="rowIndex"
              class="row-selector">
              <view
                class="selector-circle"
                :class="{ selected: isRowSelected(rowIndex) }"
                @click="selectRow(rowIndex)"></view>
            </view>
          </view>

          <!-- 货道网格 -->
          <view
            class="channel-grid"
            :style="{
              gridTemplateColumns: `repeat(${
                deviceInfo.layerAisleNum || 10
              }, 1fr)`,
              gridTemplateRows: `repeat(${deviceInfo.layerNum || 6}, 70rpx)`,
            }">
            <view
              v-for="(item, index) in channels"
              :key="index"
              :class="[
                'channel-item',
                item.selected ? 'channel-selected' : item.status,
              ]"
              @tap="handleItemTap(index)"
              @touchstart.stop.prevent="handleTouchStart(index, $event)"
              @touchmove.stop.prevent="handleTouchMove($event)"
              @touchend.stop.prevent="handleTouchEnd">
              <text class="channel-number">{{
                item.aisleNo ? item.aisleNo.slice(-2) : ""
              }}</text>

              <view class="channel-img" v-if="item.cover">
                <u--image
                  width="35rpx"
                  height="35rpx"
                  radius="8rpx"
                  :src="item.cover"
                  mode="aspectFit"
                  :lazy-load="true">
                </u--image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 图例说明 -->
      <view class="legend">
        <view class="legend-item">
          <view class="color-block active"></view>
          <text>正在设置</text>
        </view>
        <view class="legend-item">
          <view class="color-block available"></view>
          <text>可设置</text>
        </view>
        <view class="legend-item">
          <view class="color-block disabled"></view>
          <text>已补</text>
        </view>
      </view>
    </view>

    <!-- 货道商品设置 -->
    <view class="setting-form">
      <view class="form-title">货道商品设置</view>
      <u-form
        :model="formData"
        ref="uForm"
        :label-width="100"
        label-position="left">
        <!-- 添加商品选择下拉列表 -->
        <u-form-item label="选择商品" required class="form-item">
          <view class="product-select-wrap">
            <view @click="$refs.productSelector.showPopup = true">
              <u--input
                clearable
                readonly
                suffixIcon="arrow-down"
                v-model="formData.name"
                suffixIconStyle="color: #909399"
                placeholder="请选择商品"
                border="surround"></u--input>
            </view>
            <ProductSelector
              ref="productSelector"
              :products="productList"
              @confirm="onProductSelected"
              :placeholder="formData.name || '请选择商品'" />
          </view>
        </u-form-item>

        <!-- 商品图片 -->
        <u-form-item label="商品图片" class="form-item">
          <u--image
            radius="14rpx"
            width="120rpx"
            height="120rpx"
            @click="$xy.previewImg(formData.cover)"
            :src="formData.cover"
            mode="aspectFit"
            :lazy-load="true">
          </u--image>
        </u-form-item>

        <!-- 库存设置 -->
        <u-form-item label="单货道库存" class="form-item">
          <u-number-box v-model="formData.stock" :min="0"></u-number-box>
        </u-form-item>
      </u-form>
    </view>

    <view class="btn">
      <xbutton size="large" @click="saveSettings"> 保存设置 </xbutton>
    </view>
  </view>
</template>

<script>
import {
  deviceGoodsList,
  aisleInfoList,
  batchAddGoods,
  clearAisle,
} from "@/api/replenishment/replenishment.js";

import ProductSelector from "./components/product-selector.vue";

export default {
  components: {
    ProductSelector,
  },
  data() {
    return {
      deviceId: "", // 设备ID
      doorType: "", // 门类型（1左门，2右门）
      selectAll: false,
      channels: [], // 将从API获取
      formData: {
        goodsId: "",
        name: "",
        stock: 2,
        cover: "",
      },
      productPickerShow: false,
      productList: [], // 将从API获取
      fileList: [],
      isSelecting: false,
      selectionMode: null,
      // 设备基本信息
      deviceInfo: {
        deviceId: 0,
        deviceType: 0,
        layerNum: 0,
        layerAisleNum: 0,
        electronicScaleNum: 0,
        selectedAisleNos: [], // 用于存储选中的货道号集合
      },
    };
  },
  computed: {
    selectedCount() {
      return this.channels.filter((item) => item.selected).length;
    },
  },
  methods: {
    toggleSelectAll() {
      this.selectAll = !this.selectAll;
      this.handleSelectAll(this.selectAll);
    },
    // 修改toggleChannel方法，每次选择变更时都更新货道号集合
    toggleChannel(index) {
      console.log("切换货道状态:", index);
      if (index >= 0 && index < this.channels.length) {
        this.channels[index].selected = !this.channels[index].selected;
        this.checkSelectAll();

        // 更新选中的货道号集合
        this.updateSelectedAisleNos();
      }
    },
    selectColumn(colIndex) {
      if (colIndex < 0 || colIndex >= this.deviceInfo.layerAisleNum) return;

      const columnsSelected = this.isColumnSelected(colIndex);
      const layerNum = this.deviceInfo.layerNum || 6;

      for (let row = 0; row < layerNum; row++) {
        const index = row * this.deviceInfo.layerAisleNum + colIndex;

        if (index >= 0 && index < this.channels.length) {
          this.channels[index].selected = !columnsSelected;
        }
      }

      this.checkSelectAll();
      this.updateSelectedAisleNos();
    },
    selectRow(rowIndex) {
      if (rowIndex < 0 || rowIndex >= this.deviceInfo.layerNum) return;

      const rowSelected = this.isRowSelected(rowIndex);
      const layerAisleNum = this.deviceInfo.layerAisleNum || 10;

      for (let col = 0; col < layerAisleNum; col++) {
        const index = rowIndex * layerAisleNum + col;

        if (index >= 0 && index < this.channels.length) {
          this.channels[index].selected = !rowSelected;
        }
      }

      this.checkSelectAll();
      this.updateSelectedAisleNos();
    },
    isColumnSelected(colIndex) {
      if (colIndex < 0 || colIndex >= this.deviceInfo.layerAisleNum)
        return false;
      const layerNum = this.deviceInfo.layerNum || 6;

      for (let row = 0; row < layerNum; row++) {
        const index = row * this.deviceInfo.layerAisleNum + colIndex;

        if (index >= 0 && index < this.channels.length) {
          if (!this.channels[index].selected) {
            return false;
          }
        }
      }

      return true;
    },
    isRowSelected(rowIndex) {
      if (rowIndex < 0 || rowIndex >= this.deviceInfo.layerNum) return false;
      const layerAisleNum = this.deviceInfo.layerAisleNum || 10;

      for (let col = 0; col < layerAisleNum; col++) {
        const index = rowIndex * layerAisleNum + col;

        if (index >= 0 && index < this.channels.length) {
          if (!this.channels[index].selected) {
            return false;
          }
        }
      }

      return true;
    },
    handleSelectAll(value) {
      this.channels.forEach((channel) => {
        channel.selected = value;
      });
      this.updateSelectedAisleNos();
    },
    checkSelectAll() {
      this.selectAll = this.channels.every((channel) => channel.selected);
    },
    clearSelection() {
      this.channels.forEach((channel) => {
        channel.selected = false;
      });
      this.selectAll = false;
      // 清空选中的货道号集合
      this.selectedAisleNos = [];
    },

    // 添加更新选中货道号集合的方法
    updateSelectedAisleNos() {
      // 过滤出所有选中的货道
      const selectedChannels = this.channels.filter(
        (channel) => channel.selected
      );

      // 提取所有选中货道的货道号，并按一定规则排序
      this.selectedAisleNos = selectedChannels
        .map((channel) => channel.aisleNo)
        .sort((a, b) => {
          if (!a || !b) return 0;

          // 首先按照第一位数字（层号）排序
          const layerA = parseInt(a.charAt(0));
          const layerB = parseInt(b.charAt(0));

          if (layerA !== layerB) {
            return layerA - layerB; // 层号从小到大排序
          }

          // 层号相同时，按照后两位数字（列号）排序
          const colA = parseInt(a.substring(1));
          const colB = parseInt(b.substring(1));
          return colA - colB; // 列号从小到大排序
        });

      console.log("当前选中的货道号集合:", this.selectedAisleNos);
    },

    getChannelClass(channel) {
      return {
        active: channel.status === "active",
        available: channel.status === "available",
        disabled: channel.status === "disabled",
      };
    },

    handleTouchStart(index, event) {
      event.stopPropagation();

      this.isSelecting = true;

      this.selectionMode = !this.channels[index].selected
        ? "select"
        : "unselect";

      this.channels[index].selected = this.selectionMode === "select";
    },
    handleTouchMove(event) {
      if (!this.isSelecting) return;

      event.stopPropagation();

      const touch = event.touches[0];

      uni
        .createSelectorQuery()
        .selectAll(".channel-item")
        .boundingClientRect((rects) => {
          if (!rects || !rects.length) return;

          for (let i = 0; i < rects.length; i++) {
            const rect = rects[i];

            if (
              touch.clientX >= rect.left &&
              touch.clientX <= rect.right &&
              touch.clientY >= rect.top &&
              touch.clientY <= rect.bottom
            ) {
              const index = i;

              this.channels[index].selected = this.selectionMode === "select";
              break;
            }
          }
        })
        .exec();
    },
    handleTouchEnd(event) {
      event.preventDefault && event.preventDefault();
      event.stopPropagation && event.stopPropagation();

      if (this.isSelecting) {
        this.isSelecting = false;
        this.checkSelectAll();
        this.updateSelectedAisleNos();
      }
    },
    handleItemTap(index) {
      console.log("点击货道:", index);
      if (this.isSelecting) return;

      this.toggleChannel(index);
    },
    onProductSelected(e) {
      console.log("选择的商品:", e);
      const selectedProduct = e;

      if (selectedProduct) {
        this.formData.goodsId = selectedProduct.goodsId;
        this.formData.cover = selectedProduct.cover;
        this.formData.name = selectedProduct.name;
      }
    },
    saveSettings() {
      // 获取选中的货道ID列表
      const selectedAisleIds = this.channels
        .filter((c) => c.selected)
        .map((c) => c.id);

      console.log("选中的货道号集合:", this.selectedAisleNos);

      if (selectedAisleIds.length === 0) {
        uni.showToast({
          title: "请至少选择一个货道",
          icon: "none",
        });
        return;
      }

      console.log("选中的货道号集合:", this.selectedAisleNos);
      if (!this.formData.goodsId) {
        uni.showToast({
          title: "请选择商品",
          icon: "none",
        });
        return;
      }

      // 调用批量设置API
      batchAddGoods({
        aisleIds: selectedAisleIds,
        deviceId: this.deviceId,
        goodsId: this.formData.goodsId,
        stock: this.formData.stock,
      }).then((res) => {
        if (res.code === 200 && res.data) {
		  // 刷新货道数据
		  this.getAisleInfoList();
		  // 清除选择
		  this.clearSelection();
		  // 重置表单
		  this.formData = {
		    goodsId: "",
		    name: "",
		    stock: 2,
		  };
		  uni.$emit("refresh");

          setTimeout(()=>{
			  uni.showToast({
			    title: "设置已保存",
			    icon: "success",
			  });
		  },200)
        } else {
          uni.showToast({
            title: res.msg || "保存设置失败",
            icon: "none",
          });
        }
      });
    },
    // 获取设备货道信息
    getAisleInfoList() {
      const params = {
        deviceId: this.deviceId,
      };

      if (this.doorType) {
        params.doorType = this.doorType;
      }

      aisleInfoList(params).then((res) => {
        console.log(res);
        if (res.code === 200 && res.data) {
          // 设置设备基本信息
          this.deviceInfo = res.data.deviceInfo || {};

          // 处理货道列表
          if (res.data.list && res.data.list.length > 0) {
            // 直接使用接口返回的货道数据
            this.channels = res.data.list.map((item) => {
              // 根据货道状态设置显示样式
              let status = "available";
              if (item.goodsId) {
                status = "disabled"; // 已有商品的货道
              }

              return {
                id: item.id,
                aisleNo: item.aisleNo,
                layerNo: item.layerNo,
                goodsId: item.goodsId,
                name: item.name,
                selected: false,
                status: status,
                cover: item.cover,
              };
            });

            console.log("处理后的货道数据:", this.channels);
          } else {
            this.channels = [];
            uni.showToast({
              title: "未获取到货道数据",
              icon: "none",
            });
          }
        } else {
          uni.showToast({
            title: res.msg || "获取货道信息失败",
            icon: "none",
          });
        }
      });
    },

    // 获取设备商品列表
    getDeviceGoodsList() {
      deviceGoodsList({
        deviceId: this.deviceId,
      }).then((res) => {
        if (res.code === 200 && res.data) {
          this.productList = res.data;
        } else {
          uni.showToast({
            title: res.msg || "获取商品列表失败",
            icon: "none",
          });
        }
      });
    },

    // 添加格式化方法显示货道号
    formatAisleNo(aisleNo) {
      if (!aisleNo) return "";

      // 如果长度为3，取后两位（只显示列号）
      if (aisleNo.length === 3) {
        return aisleNo.substring(1); // 截取后两位作为显示的货道号
      }

      return aisleNo; // 其他情况原样返回
    },

    // 添加货道清理方法
    clearAisleFun() {
      // 获取选中的货道ID列表
      const selectedAisleIds = this.channels
        .filter((c) => c.selected)
        .map((c) => c.id);

      if (selectedAisleIds.length === 0) {
        uni.showToast({
          title: "请至少选择一个货道",
          icon: "none",
        });
        return;
      }

      uni.showModal({
        title: "确认清理货道",
        content: "确定要清理选中的货道吗？清理后将清空该货道的商品信息",
        success: (res) => {
          if (res.confirm) {
            // 调用清理货道API
            clearAisle({
              aisleIds: selectedAisleIds,
              deviceId: this.deviceId,
            }).then((res) => {
              if (res.code === 200) {
                this.$modal.msg(res.msg || "货道清理成功");

                // 刷新货道数据
                this.getAisleInfoList();
                // 清除选择
                this.clearSelection();
                // 通知其他页面刷新
                uni.$emit("refresh");
              } else {
                this.$modal.msg(res.msg || "货道清理失败");
              }
            });
          }
        },
      });
    },
  },
  onLoad(options) {
    // 获取路由参数中的设备ID
    if (options.deviceId) {
      this.deviceId = options.deviceId;

      // 获取门类型参数（如果有）
      if (options.doorType) {
        this.doorType = options.doorType;
      }

      // 获取货道信息
      this.getAisleInfoList();

      // 获取设备商品列表
      this.getDeviceGoodsList();
    } else {
      uni.showToast({
        title: "缺少设备ID参数",
        icon: "none",
      });

      // 返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f6fa;
  min-height: 100vh;
}

.tip-text {
  padding: 20rpx 0 20rpx;

  .red {
    color: #ff0000;
    font-size: 28rpx;
  }
}

.action-bar {
  padding: 20rpx 0 20rpx;
}

.selection-counter {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;

  .counter-num {
    color: #4080ff;
    font-weight: bold;
    margin: 0 4rpx;
  }
}

.grid-container {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.select-all {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;

  .select-all-label {
    display: inline-block;
    padding: 6rpx 20rpx;
    border-radius: 30rpx;
    transition: all 0.3s ease;
    font-size: 30rpx;
    font-weight: 500;
    color: #333;

    &.selected {
      background-color: #e6efff;
      color: #4080ff;
    }
  }
}

.channel-wrapper {
  position: relative;
  width: 100%;
}

.column-selectors {
  display: grid;
  gap: 4rpx;
  margin-bottom: 10rpx;
  padding-right: 0;
  box-sizing: border-box;
}

.corner-placeholder {
  width: 50rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.column-selector {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 40rpx;
}

.grid-with-row-selectors {
  display: flex;
  align-items: flex-start;
  width: 100%;
}

.row-selectors {
  display: flex;
  flex-direction: column;
  width: 40rpx;
  flex-shrink: 0;
  margin-right: 4rpx;
}

.row-selector {
  width: 40rpx;
  height: 70rpx;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selector-circle {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 1px solid #dddddd;
  background-color: #ffffff;
  transition: all 0.2s ease;

  &.selected {
    background-color: #4080ff;
    border-color: #4080ff;

    &::after {
      content: "";
      display: block;
      width: 16rpx;
      height: 8rpx;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      transform: rotate(-45deg) translate(3rpx, 0);
      margin: 8rpx auto 0;
    }
  }

  &:active {
    transform: scale(0.9);
  }
}

.channel-grid {
  display: grid;
  gap: 4rpx;
  touch-action: none;
  flex-grow: 1;
  box-sizing: border-box;
}

.channel-item {
  height: 70rpx;
  border-radius: 8rpx;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  box-sizing: border-box;

  .channel-number {
    padding-left: 12rpx;
    font-size: 24rpx;
    font-weight: 500;
    color: #333;
    transition: all 0.3s;
  }

  &.available {
    background-color: #f2f6ff;
    border: none;

    &:active {
      opacity: 0.8;
    }
  }

  &.active {
    background-color: #f2f6ff;
    border: none;

    &:active {
      opacity: 0.8;
    }
  }

  &.disabled {
    background-color: #ececec;
    border: none;

    .channel-number {
      color: #999;
    }

    &:active {
      opacity: 0.8;
    }
  }

  &.channel-selected {
    background-color: #88afff !important;
    border: none;

    .channel-number {
      color: #ffffff;
      font-weight: bold;
    }

    &:active {
      opacity: 0.8;
    }
  }

  .channel-img {
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 999;
  }
}

.legend {
  display: flex;
  justify-content: flex-end;
  gap: 30rpx;
  padding-top: 20rpx;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;

  .color-block {
    width: 36rpx;
    height: 36rpx;
    margin-right: 10rpx;
    border-radius: 8rpx;

    &.active {
      background-color: #88afff;
      border: none;
    }

    &.available {
      background-color: #f2f6ff;
      border: none;
    }

    &.disabled {
      background-color: #ececec;
      border: none;
    }
  }
}

.setting-form {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 40rpx;

  .form-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
    color: #333;
    position: relative;
    padding-left: 20rpx;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 6rpx;
      bottom: 6rpx;
      width: 6rpx;
      background-color: #4080ff;
      border-radius: 3rpx;
    }
  }

  .form-item {
    margin-bottom: 20rpx;

    ::v-deep .u-form-item {
      &__body {
        padding: 16rpx 0;
      }

      &__body__left {
        min-width: 180rpx;
        max-width: 180rpx;
        padding-right: 12rpx;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  .product-select-wrap {
    flex: 1;

    .select-trigger {
      height: 70rpx;
      border: 1rpx solid #e4e7ed;
      border-radius: 8rpx;
      padding: 0 20rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: #ffffff;
      font-size: 28rpx;
      color: #333;

      text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }
}

.btn {
  width: 724rpx;
  position: fixed;
  left: 13rpx;
  bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
  border-radius: 88rpx;
  overflow: hidden;
}
</style>
