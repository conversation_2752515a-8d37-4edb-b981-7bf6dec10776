<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="补货记录详情"></u-navbar>

		<view class="topTip">总共补了{{replenishmentList.length}}种商品<!--  数量是{{total}} --></view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100" :style="{'height':fullHeight}">
			<view class="replenishmentrecord-container" v-for="(item,index) in replenishmentList" :key="item.id">
				<view class="">
					设备商品：{{item.goodsName}}
				</view>
				<view class="" style="margin-top: 20rpx;">
					设备商品Id：{{item.deviceGoodsId}}
				</view>

				<view class="flex align-center"
					style="margin-top: 15rpx;background-color: #f5f8fb;padding: 10rpx;border-radius: 15rpx;">
					<view class="">
						<u--image radius='15rpx' :showLoading="true" :src="item.cover" width="80px" height="80px"
							@click="click"></u--image>
					</view>

					<view class="flex flex-direction align-start" style="margin-left: 35rpx;">
						<view class="flex align-center justify-around" v-if="item.oldStock!=item.newStock">
							<view class="flex flex-sub width">原库存：{{item.oldStock}}</view>
							<view class="flex">新库存：{{item.newStock}}</view>
						</view>
						<view class="flex align-center justify-around" v-if="item.oldStock!=item.newStock">
							<view class="flex flex-sub" style="color: red;">补货数量：{{item.newStock-item.oldStock}}</view>
						</view>
						<view class="flex align-center justify-around" v-if="item.oldCapacity!=item.newCapacity">
							<view class="flex flex-sub width">原容量：{{item.oldCapacity}}</view>
							<view class="flex">新容量：{{item.newCapacity}}</view>
						</view>
						<view class="flex align-center justify-around" v-if="item.newPrice!=null&&item.oldPrice!=item.newPrice">
							<view class="flex flex-sub width">原价格：￥{{(Number(item.oldPrice)/100).toFixed(2)}}</view>
							<view class="flex">新价格：￥{{(Number(item.newPrice)/100).toFixed(2)}}</view>
						</view>
						<view class="flex align-center justify-around" v-if="item.oldWarning!=item.newWarning">
							<view class="flex flex-sub width">原预警值：{{item.oldWarning}}</view>
							<view class="flex">新预警值：{{item.newWarning}}</view>
						</view>

					</view>


				</view>






			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		obj
	} from "@/api/replenishment/replenishment.js"
	export default {
		data() {
			return {
				replenishmentList: [],
				isEmpty: false,
				id: '',
				total: 0,
				fullHeight:0,
			}
		},
		async onLoad(e) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')

			this.id = e.id
			this.getObj()
		},
		methods: {
			getObj() {
				obj({
					"id": this.id,
					"activityId": 0
				}).then(res => {
					if (res.data.goods == null) {
						this.isEmpty = true
					}
					this.replenishmentList = res.data.goods;
					this.replenishmentList.forEach(item => {
						this.total = 0
						this.total += item.newStock - item.oldStock
					})

					this.isEmpty = this.replenishmentList.length == 0;

				})
			}
		}
	}
</script>
<style>
	page {
		background-color: #f7f7f7;
	}
</style>
<style lang="scss" scoped>
	.container {
		.margintop {
			margin-top: 20rpx;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.scrollview {}

		.topTip {
			padding: 15rpx;
			text-align: center;
			background-color: #fdf6ec;
			color: #faba5a;
		}

		.empty {
			margin-top: 120rpx
		}

		.width {
			width: 220rpx;
		}


		.magin {
			margin: 10rpx 20rpx;
		}

		.replenishmentrecord-container {
			background-color: #fff;
			padding: 20rpx;
			border-radius: 15rpx;
			margin: 0 20rpx 20rpx 20rpx;
			box-shadow: 0 5rpx 4rpx rgba(179, 179, 179, 0.3);
		}
	}
</style>