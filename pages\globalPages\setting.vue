<template>
	<view class="container">
		<u-navbar titleStyle="color:#333;fontSize:36rpx;" :autoBack="false" bgColor="#fff" :placeholder="true"
			title="设置" @leftClick="back"></u-navbar>
		<view class="content">
			<u-cell-group :border="false">
				<u-cell title="修改密码" :isLink="true" @click="pwdShow=true">
				</u-cell>
				<u-cell title="绑定微信小程序" :isLink="true" @click="bind('wx')">
					<text slot="value">{{wxIsBind?'已绑定':'未绑定'}}</text>
				</u-cell>
				<u-cell title="绑定微信公众号" :isLink="true" @click="bind('wp')">
					<text slot="value">{{wxMpIsBind?'已绑定':'未绑定'}}</text>
				</u-cell>
				<u-cell title="绑定支付宝" :isLink="true" @click="bind('ali')">
					<text slot="value">{{aliIsBind?'已绑定':'未绑定'}}</text>
				</u-cell>
				<u-cell title="短信通知" v-if="checkPermi(['setting:msgNotice'])">
					<u-switch slot="value" activeColor="#2C6FF3" v-model="isOrdersSms" @change="change"></u-switch>
				</u-cell>
				<u-cell title="商户信息展示">
					<u-switch slot="value" activeColor="#2C6FF3" v-model="showMercInfo" @change="change"></u-switch>
				</u-cell>
				<u-cell title="退出登录" :isLink="true" @click="loginOut">
				</u-cell>
			</u-cell-group>
		</view>

		<xpopup :show="pwdShow" @close="pwdClose" @confirm="pwdSubmit" :showBtn="true" title="修改密码">
			<view class="pwd-popup-content">
				<view class="password-tips" style="color: red;">
					提示：密码必须为字母/数字且不少于6位数！
				</view>
				<view class="pwd-content flex align-center">
					<view>新密码：</view>
					<view>
						<u--input placeholder="请输入新密码" type="password" border="surround"
							v-model="newpassword"></u--input>
					</view>
				</view>
			</view>
		</xpopup>

		<xpopup :show="bindShow" @close="bindClose" @confirm="bindSubmit" :showBtn="true" :title="bindTitle">
			<view class="pwd-popup-content flex align-center">
				<u-input placeholder="请输入验证码" v-model="smsCode" type="number">
					<template slot="suffix">
						<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
						<u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
					</template>
				</u-input>

			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		updateUserInfo,
		sendCode,
		updateMerConfig
	} from "@/api/system/user.js"
	import {
		bindWx,
		isBind,
		bindWxMp,
	} from "@/api/system/employee.js"
	import {
		updateByAli
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				newpassword: '',
				pwdShow: false,
				wxIsBind: false,
				aliIsBind: false,
				wxMpIsBind: false,
				type: null,
				bindShow: false,
				bindTitle: '绑定',
				tips: '',
				value: '',
				smsCode: null,
				code: null,
				aliId: null,
				isOrdersSms: false,
				showMercInfo: true
			}
		},
		onShow() {
			this.getUserDetail()
		},
		onLoad(o) {
			if (o.aliId) {
				this.aliId = o.aliId;
				this.scene = 3
				this.bindTitle = '绑定支付宝小程序'
				this.bindShow = true
			}
			if (o.code) {
				this.code = o.code
				this.scene = 2
				this.bindTitle = '绑定微信公众号'
				this.bindShow = true
			}
			if (o.type) {
				this.type = o.type
				this.pwdShow = true
			}
		},
		methods: {
			back() {
				if (this.type == 0) {
					this.$tab.redirectTo(`/pages/globalPages/home?tabName=首页`)
				} else {
					this.$tab.redirectTo(`/pages/globalPages/home?tabName=我的`)
				}
			},
			changePwd() {
				this.pwdShow = true
			},

			pwdSubmit() {
				let reg = /^[0-9A-Za-z]{6,}$/
				if (reg.test(this.newpassword)) {
					updateUserInfo({
						password: this.newpassword
					}).then(res => {
						this.$modal.showToast('修改成功~')
						if (this.type == 0) {
							setTimeout(() => {
								this.$tab.redirectTo(`/pages/globalPages/home?tabName=首页`)
							}, 1000)
						}
					})
					this.pwdClose()
				} else {
					this.$modal.msg('密码必须为字母/数字且不少于6位数！')
				}
			},

			pwdClose() {
				this.pwdShow = false
			},

			loginOut() {
				this.$store.dispatch('LogOut').then(res => {
					this.$tab.reLaunch('/pages/login')
				})
			},

			getUserDetail(id) {
				isBind({}).then(res => {
					this.wxIsBind = res.data.wechat;
					this.aliIsBind = res.data.alipay;
					this.wxMpIsBind = res.data.wxMp;
					this.isOrdersSms = res.data.isOrdersSms;
					this.showMercInfo = res.data.showMercInfo;
				})
			},

			change(e) {
				console.log(e)
				updateMerConfig({
					isOrdersSms: this.isOrdersSms,
					showMercInfo: this.showMercInfo
				}).then(res => {
					this.$modal.msg(`${e?'开启':'关闭'}成功！`)
				})
			},

			bindWx() {
				let type = this.wxIsBind ? '重新' : '确认'
				this.$modal.oldConfirm(`是否${type}绑定微信？`).then(res => {
					this.bindShow = true
				})
			},

			bindWxMp() {
				let type = this.wxMpIsBind ? '重新' : '确认'
				this.$modal.oldConfirm(`是否${type}绑定公众号？`).then(res => {
					//微信公众号授权
					this.$tab.navigateTo(`/pages/webView`)
				})
			},

			bindAli() {
				//支付宝绑定逻辑
				let type = this.aliIsBind ? '重新' : '确认'
				this.$modal.oldConfirm(`是否${type}绑定支付宝？`).then(res => {
					//微信公众号授权
					this.$tab.navigateTo('/pages/activeDevice/bindAliAcc')
				})

			},

			bind(type) {
				switch (type) {
					case 'wx':
						this.scene = 1
						this.bindTitle = '绑定微信小程序'
						this.bindWx()
						break;
					case 'wp':
						this.scene = 2
						this.bindTitle = '绑定微信公众号'
						this.bindWxMp()
						break;
					case 'ali':
						this.scene = 3
						this.bindTitle = '绑定支付宝小程序'
						this.bindAli()
						break;
					default:
						break;
				}
			},

			bindClose() {
				this.bindShow = false
			},

			bindSubmit() {
				if (this.bindTitle == '绑定微信小程序') {
					//微信绑定逻辑
					uni.login({
						provider: 'weixin',
						success: (loginRes) => {
							console.log('loginRes', loginRes)
							bindWx({
								code: loginRes.code,
								smsCode: this.smsCode,
								appId: uni.getAccountInfoSync().miniProgram.appId
							}).then(res => {
								this.$modal.showToast('绑定成功~')
								setTimeout(() => {
									this.getUserDetail()
								}, 1000)
							})
						}
					});
				}
				if (this.bindTitle == '绑定微信公众号') {
					bindWxMp({
						code: this.code,
						smsCode: this.smsCode
					}).then(res => {
						this.$modal.showToast('绑定成功~')
						setTimeout(() => {
							this.getUserDetail()
						}, 1000)
					})
				}
				if (this.bindTitle == '绑定支付宝小程序') {
					updateByAli({
						aliUserId: this.aliId,
						smsCode: this.smsCode
					}).then(res => {
						this.$modal.showToast('绑定成功~')
						setTimeout(() => {
							this.getUserDetail()
						}, 1000)
					})
				}
				this.bindShow = false
			},

			codeChange(text) {
				this.tips = text;
			},

			getCode() {
				if (this.$refs.uCode.canGetCode) {

					sendCode({
						scene: this.scene
					}).then(res => {
						this.$modal.showToast('短信验证码已经发送！')
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					})
				} else {
					this.$modal.showToast('倒计时结束后再发送');
				}
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {

		.content {
			min-height: 100vh;
			background-color: #fff;
			padding: 24rpx;

			::v-deep .u-cell__body {
				padding: 30rpx 6rpx;
			}
		}

		.pwd-popup-content {
			padding: 24rpx;

			.password-tips {
				padding-bottom: 36rpx;
			}

			.pwd-content {
				>view:nth-child(1) {
					width: 160rpx;
				}

				>view:nth-child(2) {
					width: 100%;
				}
			}
		}
	}
</style>