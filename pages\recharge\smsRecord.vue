<template>
	<!-- 费用账单 -->
	<view class="container">
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="rgba(0,0,0,0)"
			:placeholder="false" title="短信充值"></u-navbar>
		<view class="content">
			<view class="balance-top">
				<view class="top-title">
					剩余（条）
				</view>
				<view class="top-content flex justify-between align-center">
					<view class="balance-num">
						{{usableSize}}
					</view>
					<xbutton width="154rpx" height="58rpx" round="58rpx" v-if="checkPermi(['payRecord:recharging'])"
						@click="$tab.navigateTo('/pages/recharge/smsRech')">立即充值</xbutton>
				</view>
			</view>
			<view class="total flex">
				已使用{{hasSize}}条
			</view>
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y :style="{height:fullHeight}"
				@scrolltolower="loadMore">
				<view class="scroll-content" v-if="list&&list.length>0">
					<block v-for="item in list" :key="item.id">
						<view class="list-item flex justify-between">
							<view class="flex list-left">
								<!-- <image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/cz.png"
									mode="widthFix" v-if="current==0"></image> -->
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/kf.png"
									mode="widthFix"></image>
								<view class="list-msg">
									<view class="order-no">
										{{item.msg}}
									</view>
									<view class="order-time">
										{{item.createTime}}
									</view>
								</view>
							</view>
							<!-- <view class="list-num">
								<view :class="[item.balance<0?'black':'']">
									¥<text>{{$xy.delMoney(item.balance)}}</text>
								</view>
							</view> -->
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else style="padding-top: 40%;">
					<u-empty text="没有数据!"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		payRecord,
		account,
		smsTotal,
		pageMerc
	} from "@/api/recharge.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: [],
				deviceIds: [],
				current: 0,
				page: 1,
				size: 10,
				loadmoreStatus: "loadmore",
				fullHeight: 0,
				hasSize: 0,
				usableSize: 0
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			this.getTotal()
			this.getList()

			uni.$on('refresh', res => {
				this.getTotal()
			})
		},
		onShow() {
			// this.detail()
			// this.reset()
			// this.getList()
		},
		methods: {
			// tabClick(type) {
			// 	this.current = type
			// 	this.reset()
			// 	this.getList()
			// },
			getTotal() {
				smsTotal().then(res => {
					this.usableSize = res.data.usableSize ?? '0'
					this.hasSize = res.data.hasSize ?? '0'
				})
			},

			getList() {
				pageMerc({
					page: {
						current: this.page,
						size: this.size,
					},
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			loadMore(e) {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getList()
			},

			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},
		},

		onUnload() {
			uni.$off('refresh')
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			overflow: hidden;

			.balance-top {
				height: 378rpx;
				background: linear-gradient(0deg, #FFFFFF, #E2ECFF);
				overflow: hidden;
				padding: 220rpx 68rpx 0rpx 40rpx;

				.top-title {
					font-size: 28rpx;
				}

				.top-content {
					margin-top: 21rpx;

					.balance-num {
						font-size: 48rpx;
						font-weight: 800;
						color: #333333;
					}
				}
			}

			.total {
				line-height: 88rpx;
				padding: 0 20rpx;
				background-color: #fff;
			}

			.scrollview {
				margin-top: 20rpx;

				.scroll-content {
					background-color: #fff;
					padding: 20rpx 24rpx;

					.list-item {
						padding: 30rpx 0;

						+.list-item {
							border-top: 1rpx solid #F3F3F3;
						}

						.list-left {
							>image {
								width: 39rpx;
								height: 39rpx;
								margin-top: 20rpx;
							}

							.list-msg {
								width: 438rpx;
								line-height: 46rpx;
								margin-left: 38rpx;

								>view:nth-child(1) {
									font-size: 28rpx;
									color: #333333;
								}

								>view:nth-child(2) {
									font-size: 26rpx;
									color: #999999;
								}

								>view:nth-child(3) {
									font-size: 24rpx;
									color: #999999;
								}
							}
						}

						.list-num {
							text-align: right;
							line-height: 46rpx;

							>view:nth-child(1) {
								font-size: 28rpx;
								font-weight: 800;
								color: #FF0000;

								&.black {
									color: #333;
								}

								>text {
									margin-left: 8rpx;
								}
							}

							>view:nth-child(2) {
								font-size: 24rpx;
								font-weight: 500;
								color: #999999;
							}


						}
					}
				}
			}
		}
	}
</style>