<!-- 冲突商品 -->
<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="title"></u-navbar>
		<view class="content">
			<!-- <view class="search">
				<u-search animation placeholder="商品搜索" v-model="keyword" :showAction="false" search="search"></u-search>
			</view> -->
			<view class="list" v-if="commList&&commList.length>0">
				<view class="thumb-box" v-for="(item, index) in commList" :key="item.id"
					@click.stop="commItemSelect(item)">
					<view>
						<image class="select-img"
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
							mode="widthFix" v-show="item.checked"></image>
						<image class="select-img"
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
							mode="widthFix" v-show="!item.checked"></image>
					</view>
					<view class="check-content">
						<view class="comm-img" @click.stop="$xy.previewImg(item.cover)">
							<u--image width="130rpx" radius="6" height="130rpx" :src="item.cover" mode="aspectFit"
								:lazy-load="true"></u--image>
						</view>
						<view class="comm-main">
							<view>
								{{item.name}}
							</view>
							<view>
								条形码：{{item.barcode||'-'}}
							</view>
							<view>
								商品ID：{{item.goodsId}}
							</view>
							<view>
								算法SKUID：{{item.skuId||'-'}}
							</view>

							<view class="c-pri" v-if="item.goodsId!=1&&item.goodsId!=2">
								重量：<text v-if="item.weight">{{(Number(item.weight)/1000)}}g</text><text style="color:#999;" v-else>-</text>
								<view @click.stop="editWeight(item)" v-if="checkPermi(['dev:com:editWeight'])">修改</view>
							</view>

							<!-- <view class="c-cx">
								促销活动：
							</view> -->
							<view class="c-pri">
								价格：<text>￥{{$xy.delMoney(item.price)}}</text>
								<view @click.stop="edit(item)" v-if="checkPermi(['dev:com:editPrice'])">修改</view>
							</view>
						</view>

						<view class="status" v-if="item.name">
							<view class="s-name">
								在售/补后
							</view>
							<view class="s-num">
								{{item.stock}}/{{item.fillCount}}
							</view>
						</view>

						<view class="status" v-else>
							补货时请移除
						</view>

						<view class="re-buy">
							<xbutton size='medium' round='25rpx' bgColor="#F4F8FF" color="#2C6FF3"
								@tap.stop="reBuy(item.goodsId)">复购率</xbutton>
						</view>

						<!-- <view class="sale" v-if="item.name">
							<view>
								今日销售额
							</view>
							<view>
								￥<text>{{item.todaySalesMoney||0}}</text>
							</view>
						</view>

						<view class="sale" v-else>
							已删除商品
						</view> -->
					</view>
				</view>

				<u-loadmore :status="status" v-if="commList.length>=1" />
			</view>

			<view class="empty" v-else>
				<u-empty></u-empty>
			</view>
		</view>

		<view class="refresh" @click="search">
			刷新
		</view>

		<view class="refresh" style="bottom:330rpx;" v-if="checkPermi(['dev:com:conflict'])"
			@click="$tab.navigateTo(`/pages/commodity/conflictList?id=${id}&deviceName=${deviceName}`)">
			冲突
		</view>

		<view class="btn safe-bottom">
			<xbutton width="340rpx" round="88rpx" bgColor="#a3a3a3" size="large" @click="del"
				v-if="checkPermi(['devDelCom'])">删除商品</xbutton>
			<xbutton width="340rpx" round="88rpx" size="large" @click="add" v-if="checkPermi(['devAddCom'])">增加商品
			</xbutton>
		</view>

		<xpopup :show="editPriceShow" @close="editPriceClose" @confirm="submit" :showBtn="true" title="修改价格">
			<view class="pop-content">
				<u--input placeholder="请输入内容" type="digit" border="surround" v-model="price"></u--input>
			</view>
		</xpopup>

		<xpopup :show="editWeightShow" @close="editWeightClose" @confirm="submitWeight" :showBtn="true"
			title="修改重量-单位：g">
			<view class="pop-content">
				<u--input placeholder="请输入重量" type="number" border="surround" v-model="weight"></u--input>
			</view>
		</xpopup>


		<xpopup :show="reBuyShow" @close="reBuyClose" :showBtn="false" mode="center" title="复购率">
			<view class="pop-content">
				<view class="re-buy-list">
					<view class="list-item" v-for="(item,index) in progressData" :key="item.name">
						<view class="name">
							{{item.name}}
						</view>
						<u-line-progress :percentage="item.num" activeColor="#2C6FF3"></u-line-progress>
					</view>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		goodsList
	} from '@/api/device/device.js'
	import {
		delGoods
	} from "@/api/replenishment/replenishment.js"
	import {
		changePrice
	} from '@/api/commodity/goodsMode.js'
	import {
		updateByGoodsId
	} from '@/api/commodity/mercGoods.js'
	import {
		goodsDrawRePayPer
	} from "@/api/commodity/goods.js"

	export default {
		data() {
			return {
				keyword: '',
				commList: [],
				id: null, //设备id
				page: 1,
				size: 10,
				editPriceShow: false,
				editWeightShow: false,
				price: 0,
				weight: null,
				goodsId: null,
				mercGoodId: null,
				deviceName: null,
				title: null,
				progressData: [{
						name: '1次占比',
						num: 0,
					},
					{
						name: '2次占比',
						num: 0,
					},
					{
						name: '3-5次占比',
						num: 0,
					},
					{
						name: '6-10次占比',
						num: 0,
					},
					{
						name: '10次以上占比',
						num: 0,
					}
				],
				reBuyShow: false
			}
		},
		onLoad(o) {
			if (o.id) {
				this.id = o.id;
				this.deviceName = o.deviceName;
				this.title = '商品列表-' + this.deviceName
			}
		},

		onShow() {
			this.reset()
			this.getList()
		},

		methods: {
			search(val) {
				this.reset()
				this.getList()
			},
			// 商品选中状态改变
			commItemSelect(e) {
				if (e.name == '未知商品' || e.name == '非友好购物商品') {
					this.$modal.msg('预设商品不能删除!!!')
					return
				}
				e.checked = !e.checked;
			},

			edit(item) {
				this.goodsId = item.id;
				this.editPriceShow = true;
				this.price = (Number(item.price) / 100).toFixed(2)
			},

			editWeight(item) {
				this.mercGoodId = item.goodsId;
				this.editWeightShow = true;
				if (item.weight) {
					this.weight = (Number(item.weight) / 1000)
				}
			},


			//删除商品
			del() {
				let delComList = [];
				this.commList.forEach(i => {
					if (i.checked) {
						delComList.push(i.goodsId)
					}
				})

				if (delComList.length == 0) {
					this.$modal.msg('请选择需要删除的商品~')
					return
				}
				uni.showModal({
					title: '提示',
					content: '是否确认删除',
					success: res => {
						if (res.confirm) {
							delGoods({
								deviceId: this.id,
								goodsIds: delComList
							}).then(res => {
								this.$modal.oldConfirm('删除成功,请拿走货架上当前商品。').then(resolve => {
									console.log('确认拿走~')
								})
								this.reset()
								this.getList()
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},

			/**
			 * 复购率
			 */
			reBuy(id) {
				goodsDrawRePayPer({
					goodsId: id,
					deviceId: this.id
				}).then(res => {
					if (res.data) {
						let i = 0
						for (let key in res.data) {
							this.progressData[i].num = res.data[key]
							i++
						}
						this.reBuyShow = true
					}
				})
			},

			add() {
				this.$tab.navigateTo(`/pages/equipment/addCom?id=${this.id}`)
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.commList = [];
			},

			getList() {
				goodsList({
					page: {
						current: this.page,
						size: this.size
					},
					deviceId: this.id
				}).then(res => {
					let data = res.data.records;
					for (let i = 0; i < data.length; i++) {
						let item = data[i];
						item.checked = false;
					}
					if (data.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.commList = this.commList.concat(data)
				})
			},

			onReachBottom() {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			editPriceClose() {
				this.editPriceShow = false;
			},
			editWeightClose() {
				this.editWeightShow = false;
			},

			reBuyClose() {
				this.reBuyShow = false;
			},
			submitWeight() {
				if (/^([1-9]\d*(\.\d{1,2})?|([0](\.([0][1-9]|[1-9]\d{0,1}))))$/.test(this.weight)) {
					updateByGoodsId({
						goodsId: this.mercGoodId,
						weight: this.$xy.weight2Mg(this.weight)
					}).then(res => {
						if (res.code == 200) {
							this.$modal.msg('修改成功~')
							this.reset()
							this.getList()
						}
					}).catch(err => {})
					this.editWeightClose()
				} else {
					this.$modal.msg('商品重量必须大于0！')
				}
			},
			submit() {
				if (/^([1-9]\d*(\.\d{1,2})?|([0](\.([0][1-9]|[1-9]\d{0,1}))))$/.test(this.price)) {
					changePrice({
						id: this.goodsId,
						price: this.$xy.delMoneyL(this.price)
					}).then(res => {
						if (res.code == 200) {
							this.$modal.msg('修改成功~')
							this.reset()
							this.getList()
						}
					}).catch(err => {})
					this.editPriceClose()
				} else {
					this.$modal.msg('商品价格必须大于0！')
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {
			padding-bottom: 128rpx;

			.search {
				padding: 24rpx 24rpx;
				background-color: #fff;
			}

			.list {
				width: 100%;
				padding: 12rpx 24rpx;

				.thumb-box {
					margin-bottom: 12rpx;
					border-bottom: 1rpx solid #f4f4f4;
					display: flex;
					flex-flow: row nowrap;
					padding: 12rpx 12rpx;
					align-items: center;
					background-color: #fff;
					border-radius: 12rpx;
				}

				.select-img {
					width: 34rpx;
					height: 34rpx;
				}

				.check-content {
					width: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					padding-left: 12rpx;
					position: relative;

					.comm-img {
						width: 130rpx;
						height: 130rpx;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-around;

						image {
							width: 100%;
						}
					}

					.comm-main {
						box-sizing: border-box;
						padding-left: 18rpx;
						color: #999;

						>view {
							padding: 8rpx 0;
							width: 330rpx;
						}

						>view:nth-child(1) {
							font-size: 28rpx;
							font-weight: bold;
							color: #333;
							padding: 12rpx 0 8rpx 0;
						}

						>view:nth-child(2) {
							width: 400rpx;
							font-size: 26rpx;
						}

						>view:nth-child(3) {
							width: 450rpx;
							font-size: 26rpx;
						}

						>view:nth-child(4) {
							width: 450rpx;
							font-size: 26rpx;
							padding: 0;
							line-height: 40rpx;
						}

						>.c-cx {
							font-size: 26rpx;
						}

						>.c-pri {
							font-size: 26rpx;

							text {
								font-weight: bold;
								color: red;
								font-size: 30rpx;
							}

							>view {
								display: inline-block;
								font-size: 26rpx;
								color: #2C6FF3;
								text-decoration: underline;
								margin-left: 12rpx;
							}
						}
					}

					.status {
						width: 120rpx;
						height: 120rpx;
						box-sizing: border-box;
						border-radius: 120rpx;
						// border: 6rpx solid #2C6FF3;
						text-align: right;
						display: flex;
						flex-flow: column;
						justify-content: space-around;
						align-items: center;
						position: absolute;
						right: 0;
						top: -8rpx;

						.s-name {
							font-size: 24rpx;
							padding-top: 16rpx;
							font-weight: bold;
						}

						.s-num {
							font-size: 28rpx;
							padding-bottom: 20rpx;
						}
					}

					.sale {
						position: absolute;
						right: 12rpx;
						bottom: 12rpx;
						text-align: center;
						color: #999;

						>view:nth-child(1) {
							font-size: 24rpx;
						}

						>view:nth-child(2) {
							font-size: 32rpx;
							color: #2C6FF3;
							font-weight: bold;
						}
					}

					.re-buy {
						position: absolute;
						right: 12rpx;
						bottom: 0rpx;
					}
				}
			}
		}

		.empty {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.refresh {
			width: 80rpx;
			height: 80rpx;
			border-radius: 80rpx;
			text-align: center;
			line-height: 80rpx;
			position: fixed;
			right: 24rpx;
			bottom: 230rpx;
			background-color: #999;
			color: #fff;
			opacity: .8;
		}

		.btn {
			width: 100%;
			position: fixed;
			bottom: 24rpx;
			left: 0;
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-between;
			padding: 0 24rpx;

			.cu-btn {
				background-color: #2C6FF3;
				color: #fff;
				width: 48%;
			}
		}

		.pop-content {
			padding: 24rpx;

			.re-buy-list {
				padding: 10rpx 20rpx 30rpx 20rpx;
				width: 600rpx;

				.list-item {
					position: relative;
					padding-left: 170rpx;
					padding: 16rpx 0 16rpx 180rpx;

					.name {
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}
	}
</style>