<template>
	<view class="container">
		<!-- 页面显示 -->
		<swiper class="swiper pages" circular @animationfinish="swiperChange" :current="current">
			<swiper-item class="swiper-item" v-if="menu.some(i=>i.name=='首页')">
				<Home ref="home" @typeClick="typeClick" />
			</swiper-item>
			<swiper-item class="swiper-item" v-if="menu.some(i=>i.name=='设备')">
				<Equipment ref="equipment" />
			</swiper-item>
			<swiper-item class="swiper-item" v-if="menu.some(i=>i.name=='商品')">
				<Commodity ref="commodity" />
			</swiper-item>
			<swiper-item class="swiper-item" v-if="menu.some(i=>i.name=='我的')">
				<Account ref="account" />
			</swiper-item>
		</swiper>

		<!-- 子商户返回按钮 -->
		<view class="back-main-merc" v-if="type==1" @click="backMainMerc">
			返回主商户
		</view>

		<!-- 绑定公众号 -->
		<xpopup :show="subShow" @close="subClose" :showBtn="false" mode="center">
			<view class="sub-popup-content flex flex-direction align-center">
				<view class="sub-tips">
					您未绑定微信公众号，设备的重要信息无法推送至您的微信，为方便您接收设备信息，请先[<text style="color: #2C6FF3;">关注</text>]后点击[<text
						style="color: #2C6FF3;">绑定</text>]公众号。
				</view>
				<view class="qr-code">
					<image :src="qrCode" mode="widthFix" :show-menu-by-longpress="true"></image>
				</view>

				<view class="qr-down" @click="downLoadQr">
					长按二维码前往公众号关注
				</view>

				<view class="sub-view">
					<official-account></official-account>
				</view>
				<view class="sub-btn" @click="subSubmit">
					绑定
				</view>
			</view>
		</xpopup>

		<!-- 公告 -->
		<!-- <xpopup :show="tipsShow" @close="tipsShow=false" round="12" :showBtn="false" mode="center">
			<view class="tips-pop-content">
				<view class="tips-title" @click="tipsShow=false">
					{{tipsDetail.noticeTitle}}
				</view>
				<scroll-view scroll-y style="height:600rpx;">
					<rich-text :nodes="tipsDetail.noticeContent"></rich-text>
				</scroll-view>
				<view class="tips-btn" @click="tipsClose">
					<xbutton>{{tipsIndex==tipsList.length-1?'关闭':'下一条'}}</xbutton>
				</view>
			</view>
		</xpopup> -->

		<!-- 底部菜单栏 -->
		<u-tabbar v-if="menu&&menu.length>0" :value="current" @change="tabChange" :fixed="true" :placeholder="false"
			:safeAreaInsetBottom="true">
			<u-tabbar-item :text="item.name" v-for="(item,index) in menu" :key="item.id" @click="tabClick(item)">
				<image class="u-page__item__slot-icon" slot="inactive-icon" :src="tabIcon[item.name][0]"></image>
				<image class="u-page__item__slot-icon" slot="active-icon" :src="tabIcon[item.name][1]"></image>
			</u-tabbar-item>
		</u-tabbar>

		<!-- 客诉 -->
		<xnoticeDialog @close="complainClose" ref="complain" :tipsList="complainList" zIndex="100000" height="400" />
		<!-- 消息通知 -->
		<xnoticeDialog @close="msgClose" ref="msg" :tipsList="msgList" zIndex="100015"/>
		<!-- 系统公告 -->
		<xnoticeDialog @close="tipsClose" ref="tips" :tipsList="tipsList" zIndex="100075" />
		<!-- 算法欠费通知 -->
		<xnoticeDialog @close="algoClose" ref="algo" :tipsList="algoList" zIndex="100100" />
		<!-- 每日一图 -->
		<DayImgDialog @close="dayImgClose" ref="dayImg" :tipsList="dayImgList" zIndex="100200" @conClick="dayImgDetail"
			@refresh="getDayImg" />
	</view>
</template>

<script>
	import Home from './components/home.vue'
	import Equipment from './components/equipment.vue'
	import Commodity from './components/commodity.vue'
	import Account from './components/account.vue'
	import DayImgDialog from './components/dayImgDialog.vue'
	import {
		subAuthBack
	} from '@/api/merchant.js'
	import {
		isBind,
		bindWxMp
	} from "@/api/system/employee.js"
	import {
		notifyList,
		myMsgPopUp,
		readById,
		algoLow,
		arrearageCount,
		dayImg
	} from "@/api/message.js"

	import {
		setToken,
		setMercId
	} from '@/utils/auth'
	import {
		imgDownLoad
	} from '@/utils/download.js'
	import {
		complaintCount
	} from "@/api/order/order.js"

	let mqtt = require('../../static/js/mqtt.min.js')

	export default {
		components: {
			Home,
			Equipment,
			Commodity,
			Account,
			DayImgDialog
		},

		data() {
			return {
				current: 0,
				tabIcon: {
					'首页': [require('../../static/images/tabbar/home.png'), require(
						'../../static/images/tabbar/home_.png')],
					'设备': [require('../../static/images/tabbar/equipment.png'), require(
						'../../static/images/tabbar/equipment_.png')],
					'商品': [require('../../static/images/tabbar/commodity.png'), require(
						'../../static/images/tabbar/commodity_.png')],
					'我的': [require('../../static/images/tabbar/mine.png'), require(
						'../../static/images/tabbar/mine_.png')],
				},
				client: null,
				tabName: null,
				menu: [],
				type: null,
				wxMpIsBind: true,
				subShow: false,
				qrCode: 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/wxmp.jpg',
				tipsList: [],
				msgList: [],
				algoList: [],
				complainList: [],
				dayImgList: [],
			}
		},

		computed: {
			tipsDetail() {
				let msg = [];
				if (this.tipsList.length > 0) {
					msg = this.tipsList[this.tipsIndex]
				}
				return msg
			}
		},

		onShow() {
			// 版本自动更新代码
			const updateManager = wx.getUpdateManager()
			updateManager.onUpdateReady(function() {
				wx.showModal({
					title: '更新检测',
					content: '检测到新版本，是否重启小程序？',
					success: function(res) {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate()
						}
					}
				})
			})
			updateManager.onUpdateFailed(function() {
				// 新的版本下载失败
				wx.showModal({
					title: '已有新版本咯',
					content: '请您删除当前小程序，重新打开呦~',
					showCancel: false
				})
			})
		},

		onLoad(o) {
			if (this.$store.state.permission.permissions_menu && this.$store.state.permission.permissions_menu.length >
				0) {
				this.menu = JSON.parse(this.$store.state.permission.permissions_menu)
				console.log(this.menu)
				this.tabName = this.menu[0].name
				if (o.tabName) { //公库添加商品到私库完毕，显示商品界面
					this.tabName = o.tabName;
					if (this.menu && this.menu.length > 0) {
						this.menu.forEach((item, index) => {
							if (item.name == o.tabName) {
								this.current = index
							}
						})
					}
				}
			}

			if (o.type) {
				this.type = o.type
			}

			if (getApp().globalData.defaultPwd) { //未修改过密码
				this.$modal.oldConfirm('您的密码为初始密码，请前往修改密码！！！').then(res => {
					getApp().globalData.defaultPwd = false
					this.$tab.navigateTo(`/pages/globalPages/setting?type=0`)
				}).catch(err => {
					getApp().globalData.defaultPwd = false
					//检测是否绑定公众号
					// this.isBindWxMp()
				})
			} else {
				//检测是否绑定公众号
				// this.isBindWxMp()
			}
			// this.mqttConnect()

			//获取系统通知
			this.sysNotify()

			//获取消息通知
			this.getMsg()

			//获取算法欠费通知
			this.getAlgoLow()

			//客诉处理弹框
			this.getComplaintsNum()

			//每日一图
			this.getDayImg()

			//等待组件挂在完毕，加载组件方法
			this.$nextTick(() => {
				this.refreshData()
			})

			uni.$once('refreshData', res => {
				this.refreshData()
			})
		},

		methods: {
			/**
			 * 页面滑动
			 */
			swiperChange(e) {
				console.log(e)
				this.current = e.detail.current
				this.tabName = this.menu[this.current].name
				this.refreshData()
			},
			/**
			 * 刷新页面数据
			 */
			refreshData() {
				console.log(this.tabName)
				if (this.tabName == '首页') {
					this.$refs.home.refreshData()
				}
				if (this.tabName == '设备') {
					this.$refs.equipment.refreshData()
				}
				if (this.tabName == '商品') {
					this.$refs.commodity.refreshData()
				}
				if (this.tabName == '我的') {
					this.$refs.account.refreshData()
				}
			},

			typeClick(e) {
				this.current = e
				this.tabName = this.menu[e].name
				this.refreshData()
			},

			isBindWxMp() {
				isBind({}).then(res => {
					this.wxMpIsBind = res.data.wxMp;
					this.subShow = !res.data.wxMp;
				})
			},

			subClose() {
				this.subShow = false
			},

			subSubmit() {
				this.$tab.navigateTo(`/pages/webView`)
			},

			mqttConnect() {
				let _this = this;

				//测试用，生产通过接口（/sys/user-info/webUserMqtt）获取options
				const options = {
					clean: true, // true: 清除会话, false: 保留会话
					connectTimeout: 4000, // 超时时间
					// 认证信息
					clientId: 'web-user-wxc-1',
					username: 'webuser',
					password: 'xy20220101',
				}
				// 连接字符串, 通过协议指定使用的连接方式
				// ws 未加密 WebSocket 连接
				// wss 加密 WebSocket 连接
				// mqtt 未加密 TCP 连接
				// mqtts 加密 TCP 连接
				// wxs 微信小程序连接
				// alis 支付宝小程序连接
				const connectUrl = 'wxs://mqtt.mxrvending.com:8084/mqtt'
				this.client = mqtt.connect(connectUrl, options)
				this.client.on('connect', function() {
					//订阅消息
					_this.client.subscribe('web-user-wxc-1', function(err) {
						if (!err) {
							console.log('订阅成功', err)
						} else {
							console.log('订阅失败', err)
						}
					})
				}).on('reconnect', function() {
					console.log('正在重连')
				}).on('error', function() {
					console.log('错误')
				}).on('end', function() {
					console.log('连接结束')
				}).on('message', function(topic, message) {
					console.log('接收到的消息==========>>>>>>>>', message.toString())
				})
			},

			tabChange(e) {
				this.current = e
			},

			tabClick(e) {
				this.tabName = e.name
			},

			backMainMerc() {
				subAuthBack({
					parentKey: uni.getStorageSync('parentKey')
				}).then(res => {
					setToken(res.data.satoken)
					setMercId(res.data.mercId)
					uni.setStorageSync('parentKey', '')
					this.$store.commit('SET_TOKEN', res.data.satoken)
					this.$store.commit('SET_NAME', res.data.mercName)
					this.$tab.reLaunch('/pages/globalPages/home')
				})
			},

			downLoadQr() {
				imgDownLoad(this.qrCode).then(res => {
					this.$modal.showToast('保存成功~')
				})
			},

			sysNotify() {
				notifyList({
					sysCode: 'xy_merc_mini'
				}).then(res => {
					let data = res.data
					if (data && data.length > 0) {
						this.$refs.tips.tipsShow = true
						this.tipsList = data
					}
				})
			},

			getMsg() {
				myMsgPopUp({}).then(res => {
					let data = res.data
					if (data && data.length > 0) {
						let tempData = data.map(i => {
							i.noticeTitle = i.title
							i.noticeContent = i.content
							return i
						})
						this.$refs.msg.tipsShow = true
						this.msgList = tempData
					}
				})
			},

			/**
			 * 算法欠费通知
			 */
			getAlgoLow() {
				arrearageCount({}).then(res => {
					let data = res.data;
					let tempData = this.feeDel(data)
					if (tempData) {
						this.$refs.algo.tipsShow = true
						this.algoList = tempData
					}
				})
				// algoLow({}).then(res => {
				// 	let data = res.data
				// 	if (data.balance < 0) {
				// 		let tempData = [{
				// 			noticeTitle: '算法欠费通知',
				// 			noticeContent: `<div style="line-height:30px;"><p style="text-indent: 20px;">您的<span style="color:red;">算法费</span>余额已不足，欠费设备将被<span style="color:red;">冻结</span>，请及时充值：</p>
				// 							<p style="text-indent: 20px;">充值方式：<span style="color:#2C6FF3;">我的→算法充值。</span></p>
				// 							<p style="text-indent: 20px;">请根据您设备的交易情况，预充足够的算法费，欠费后设备会自动<span style="color:red;">停机</span>。</p></div>`
				// 		}]
				// 		this.$refs.algo.tipsShow = true
				// 		this.algoList = tempData
				// 	}
				// })
			},

			/**
			 * @param {Object} data
			 * 欠费通知处理
			 */
			feeDel(data) {
				let tempData = []
				if (data && data.mercBalance < 0 && !data.isAllDeviceAlgorithm) {
					tempData = [{
						noticeTitle: '算法欠费通知',
						noticeContent: `<div style="line-height:30px;"><p style="text-indent: 20px;">您的<span style="color:red;">算法费</span>余额已不足，欠费设备将被<span style="color:red;">冻结</span>，请及时充值：</p>
												<p style="text-indent: 20px;">充值方式：<span style="color:#2C6FF3;">我的→算法充值。</span></p>
												<p style="text-indent: 20px;">请根据您设备的交易情况，预充足够的算法费，欠费后设备会自动<span style="color:red;">停机</span>。</p></div>`
					}]
					return tempData
				}
				if (data && data.mercBalance < data.mercBalanceThre && !data.isAllDeviceAlgorithm) {
					tempData = [{
						noticeTitle: '算法即将欠费通知',
						noticeContent: `<div style="line-height:30px;"><p style="text-indent: 20px;">您的<span style="color:red;">算法费</span>即将欠费，欠费设备将被<span style="color:red;">冻结</span>，请及时充值：</p>
												<p style="text-indent: 20px;">充值方式：<span style="color:#2C6FF3;">我的→算法充值。</span></p>
												<p style="text-indent: 20px;">请根据您设备的交易情况，预充足够的算法费，欠费后设备会自动<span style="color:red;">停机</span>。</p></div>`
					}]
					return tempData
				}
				if (data && data.deviceChargingTimeoutCout > 0) {
					tempData = [{
						noticeTitle: '设备管理费欠费通知',
						noticeContent: `<div style="line-height:30px;"><p style="text-indent: 20px;">您的<span style="color:red;">设备管理费</span>已欠费,请及时充值：</p>
												<p style="text-indent: 20px;">充值方式：<span style="color:#2C6FF3;">我的→设备管理费。</span></p></div>`
					}]
					return tempData
				}
				if (data && data.deviceChargingBeTimeoutCout > 0) {
					tempData = [{
						noticeTitle: '设备管理费即将欠费通知',
						noticeContent: `<div style="line-height:30px;"><p style="text-indent: 20px;">您的<span style="color:red;">设备管理费</span>即将欠费,请及时充值：</p>
												<p style="text-indent: 20px;">充值方式：<span style="color:#2C6FF3;">我的→设备管理费。</span></p></div>`
					}]
					return tempData
				}
				if (data && data.deviceSimTimeoutCout > 0) {
					tempData = [{
						noticeTitle: '设备流量卡费欠费通知',
						noticeContent: `<div style="line-height:30px;"><p style="text-indent: 20px;">您的<span style="color:red;">流量卡费</span>已欠费,请及时充值：</p>
												<p style="text-indent: 20px;">充值方式：<span style="color:#2C6FF3;">我的→流量卡续费。</span></p></div>`
					}]
					return tempData
				}
				if (data && data.deviceSimBeTimeoutCout > 0) {
					tempData = [{
						noticeTitle: '设备流量卡即将欠费通知',
						noticeContent: `<div style="line-height:30px;"><p style="text-indent: 20px;">您的<span style="color:red;">流量卡费</span>即将欠费,请及时充值：</p>
												<p style="text-indent: 20px;">充值方式：<span style="color:#2C6FF3;">我的→流量卡续费。</span></p></div>`
					}]
					return tempData
				}
				return false
			},

			/**
			 * 每日一图
			 */
			getDayImg() {
				let date = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd')
				// if (uni.getStorageSync('day') == date) return
				uni.setStorageSync('day', date)
				dayImg({}).then(res => {
					let data = res.data
					if (data.teachImgs) {
						let tempData = [{
							linkType: data.linkType,
							linkUrl: data.linkUrl,
							noticeTitle: '每日一图',
							noticeContent: `<img src=${data.teachImgs} style="width:100%;height:100%;"></img>`
						}]
						this.$refs.dayImg.tipsShow = true
						this.dayImgList = tempData
					}
				})
			},

			dayImgDetail() {
				// 链接类型（1小程序 2富文本 3外链）字典 merc_day_img_link_type
				let url = this.dayImgList[0].linkUrl
				switch (this.dayImgList[0].linkType) {
					case 1:
						this.$tab.navigateTo(url)
						break;
					case 2:
						uni.setStorageSync('nodes', url)
						this.$tab.navigateTo(`/pages/globalPages/richView`)
						break;
					case 3:
						this.$tab.navigateTo(`/pages/globalPages/webView?src=${url}`)
						break;
					default:
						break;
				}
			},

			tipsClose() {},

			algoClose() {},

			dayImgClose() {},

			/**
			 * 消息通知
			 * @param {Object} index
			 */
			msgClose(index) {
				let idList = []
				for (let i = 0; i < this.msgList.length; i++) {
					let item = this.msgList[i];
					idList.push(item.id)
					if (index == i) {
						readById({
							ids: idList
						})
						break
					}
				}
			},

			//客诉处理弹框
			getComplaintsNum() {
				complaintCount({}).then(res => {
					if (res.data.untreatedCount > 0) {
						this.$refs.complain.tipsShow = true
						this.complainList = [{
							noticeTitle: '客诉处理通知',
							noticeContent: `<p>您有<span style="color:red;margin:0 12px;">${res.data.untreatedCount}</span>条<span style="color:#2c6ff3;margin:0 12px;">客诉</span>需要处理，请立即前往处理。</p>`
						}]
					}
				})
			},

			complainClose() {
				this.goComplain()
			},

			goComplain() {
				// wx.navigateToMiniProgram({
				// 	appId: 'wx49625208931d29ec',
				// 	path: 'subpackages/order_complaint/index/index',
				// 	// extraData: {
				// 	// 	mercId: data.mercId,
				// 	// 	mercName: data.mercName,
				// 	// 	token: data.mercSatoken,
				// 	// 	type: 1
				// 	// },
				// 	// envVersion:'trial',//跳转体验版
				// 	success(res) {
				// 		console.log(res);
				// 	}
				// })
				this.$tab.navigateTo('/pages/complain/list')
			},
		},

		onUnload() {
			uni.$off('refreshData')
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
		height: 100vh;

		.swiper {
			height: 100%;
			padding-bottom: calc(100rpx + env(safe-area-inset-bottom) / 2);

			.swiper-item {
				height: 100%;
			}
		}

		.u-page__item__slot-icon {
			width: 44rpx;
			height: 44rpx;
		}

		.back-main-merc {
			width: 120rpx;
			height: 120rpx;
			border-radius: 120rpx;
			text-align: center;
			line-height: 40rpx;
			position: fixed;
			padding: 20rpx 12rpx;
			right: 24rpx;
			bottom: 230rpx;
			background-color: #999;
			color: #fff;
			opacity: .8;
		}

		.sub-popup-content {
			width: 702rpx;
			padding: 40rpx;

			.sub-tips {
				line-height: 44rpx;
			}

			.qr-code {
				width: 400rpx;
				height: 400rpx;
			}

			.qr-down {
				color: #2C6FF3;
				text-decoration: underline;
				font-size: 26rpx;
				text-align: center;
				margin-bottom: 20rpx;
			}

			.sub-view {
				width: 622rpx;
				margin-bottom: 24rpx;

				official-account {
					width: 622rpx;
				}
			}

			.sub-btn {
				width: 622rpx;
				border: 1rpx solid #eee;
				border-radius: 8rpx;
				text-align: center;
				line-height: 70rpx;
			}
		}

		.tips-pop-content {
			width: 602rpx;
			padding: 40rpx;
			border-radius: 24rpx;
			min-height: 600rpx;
			position: relative;

			.tips-title {
				font-size: 34rpx;
				text-align: center;
				padding-bottom: 30rpx;

			}

			.tips-btn {
				width: 200rpx;
				position: absolute;
				bottom: 24rpx;
				left: 50%;
				transform: translateX(-50%);
			}
		}
	}
</style>