<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="交易日志"></u-navbar>
		<scroll-view   scroll-y="true" scroll-with-animation="true"
			lower-threshold="100" @scrolltolower="loadMore">
			<block v-if="logList&&logList.length>0">
				<view class="" v-for="(item,index) in logList" :key="item.id">
					<view class="flex">
						<view class="" style="min-width: 200rpx;color: gray;">{{ item.activityTime.substring(5)}}</view>
						<view class="">{{item.msg}}</view>			
					</view>
				
				</view>
			</block>
			
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		byId,
		orderLogs,
		refundDetail
	} from "@/api/order/order.js"
	export default {
		data() {
			return {
				activityId: null,
				logList:[]
			}
		},
		onLoad(o) {
			this.activityId = o.id;
			this.queryLogs()
		},
		methods: {
			queryLogs(){
				orderLogs({
					activityId:this.activityId
				}).then(res => {
					this.logList = res.data;
				})
			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		padding: 24rpx;
		line-height: 50rpx;
		.text {
			color: #2C6FF3;
		}
		
		.empty {
			padding-top: 50%;
		}
	}
</style>

