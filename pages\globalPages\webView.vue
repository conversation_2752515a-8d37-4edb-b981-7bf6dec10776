<template>
	<view id="wrap">
		<web-view :src="src"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				src: ''
			}
		},
		onLoad(o) {
			this.src = `${decodeURIComponent(o.src)}?id=${o.id}`
		},
	}
</script>

<style scoped lang="scss">
	web-view {
		width: 100%;
		height: 100vh;
		padding: 0;
		margin: 0;
		overflow: hidden;
	}

	#wrap {
		width: 100%;
		height: 100%;
		overflow-x: hidden;
	}

	web-view {
		border: none;
	}
</style>