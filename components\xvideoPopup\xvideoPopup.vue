<template>
	<u-popup :show="show" @close="close" :overlayStyle="{zIndex:zIndex-5}" :zIndex="zIndex" @open="open" :mode="mode"
		:safeAreaInsetBottom="false">
		<view class="pop-content">
			<view class="video-wrap" v-if="src&&srcNormal">
				<xvideo :poster="poster" :showMuteBtn="showMuteBtn" :isExitFullScreen="isExitFullScreen" :src="src"
					@timeupdate="timeupdate" @playError="playError" />
			</view>
			<view class="no-video" v-else>
				<view>
					<view class="no-video-getting flex flex-direction" v-if="getting=='getting'">
						<view class="loading">
							<u-loading-icon></u-loading-icon>
						</view>
						<view>捞取中</view>
					</view>
					<view class="no-video-get" v-else-if="getting=='get'">
						暂无视频,点击<text @click="getVideo">捞取视频</text>
					</view>
					<view class="no-video-get" v-else-if="getting=='fail'">
						捞取失败,点击<text @click="getVideo">捞取视频</text>重试
					</view>
					<view class="no-video-get" v-else-if="getting=='lose'">
						捞取失败,视频超期已被清除
					</view>
				</view>
				<!-- 	<view class="no-video-get" v-else>
					暂无视频
				</view> -->
			</view>
			<view class="video-btn flex justify-end">
				<view class="marright">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx'
						:bgColor='[videoType==0?"#55aaff":"#2C6FF3"]' @tap="play(0)">主视频
					</xbutton>
				</view>
				<view class="marright">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx'
						:bgColor='[videoType==1?"#55aaff":"#2C6FF3"]' @tap="play(1)">副视频
					</xbutton>
				</view>
				<view class="marright" v-if="urls&&urls.length>2">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx'
						:bgColor='[videoType==1?"#55aaff":"#2C6FF3"]' @tap="play(2)">超时视频
					</xbutton>
				</view>
				<view class="marright">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' bgColor='#2C6FF3' @tap="shareVideo">转发视频
					</xbutton>
				</view>
			</view>
		</view>
	</u-popup>
</template>

<!-- 三个按钮，第一二个主辅视频，第三个超时视频 -->
<!-- 支付宝视频支持捞取 -->
<!-- 其他视频捞取逻辑在使用组件，自行处理 -->

<script>
	import {
		alipayGetVideo,
		alipayQueryVideo
	} from '@/api/order/order.js'
	import {
		doesObjectExist
	} from '@/api/public.js'
	import {
		sendCommand
	} from '@/api/device/device.js'
	export default {
		data() {
			return {
				videoType: 0,
				getting: 'get',
				newUrl: '',
				srcNormal: true
			}
		},
		props: {
			mode: {
				type: String,
				require: false,
				default: 'center'
			},
			show: {
				type: Boolean,
				require: true,
				default: false
			},
			zIndex: {
				type: Number,
				require: false,
				default: 10075
			},
			// 视频封面
			poster: {
				type: String,
				default: ''
			},
			// 视频地址
			urls: {
				type: Array,
				default: () => {
					return []
				}
			},
			// 是否显示静音按钮
			showMuteBtn: {
				type: Boolean,
				default: true
			},
			// 播放完毕是否退出全屏
			isExitFullScreen: {
				type: Boolean,
				default: true
			},

			//是否支付宝的设备，捞取方式不一样
			isAli: {
				type: Boolean,
				require: false,
				default: false
			},

			//订单id
			orderId: {
				type: [Number, String],
				require: false,
				default: ''
			},

			deviceId: {
				type: [Number, String],
				require: false,
				default: ''
			},
			
			createTime: {
				type: [Number, String],
				require: false,
				default: ''
			},
			
			activityId:{
				type: [Number, String],
				require: false,
				default: ''
			},
		},

		computed: {
			src() {
				let url = ''
				if (this.urls[this.videoType]) { //优先展示已有视频，否则展示捞取视频
					url = this.urls[this.videoType]
				} else {
					url = this.newUrl
				}
				return url
			}
		},

		watch: {
			src:{
				handler(newVal, oldVal) {
					if (newVal) {
						if (this.isAli) {
							this.srcNormal = true
						} else { //不是ali的机子，校验视频是否已经存在
							doesObjectExist({
								url: newVal
							}).then(res => {
								this.srcNormal = res.data
							})
						}
					}
				},
				immediate: true
			}
		},

		methods: {
			timeupdate(e) {
				this.$emit('timeupdate', e)
			},
			close() {
				this.$emit('close', false)
			},
			open() {
				this.$emit('open', true)
			},
			play(e) {
				this.videoType = e
			},

			// 分享
			shareVideo() {
				if (this.src) {
					console.log(this.src)
					this.$xy.shareVideo(this.src)
				} else {
					this.$modal.showToast('当前状态无法转发！')
				}
			},

			/**
			 * 捞取视频
			 */
			getVideo() {
				if (this.isAli) { //支付宝的设备
					this.getAliVideo()
				} else { //非支付宝的设备
					if (this.videoType == 0 || this.videoType == 1) { //主辅视频捞取
						this.getMainOrMinorVideo()
					}else{ //超时视频捞取
						this.getOverTimeVideo()
					}

				}
			},

			/**
			 * 捞取支付宝视频
			 */
			getAliVideo() {
				alipayGetVideo({
					type: this.videoType == 0 ? 'lite_main_video' : 'lite_minor_video',
					payQueryOrderId: this.orderId
				}).then(res => {
					this.videoStatus()
				})
			},

			/**
			 * 捞取视频结果
			 */
			videoStatus() {
				this.getting = 'getting'
				alipayQueryVideo({
					type: this.videoType == 0 ? 'lite_main_video' : 'lite_minor_video',
					payQueryOrderId: this.orderId
				}).then(res => {
					let data = res.data;
					if (data.status == 'SUCCESS') { //成功
						this.getting = 'get'
						this.newUrl = data.videoInfos[0] ? data.videoInfos[0].segUrl : data.videoInfos.segUrl
					} else if (data.status == 'QUERYING') { //捞取中
						setTimeout(() => {
							this.videoStatus()
						}, 2000)
					} else if (data.retry == false && data.status == 'FAILED' && data.resultMsg ==
						'FilesNotFound') { //捞取失败,视频超期被删除
						this.getting = 'lose'
					} else {
						this.getting = 'fail'
					}
				})
			},

			/**
			 * 捞取支付宝视频外的主辅视频
			 */
			async getMainOrMinorVideo() {
				var templet = {};
				await this.getDict('mqtt_cmd_templet_task', 'video').then(res => {
					console.log(res);
					templet = JSON.parse(res[0].value);
					templet.data.task = "uploadFile";
					templet.data.activityId = this.activityId;
					templet.data.videoIndex = this.videoType;
				});
				await sendCommand([{
					deviceId: this.deviceId,
					templet: templet
				}]).then(res => {
					this.$modal.showToast('正在捞取机器视频，请稍后再重新打开查看~')
					setTimeout(()=>{
						this.close()
					},500)
				});
			},

			async getOverTimeVideo() {
				var templet = {};
				var idx = this.url.lastIndexOf("/") + 1;
				var url = this.url.replace("sjgFile", "uploadFiles");
				var idx = url.lastIndexOf("/") + 1;
				var name = this.createTime.substring(0, 10) + "_" + url.substring(idx).replace("T_c00", "T_c01");
				
				await this.getDict('mqtt_cmd_templet_task', 'file').then(res => {
					console.log(res);
					templet = JSON.parse(res[0].value);
					templet.data.task = "upload";
					templet.data.file = "/storage/emulated/0/aiShj/videos/old_backs/" + name;
				});
				
				await sendCommand([{
					deviceId: this.deviceId,
					templet: templet
				}]).then(res => {
					this.$modal.showToast('正在捞取机器视频，请稍后再重新打开查看~')
				});
			},

			playError(e) {
				this.$emit('playError', this.videoType)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.pop-content {
		box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);
		width: 702rpx;

		.video-wrap {
			width: 100%;
			// height: 396rpx;
		}

		.no-video {
			width: 100%;
			height: 396rpx;
			background-color: #bebebe;
			font-size: 30rpx;
			text-align: center;

			.no-video-getting {
				.loading {
					padding: 140rpx 0 20rpx;
				}
			}

			.no-video-get {
				line-height: 396rpx;

				text {
					color: #2C6FF3;
					text-decoration: underline;
				}
			}
		}

		.video-btn {
			padding: 12rpx 12rpx 24rpx;
		}

		.marright {
			margin-right: 20rpx;
		}
	}
</style>