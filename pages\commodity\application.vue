<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="modeName"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search :animation="true" searchIcon="search" placeholder="输入设备名称" v-model="keyword"
					:showAction="false" @search="search"></u-search>
			</view>
			<view class="list" v-if="deviceList&&deviceList.length>0">
				<view class="thumb-box" v-for="(item, index) in deviceList" :key="item.deviceId"
					@click.stop="commItemSelect(item)">
					<view>
						<image class="select-img"
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/selected.png"
							mode="widthFix" v-show="item.checked"></image>
						<image class="select-img"
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/select.png"
							mode="widthFix" v-show="!item.checked"></image>
					</view>
					<view class="check-content">
						<!-- <view class="comm-img">
							<u--image width="130rpx" height="130rpx" :src="item.cover" mode="widthFix"
								:lazy-load="true"></u--image>
						</view> -->
						<view class="comm-main">
							<view>
								{{item.deviceName}}
							</view>
							<view>
								状态：{{item.deviceStateLName}}
							</view>
							<view>
								状态：{{item.deviceStateLName}}
							</view>
						</view>
					</view>
				</view>

				<u-loadmore :status="status" v-if="deviceList.length>=1" />
			</view>

			<view class="empty" v-else>
				<u-empty></u-empty>
			</view>
		</view>

		<view class='btn safe-bottom'>
			<xbutton size='large' width='100%' @tap='applyToDevice'>确定应用到设备</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		searchPage
	} from '@/api/device/device.js'
	
	import {
		saveGoodsDevice,
	} from "@/api/commoditylist/commoditylist.js"

	export default {
		data() {
			return {
				keyword: '',
				deviceList: [],
				page: 1,
				size: 10,
				modelId:null,
				modeName:null
			}
		},
		onLoad(o) {
			if (o.modelId) {
				this.modelId=modelId
				this.modeName=modeName
			}
		},

		onShow() {
			this.getList()
		},

		methods: {
			search(val) {
				this.reset()
				this.getList()
			},
			// 商品选中状态改变
			commItemSelect(e) {
				e.checked = !e.checked;
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.deviceList = [];
			},

			getList() {
				searchPage({
					page: {
						current: this.page,
						size: this.size
					},
					keywords: this.keyword,
				}).then(res => {
					let data = res.data.records;
					for (let i = 0; i < data.length; i++) {
						let item = data[i];
						item.checked = false;
					}
					if (data.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.deviceList = this.deviceList.concat(data)
				})
			},

			onReachBottom() {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			applyToDevice() {
				saveGoodsDevice({
					"deviceIds": [], //设备信息id
					"mercId": 0, //商户ID
					"modelId": 0 //商品清单ID
				}).then(res => {})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {
			padding-bottom: 88rpx;

			.search {
				padding: 24rpx 24rpx;
				background-color: #fff;
			}

			.list {
				width: 100%;
				padding: 12rpx 24rpx;

				.thumb-box {
					margin-bottom: 12rpx;
					border-bottom: 1rpx solid #f4f4f4;
					display: flex;
					flex-flow: row nowrap;
					padding: 12rpx 12rpx;
					align-items: center;
					background-color: #fff;
					border-radius: 12rpx;
				}

				.select-img {
					width: 40rpx;
					height: 40rpx;
				}

				.check-content {
					width: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					padding-left: 12rpx;
					position: relative;

					.comm-img {
						width: 130rpx;
						height: 130rpx;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-around;

						image {
							width: 100%;
						}
					}

					.comm-main {
						box-sizing: border-box;
						padding-left: 18rpx;
						color: #999;

						>view {
							padding: 8rpx 0;
							width: 330rpx;
							font-size: 28rpx;
						}

					}
				}
			}
		}

		.empty {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
		}
	}
</style>