<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" bgColor="#2C6FF3" :placeholder="true">
			<view class="u-nav-slot flex align-center" slot="left">
				<view class="nav-back flex align-center" @click="$tab.navigateBack()">
					<u-icon name="arrow-left" size="22" color="#fff"></u-icon>
					<view class="nav-title">通知</view>
				</view>
				<view class="clear-img flex align-center justify-around" @click="clearNotice">
					<image
						src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/clear-notice.png"
						mode="widthFix"></image>
				</view>
			</view>
		</u-navbar>

		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold'}" :scrollable="true"
					:current="current" @click="tabClick" lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>

		<view class="content">
			<scroll-view @scrolltolower="loadMore" class="scrollview" :scroll-with-animation="true" scroll-y
				lower-threshold="100" :style="{height:fullHeight}">
				<view class="list" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="index">
						<view class="comm-main" @click="detail(item)">
							<view class="item-head flex justify-between">
								<view class="title">
									{{item.title||'无'}}
								</view>
								<view class="read-status" v-if="item.readState==1"></view>
							</view>
							<view class="item-body">
								<view class="item-body-sec">
									<view style="width:150rpx;">创建时间：</view>
									<view>{{item.createTime||'无'}}</view>
								</view>
								<view class="item-body-sec">
									<view style="width:150rpx;">通知内容：</view>
									<view>{{item.content||'无'}}</view>
								</view>
							</view>
						</view>
					</block>
					<view class="more" style="overflow: hidden;">
						<u-loadmore :status="status" v-if="list.length>=1" />
					</view>
				</view>
				<view class="empty" v-if="list.length==0">
					<u-empty mode="list" text="没有任何通知!"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		myMsg,
		myMsgCount,
		readAll
	} from "@/api/message.js"
	export default {
		data() {
			return {
				list: [], //列表
				fullHeight: 0,
				page: 1, //当前分页
				size: 10, //分页数据条数
				status: 'loadmore', //加载更多
				type: 0,
				current: 0,
				tabList: [{
						name: '全部',
						badge: {
							value: 0,
						}
					},
					{
						name: '交易',
						badge: {
							value: 0,
						}
					},
					{
						name: '故障',
						badge: {
							value: 0,
						}
					},
					{
						name: '费用',
						badge: {
							value: 0,
						}
					},
					{
						name: '其他',
						badge: {
							value: 0,
						}
					},
				],
			}
		},

		async onLoad() {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)

			this.getListCount()
			this.getList()
		},

		methods: {
			tabClick(e) {
				this.current = e.index
				switch (this.current) {
					case 0:
						this.type = 0
						break;
					case 1:
						this.type = 2
						break;
					case 2:
						this.type = 1
						break;
					case 3: //费用
						this.type = 4
						break;
					case 4: //其他
						this.type = 3
						break;
					default:
						break;
				}
				this.reset()
				this.getListCount()
				this.getList()
			},

			//消息中心
			getList() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					}
				}
				if (this.type) {
					params.type = this.type;
				}
				myMsg(params).then(res => {
					let newData = res.data.records;
					if (newData.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.list = this.list.concat(newData)
				})
			},
			//徽标统计未读
			getListCount() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					}
				}
				if (this.type) {
					params.type = this.type;
				}
				myMsgCount(params).then(res => {
					let allNum = res.data.allNum;
					let tradeNum = res.data.tradeNum;
					let riskNum = res.data.riskNum;
					let otherNum = res.data.otherNum;
					let costNum = res.data.costNum;

					this.tabList.forEach((item, index) => {
						if (index == 1) {
							this.tabList[index - 1].badge.value = allNum;
						} else if (index == 2) {
							this.tabList[index - 1].badge.value = tradeNum;
						} else if (index == 3) {
							this.tabList[index - 1].badge.value = riskNum;
						} else if (index == 4) {
							this.tabList[index - 1].badge.value = costNum;
						} else if (index == 5) {
							this.tabList[index - 1].badge.value = otherNum;
						}
					})
				})
			},

			//重置数据
			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			//上拉加载
			loadMore(e) {
				if (this.status == 'nomore') return
				this.page++
				this.getListCount()
				this.getList()
			},

			detail(item) {
				if (this.tabList[this.current].badge.value > 0 && item.readState == 1) {
					this.tabList[this.current].badge.value--
				}
				item.readState = 2 //改变已读显示状态
				this.$forceUpdate()
				this.$tab.navigateTo('/pages/globalPages/noticeDetail?id=' + item.id)

			},

			//消息全部已读
			clearNotice() {
				this.$modal.confirm(`是否标记所有消息为已读状态？`).then(res => {
					readAll().then(res => {
						//标记全部已读
						let tempList = JSON.parse(JSON.stringify(this.list))
						for (let item of tempList) {
							item.readState = 2
						}
						this.list = tempList

						for (let item of this.tabList) {
							item.badge.value = 0
						}
					})
				})

			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.u-nav-slot {
			.nav-title {
				font-size: 32rpx;
				color: #fff;
			}

			.clear-img {
				width: 50rpx;
				height: 50rpx;
				border-radius: 50rpx;
				background-color: rgba(0, 0, 0, 0.2);
				margin-left: 44rpx;

				>image {
					width: 36rpx;
					height: 36rpx;
				}
			}
		}

		.content {

			.list {
				width: 100%;
				padding: 0rpx 13rpx 12rpx;
				padding-bottom: calc(110rpx + env(safe-area-inset-bottom) / 2);
				overflow: hidden;

				.comm-img {
					width: 130rpx;
					height: 130rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-around;

					image {
						width: 130rpx;
						height: 130rpx;
					}
				}

				.comm-main {
					padding: 20rpx 30rpx;
					background-color: #fff;
					border-radius: 12rpx;
					margin-top: 12rpx;
					box-sizing: border-box;
					color: #999;
					line-height: 60rpx;
					position: relative;

					.item-head {
						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}

						.status {
							font-size: 28rpx;
						}

						.read-status {
							width: 20rpx;
							height: 20rpx;
							border-radius: 20rpx;
							background-color: red;
						}
					}

					.item-body {
						margin-top: 22rpx;
						color: #333;

						.item-body-sec {
							line-height: 44rpx;
							overflow: hidden;

							>view:nth-child(1) {
								float: left;
							}

							>view:nth-child(2) {
								padding-left: 150rpx;
								word-break: break-all;
							}
						}
					}
				}
			}
		}
	}

	.empty {
		padding-top: 40%;
	}
</style>