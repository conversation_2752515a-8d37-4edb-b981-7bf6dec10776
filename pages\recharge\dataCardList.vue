<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="流量卡购买记录"></u-navbar>

		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
					:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
					lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>
		<!-- 
		<view class="search">
			<u-search animation placeholder="请输入" :clearabled="true" v-model="keyword" :showAction="false"
				@search="search"></u-search>
		</view> -->

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="item">
						<view class="flex justify-between align-center">
							<view class="title">
								{{item.createTime}}
							</view>
							<view :class="[item.deliveryStatus?'status status_yes':'status']">
								{{item.deliveryStatus?'已发货':'未发货'}}
							</view>
						</view>
						<view class="rows">
							快递：{{item.express||'-'}}
						</view>
						<view class="rows">
							价格：<text style="color:red;">￥{{$xy.delMoney(item.money)}}</text>
						</view>
						<view class="rows" v-if="item.deliveryStatus">
							发货时间：{{item.deliveryTime||'-'}}
						</view>
						<view class="rows" v-if="item.deliveryStatus">
							运货单：{{item. expressNumber||'-'}}<text v-if="item.expressNumber" class="under-line-text"
								@click.stop="$xy.copy(item. expressNumber)">复制</text>
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>10" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>


		<u-action-sheet :show="actionSheetShow" :actions="actions" title="套餐类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		dataCardList
	} from "@/api/recharge.js"
	import config from "@/config.js"

	export default {
		data() {
			return {
				keyword: null,
				list: [],
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				title: '',
				fullHeight: 0,
				current: 1,

				tabList: [{
						name: '全部',
						badge: {
							value: 0
						}
					},
					{
						name: '已发货',
						badge: {
							value: 0
						}
					},
					{
						name: '未发货',
						badge: {
							value: 0
						}
					},
				],

			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			this.search()
			uni.$on('refresh', res => {
				this.search()
			})
		},

		methods: {
			tabClick(e) {
				this.current = e.index
				this.reset()
				this.getpage()
			},

			//获取订单列表
			getpage() {
				let deliveryStatus = ''
				switch (this.current) {
					case 0:
						deliveryStatus = ''
						break;
					case 1:
						deliveryStatus = 1
						break;
					case 2:
						deliveryStatus = 0
						break;
					default:
						break;
				}
				dataCardList({
					page: {
						current: this.page,
						size: this.size
					},
					deliveryStatus: deliveryStatus
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
					this.tabList[this.current].badge.value = res.data.total
					this.tabList = JSON.parse(JSON.stringify(this.tabList))
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			detail(item) {
				this.$tab.navigateTo(`/pages/complain/detail?id=${item.id}&&title=${item.problemDescription}`)
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		},

		onUnload() {
			uni.$off('refresh')
		}
	}
</script>
<style scoped lang="scss">
	.container {
		background-color: #fff;

		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.scrollview {
			padding: 0 12rpx;
		}

		.item {
			background-color: #e2e7f0;
			padding: 20rpx;
			border-radius: 12rpx;
			margin-top: 20rpx;

			.title {
				font-size: 30rpx;
				font-weight: bold;
			}
			
			.status{
				&.status_yes{
					color:green;
				}
			}

			.rows {
				margin-top: 16rpx;
				padding-left: 12rpx;
			}

			.under-line-text {
				font-size: 26rpx !important;
				font-weight: 500;
				font-style: italic;
				text-decoration: underline;
				color: #2C6FF3 !important;
				margin-left: 24rpx;
			}
		}
	}
</style>