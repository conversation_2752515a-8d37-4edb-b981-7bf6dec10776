import request from '@/utils/request'
/**
 * @param {Object} data
 * 开票列表
 */
export function invoiceList(data) {
	return request({
		url: '/order/order-merc-manage-mini/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 抬头列表
 */
export function titleList(data) {
	return request({
		url: '/order/merc/invoice/invoiceTitle/list',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 抬头编辑
 */
export function titleEdit(data) {
	return request({
		url: '/order/merc/invoice/invoiceTitle/edit',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 抬头新增
 */
export function titleAdd(data) {
	return request({
		url: '/order/merc/invoice/invoiceTitle/add',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 开票申请
 */
export function apply(data) {
	return request({
		url: '/order/merc/invoice/apply',
		method: 'post',
		data: data
	})
}




