import request from '@/utils/request'

// 异常单补扣申请 
export function apply(data) {
	return request({
		url: '/order/order-risk-cut/apply',
		method: 'post',
		data: data
	})
}

//风险订单-分页 
export function page(data) {
	return request({
		url: '/order/order-riskCut-merc-mini/page',
		method: 'post',
		data: data
	})
}

//所有待处理异常单
export function todoNum(data) {
	return request({
		url: '/order/order-riskCut-merc-mini/todoNum',
		method: 'post',
		data: data
	})
}

//风险订单结束
export function cancelOrder(data) {
	return request({
		url: '/order/orders/cancel',
		method: 'post',
		data: data
	})
}

//风险订单补扣申请撤回
export function rollback(data) {
	return request({
		url: '/order/order-riskCut-merc-mini/rollback',
		method: 'post',
		data: data
	})
}






