<template>
	<view class="notice-wrap">
		<!-- 公告 -->
		<xpopup :show="tipsShow" @close="tipsClose" round="12" :zIndex="zIndex" :showBtn="false" mode="center">
			<view class="tips-pop-content"
				:class="[btnShow?'tips-pop-content tips-pop-content-btn':'tips-pop-content']">
				<view class="tips-title">
					{{tipsDetail.noticeTitle}}
				</view>
				<scroll-view scroll-y class="scroll-content" @click.stop="detail" :style="{height:height+'rpx'}">
					<rich-text :nodes="tipsDetail.noticeContent"></rich-text>
					<view class="flex">
						<view @click.stop="linkDetail(item)" class="linkUrl" v-for="(item,index) in tipsLinks"
							:key="item">链接<text v-if="tipsLinks.length>1">{{index+1}}</text></view>
					</view>
				</scroll-view>
				<view class="tips-btn" @click="btnClick" v-if="btnShow">
					<xbutton>{{tipsIndex==tipsList.length-1?'关闭':'下一条'}}</xbutton>
				</view>
				<view class="close">
					<u-icon name="close" color="#dddddd" size="18" @click="tipsClose"></u-icon>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	export default {
		props: {
			tipsList: {
				type: Array,
				default: function() {
					return []
				}
			},
			zIndex: {
				type: Number,
				require: false,
				default: 10075
			},
			btnShow: {
				type: Boolean,
				require: false,
				default: true
			},

			height: {
				require: false,
				default: 600
			},
		},

		data() {
			return {
				tipsShow: false,
				tipsIndex: 0
			}
		},

		computed: {
			tipsDetail() {
				let obj = this.tipsList[this.tipsIndex]
				return obj
			},
			tipsLinks() {
				let tipsDetail = this.tipsList[this.tipsIndex]?.noticeContent
				if (tipsDetail && tipsDetail.indexOf('<a href="') != -1) {
					let regex = /<a href="(.*?)"/g;
					let hrefs = tipsDetail.match(regex);
					let arr = []
					hrefs.forEach((href) => {
						let end = href.length - 10
						arr.push(href.substr(9, end))
					});
					return arr
				} else {
					return false
				}
			},
		},

		methods: {
			btnClick() {
				if (this.tipsIndex == this.tipsList.length - 1) {
					this.tipsClose()
				} else {
					this.tipsIndex++
				}
			},
			tipsClose() {
				this.tipsShow = false
				this.$emit('close', this.tipsIndex)
			},
			detail() {
				this.$emit('cossnClick')
			},

			linkDetail(url) {
				let imgRealUrl = ''
				if (url.indexOf('?') != -1) {
					imgRealUrl = url.split('?')[0]
				} else {
					imgRealUrl = url
				}
				var reg = /\.(jpg|jpeg|png|gif)$/i; // 正则表达式匹配图片格式
				if (reg.test(imgRealUrl)) { //图片的话，进行预览
					this.$xy.previewImg(url)
				} else {
					this.$tab.navigateTo(`/pages/globalPages/webView?src=${encodeURIComponent(url)}`)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.tips-pop-content {
		width: 602rpx;
		padding: 30rpx 40rpx 40rpx;
		border-radius: 24rpx;
		min-height: 600rpx;
		position: relative;

		&.tips-pop-content-btn {
			padding: 20rpx 40rpx 100rpx;
		}

		.scroll-content {
			border-radius: 6rpx;
			overflow: hidden;
		}

		.close {
			position: absolute;
			right: 0rpx;
			top: 0rpx;
			padding: 16rpx;
		}

		.tips-title {
			font-size: 34rpx;
			text-align: center;
			padding-bottom: 20rpx;
		}

		.tips-btn {
			width: 200rpx;
			position: absolute;
			bottom: 24rpx;
			left: 50%;
			transform: translateX(-50%);
		}

		.linkUrl {
			color: #007AFF;
			text-decoration: underline;
			margin-right: 20rpx;
		}
	}
</style>