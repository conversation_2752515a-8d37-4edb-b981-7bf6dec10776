<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="积分记录"></u-navbar>

		<view class="flex align-center justify-between search">
			<u-search animation placeholder="请输入手机号" :clearabled="true" type="number" v-model="keyword"
				:showAction="false" @search="inputSearch"></u-search>
		</view>

		<view class="flex align-center justify-between screen-container">
			<view class="flex align-center date-container">
				<view class="" style="margin-right: 10rpx;" @tap="dateSelect('start')">
					{{dateStart?dateStart:'开始日期'}}
				</view>
				<view class="" style="margin-right: 10rpx;">至</view>
				<view class="" @tap="dateSelect('end')">{{dateend?dateend:'结束日期'}}</view>
			</view>

			<view class="flex align-center">
				<view style="margin-right: 20rpx;">
					<view class="change-month" @tap="changeMonth(-1)">
						上一月
					</view>
					<view class="change-month" style="margin-left: 10rpx;" @tap="changeMonth(1)">
						下一月
					</view>
				</view>

				<view class="flex align-center justify-end screen" @tap="actionSheetShow=true">
					<view>{{meal.name||'全部'}}</view>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png"
						mode="widthFix">
					</image>
				</view>
			</view>
		</view>

		<!-- <view class="total">
			<view class="flex">
				<view>总积分：100，</view>
				<view>剩余积分：100000</view>
			</view>
			<view class="flex">
				<view>赠送积分：100，</view>
				<view>兑换积分：100000</view>
			</view>
		</view> -->

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="equipment-container">
						<view class="title">
							{{actionsObj[item.type]||'其他'}}（剩余{{item.newNum}}）
						</view>
						<view class="order-detail-item">
							<view>手机号：</view>{{item.tel}}
						</view>
						<view class="order-detail-item">
							{{item.createTime}}
						</view>
						<view :class="[item.num>=0?'num':'num red']">
							{{item.num}}
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<view class="btn safe-bottom">
			<xbutton width="340rpx" round="88rpx" size="large" @click="exports('all')">全部会员积分导出</xbutton>
			<xbutton width="340rpx" round="88rpx" size="large" @click="exports('detail')">积分明细导出
			</xbutton>
		</view>

		<u-action-sheet :show="actionSheetShow" :actions="actions" title="套餐类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
		
		<u-datetime-picker :show="show" v-model="timeStamp" :closeOnClickOverlay="true" mode="date" @confirm="confirm"
			@cancel="close" @close="close" confirmColor="#2C6FF3">
		</u-datetime-picker>
	</view>
</template>

<script>
	import {
		integralTypeList,
		recordList,
		exportMember,
		exportHistory
	} from "@/api/integral.js"

	export default {
		data() {
			return {
				keyword: '',
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				title: '',

				pickerType: null,

				fullHeight: 3000,
				list: [],
				meal: {
					id: '',
					name: '全部'
				},
				actionSheetShow: false,
				actions: [],
				actionsObj: {},
				id: null,
				
				type: '',
				dateStart: '',
				dateend: '',
				dateDx: 0,
				show:false,
				timeStamp: new Date(), //时间picker显示时间
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			this.dateStart = uni.$u.timeFormat(new Date().getTime() - 30 * 24 * 60 * 60 * 1000, 'yyyy-mm-dd');
			this.dateend = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd');

			this.id = o.id;
			this.getType()
			this.search()
		},

		methods: {
			/**
			 * 积分类型
			 */
			getType() {
				integralTypeList({}).then(res => {
					let data = res.data
					let actions = [{
						id: '',
						name: '全部'
					}]
					for (let key in data) {
						let val = data[key]
						actions.push({
							name: val,
							id: key
						})
					}
					this.actionsObj = data
					this.actions = actions
				})
			},

			/**
			 * @param {Object} e
			 * 筛选
			 */
			actionsheetSelect(e) {
				this.meal = e
				this.search()
			},

			changeMonth(add) {
				this.dateDx += add;
				var date = new Date();
				var year = date.getFullYear(); //当前年：四位数字
				var month = date.getMonth() + 1 + this.dateDx; //当前月：0-11
				var yeardx = Math.ceil(month / 12) - 1
				month %= 12;
				if (month <= 0) month += 12;
				year += yeardx;
				month = month < 10 ? ('0' + month) : month;

				this.dateStart = year + '-' + month + '-01';

				var day = 28;
				if (2 == month) {
					if (year % 4 == 0) {
						day = 29;
					}
				} else {
					if (month < 8) {
						if (1 == month % 2) {
							day = 31;
						} else {
							day = 30;
						}
					} else {
						if (1 == month % 2) {
							day = 30;
						} else {
							day = 31;
						}
					}
				}
				this.dateend = year + '-' + month + '-' + (day < 10 ? ('0' + day) : day);
				this.search();
			},
			
			dateSelect(type) {
				if (type == 'start') {
					this.type = type
				}
				if (type == 'end') {
					this.type = type
				}
				this.show = true
			},
			
			confirm(e) {
				if (this.type == 'start') {
					this.dateStart = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				}
				if (this.type == 'end') {
					this.dateEnd = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
			
				}
				if (this.dateStart && this.dateEnd) {
					if (this.dateStart > this.dateEnd) {
						uni.$u.toast('开始日期不能大于结束日期')
						return;
					}
				}
			
				this.search()
				this.show = false
			},
			close() {
				this.show = false
			},

			/**
			 * 获取列表
			 */
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					type: this.meal.id,
					beginTime: this.dateStart+' 00:00:00',
					endTime: this.dateend+' 23:59:59',
					tel: this.keyword,
					memberId: this.id
				}
				recordList(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
					console.log(this.list)
				})
			},

			/**
			 * 输入框搜索
			 * 搜索清除ID，查找全部用户
			 */
			inputSearch() {
				this.id = null
				this.search()
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
			
			exports(type){
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					type: this.meal.id,
					beginTime: this.dateStart+' 00:00:00',
					endTime: this.dateend+' 23:59:59',
					tel: this.keyword,
					memberId: this.id
				}
				if(type=='all'){
					exportMember(params,`全部会员积分.xlsx`).then(res=>{
						setTimeout(()=>{
							this.$modal.showToast('导出成功~')
						},500)
					})
				}else{
					exportHistory(params,`积分明细.xlsx`).then(res=>{
						setTimeout(()=>{
							this.$modal.showToast('导出成功~')
						},500)
					})
				}
			}
		}
	}
</script>
<style scoped lang="scss">
	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.search {
			background-color: #fff;
			padding: 20rpx 13rpx 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;
		}

		.total {
			font-size: 24rpx;
			color: #777777;
			line-height: 50rpx;
			padding: 0rpx 30rpx 20rpx;
			background-color: #fff;
		}

		.change-month {
			display: inline-block;
			width: 100rpx;
			height: 45rpx;
			border: 1px solid #CCCCCC;
			border-radius: 6rpx;
			line-height: 43rpx;
			text-align: center;
			border-radius: 10rpx;
			text-align: center;
			font-size: 22rpx;
			color: #777777;
		}

		.screen-container {
			background-color: #fff;
			padding: 14rpx 13rpx 18rpx;

			.screen {
				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 12rpx;
				}
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 15rpx 34rpx 30rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
			border-right: 10rpx solid #2C6FF3;
			position: relative;

			&::after {
				content: '';
				width: 2rpx;
				height: 140rpx;
				border-right: 2rpx dashed #CCCCCC;
				position: absolute;
				right: 250rpx;
				top: 50%;
				transform: translateY(-50%);
			}

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
			}

			.order-detail-item {
				font-size: 26rpx;
				margin-top: 16rpx;
				color: #777;

				>view {
					display: inline-block;
					width: 120rpx;
				}
			}

			.num {
				position: absolute;
				right: 65rpx;
				top: 50%;
				transform: translateY(-50%);
				font-size: 48rpx;
				font-weight: 800;
				color: #333333;

				&.red {
					color: #FF0000;
				}
			}
		}
		
		.btn {
			width: 100%;
			position: fixed;
			bottom: 24rpx;
			left: 0;
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-between;
			padding: 0 24rpx;
		
			.cu-btn {
				background-color: #2C6FF3;
				color: #fff;
				width: 48%;
			}
		}
	}
</style>