import config from '@/config'
import storage from '@/utils/storage'
import constant from '@/utils/constant'
import {
	login,
	logout,
	getInfo
} from '@/api/login'
import {
	getToken,
	setToken,
	removeToken,
	setMercId
} from '@/utils/auth'

const baseUrl = config.baseUrl

const user = {
	state: {
		token: getToken(),
		name: storage.get(constant.name),
		avatar: storage.get(constant.avatar),
		roles: storage.get(constant.roles),
		permissions: storage.get(constant.permissions)
	},

	mutations: {
		SET_TOKEN: (state, token) => {
			state.token = token
		},
		SET_NAME: (state, name) => {
			state.name = name
			storage.set(constant.name, name)
		},
		SET_AVATAR: (state, avatar) => {
			state.avatar = avatar
			storage.set(constant.avatar, avatar)
		},
		SET_ROLES: (state, roles) => {
			state.roles = roles
			storage.set(constant.roles, roles)
		},
		SET_PERMISSIONS: (state, permissions) => {
			state.permissions = permissions
			storage.set(constant.permissions, permissions)
		}
	},

	actions: {
		// 直接登录
		Login({
			commit
		}, userInfo) {
			const clientType = "wx_app"
			const code = ""
			const loginName = userInfo.username.trim()
			const password = userInfo.password
			const pointJson = userInfo.pointJson
			return new Promise((resolve, reject) => {
				login(loginName, password, code, clientType, pointJson).then(res => {
					setToken(res.data.satoken)
					setMercId(res.data.mercId)
					commit('SET_TOKEN', res.data.satoken)
					commit('SET_NAME', res.data.mercName)
					resolve(res.data)
				}).catch(error => {
					reject(error)
				})
			})
		},
		
		//管理平台登录
		manageLogin({
			commit
		}, options) {
			let mercId = options.referrerInfo.extraData.mercId
			let mercName = options.referrerInfo.extraData.mercName
			let token = options.referrerInfo.extraData.token
			setToken(token)
			setMercId(mercId)
			commit('SET_TOKEN', token)
			commit('SET_NAME', mercName)
		},

		// 获取用户信息
		GetInfo({
			commit,
			state
		}) {
			return new Promise((resolve, reject) => {
				getInfo().then(res => {
					const user = res.user
					const avatar = (user == null || user.avatar == "" || user.avatar == null) ?
						'' : baseUrl + user.avatar
					const username = (user == null || user.userName == "" || user.userName ==
						null) ? "" : user.userName
					if (res.roles && res.roles.length > 0) {
						commit('SET_ROLES', res.roles)
						commit('SET_PERMISSIONS', res.permissions)
					} else {
						commit('SET_ROLES', ['ROLE_DEFAULT'])
					}
					commit('SET_NAME', username)
					commit('SET_AVATAR', avatar)
					resolve(res)
				}).catch(error => {
					reject(error)
				})
			})
		},

		// 退出系统
		LogOut({
			commit,
			state
		}) {
			return new Promise((resolve, reject) => {
				commit('SET_TOKEN', '')
				commit('SET_NAME', '')
				commit('SET_AVATAR', '')
				commit('SET_PERMISSIONS_MENU', '')
				commit('SET_PERMISSIONS_BTN', '')
				removeToken()
				storage.clean()
				resolve('退出登录成功~')
			})
		}
	}
}

export default user

