<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="异常订单"></u-navbar>
		<xhelpPopup guideId="DJ0003" />
		<!-- <view class="time-choose">
			<view class="flex align-center justify-between top">
				<view class="con-btn" @tap="changeMonth(-1)">
					上一月</view>
				<view class="flex align-center justify-around date-container">
					<view class="" style="margin-right: 10rpx;" @tap="dateSelect('start')">
						{{dateStart?dateStart:'开始日期'}}
					</view>
					<view class="" style="margin-right: 10rpx;">至</view>
					<view class="" @tap="dateSelect('end')">{{dateEnd?dateEnd:'结束日期'}}</view>
				</view>
				<view class="con-btn" @tap="changeMonth(1)">下一月</view>
			</view>
		</view> -->

		<view class="header-container">
			<view class="header-text flex align-center">
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/order/risk-num.png"
					mode="widthFix"></image>您有待处理异常订单：<text style="font-weight: bold;">{{orderNum}}</text>笔
			</view>
			<view class="search">
				<u-search animation placeholder="可输入订单编号,设备号,用户手机号" :clearabled="false" v-model="keyword"
					:showAction="false" @search="search"></u-search>
			</view>

			<view class="flex align-center justify-between" style="margin-top: 28rpx;">
				<!-- <view class="exceptiontype-container" @tap="actionSheet">
					{{status?status:'选择状态'}}
				</view> -->

				<view class="flex align-center  date-container">
					<view class="" style="margin-right: 10rpx;" @tap="dateSelect('start')">
						{{dateStart?dateStart:'开始日期'}}
					</view>
					<view class="" style="margin-right: 10rpx;">至</view>
					<view class="" @tap="dateSelect('end')">{{dateEnd?dateEnd:'结束日期'}}</view>
				</view>

				<view>
					<view class="change-month" @tap="changeMonth(-1)">
						上一月
					</view>
					<view class="change-month" style="margin-left: 20rpx;" @tap="changeMonth(1)">
						下一月
					</view>
				</view>
			</view>
			<!-- <view class='martop'>
				<u-subsection :list="list" activeColor="#2C6FF3" :current="current"
					@change="sectionChange"></u-subsection>
			</view> -->
			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="list" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'28rpx'}"
						:inactiveStyle="{fontSize:'28rpx'}" :scrollable="false" :current="current"
						@click="sectionChange" lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>
		</view>
		<scroll-view class="scrollview" scroll-y="true" scroll-with-animation="true" lower-threshold="100"
			@scrolltolower="loadMore" :style="{height:fullHeight}">
			<view class="scroll-content" v-if="commList&&commList.length>0">
				<block v-for="(item,index) in commList" :key="item.id">

					<!-- 识别中订单 -->
					<view class="abnormal-container" v-if="current=='4'">
						<view class="flex align-center justify-between">
							<view class="flex align-center error-type">
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/order/risk-tip.png"
									mode="widthFix"></image>识别中
							</view>
							<view class="com-status" style="color: #ffaa00;">识别中</view>
						</view>
						<view class="fx-box">
							<view class="device-name" v-if="item.deviceName">
								{{item.deviceName}}<text>({{item.deviceId}})</text>
							</view>
							<view class="device-name" v-else>{{item.deviceId}}</view>
							<view class="flex align-center martop12">
								<view class="">
									<view class="oc-name">订单编号：</view>{{item.id||'-'}}<text class="under-line-text"
										@tap="copy(item.id)">复制</text>
								</view>
							</view>

							<view class="flex align-center martop12">
								<view class="oc-name">支付类型：</view>{{item.payStatusDesc||'-'}}
							</view>

							<view class="order-detail-item martop12">
								<view class="oc-name">设备类型：</view>{{item.deviceType||'-'}}
							</view>

							<view class="order-detail-item martop12">
								<view class="oc-name">会员卡：</view>{{item.plusMealName||'普通会员'}}
							</view>

							<view class="flex align-center martop12" style="color: red;">
								<view class="oc-name">订单状态：</view><text>识别中</text>
							</view>

							<view class="flex align-center martop12">
								<view class="oc-name">支付金额：</view>￥{{$xy.delMoney(item.payMoney)}}
							</view>

							<view class="flex align-center martop12">
								<view class="oc-name">创建时间：</view>{{item.createTime||'-'}}
							</view>
						</view>

						<view class="martop">
							<view class="flex justify-end martop">
								<view class="marleft24">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx'
										@click="changeStatus(item.id)">转为异常
									</xbutton>
								</view>
							</view>
						</view>
					</view>

					<!-- 风险订单 -->
					<view class="abnormal-container" v-else>
						<view class="flex align-center justify-between">
							<!-- 				<view class="flex align-center image-container">
								<view style="margin-right: 16rpx;" v-for="(item1,index1) in item.orderGoods" :key="item1.id">
									<u--image width="110rpx" :src="item1.goodslmgUrl" mode="widthFix" :lazy-lord="true">
									</u--image>
								</view>
							</view> -->
							<view class="flex align-center error-type">
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/order/risk-tip.png"
									mode="widthFix"></image>{{item.riskTypeDesc}}
							</view>
							<view class="com-status" style="color: #FF1E1E;" v-if="item.status==1">待处理</view>
							<view class="com-status" style="color: #22ca08;" v-if="item.status==2">已审核</view>
							<view class="com-status" style="color: #ffaa00;" v-if="item.status==3">审核中</view>
							<view class="com-status" style="color: #bdbdbd;" v-if="item.status==4">已结束</view>
						</view>
						<view class="fx-box">
							<view class="device-name" v-if="item.deviceName">
								{{item.deviceName}}<text>({{item.deviceId}})</text>
							</view>
							<view class="device-name" v-else>{{item.deviceId}}</view>
							<view class="flex align-center martop12">
								<view class="">
									<view class="oc-name">订单编号：</view>{{item.orderId||'-'}}<text class="under-line-text"
										@tap="copy(item.orderId)">复制</text>
								</view>
							</view>

							<view class="flex align-center martop12">
								<view class="oc-name">支付类型：</view>{{item.payStatus||'-'}}
							</view>

							<view class="order-detail-item martop12">
								<view class="oc-name">设备类型：</view>{{item.deviceType||'-'}}
							</view>

							<view class="order-detail-item martop12">
								<view class="oc-name">会员卡：</view>{{item.plusMealName||'普通会员'}}
							</view>

							<view class="flex align-center martop12" style="color: red;">
								<view class="oc-name">订单状态：</view><text>{{item.orderStatusDesc||'-'}}</text>
							</view>

							<view class="flex align-center martop12">
								<view class="oc-name">支付金额：</view>￥{{$xy.delMoney(item.payMoney)}}
							</view>

							<view class="flex align-center martop12" v-if="item.cutMoney">
								<view class="oc-name">补扣金额：</view>￥{{$xy.delMoney(item.cutMoney)}}
							</view>

							<view class="flex align-center martop12">
								<view class="oc-name">联系电话：</view>{{$xy.delPhone(item.memberPhone)||'-'}}
								<view class="phone" @click="call(item.memberPhone)" v-if="item.memberPhone">
									<image
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/order/phone.png"
										mode="widthFix"></image>拨打
								</view>
								<view class="phone"
									@click="$tab.navigateTo(`/pages/order/userInfo?id=${item.memberId}`)">
									会员信息
								</view>
							</view>

							<view class="flex align-center martop12">
								<view class="oc-name">创建时间：</view>{{item.orderCreateTime||'-'}}
							</view>
							<view class="flex align-center martop12" style="color: red;">
								<view class="oc-name">异常时间：</view><text>{{item.createTime||'-'}}</text>
							</view>

							<view class="goods-container" v-for="(item1,index1) in item.orderGoods" :key="item1.id">
								<view class="flex align-center justify-center image-container">
									<u--image radius="4" width="130rpx" height="130rpx" :src="item1.goodsImgUrl"
										mode="aspectFit" :lazy-lord="true"></u--image>
								</view>
								<view class="details-container">
									<view class="flex align-center goods-name-num justify-between">
										<view class="goods-name">{{item1.goodsName}}</view>
										<view class="goods-num">×{{item1.totalNumber}}</view>
									</view>
									<view class="flex">
										<view class="gp-item">
											单价：￥{{(Number(item1.totalMoney)/Number(item1.totalNumber))/100}}
										</view>
										<view class="gp-item" v-if="item1.refundMoney&&item1.refundMoney!=0">
											已退款：￥{{$xy.delMoney(item1.refundMoney)}}
										</view>
									</view>
								</view>
							</view>

							<!-- <view class="flex align-center justify-end" v-if="item.cutGoodsNumber!=null">
								共计<text style="color: red;font-weight: bold;">{{item.cutGoodsNumber}}</text>件
							</view> -->
						</view>

						<view class="flex align-center martop12 re-back"
							v-if="item.status!=2&&item.status!=4&&item.auditRemark">
							<view class="oc-name">驳回原因：</view>{{item.auditRemark||'-'}}
						</view>

						<view class="martop">
							<view class="flex justify-start">
								<view class="" v-if="checkPermi(['riskOrder:log'])">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' bgColor='#43cf7c'
										width='140rpx' @tap="showlogs(item.activityId)">交易日志</xbutton>
								</view>
								<!-- 	<view class="marleft24">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' width='200rpx'
										bgColor='#43cf7c'>查看处理记录
									</xbutton>
								</view> -->
								<view class="marleft24" v-if="checkPermi(['riskOrder:devCom'])">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' width='200rpx'
										bgColor='#43cf7c' @tap="deviceCom(item)">查看设备商品
									</xbutton>
								</view>
							</view>
							<view class="flex justify-end martop">
								<view class="marleft24" v-if="checkPermi(['riskOrder:black'])">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' @click="block(item)">拉黑
									</xbutton>
								</view>
								<view v-if="item.status=='1'&&checkPermi(['riskOrder:closeOrder'])" class="marleft24">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx'
										@tap='showFinishOrderView(item)'>结束</xbutton>
								</view>
								<view class="marleft24" v-if="item.status=='3'&&checkPermi(['riskOrder:back'])">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' @tap='backApply(item)'>撤回
									</xbutton>
								</view>
								<view class="marleft24" v-if="checkPermi(['riskOrder:radio'])">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' width='140rpx'
										@tap="showVideoView(item)">查看视频</xbutton>
								</view>

								<view class="marleft24" v-if="item.status=='1'&&checkPermi(['riskOrder:supp'])">
									<xbutton size='medium' round='25rpx' padding='0rpx 20rpx'
										@tap='supplementaryDeduction(item)'>申请立即扣款
									</xbutton>
								</view>
							</view>
						</view>
					</view>
				</block>
				<view class="load-more" v-if="commList.length>0">
					<u-loadmore :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>
		<!-- <u-action-sheet :show="actionSheetShow" :actions="exceptionTypeList" title="请选择状态"
			@close="actionSheetShow = false" @select="actionsheetSelect($event)"></u-action-sheet> -->
		<!-- :minDate="minDate" :maxDate="maxDate" -->
		<u-datetime-picker :show="show" v-model="timeStamp" :closeOnClickOverlay="true" mode="date" @confirm="confirm"
			@cancel="close" @close="close" confirmColor="#2C6FF3">
		</u-datetime-picker>
		<!-- <view :hidden="hiddenOrderVideos" class="popup_content">
			<view class="flex">
				<video id="myVideo" @error="onVideoError" :src="videoUrl"
					style="width:100%;height: 372rpx; margin: 10rpx 10rpx;"></video>
			</view>
			<view class="flex" style="margin-top: 10rpx;">
				<view class="marleft">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' bgColor='#2C6FF3' @tap="playVideo(0)">主视频
					</xbutton>
				</view>
				<view class="marleft">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' bgColor='#2C6FF3' @tap="playVideo(1)">副视频
					</xbutton>
				</view>
				<view class="marleft" v-show="selOrder.riskType=='18'">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' bgColor='#2C6FF3' @tap="playVideo(10)">
						超时视频
					</xbutton>
				</view>
				<view class="marleft">
					<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' bgColor='#2C6FF3' @tap="shareVideo">转发视频
					</xbutton>
				</view>
			</view>
		</view>
		<view class="popup_overlay" :hidden="hiddenOrderVideos" @click="closeVideoView()"></view> -->

		<!-- 查看视频 -->
		<!-- 支付宝柜子支持视频捞取 -->
		<xvideoPopup v-if="videoShow" :show="videoShow" :orderId='item.orderId' :deviceId="item.deviceId" :createTime="item.createTime" :activityId="item.activityId" :showMuteBtn="false" :urls="videoUrls"
			@timeupdate="timeupdate" @close="videoClose" @open="videoOpen" @playError="playError"
			:isAli="item.payQueryOrderId.indexOf('visionpay')!=-1">
		</xvideoPopup>

		<xpopup :show="blockShow" @close="blockClose" @confirm="submit" :showBtn="true" :title="popTitle">
			<!-- 拉黑 -->
			<view class="pop-content restart" v-if="popTitle=='拉黑'">
				<view>
					是否确定拉黑该用户?
				</view>
				<view style="margin-top: 24rpx;">
					<u--textarea v-model="banReason" placeholder="请输入拉黑原因"></u--textarea>
				</view>
			</view>
			<!-- 撤回 -->
			<view class="pop-content restart" v-if="popTitle=='撤回'">
				是否确定撤回该申请?
			</view>
		</xpopup>
		<xpopup :show="finishOrderDetail.show" @close="blockClose" @confirm="finshOrder" :showBtn="true" title="结束订单">
			<view class="pop-content">
				<view class="text-notice">
					警告：订单结束后，将不能再进行补扣
				</view>
				<view class="input">
					<u--textarea placeholder="请输入结束原因" border="surround" v-model="finishOrderDetail.reason" count
						maxlength="140"></u--textarea>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		cancelOrder,
		page,
		todoNum,
		rollback
	} from "@/api/order/riskorder.js"
	import {
		sendCommand
	} from "@/api/device/device.js"
	import {
		setBlacklist,
		page as orderPage,
		riskInfo
	} from "@/api/order/order.js"

	export default {
		data() {
			return {
				hiddenOrderVideos: true,
				selOrder: undefined,
				videoUrl: '',
				minDate: '',
				maxDate: '',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				actionSheetShow: false,
				riskOrderList: [],
				loadmoreStatus: 'loadmore',
				exceptionTypeList: [],
				show: false,
				dateStart: '',
				dateEnd: '',
				dateDx: 0,
				// beginTime: '',
				// endTime: '',
				type: '',
				current: 0,
				status: '',
				list: [{
					name: '待处理'
				}, {
					name: '审核中'
				}, {
					name: '已审核'
				}, {
					name: '已结束'
				}, {
					name: '识别中'
				}],
				commList: [],
				orderNum: 0, //异常订单数量
				code: '',
				deviceId: '',
				timeStamp: new Date(), //时间picker显示时间
				orderObj: null,
				goodId: null, //商品id
				finishOrderDetail: {
					show: false,
					reason: "未拿取商品",
					id: undefined
				},
				blockShow: false, //拉黑弹框
				keyword: '',
				fullHeight: 0,
				popTitle: '拉黑',
				banReason: '',

				videoShow: false,
				videoUrls: [],
				orderId: null,
				item: {}
			}
		},

		computed: {
			orderStatus() {
				let status = 1
				switch (this.current) {
					case 0:
						status = 1
						break;
					case 1:
						status = 3
						break;
					case 2:
						status = 2
						break;
					case 3:
						status = 4
						break;
					default:
						break;
				}
				return status
			}
		},

		async onLoad() {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			this.clearStorage()
			this.search()
			uni.$on('refreshData', res => {
				this.search()
			})
		},

		methods: {
			async actionSheet() {
				if (typeof this.exceptionTypeList == 'object' && this.exceptionTypeList == []) {
					await this.getDict('order_risk_type').then(res => {
						let newData = []
						res.forEach(item => {
							newData.push({
								name: item.msg,
								code: item.code
							})
						})
						this.exceptionTypeList = newData;
					})
				}
				this.actionSheetShow = true
			},

			// 清除缓存
			clearStorage() {
				uni.setStorageSync('riskGoodsTotal', '')
				uni.setStorageSync('riskNewGoodIds', '')
				uni.setStorageSync('riskOrderVideo', '')
				uni.setStorageSync('riskOrderGoods', '')
			},

			// function getLastMonth() {
			//     var date = new Date();
			//     var year = date.getFullYear();   //当前年：四位数字
			//     var month = date.getMonth();     //当前月：0-11

			//     if (month == 0) {   //如果是0，则说明是1月份，上一个月就是去年的12月
			//         year -= 1;
			//         month = 12;
			//     }

			//     month = month < 10 ? ('0' + month) : month;   //月份格式化：月份小于10则追加个0

			//     let lastYearMonth = year + '-' + month;

			//     return lastYearMonth;
			// },
			changeMonth(add) {
				this.dateDx += add;
				var date = new Date();
				var year = date.getFullYear(); //当前年：四位数字
				var month = date.getMonth() + 1 + this.dateDx; //当前月：0-11
				var yeardx = Math.ceil(month / 12) - 1
				month %= 12;
				if (month <= 0) month += 12;
				year += yeardx;
				month = month < 10 ? ('0' + month) : month;

				this.dateStart = year + '-' + month + '-01';

				var day = 28;
				if (2 == month) {
					if (year % 4 == 0) {
						day = 29;
					}
				} else {
					if (month < 8) {
						if (1 == month % 2) {
							day = 31;
						} else {
							day = 30;
						}
					} else {
						if (1 == month % 2) {
							day = 30;
						} else {
							day = 31;
						}
					}
				}
				this.dateEnd = year + '-' + month + '-' + (day < 10 ? ('0' + day) : day);
				this.search()
			},
			getOrderNum() {
				todoNum().then(res => {
					let orderNum = res.data
					if (res.code == 200) {
						this.orderNum = res.data
					} else {
						this.orderNum = 0
					}
				})
			},

			loadMore(e) {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
			sectionChange(e) {
				this.current = e.index
				this.search()
			},

			// 搜索列表
			search() {
				this.reset();
				this.getpage()
				this.getOrderNum()
			},

			call(tell) {
				uni.makePhoneCall({
					phoneNumber: tell
				})
			},
			actionsheetSelect(e) {
				this.status = e.name
				this.code = e.code
				this.search()
			},
			copy(text) {
				uni.setClipboardData({
					data: text,
					success: (data) => {
						uni.showToast({
							title: '复制成功'
						})
					},
					fail: function(err) {

					},
					complete: function(res) {

					}
				})
			},
			dateSelect(type) {
				if (type == 'start') {
					this.type = type
				}
				if (type == 'end') {
					this.type = type
				}
				this.show = true
			},
			confirm(e) {
				console.log(e)
				if (this.type == 'start') {
					this.dateStart = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				}
				if (this.type == 'end') {
					this.dateEnd = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')

				}
				if (this.dateStart && this.dateEnd) {
					if (this.dateStart > this.dateEnd) {
						uni.$u.toast('开始日期不能大于结束日期')
						return;
					}
				}

				this.search()
				this.show = false
			},
			close() {
				this.show = false
			},
			supplementaryDeduction(item) {
				uni.setStorageSync('riskGoodsTotal', '')
				let type = item.bendPayScore ? 1 : 2 //1为扣款，2为补单	
				this.$tab.navigateTo(`/pages/order/riskOrderDel?id=${item.id}&deviceId=${item.deviceId}&type=${type}`)
			},
			getpage() {
				if (this.current == '4') { //识别中
					orderPage({
						page: {
							current: this.page,
							size: this.size,
						},
						identifying: true //识别中订单
					}).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.commList = this.commList.concat(data)
					})
				} else {
					page({
						page: {
							current: this.page,
							size: this.size,
						},
						search: this.keyword,
						riskType: this.code, //异常类型
						status: this.orderStatus, //状态
						beginDate: this.dateStart,
						endDate: this.dateEnd
					}).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.commList = this.commList.concat(data)
					})
				}

			},

			reset() {
				this.loadmoreStatus = 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},
			showlogs(activtyId) {
				this.$tab.navigateTo('/pages/order/orderLogs?id=' + activtyId);
			},

			// showVideoView(item) {
			// 	console.log(item);
			// 	this.hiddenOrderVideos = false;
			// 	this.selOrder = item;
			// 	var urls = item.video.split(',');
			// 	this.selOrder.url0 = this.$xy.cdnUrl(urls[0]);
			// 	this.selOrder.url1 = this.$xy.cdnUrl(urls[1]);
			// 	this.playVideo(0);
			// },

			/**
			 * 查看视频
			 */
			showVideoView(item) {
				this.videoShow = true;
				this.item = item
				let urls = []
				if (item.video) {
					urls = item.video.indexOf(',') != -1 ? item.video.split(',') : [item.video,
						undefined
					]
				}

				this.videoUrls = [
					this.$xy.cdnUrl(urls[0]),
					this.$xy.cdnUrl(urls[1])
				]
				if (item.riskType == 18) { //有超时视频
					console.log('有超时视频')
					var url = this.videoUrls[0].replace("sjgFile", "uploadFiles");
					var idx = url.lastIndexOf("/") + 1;
					var name = item.orderCreateTime.substring(0, 10) + "_" + url.substring(idx).replace("T_c00",
						"T_c01");
					let overTimeUrl = url.substring(0, idx) + name;
					this.videoUrls.push(overTimeUrl);
				}
				console.log(this.videoUrls)
			},


			async playError(e) {
				let url = this.videoUrls[e]
				if (url.indexOf("T_c01") >= 0) {
					var templet = {};
					var idx = url.lastIndexOf("/") + 1;
					var name = url.substring(idx);
					var time = this.item.orderCreateTime.substring(0, 10);
					if (!name.startsWith(time)) {
						name = this.item.orderCreateTime.substring(0, 10) + "_" + url.substring(idx);
					}
					//console.log("/storage/emulated/0/aiShj/videos/old_backs/"+name)
					await this.getDict('mqtt_cmd_templet_task', 'file').then(res => {
						console.log(res);
						templet = JSON.parse(res[0].value);
						templet.data.task = "upload";
						templet.data.file = "/storage/emulated/0/aiShj/videos/old_backs/" + name;
					});
					console.log(templet);
					await sendCommand([{
						deviceId: this.selOrder.deviceId,
						templet: templet
					}]).then(res => {
						this.$modal.showToast('正在捞取机器视频，请稍后再重新打开查看~')
						reSn = res.data[0].v1;
					});
				}
			},


			timeupdate(e) {

			},

			videoClose() {
				this.videoShow = false
			},

			videoOpen() {
				this.videoShow = true
			},

			block(e) {
				this.popTitle = '拉黑'
				this.orderObj = e
				this.blockShow = true;
			},

			// 关闭弹框
			blockClose(e) {
				this.blockShow = false;
				this.finishOrderDetail.show = false;
			},

			// 弹框确定
			submit() {
				if (this.popTitle == '拉黑') {
					setBlacklist({
						memberId: this.orderObj.memberId,
						banReason: this.banReason,
						banOrderId: this.orderObj.orderId,
						banOrderCreateTime: this.orderObj.orderCreateTime
					}).then(res => {
						this.$modal.msg('拉黑成功~')
					}).catch(err => {

					})
				} else {
					rollback({
						riskId: this.goodId
					}).then(res => {
						this.$modal.msg('撤回成功~')
						this.search();
					})
				}

				this.blockClose()
			},
			showFinishOrderView(v) {
				this.finishOrderDetail.id = v.orderId;
				this.finishOrderDetail.reason = "未拿商品";
				this.finishOrderDetail.show = true;
			},
			finshOrder() {
				if (!uni.$u.test.rangeLength(this.finishOrderDetail.reason, [4, 140])) {
					this.$modal.msg('请输入结束原因')
					return;
				}
				cancelOrder({
					"id": this.finishOrderDetail.id,
					"reason": this.finishOrderDetail.reason
				}).then(res => {
					this.$modal.msg('提交申请成功~')
					this.finishOrderDetail.show = false;
					this.getOrderNum();
					this.search();
				})
			},

			// 撤回
			backApply(item) {
				this.popTitle = '撤回'
				this.goodId = item.id
				this.blockShow = true;
			},

			// 设备商品查看
			deviceCom(item) {
				this.$tab.navigateTo(`/pages/equipment/comManage?id=${item.deviceId}&deviceName=${item.deviceName}`)
			},

			shareVideo() {
				this.$xy.shareVideo(this.videoUrl)
			},

			changeStatus(id) {
				let _this=this
				_this.$modal.oldConfirm('是否确认转为异常订单?').then(res => {
					riskInfo({
						ordersId: [id]
					}).then(res => {
						_this.$modal.msg('转异常成功！')
						setTimeout(() => {
							_this.current = 0
							_this.keyword = id
							_this.search()
						}, 1000)
					})
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {

		.marleft24 {
			margin-left: 24rpx;
		}

		.martop {
			margin-top: 20rpx;
		}

		.xian {
			border-bottom: 1px solid #5b5b5b;
			margin: 20rpx 0;
		}

		.marleft {
			margin-left: 12rpx;
		}

		// .time-choose {
		// 	background-color: #fff;

		// 	.top {
		// 		height: 80rpx;
		// 		color: #fff;
		// 		padding: 0 20rpx;
		// 		background-color: #2C6FF3;
		// 		border-radius: 0px 0px 50rpx 50rpx;

		// 		.con-btn {
		// 			padding: 0 36rpx;
		// 		}

		// 		.date-container {
		// 			min-width:380rpx;
		// 			height: 50rpx;
		// 			background-color: #fff;
		// 			padding: 0 20rpx;
		// 			border-radius: 10rpx;
		// 			line-height: 50rpx;
		// 			color: #000;
		// 		}

		// 	}
		// }

		.header-container {
			padding: 27rpx 13rpx 0;
			background-color: #fff;

			.header-text {
				width: 716;
				margin-left: 21rpx;
				height: 52rpx;
				font-size: 28rpx;
				color: #333;
				background: linear-gradient(90deg, #FEE2E2, #FFFFFF);

				>image {
					width: 52rpx;
					height: 52rpx;
					margin-right: 13rpx;
					margin-left: -21rpx;
				}

				text {
					color: #FF0000;
					font-size: 36rpx;
					padding: 0 12rpx;
				}
			}

			.search {
				margin-top: 27rpx;
			}

			.exceptiontype-container {
				background-color: #fff;
				border-radius: 10rpx;
				padding: 0 10rpx;
				width: 140rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				line-height: 50rpx;
			}

			.change-month {
				display: inline-block;
				width: 100rpx;
				height: 45rpx;
				border: 1px solid #CCCCCC;
				border-radius: 6rpx;
				line-height: 43rpx;
				text-align: center;
				border-radius: 10rpx;
				text-align: center;
				font-size: 22rpx;
				color: #777777;
			}
		}

		.scrollview {
			.scroll-content {
				width: 724rpx;
				margin-left: 13rpx;
				overflow: hidden;
			}

			.load-more {
				padding-bottom: 24rpx;
			}

			.empty {
				padding-top: 40%;
			}
		}


		.abnormal-container {
			margin-top: 20rpx;
			padding: 30rpx 32rpx;
			border-radius: 15rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.error-type {
				font-size: 32rpx;
				font-weight: 800;
				color: #333333;

				>image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 22rpx;
				}
			}

			.fx-box {
				margin-top: 24rpx;
				padding-left: 8rpx;
				font-size: 28rpx;
				color: #777;

				.device-name {
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 12rpx;

					>text {
						font-size: 24rpx;
					}
				}

				.under-line-text {
					font-size: 26rpx !important;
					font-weight: 500;
					font-style: italic;
					text-decoration: underline;
					color: #2C6FF3 !important;
					margin-left: 24rpx;
					background-color: #fff !important;
				}

				.oc-name {
					display: inline-block;
					width: 170rpx;
				}

				.phone {
					display: inline-block;
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #2C6FF3;
					padding: 0 16rpx;
					line-height: 38rpx;
					background: #F4F8FF;
					border-radius: 8rpx;
					margin-left: 24rpx;

					>image {
						width: 20rpx;
						height: 20rpx;
						margin-right: 13rpx;
					}
				}

				.goods-container {
					height: 154rpx;
					margin-top: 14rpx;
					padding: 12rpx 12rpx 12rpx 164rpx;
					box-sizing: border-box;
					position: relative;
					border-radius: 8rpx;
					background-color: #f5f8fb;

					.image-container {
						height: 130rpx;
						width: 130rpx;
						position: absolute;
						left: 12rpx;
						top: 50%;
						transform: translateY(-50%);
					}

					.details-container {
						position: relative;
						padding: 12rpx;

						.goods-name-num {
							.goods-name {
								font-size: 26rpx;
								color: #333;
								font-weight: bold;
							}

							.goods-num {
								font-size: 26rpx;
								color: red;
								margin-right: 40rpx;
								font-weight: bold;
							}
						}

						.goods-price {
							font-size: 28rpx;
						}

						.good-act {
							margin-top: 12rpx;
						}

						.gp-item {
							width: 50%;
							margin-top: 12rpx;
						}

						.goodf-act {
							font-size: 28rpx;
							color: #333;
							margin-top: 12rpx;
						}

						.goods-btn {
							margin-top: 12rpx;
						}

						.refund {
							position: absolute;
							right: 0;
							top: 0;
							background-color: red;
							color: #fff;
							border-radius: 4rpx;
							padding: 0 12rpx;
							line-height: 40rpx;
							font-size: 24rpx;
						}
					}

				}
			}

			.image-container {
				view {
					width: 100rpx;
					height: 100rpx;
					background-color: #ccc;
				}
			}

			.orderstatus {
				padding: 20rpx;
				background-color: #43cf7c;
				border-radius: 80rpx;
				color: #fff;
			}
		}

		.re-back {
			background-color: #FFF7F7;
			padding: 10rpx 10rpx 36rpx;
			line-height: 36rpx;
			font-size: 28rpx;
			border-radius: 14rpx;

			.oc-name {
				display: inline-block;
				width: 170rpx;
			}
		}
	}

	.popup_overlay {
		position: fixed;
		top: 0%;
		left: 0%;
		width: 100%;
		height: 100%;
		background-color: black;
		z-index: 1001;
		-moz-opacity: 0.8;
		opacity: .80;
		filter: alpha(opacity=88);
	}

	.popup_content {
		position: fixed;
		top: 50%;
		left: 50%;
		width: 700rpx;
		height: 500rpx;
		margin-left: -350rpx;
		margin-top: -250rpx;
		border: 10px solid white;
		background-color: white;
		z-index: 1002;
		overflow: auto;
	}

	.pop-content {
		padding: 24rpx;
	}

	.text-notice {
		color: red;
		font-size: 28rpx;
		height: 40rpx;
		line-height: 40rpx;
	}

	.input {
		margin-top: 20rpx;
		height: 200rpx;
		width: 100%;
		// border-style: solid;
		// border-width: 1rpx;
		// border-color: #2C6FF3;
		// border-radius: 10rpx;
	}

	.marginTop {
		margin-top: 24rpx;
	}

	.martop12 {
		margin-top: 12rpx;
	}
</style>