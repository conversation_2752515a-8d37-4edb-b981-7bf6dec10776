<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="余额明细"></u-navbar>
		<view class="search flex align-center">
			<u-search animation placeholder="请输入手机号码" :clearabled="true" v-model="keyword" :showAction="false"
				@search="search"></u-search>
			<view class="flex align-center justify-end screen" style="width:230rpx;" @tap="screen">
				<view>{{meal.name||'类型'}}</view>
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png" mode="widthFix">
				</image>
			</view>
		</view>

		<view class="flex align-center justify-end screen-container">
			<view class="flex align-center time" @click="selectTime">
				<view>{{ params.time || "时间" }}</view>
				<view class="arrow">
					<u-icon name="arrow-down-fill" size="12" color="#333"></u-icon>
				</view>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="equipment-container">
						<view class="flex align-center justify-between">
							<view class="title">
								{{item.tel||'-'}}
							</view>
							<view class="status">{{item.typeVal}}</view>
						</view>

						<view class="order-detail-item">
							<view>变动金额：</view>￥{{$xy.delMoney(item.amount)}}
						</view>

						<view class="order-detail-item">
							<view>变动前金额：</view>￥{{$xy.delMoney(item.oldBalance+item.oldBalanceGive)}}
						</view>
						<view class="order-detail-item">
							<view>变动后金额：</view>￥{{$xy.delMoney(item.newBalance+item.newBalanceGive)}}
						</view>
						<view class="order-detail-item">
							<view>备注：</view>{{item.remark||'-'}}
						</view>

						<view class="flex justify-end martop" v-if="item.typeVal=='购物'">
							<view class="marleft24">
								<xbutton padding='0rpx 20rpx' bgColor="#F4F8FF" borderColor="#F4F8FF" color="#2C6FF3"
									@tap='orderDetail(item)'>订单详情
								</xbutton>
							</view>
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>


		<u-action-sheet :show="actionSheetShow" :actions="actions" title="类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
		<u-datetime-picker :show="show" v-model="time" confirmColor="#2C6FF3" mode="date" :closeOnClickOverlay="true"
			@cancel="show = false" @confirm="confirm" @close="show = false"></u-datetime-picker>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		balanceRecordsPage
	} from "@/api/rechargeAct.js"

	export default {
		data() {
			return {
				keyword: '',
				list: [],
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				mode: 'single',
				actionSheetShow: false,
				actions: [],
				title: '',
				fullHeight: 0,
				tabList: [{
						name: '全部用户'
					},
					{
						name: 'vip会员'
					},
				],

				meal: {
					id: null,
					name: null
				},
				params: {
					time: null
				},

				show: false,
				time: new Date(),
				memberId: null
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			if (o.tel && o.tel != 'null') {
				this.keyword = o.tel
			} else {
				this.memberId = o.id
			}

			await this.getDict('member_balance_records_type').then(res => {
				let actions = res.map(i => ({
					name: i.msg,
					value: i.value
				}))
				this.actions = [{
					name: '全部',
					value: ''
				}, ...actions]
			})
			this.getpage()
		},

		methods: {
			confirm(e) {
				this.params.time = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
				this.show = false;
				this.search()
			},

			selectTime() {
				this.show = true;
			},

			actionsheetSelect(e) {
				this.meal = {
					name: e.name,
					id: e.value
				}

				this.search()
			},


			//是否展示黑名单用户
			showBlackChange() {
				this.search()
			},

			//点击筛选
			screen() {
				this.actionSheetShow = true
			},

			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
				}
				let obj = {
					startTime: this.params.time ? this.params.time + ' 00:00:00' : '',
					endTime: this.params.time ? this.params.time + ' 23:59:59' : '',
					tel: this.keyword || null,
					type: this.meal.id,
					memberId: this.memberId
				}
				Object.assign(params, obj)
				balanceRecordsPage(params).then(res => {
					let data = res.data.records;
					for (let item of data) {
						for (let dict of this.actions) {
							if (item.type == dict.value) {
								item.typeVal = dict.name
								break;
							}
						}
					}
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.memberId = null
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			orderDetail(item) {
				this.$tab.navigateTo('/pages/order/orderDetails?id=' + item.orderId)
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		}
	}
</script>
<style scoped lang="scss">
	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;

			.screen {
				padding-right: 12rpx;
			}

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 12rpx;
			}
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.screen-container {
			background-color: #fff;
			padding: 10rpx 13rpx 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;

			.time {
				fontsize: 24rpx;
				line-height: 45rpx;
				background: #F2F2F2;
				border-radius: 14rpx;
				padding: 0 16rpx;
				position: relative;
				color: #333;

				.arrow {
					margin-left: 16rpx;
				}
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 26rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.status {
				color: #2C6FF3;
			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 16rpx;
				color: #777;
				padding-left: 8rpx;

				>view {
					display: inline-block;
				}
			}
		}
	}
</style>