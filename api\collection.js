import request from "@/utils/request";

// 待采集商品列表
export function waitGatherProduct(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/waitGatherProduct",
		method: "post",
		data: data,
	});
}

// 学习中商品列表
export function studyingProduct(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/studyingProduct",
		method: "post",
		data: data,
	});
}

export function getSkuList(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/getSkuList",
		method: "post",
		data: data,
	});
}

export function createProduct(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/createProduct",
		method: "post",
		data: data,
	});
}

export function connectWifi(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/connectWifi",
		method: "post",
		data: data,

	});
}

export function gatherProduct(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/gatherProduct",
		method: "post",
		data: data,
	});
}

export function gatherProducts(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/gatherProducts",
		method: "post",
		data: data,
	});
}

export function endAll(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/endAll",
		method: "post",
		data: data,
	});
}

export function getOkGoodsPageData(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/getOkGoodsPageData",
		method: "post",
		data: data,
	});
}

export function commitTraining(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/commitTraining",
		method: "post",
		data: data,
	});
}

// 开门
export function gatherOpenDoor(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/gatherOpenDoor",
		method: "post",
		data: data,
	});
}

// 开门状态
export function doorStatus(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/doorStatus",
		method: "post",
		data: data,
	});
}

export function endOne(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/endOne",
		method: "post",
		data: data,
	});
}

export function goOnGather(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/goOnGather",
		method: "post",
		data: data,
	});
}

export function connectWifiResult(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/connectWifiResult",
		method: "post",
		data: data,
		hiddenLoading: true
	});
}

export function queryDeviceNetwork(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/queryDeviceNetwork",
		method: "post",
		data: data,
	});
}

export function checkConfig(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/checkConfig",
		method: "post",
		data: data,
	});
}

export function confirmStart(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/confirmStart",
		method: "post",
		data: data,
	});
}

export function gatherError(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/gatherError",
		method: "post",
		data: data,
	});
}

export function uploadInfo(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/uploadInfo",
		method: "post",
		data: data,
	});
}

export function deleteBatchNo(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/deleteBatchNo",
		method: "post",
		data: data,
	});
}

export function auditProductList(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/auditProductList",
		method: "post",
		data: data,
	});
}

export function categoryList(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/categoryList",
		method: "post",
		data: data,
	});
}

export function openGatherMode(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/openGatherMode",
		method: "post",
		data: data,
	});
}

export function waitGatherProductNew(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/waitGatherProductNew",
		method: "post",
		data: data,
	});
}

export function goOnUpload(data) {
	return request({
		url: "/goods/merc-mini/jiangYiApi/goOnUpload",
		method: "post",
		data: data,
	});
}
