<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" bgColor="#2C6FF3" :placeholder="true"
			title="增加商品">
			<view class="u-nav-slot flex align-center" slot="left">
				<u-icon name="arrow-left" color="#fff" size="19" @click="$tab.navigateBack()"></u-icon>
				<!-- 	<view class="com-type" @click="changeType">{{algoTypeName}}</view> -->
			</view>
		</u-navbar>
		<!-- <view class="search" @click.native="searchClick">
			<u-search animation placeholder="商品搜索" disabled v-model="keyword" :showAction="false" search="search">
			</u-search>
		</view> -->

		<view class="search" @click="searchComm">
			<view class="search-input">
				<u-search placeholder="商品搜索" actionText="取消" :actionStyle="{color:'#2C6FF3'}" :showAction="!leftShow"
					:clearabled="false" v-model="keyword" @search="search" @custom="cancle"></u-search>
				<view @click="scan" :class="[leftShow?'scan-icon scan-left-show':'scan-icon scan-left-hidden']">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>
			</view>

			<view class="search-history flex flex-wrap flex-start" v-if="!leftShow">
				<view class="history-item" v-for="(item,index) in historyList" :key="index" @click="searchFast(item)">
					{{item}}
				</view>
			</view>
		</view>

		<view class="xy-card tab-list" style="padding:0 0 24rpx;">
			<view class="tab-left">
				<u-tabs :list="tabList" :scrollable="false" :current="current" @click="tabClick" lineColor="#2C6FF3">
				</u-tabs>
			</view>
			<view class="use-list" @click="$tab.navigateTo(`/pages/commodity/commoditylist?id=${id}`)"
				v-if="checkPermi(['devComBatchAdd'])">
				常用商品清单
			</view>
		</view>
		<view class="content">
			<view class="swiperitem-content" v-if="current==0">
				<view class="classify-wrap">
					<classify storeName="perStor" :selectShow="true" :showId="true" :tabList="perTabList"
						:status="perStatus" :commList="perCommList" @switchMenu="perSwitchMenu"
						@lowerBottom="perLowerBottom" :isModal="true" :height="fullHeight" @comClick="comClick"
						:leftShow="leftShow" />
				</view>
				<view class="btn safe-bottom">
					<xbutton size="large" round="88rpx" @click="addCom">添加到设备</xbutton>
				</view>
			</view>
			<view class="swiperitem-content" v-if="current==1">
				<view class="classify-wrap">
					<classify storeName="pubStor" :selectShow="true" :showId="true" :tabList="pubTabList"
						:status="pubStatus" :commList="pubCommList" @switchMenu="pubSwitchMenu"
						@lowerBottom="pubLowerBottom" :height="fullHeight" :leftShow="leftShow" />
				</view>
				<view class="btn safe-bottom">
					<xbutton size="large" round="88rpx" @click="addCom">添加到设备</xbutton>
				</view>
			</view>
		</view>

		<u-action-sheet :show="actionSheetShow" :actions="actions" title="切换商品种类" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		detail
	} from "@/api/device/device.js"
	import {
		goodsCategory as perGoodsCategory,
	} from "@/api/commodity/mercGoods.js"
	import {
		goodsCategory as pubGoodsCategory,
		pageByGoods,
		pageByGoodsMerc,
		bindDeviceByMercGoods,
		mercAiList
	} from "@/api/commodity/goods.js"
	export default {
		data() {
			return {
				tabList: [{
						name: '私有商品库'
					},
					{
						name: '官方商品库'
					}
				],
				current: 0,
				fullHeight: "0",

				// 私库
				perTabList: [], //商品类目
				perCommList: [], //商品列表
				perPage: 1, //商品分页
				perSize: 10,
				perStatus: 'loadmore', //加载更多

				// 公库
				pubTabList: [], //商品类目
				pubCommList: [], //商品列表
				pubPage: 1, //商品分页
				pubSize: 10,
				pubStatus: 'loadmore', //加载更多

				id: null, //设备id
				categoryCode: null,

				leftShow: true,
				historyList: [],
				keyword: '',
				fromRisk: false,
				algoType: '1',
				algoTypeName: '切换商品库',
				actionSheetShow: false,
				actions: [],
				algorithmId: null
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.classify-wrap', 54)

			if (o.id) {
				this.id = o.id;
			}
			if (o.fromRisk == 'fromRisk') {
				this.fromRisk = true;
			}

			if (uni.getStorageSync('goods')) {
				this.historyList = JSON.parse(uni.getStorageSync('goods'))
			}
			
			//清空缓存
			uni.setStorageSync('perStor', '')
			uni.setStorageSync('pubStor', '')

			// 算法
			await this.getAlgo()

			// 类目tab
			this.getPubCategory()
			await this.getPerCategory()

			// 商品列表
			this.search()
			
			uni.$on('refreshData',res=>{
				this.search()
			})
		},

		methods: {
			// 获取当前设备算法类型
			getAlgo() {
				return new Promise((resolve, reject) => {
					detail({
						deviceId: this.id,
						isSysinfo: false,
						isStatus: false,
						isRegister: false
					}).then(res => {
						this.algorithmId = res.data.algorithmId
						resolve(res.data.algorithmId)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//商家算法列表
			getAlgoList() {
				mercAiList({}).then(res => {
					let data = res.data
					let newData = data.map(i => {
						return {
							type: i.id,
							name: i.alias
						}
					})
					this.actions = newData
				})
			},

			changeType() {
				this.actionSheetShow = true
			},

			actionsheetSelect(e) {
				this.algoType = e.type
				this.algoTypeName = e.name
				this.search()
			},

			searchComm() {
				this.leftShow = false
			},

			search(val) {
				this.saveKeyWord('goods', this.keyword)
				if (this.current == 0) { //私库
					this.categoryCode = this.perTabList[0].categoryCode
					this.perReset();
					this.getPerCommList()
				} else { //公库
					this.categoryCode = this.pubTabList[0].categoryCode
					this.pubReset();
					this.getPubCommList()
				}
			},

			cancle(val) {
				this.keyword = ''
				this.leftShow = true
				this.search()
			},

			saveKeyWord(type, val) {
				if (val) {
					let arr = []
					if (uni.getStorageSync(type)) {
						let arr = JSON.parse(uni.getStorageSync(type))
						if (arr.indexOf(val) != -1) {
							console.log('arr.indexOf(val)', arr.indexOf(val))
							arr.splice(arr.indexOf(val), 1)
						}
						arr.unshift(val)
						if (arr.length > 6) {
							arr.pop()
						}
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					} else {
						arr.unshift(val)
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					}
				} else {
					return
				}
			},

			searchFast(e) {
				this.keyword = e
				this.search()
			},

			//扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.keyword = res.result;
						this.search()
					}
				});
			},

			/************************ 私库方法 ********************************/
			// 搜索商品
			searchClick() {
				let storeName = this.current == 0 ? 'perStor' : 'pubStor'
				this.$tab.navigateTo('/pages/commodity/allGoodsSearch?type=' + this.current + '&storeName=' + storeName +
					'&id=' +
					this.id)
			},

			//获取类目列表
			getPerCategory() {
				return new Promise((resolve, reject) => {
					perGoodsCategory({
						algorithmId: this.algorithmId
					}, false).then(res => {
						if (res.data && res.data.length > 0) {
							this.perTabList = res.data.map(i => {
								if (i.categoryCode == null) {
									i.categoryName = '未定义'
									return i
								} else {
									return i
								}
							});
						}
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//商品类目切换
			perSwitchMenu(item) {
				this.categoryCode = item.categoryCode
				this.perReset()
				this.getPerCommList()
			},

			//根据类目获取商品列表
			getPerCommList() {
				let params = {}
				if (this.leftShow) { //搜索
					params = {
						categoryCode: this.categoryCode,
						page: {
							current: this.perPage,
							size: this.perSize
						},
						deviceId: this.id,
						algorithmId: this.algorithmId
					}
				} else { //非搜索
					params = {
						page: {
							current: this.perPage,
							size: this.perSize
						},
						deviceId: this.id,
						keyword: this.keyword,
						algorithmId: this.algorithmId
					}
				}
				return new Promise((resolve, reject) => {
					pageByGoodsMerc(params).then(res => {
						let data = res.data.records;
						if (data && data.length > 0) {
							data = data.map(i => {
								i.name = i.goodsName;
								i.barcode = i.goodsBarcode;
								i.cover = i.goodsCover;
								i.price = i.price != null ? Number(i.price) / 100 : null;
								i.categoryName = i.categoryCodeName;
								i.noSelect = i.isBind;
								i.showId = i.goodsId;
								return i
							})
						}
						if (data.length < 10) {
							this.perStatus = "nomore"
						} else {
							this.perStatus = "loadmore"
						}
						this.perCommList = this.perCommList.concat(data)

						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//触底加载更多
			perLowerBottom() {
				if (this.perStatus == 'nomore') return
				this.perPage++
				this.getPerCommList()
			},

			//重置
			perReset() {
				this.perStatus == 'loadmore'
				this.perPage = 1;
				this.perSize = 10;
				this.perCommList = [];
			},

			//新建模块商品先需要设置价格
			comClick(e) {
				uni.showModal({
					title: '提示',
					content: '当前商品为新商品需先设置商品价格,是否前往设置?',
					success: res => {
						if (res.confirm) {
							this.$tab.navigateTo('/pages/commodity/comEdit?id=' + e.id)
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},


			/********************** 公库方法 *************************/
			//获取类目列表
			getPubCategory() {
				return new Promise((resolve, reject) => {
					pubGoodsCategory({
						deviceId: this.id,
						algorithmId: this.algorithmId,
					}, false).then(res => {
						if (res.data && res.data.length > 0) {
							this.pubTabList = res.data.map(i => {
								if (i.categoryCode == null) {
									i.categoryName = '未定义'
									return i
								} else {
									return i
								}
							});
						}
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//商品类目切换
			pubSwitchMenu(item) {
				this.categoryCode = item.categoryCode
				this.pubReset()
				this.getPubCommList()
			},

			//根据类目获取商品列表
			getPubCommList() {
				let params = {}
				if (this.leftShow) { //搜索
					params = {
						categoryCodeLevel1: this.categoryCode,
						page: {
							current: this.pubPage,
							size: this.pubSize
						},
						deviceId: this.id,
						algorithmId: this.algorithmId
					}
				} else { //非搜索
					params = {
						page: {
							current: this.pubPage,
							size: this.pubSize
						},
						deviceId: this.id,
						keyword: this.keyword,
						algorithmId: this.algorithmId
					}
				}
				return new Promise((resolve, reject) => {
					pageByGoods(params).then(res => {
						let data = res.data.records;
						let newData = data.map(i => {
							i.categoryCode = i.categoryCodeLevel1;
							i.noSelect = i.isBind;
							i.showId = i.id;
							return i
						})
						if (data.length < 10) {
							this.pubStatus = "nomore"
						} else {
							this.pubStatus = "loadmore"
						}
						this.pubCommList = this.pubCommList.concat(newData)
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//触底加载更多
			pubLowerBottom() {
				if (this.pubStatus == 'nomore') return
				this.pubPage++
				this.getPubCommList()
			},

			//重置
			pubReset() {
				this.pubStatus == 'loadmore'
				this.pubPage = 1;
				this.pubSize = 10;
				this.pubCommList = [];
			},

			tabClick(e) {
				this.current = e.index
				this.search()
			},

			change(e) {
				this.current = e.detail.current
			},

			/**
			 * 添加商品
			 * 私库添加到设备,不需要设置参数
			 */
			addCom() {
				let storeName = this.current == 0 ? 'perStor' : 'pubStor';
				if (uni.getStorageSync(storeName) && JSON.parse(uni.getStorageSync(
						storeName)).length > 0) {
					if (this.current == 0) { //私库
						//添加前，判断是否为风险订单补扣添加商品
						if (this.fromRisk) {
							this.addRiskGoods()
						}

						let commList = JSON.parse(uni.getStorageSync(storeName))
						let goodsIdList = commList.map(i => {
							return i.goodsId
						})
						bindDeviceByMercGoods({
							deviceIds: [this.id],
							goodsIdList: goodsIdList
						}).then(res => {
							if (res.code == 200) {
								uni.setStorageSync(storeName, '') //清空购物车
								uni.$emit('refresh')
								setTimeout(() => {
									// this.$tab.redirectTo(`/pages/equipment/comManage?id=${this.id}`)
									this.$modal.msg('添加成功~')
									uni.navigateBack({
										delta: 1
									})
								}, 200)
							}
						})
					}
					if (this.current == 1) { //公库
						let url = '';
						let type = this.current == 0 ? 1 : 2;
						url =
							`/pages/equipment/addComList?type=${type}&storeName=${storeName}&id=${this.id}&fromRisk=fromRisk`
						this.$tab.navigateTo(url)
					}
				} else {
					uni.$u.toast('请先选择商品！')
					return
				}
			},

			/**
			 * 风险订单添加补扣商品
			 */
			addRiskGoods() {
				let storeName = this.current == 0 ? 'perStor' : 'pubStor';
				if (uni.getStorageSync(storeName) && JSON.parse(uni.getStorageSync(
						storeName)).length > 0) {

					let commList = JSON.parse(uni.getStorageSync(storeName))
					console.log('commList', commList)
					let goodsIdList = commList.map(i => i.goodsId)
					if (uni.getStorageSync('riskNewGoodIds') && JSON.parse(uni.getStorageSync('riskNewGoodIds')).length >
						0) {
						let riskNewGoodIds = JSON.parse(uni.getStorageSync('riskNewGoodIds'))
						console.log('riskNewGoodIds', riskNewGoodIds)
						riskNewGoodIds = riskNewGoodIds.concat(goodsIdList)
						uni.setStorageSync('riskNewGoodIds', JSON.stringify(riskNewGoodIds))
					} else {
						uni.setStorageSync('riskNewGoodIds', JSON.stringify(goodsIdList))
					}
				}
			},

			//私库添加至设备
			perToDev() {
				uni.showModal({
					title: '提示',
					content: '是否确认添加商品',
					success: res => {
						if (res.confirm) {

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
		},
		
		onUnload() {
			uni.setStorageSync('perStor', '')
			uni.setStorageSync('pubStor', '')
			uni.$off('refreshData')
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: calc(100vh);
		/* #ifdef H5 */
		height: calc(100vh - var(--window-top));
		/* #endif */
		display: flex;
		flex-direction: column;

		.u-nav-slot {
			color: #fff;

			.com-type {
				margin-left: 24rpx;
			}
		}

		.search {
			padding: 24rpx 24rpx;
			background-color: #fff;

			.search-input {
				position: relative;

				.scan-icon {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					z-index: 2;

					&.scan-left-show {
						right: 36rpx;
					}

					&.scan-left-hidden {
						right: 100rpx;
					}
				}
			}

			.search-history {

				.history-item {
					margin-right: 24rpx;
					padding: 0 12rpx;
					background-color: #f2f2f2;
					color: #333;
					font-size: 24rpx;
					line-height: 40rpx;
					border-radius: 40rpx;
					margin-top: 24rpx;
				}
			}
		}

		.tab-list {
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-around;

			.tab-left {
				width: 60%;
			}

			.use-list {
				font-size: 30rpx;
				color: #606266;
				line-height: 88rpx;
				padding-right: 24rpx;
			}
		}

		.content {}
	}

	.swiperitem-content {
		// position: relative;
	}

	.btn {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 24rpx;
		padding: 0 24rpx;
	}
</style>