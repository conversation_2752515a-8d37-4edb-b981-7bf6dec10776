<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="报表生成"></u-navbar>
		<view class="content">
			<view class="xy-card section">
				<view class="item flex justify-between align-center">
					<view class="item-left">
						商户
					</view>
					<view class="item-right flex align-center" @click="mercChoose">
						<text class="item-val">{{params.mercName}}</text>
						<u-icon name="arrow-right" color="#777" size="16px"></u-icon>
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left">
						报表名称
					</view>
					<view class="item-right flex align-center" style="width:500rpx;">
						<u--input v-model="params.name" inputAlign="right" placeholder="请输入报表名称" type="text"></u--input>
					</view>

				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left">
						报表类型
					</view>
					<view class="item-right flex align-center">
						<view class="report-type" @click="typeShow=true">
							{{params.typeName}}
						</view>
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left">
						交易设备
					</view>
					<view class="item-right flex align-center">
						<text class="item-val device">{{params.deviceIds.join(',')}}</text>
						<xbutton bgColor="#F4F8FF" color="#2C6FF3" @click="chooseDevice">选择</xbutton>
					</view>
				</view>
				<view class="item flex justify-between align-center"
					v-if="params.type==2||params.type==5||params.type==6">
					<view class="item-left">
						商品
					</view>
					<view class="item-right flex align-center">
						<text class="item-val device">{{params.goodsNames.join(',')}}</text>
						<xbutton bgColor="#F4F8FF" color="#2C6FF3" @click="chooseGoods">选择</xbutton>
					</view>
				</view>
				<!-- <view class="item flex justify-between align-center">
					<view class="item-left">
						开始{{tab}}：
					</view>
					<view class="item-right flex align-center">
						<text class="item-val device">{{params.reportBeginTime}}</text>
						<xbutton bgColor="#F4F8FF" color="#2C6FF3" @click="timeChoose('start')">选择</xbutton>
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left">
						结束{{tab}}：
					</view>
					<view class="item-right flex align-center">
						<text class="item-val device">{{params.reportEndTime}}</text>
						<xbutton bgColor="#F4F8FF" color="#2C6FF3" @click="timeChoose('end')">选择</xbutton>
					</view>
				</view> -->

				<view class="item flex justify-between align-center" v-if="params.type==1">
					<view class="item-left">
						导出类型
					</view>
					<u-radio-group v-model="params.timeType" placement="row">
						<u-radio :customStyle="{marginRight: '40rpx'}" label="订单创建时间" name="1">
						</u-radio>
						<u-radio label="付款时间" name="2">
						</u-radio>
					</u-radio-group>
				</view>
			</view>

			<view v-if="params.type!=8">
				<view class="tab">
					<u-tabs :list="list" @click="tabClick" :current="params.tab" lineColor="#2C6FF3"></u-tabs>
				</view>

				<view class="xy-card section section-time" v-if="params.tab==1">
					<view class="item flex justify-between align-center">
						<view class="item-left">
							开始{{tab}}：
						</view>
						<view class="item-right flex align-center">
							<text class="item-val device">{{params.reportBeginTime}}</text>
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @click="timeChoose('start')">选择</xbutton>
						</view>
					</view>
					<view class="item flex justify-between align-center">
						<view class="item-left">
							结束{{tab}}：
						</view>
						<view class="item-right flex align-center">
							<text class="item-val device">{{params.reportEndTime}}</text>
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @click="timeChoose('end')">选择</xbutton>
						</view>
					</view>
				</view>

				<view class="xy-card section section-time" v-else>
					<view class="item flex justify-between align-center">
						<view class="item-left">
							月份：
						</view>
						<view class="item-right flex align-center">
							<text class="item-val device">{{params.reportBeginTime}}</text>
							<xbutton bgColor="#F4F8FF" color="#2C6FF3" @click="timeChoose('start')">选择</xbutton>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="btn safe-bottom">
			<xbutton width="724rpx" size="large" round="50rpx" delay="2000" @click="submit">生成报表</xbutton>
		</view>

		<u-picker :show="typeShow" :columns="actions" confirmColor="#2C6FF3" :closeOnClickOverlay="true" keyName="msg"
			@close="typeShow=false" @confirm="actionSelect" @cancel="typeShow = false">
		</u-picker>

		<u-picker :show="employeeShow" :columns="employeeActions" confirmColor="#2C6FF3" :closeOnClickOverlay="true"
			keyName="name" @close="employeeShow=false" @confirm="employeeSelect" @cancel="employeeShow = false">
		</u-picker>

		<u-datetime-picker :show="show" v-model="time" confirmColor="#2C6FF3" :mode="params.mode"
			:closeOnClickOverlay="true" @close="close" @confirm="confirm"></u-datetime-picker>

		<!-- 商户选择 -->
		<xtree ref="tkitree" :range="mercTree" :selectParent="true" rangeKey="name" confirmColor="#2C6FF3"
			@confirm="mercSubmit"></xtree>
	</view>
</template>

<script>
	import {
		typs,
		save,
		mercTree
	} from '@/api/report.js'
	import {
		list
	} from "@/api/system/employee.js"


	export default {
		data() {
			return {
				list: [{
					name: '月份',
				}, {
					name: '日期',
				}],
				show: false,
				time: new Date(),
				typeShow: false,
				actions: [],
				employeeShow: false,
				employeeActions: [],

				mercTree: [],

				timeType: '',
				params: {
					name: '',
					mercId: '',
					mercName: '请选择商户',
					type: '',
					typeName: '请选择报表类型',
					deviceIds: [],
					goodsIds: [],
					goodsNames: [],
					reportBeginTime: '',
					reportEndTime: '',
					startTime: '',
					endTime: '',
					tab: 0,
					mode: 'datetime',
					timeType: '1',
				},
			}
		},

		computed: {
			tab() {
				let msg = ''
				switch (this.params.tab) {
					case 0:
						msg = '月份'
						break
					case 1:
						msg = '日期'
						break
					case 2:
						msg = '时间'
						break
					default:
						break
				}
				return msg
			}
		},

		async onLoad(o) {
			await this.getTypes()
			await this.getMercTree()
			if (o.type) {
				this.params.type = o.type
				for (let i = 0; i < this.actions[0].length; i++) {
					let item = this.actions[0][i]
					if (item.type == o.type) {
						this.params.typeName = item.msg
					}
				}
			} else {
				if (uni.getStorageSync('report')) {
					this.params = JSON.parse(uni.getStorageSync('report'))
				}
			}

			console.log('merc', this.mercTree)
			if (!this.params.mercId) {
				this.params.mercId = this.mercTree[0].id
				this.params.mercName = this.mercTree[0].name
			}

			uni.$on('update', res => {
				console.log(res)
				this.params.deviceIds = res.deviceIds
				this.params.goodsIds = res.goodsIds
				this.params.goodsNames = res.goodsNames
				this.$forceUpdate()
			})

			// this.getEmployee()
		},

		watch: {
			params: {
				handler(newVal, oldVal) {
					uni.setStorageSync('report', JSON.stringify(newVal))
				},
				deep: true
			},

			'params.typeName'(newValue, oldValue) {
				if (!this.params.name) {
					this.params.name = `${this.params.typeName}(${uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM:ss')})`
				}
			}
		},

		methods: {
			//获取分页
			// getEmployee() {
			// 	list({}).then(res => {
			// 		this.employeeActions = [res.data]
			// 	})
			// },
			mercChoose() {
				this.$refs.tkitree._show();
			},

			employeeSelect(e) {
				this.params.mercName = e.value[0].name
				this.params.mercId = e.value[0].userId
				this.employeeShow = false
			},

			getTypes() {
				return new Promise((resolve, reject) => {
					typs().then(res => {
						this.actions = [res.data]
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			// 下拉选择
			actionSelect(e) {
				console.log(e)
				this.params.type = e.value[0].type
				this.params.typeName = e.value[0].msg
				this.typeShow = false
			},
			
			tabClick(e) {
				this.params.tab = e.index
				this.params.reportBeginTime = ''
				this.params.reportEndTime = ''
				this.params.startTime = ''
				this.params.endTime = ''
			},

			timeChoose(e) {
				this.timeType = e
				switch (this.tab) {
					case '月份':
						this.params.mode = 'year-month'
						break
					case '日期':
						this.params.mode = 'date'
						break
					case '时间':
						this.params.mode = 'datetime'
						break
					default:
						break
				}
				this.show = true
			},

			close() {
				this.show = false
			},

			chooseDevice() {
				if (this.params.mercId) {
					this.$tab.navigateTo('/pages/report/chooseDevice')
				} else {
					this.$modal.msg('请先选择商户！')
				}
			},

			chooseGoods() {
				if (this.params.mercId && this.params.deviceIds) {
					this.$tab.navigateTo('/pages/report/chooseGoods')
				} else {
					this.$modal.msg('请先选择设备及商户！')
				}
			},

			confirm(e) {

				let timeFormat = ''
				switch (this.params.tab) {
					case 0:
						timeFormat = 'yyyy-mm'
						break
					case 1:
						timeFormat = 'yyyy-mm-dd'
						break
					case 2:
						timeFormat = 'yyyy-mm-dd hh:MM:ss'
						break
					default:
						break
				}

				if (this.params.tab == 1) {
					if (this.timeType == 'start') {
						if (this.params.reportEndTime != '' && new Date(uni.$u.timeFormat(e.value, timeFormat)).getTime() >
							new Date(this.params.reportEndTime).getTime()) {
							this.$modal.msg('开始时间不能大于结束时间！')
							return
						}
					} else {
						if (this.params.reportBeginTime != '' && new Date(this.params.reportBeginTime).getTime() >
							new Date(uni
								.$u.timeFormat(e.value, timeFormat)).getTime()) {
							this.$modal.msg('开始时间需不大于结束时间！')
							return
						}
					}
				}

				if (this.timeType == 'start') {
					if (this.params.tab == 0) {
						this.params.reportBeginTime = uni.$u.timeFormat(e.value, timeFormat)
						this.params.reportEndTime = uni.$u.timeFormat(e.value, timeFormat)
						this.params.startTime = uni.$u.timeFormat(e.value, timeFormat) + '-01'
						this.params.endTime = this.getMMlastDay(uni.$u.timeFormat(e.value, timeFormat))
					} else {
						this.params.reportBeginTime = uni.$u.timeFormat(e.value, timeFormat)
						this.params.startTime = this.params.tab == 0 ? uni.$u.timeFormat(e.value, timeFormat) + '-01' : uni
							.$u.timeFormat(e.value, timeFormat)
					}
				} else {
					this.params.reportEndTime = uni.$u.timeFormat(e.value, timeFormat)
					this.params.endTime = uni.$u.timeFormat(e.value, timeFormat)
				}
				this.show = false
			},

			getMMlastDay(mm) {
				// mm=2022-06
				let arr = mm.split('-')
				let lastDay = new Date(arr[0], arr[1], 0).getDate()
				return mm + '-' + lastDay
			},

			getMercTree() {
				return new Promise((resolve, reject) => {
					mercTree({}).then(res => {
						this.mercTree = res.data
						resolve(res)
					})
				})
			},

			mercSubmit(e) {
				this.params.mercId = e[0].id
				this.params.mercName = e[0].name
				this.params.deviceIds = []
				this.params.goodsIds = []
				this.mercShow = false
			},

			submit() {
				console.log(this.params.timeType)
				if (this.validateParams()) return //校验参数
				let params = {
					"deviceIds": this.params.deviceIds,
					"goodsIds": this.params.type == 2 || this.params.type == 5 || this.params.type == 6 ? this.params
						.goodsIds : [],
					"mercId": this.params.mercId,
					"name": this.params.name,
					"reportBeginTime": this.params.type != 8 ? this.params.startTime : uni.$u.timeFormat(new Date()
						.getTime(), 'yyy-mm-dd'),
					"reportEndTime": this.params.type != 8 ? this.params.endTime : uni.$u.timeFormat(new Date()
						.getTime(), 'yyy-mm-dd'),
					"type": this.params.type,
					"timeType": this.params.timeType
				}
				save(params).then(res => {
					this.$modal.showToast('报表生成中！')
					setTimeout(() => {
						uni.navigateBack()
					}, 500)
				})
			},

			validateParams() {
				if (!this.params.name) {
					this.$modal.showToast('请输入报表名称！')
					return true
				}
				// if (this.params.type != 5 && this.params.deviceIds.length == 0) {
				// 	this.$modal.showToast('请选择设备！')
				// 	return true
				// }
				// if ((this.params.type == 2 || this.params.type == 5 || this.params.type == 6) && this.params.length == 0) {
				// 	this.$modal.showToast('请选择商品！')
				// 	return true
				// }
				if (this.params.type != 8) {
					if (!this.params.reportBeginTime || !this.params.reportEndTime) {
						this.$modal.showToast('请选择开始或结束时间！')
						return true
					}
				}
				return false
			}
		},

		onUnload() {
			uni.$off('update')
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		::v-deep .u-border {
			border-color: transparent !important;
			text-align: right;
		}
		
		::v-deep .u-radio-group--row{
			justify-content: flex-end;
		}

		.content {
			padding: 0 13rpx;

			.section {
				padding: 24rpx 40rpx 0;

				.item {
					line-height: 28rpx;
					padding: 25rpx 0 30rpx;
					height: 130rpx;

					&:not(:last-child) {
						border-bottom: 1rpx solid #D8D8D8;
					}

					.item-left {}

					.item-right {

						.item-val {
							color: #555;
							display: inline-block;
							max-width: 362rpx;
							text-align: right;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.report-type {
							text-decoration: underline;
							color: #2C6FF3;
							font-style: oblique;
						}

						.device {
							margin-right: 24rpx;
						}
					}
				}
			}

			.tab {
				width: 300rpx;
				margin-top: 24rpx;
			}

			.section-time {
				margin-top: 12rpx;
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}

		.popup-content {
			padding: 0 36rpx;
		}
	}
</style>