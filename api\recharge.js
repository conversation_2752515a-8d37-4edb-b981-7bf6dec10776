import request from "@/utils/request";

/**
 * 设备管理费列表
 * @param {Object} data
 */
export function rechargeList(data) {
  return request({
    url: "/device/merc-mini/device-charging/page",
    method: "post",
    data: data,
  });
}

/**
 * 充值
 * @param {Object} data
 */
export function rechargeCreate(data) {
  return request({
    url: "/order/order-merc-manage/create",
    method: "post",
    data: data,
  });
}

/**
 * 流量卡充值
 * @param {Object} data
 */
export function elseRechargeCreate(data) {
  return request({
    url: "/order/order-deviceSim-mini/create",
    method: "post",
    data: data,
  });
}

/**
 * 激活反显
 * @param {Object} data
 */
export function payCheck(data) {
  return request({
    url: "/device/device-charging/payCheck",
    method: "post",
    data: data,
  });
}

/**
 * 收支明细
 * @param {Object} data
 */
export function payRecord(data) {
  return request({
    url: "/merc/merc-balance-records/page",
    method: "post",
    data: data,
  });
}

/**
 * 设备卡包分页
 * @param {Object} data
 */
export function devCardPackList(data) {
  return request({
    url: "/device/merc-mini/device-algorithm-charging/page",
    method: "post",
    data: data,
  });
}

/**
 * 设备管理费明细
 * @param {Object} data
 */
export function costDevDetailList(data) {
  return request({
    url: "/device/merc-mini/device-charging-history/page",
    method: "post",
    data: data,
  });
}

/**
 * 余额查询
 * @param {Object} data
 */
export function account(data) {
  return request({
    url: "/merc/merc-account/obj",
    method: "post",
    data: data,
  });
}

/**
 * 微信充值
 * @param {Object} data
 */
export function wxAppPay(data) {
  return request({
    url: "/order/jssdk-pay/wxAppPay",
    method: "post",
    data: data,
  });
}

/**
 * 算法费明细
 * @param {Object} data
 */
export function costAlgoDetailList(data) {
  return request({
    url: "/merc/merc-device-algorithm-charging/mercPage",
    method: "post",
    data: data,
  });
}

/**
 * 费用明细列表
 * @param {Object} data
 */
export function algorithmChargingDevice(data) {
  return request({
    url: "/device/merc-mini/device/algorithmChargingDevice",
    method: "post",
    data: data,
  });
}

/**
 * 流量卡列表
 * @param {Object} data
 */
export function simFeeList(data) {
  return request({
    url: "/device/merc-mini/device-sim/page",
    method: "post",
    data: data,
  });
}

/**
 * 短信统计
 * @param {Object} data
 */
export function smsTotal(data) {
  return request({
    url: "/merc/merc-sms-package/byId",
    method: "post",
    data: data,
  });
}

/**
 * 短信使用流水
 * @param {Object} data
 */
export function pageMerc(data) {
  return request({
    url: "/merc/merc-sms-charging/pageMerc",
    method: "post",
    data: data,
  });
}

/**
 * 手机号剩余数量
 * @param {Object} data
 */
export function telNum(data) {
  return request({
    url: "/merc/merc-member-tel-package/mercObj",
    method: "post",
    data: data,
  });
}

/**
 * 手机号购买记录
 * @param {Object} data
 */
export function telBuy(data) {
  return request({
    url: "/merc/merc-member-tel-package-orders/mercPage",
    method: "post",
    data: data,
  });
}

/**
 * 手机号使用记录
 * @param {Object} data
 */
export function telUse(data) {
  return request({
    url: "/merc/merc-member-tel-package-bill/mercPage",
    method: "post",
    data: data,
  });
}

/**
 * 自动续费反显
 * @param {Object} data
 */
export function merObj(data) {
  return request({
    url: "/merc/merc-config/merObj",
    method: "post",
    data: data,
  });
}

/**
 * 流量卡购买记录
 * @param {Object} data
 */
export function dataCardList(data) {
  return request({
    url: "/order/order-deviceSim-mini/page",
    method: "post",
    data: data,
  });
}

/**
 * 自动续费设置
 * @param {Object} data
 */
export function updateMerConfig(data) {
  return request({
    url: "/merc/merc-config/updateMerConfig",
    method: "post",
    data: data,
  });
}

/**
 * 年费列表
 * @param {Object} data
 */
export function yearFeeList(data) {
  return request({
    url: "/device/device-annual-fee/mercPage",
    method: "post",
    data: data,
  });
}
