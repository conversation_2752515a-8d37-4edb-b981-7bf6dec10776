export default {
	computed:{
		share(){
			let appName = ''
			switch (uni.getAccountInfoSync().miniProgram.appId) {
				case 'wxe875ab8368fdbe57':
					appName = '小白商家助手'
					break;
				case 'wxde6edfb4b2c1e7db':
					appName = '喵星人商家助手'
					break;
			}
			let obj={
				title: appName,
				path: '/pages/login', // 全局分享的路径
				imageUrl: 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/merc_qrcode.jpg', // 全局分享的图片(可本地可网络)
			}
			return obj
		}
	},
	
	// 定义全局分享
	// 1.发送给朋友
	onShareAppMessage(res) {
		return {
			title: this.share.title,
			path: this.share.path,
			imageUrl: this.share.imageUrl,
		}
	},
	//2.分享到朋友圈
	onShareTimeline(res) {
		return {
			title: this.share.title,
			path: this.share.path,
			imageUrl: this.share.imageUrl,
		}
	}
}