import request from '@/utils/request'
// 新增修改线路
export function lineSave(data) {
  return request({
    url: '/merc/mini/mercLine/save',
    method: 'post',
	data:data
  })
}

// 分页
export function linePage(data) {
  return request({
    url: '/merc/mini/mercLine/page',
    method: 'post',
	data:data
  })
}

// 绑定/解绑设备 
export function bindDevice(data) {
  return request({
    url: '/merc/mini/mercLine/bindDevice',
    method: 'post',
    data:data
  })
}

// 列表(内部服务调用) 
export function listById(data) {
  return request({
    url: '/merc/mini/mercLine/listById',
    method: 'post',
    data:data
  })
}

// 列表
export function lineListByMerc(data) {
  return request({
    url: '/merc/mini/mercLine/listByMerc',
    method: 'post',
    data:data
  })
}

// 删除
export function lineDel(data) {
  return request({
    url: '/merc/mini/mercLine/del',
    method: 'post',
    data:data
  })
}


