import request from "@/utils/request";
import { downLoadReq } from "@/utils/request";

// 补货首页
export function supplyPage(data) {
  return request({
    url: "/goods/merc-mini/goodsDevice/supplyPage",
    method: "post",
    data: data,
  });
}

// H5补货首页
export function supplyH5List(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/supplyH5List",
    method: "post",
    data: data,
  });
}

//补货删除
export function delGoods(data) {
  return request({
    url: "/goods/merc-mini/goodsDevice/delGoods",
    method: "post",
    data: data,
  });
}
// 补货保存
export function save(data) {
  return request({
    url: "/merc/mini/mercSupply/save",
    method: "post",
    data: data,
  });
}

// h5补货保存
export function h5save(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/supplyH5UpdateStock",
    method: "post",
    data: data,
  });
}

// 一键开柜
export function saveByOpenDoor(data) {
  return request({
    url: "/merc/mini/mercSupply/saveByOpenDoor",
    method: "post",
    data: data,
  });
}

//补货记录分页查询
export function page(data) {
  return request({
    url: "/merc/mini/mercSupply/page",
    method: "post",
    data: data,
  });
}
//补货记录对象查询
export function obj(data) {
  return request({
    url: "/merc/mini/mercSupply/obj",
    method: "post",
    data: data,
  });
}

// 效验
export function check(data) {
  return request({
    url: "/order/activity-info/check",
    method: "post",
    data: data,
  });
}
// 按线路+商品分组
export function stockByLineAndGoods(data) {
  return request({
    url: "/goods/merc-mini/stockData/stockByLineAndGoods",
    method: "post",
    data: data,
  });
}

// 按设备+商品分组
export function stockByDeviceAndGoods(data) {
  return request({
    url: "/goods/merc-mini/stockData/stockByDeviceAndGoods",
    method: "post",
    data: data,
  });
}

// 按设备分组
export function stockByDevice(data) {
  return request({
    url: "/goods/merc-mini/stockData/stockByDevice",
    method: "post",
    data: data,
  });
}

// 按商品分组
export function stockByGoods(data) {
  return request({
    url: "/goods/merc-mini/stockData/stockByGoods",
    method: "post",
    data: data,
  });
}

// 按线路分组
export function stockByLine(data) {
  return request({
    url: "/goods/merc-mini/stockData/StockByLine",
    method: "post",
    data: data,
  });
}

// 补货首页全部列表
export function deviceStockList(data) {
  return request({
    url: "/goods/merc-mini/goodsDevice/deviceStockList",
    method: "post",
    data: data,
  });
}

// 补货首页缺货列表
export function deviceStockOutList(data) {
  return request({
    url: "/goods/merc-mini/goodsDevice/deviceStockOutList",
    method: "post",
    data: data,
  });
}

// 创建补货订单
export function create(data) {
  return request({
    url: "/order/activity-info/create",
    method: "post",
    data: data,
  });
}

// 库存详情设备
export function stockByDeviceDetail(data) {
  return request({
    url: "/goods/merc-mini/stockData/stockByDeviceDetail",
    method: "post",
    data: data,
  });
}

// 库存详情商品
export function stockByGoodsDetail(data) {
  return request({
    url: "/goods/merc-mini/stockData/stockByGoodsDetail",
    method: "post",
    data: data,
  });
}

/**
 * 库存导出-按设备
 * @param {Object} data
 */
export function exportStockByDevice(data, name) {
  return downLoadReq({
    url: "/goods/merc-mini/stockData/exportStockByDevice",
    method: "post",
    data: data,
    name: name,
  });
}

/**
 * 库存导出-按商品
 * @param {Object} data
 */
export function exportStockByGoods(data, name) {
  return downLoadReq({
    url: "/goods/merc-mini/stockData/exportStockByGoods",
    method: "post",
    data: data,
    name: name,
  });
}

/**
 * 订单导出-按设备
 * @param {Object} data
 */
export function exportOrdersDetailList(data, name) {
  return downLoadReq({
    url: "/order/order-merc-device-order/exportOrdersDetailList",
    method: "post",
    data: data,
    name: name,
  });
}

/**
 * 修改库存（盘点）
 * @param {Object} data
 */
export function stockFixed(data) {
  return request({
    url: "/goods/merc-mini/GoodsInventory/save",
    method: "post",
    data: data,
  });
}

/**
 * 盘点记录
 * @param {Object} data
 */
export function physicalList(data) {
  return request({
    url: "/goods/goods-inventory-records/page",
    method: "post",
    data: data,
  });
}

/**
 * 盘点详情
 * @param {Object} data
 */
export function physicalDetail(data) {
  return request({
    url: "/goods/goods-inventory-records/obj",
    method: "post",
    data: data,
  });
}

/**
 * 补货记录
 * @param {Object} data
 */
export function totalRep(data) {
  return request({
    url: "/merc/mini/mercSupply/listByDay",
    method: "post",
    data: data,
  });
}

/**
 * 盘点记录
 * @param {Object} data
 */
export function goodsRep(data) {
  return request({
    url: "/goods/merc-mini/GoodsInventory/listByDay",
    method: "post",
    data: data,
  });
}

/**
 * 库存变化记录
 * @param {Object} data
 */
export function listByDeviceGoods(data) {
  return request({
    url: "/goods/merc-mini/goodsStockRecords/listByDeviceGoods",
    method: "post",
    data: data,
  });
}

/**
 * 盘点
 * @param {Object} data
 */
export function saveBySnapShoot(data) {
  return request({
    url: "/goods/merc-mini/GoodsInventory/saveBySnapShoot",
    method: "post",
    data: data,
  });
}

/**
 * 补货导出
 * @param {Object} data 参数
 * @param {String} name 文件名称
 */
export function repExport(data, name) {
  return downLoadReq({
    url: "/merc/mini/mercSupply/excelByDay",
    method: "post",
    data: data,
    name: name,
  });
}

/**
 * 盘点导出
 * @param {Object} data 参数
 * @param {String} name 文件名称
 */
export function thysicExport(data, name) {
  return downLoadReq({
    url: "/goods/goodsInventoryService/excelByDay",
    method: "post",
    data: data,
    name: name,
  });
}

/**
 * 补货图片列表
 * @param {Object} data 参数
 */
export function imgList(data) {
  return request({
    url: "/merc/mini/mercSupplyImg/page",
    method: "post",
    data: data,
  });
}

/**
 * 补货图片新增
 * @param {Object} data 参数
 */
export function imgSave(data) {
  return request({
    url: "/merc/mini/mercSupplyImg/save",
    method: "post",
    data: data,
  });
}

/**
 * 上传人员列表
 * @param {Object} data 参数
 */
export function createUserList(data) {
  return request({
    url: "/merc/mini/mercSupplyImg/createUserList",
    method: "post",
    data: data,
  });
}

/**
 * 盘点列表
 * @param {Object} data 参数
 */
export function pageByGroupDate(data) {
  return request({
    url: "/goods/merc-mini/GoodsInventory/pageByGroupDate",
    method: "post",
    data: data,
  });
}

/**
 * 设备列表
 */
export function simpleDeviceSearchPage(data) {
  return request({
    url: "/device/merc-mini/device/simpleDeviceSearchPage",
    method: "post",
    data: data,
  });
}

/**
 * 同步商品
 */
export function syncGoodsDeviceAisle(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/syncGoodsDeviceAisle",
    method: "post",
    data: data,
  });
}

/**
 * 同步库存
 */
export function syncGoodsStock(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/syncGoodsStock",
    method: "post",
    data: data,
  });
}

/**
 * 修改商品价格
 */
export function modifyGoodsPrice(data) {
  return request({
    url: "/goods/merc-mini/goodsDevice/modifyGoodsPrice",
    method: "post",
    data: data,
  });
}

/**
 * 商品确认
 */
export function goodsRefSet(data) {
  return request({
    url: "/goods/merc-mini/goods/goodsRefSet",
    method: "post",
    data: data,
  });
}

/**
 * 设备货道全揽信息列表
 */
export function aisleInfoList(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/aisleInfoList",
    method: "post",
    data: data,
  });
}

/**
 * 设备商品列表-下拉框
 */
export function deviceGoodsList(data) {
  return request({
    url: "/goods/merc-mini/goodsDevice/deviceGoodsList",
    method: "post",
    data: data,
  });
}

/**
 * 货道商品批量设置
 */
export function batchAddGoods(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/batchAddGoods",
    method: "post",
    data: data,
  });
}

/**
 * 单货道添加商品
 */
export function supplyH5AddGoods(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/supplyH5AddGoods",
    method: "post",
    data: data,
  });
}

/**
 * 整层设置商品
 */
export function setCurLayerGoods(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/setCurLayerGoods",
    method: "post",
    data: data,
  });
}

/**
 * 整层设置库存
 */
export function setCurLayerStock(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/setCurLayerStock",
    method: "post",
    data: data,
  });
}

/**
 * 批量保存补货
 */
export function batchSave(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/batchSave",
    method: "post",
    data: data,
  });
}

/**
 * 货道清理
 */
export function clearAisle(data) {
  return request({
    url: "/goods/merc-mini/goodsDeviceAisle/clearAisle",
    method: "post",
    data: data,
  });
}

/**
 * @param {Object} data
 * 视觉重力柜是否打开
 */
export function devicePart(data) {
  return request({
    url: "/device/device-part/list",
    method: "post",
    data: data,
  });
}
