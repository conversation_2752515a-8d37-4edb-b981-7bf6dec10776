<template>
	<view class="container">
		<u-navbar bgColor="#2C6FF3" :placeholder="true" :autoBack="false">
			<view slot="left" style="color:#fff;font-size: 36rpx;">
				设备管理
			</view>
		</u-navbar>
		<view class="total">
			<view class="total-item" @click="onlineTabChange">
				<view class="num">
					{{totalData.onlineNum}}/{{totalData.offlineNum}}
				</view>
				<view class="name">
					<text :style="{color:searchQuery.onlineStatus=='1'?'#00ff00':''}">在线</text>/<text
						:style="{color:searchQuery.onlineStatus=='2'?'#00ff00':''}">离线</text>
				</view>
			</view>
			<view class="total-item" @click="busyTabChange">
				<view class="num">
					{{totalData.operatingNum}}/{{totalData.closedNum}}
				</view>
				<view class="name">
					<text :style="{color:searchQuery.busyStatus=='1'?'#00ff00':''}">运营</text>/<text
						:style="{color:searchQuery.busyStatus=='2'?'#00ff00':''}">停业</text>
				</view>
			</view>
			<view class="total-item" @click="$tab.navigateTo('/pages/replenish/replenishmentManagement')">
				<view class="num">
					{{totalData.needToFillNum}}
				</view>
				<view class="name">
					待补货
				</view>
			</view>
		</view>
		<view class="search-filter flex justify-between align-item">
			<view class="search">
				<!-- <u-search animation placeholder="请输入设备名称/编号搜索" :showAction="false"
					disabled @click.native="$tab.navigateTo('/pages/equipment/search')">
				</u-search> -->
				<u-search animation placeholder="请输入设备名称/编号搜索" :clearabled="false" v-model="searchQuery.searchKey"
					:showAction="false" @search="search"></u-search>
				<view class="scan-icon" @click="scan">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>
			</view>
			<view class="flex align-center justify-center" @tap="screen">
				<view class="" style="font-size: 28rpx;font-weight: 500;color: #333333;">筛选</view>
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png"
					style="width: 32rpx;height: 32rpx;margin-left: 12rpx;" mode="widthFix"></image>
			</view>
		</view>
		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y :style="{height:fullHeight}">
			<view class="content">
				<view class="xy-card" v-for="(item,index) in list" :key="item.placeLineId">
					<view class="eq-line-title flex align-center">
						<image
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/area-icon.png"
							style="width: 34rpx;height: 34rpx;margin-right: 14rpx;" mode="widthFix"></image>
						<view>
							{{item.adminName}}（{{item.deviceNum}}台）
						</view>
					</view>
					<view class="eq-item" @click="$tab.navigateTo(`/pages/equipment/detail/index?id=${item1.deviceId}`)"
						v-for="(item1,index1) in item.deviceInfos" :key="item1.deviceId">
						<view class="eq-content">
							<view class="eq-wrap">
								<view class="eq-name flex justify-between">
									<view class="eq-title" v-if="item1.deviceName">{{item1.deviceName}}<text
											style="color: #666;">({{item1.deviceId}})</text></view>
									<view class="eq-title" v-else>{{item1.deviceId}}</view>
									<view class="arrow-right">
										<u-icon name="arrow-right" color="#333" size="14"></u-icon>
									</view>
									<!-- <view class="eq-status-box flex align-center">
										<view class="eq-status" v-if="item1.sysPower==2">
											<text></text>已断电
										</view>
										<view class="eq-status" v-else :class="[item1.netState==1?'online':'']">
											<text></text>{{item1.netStateName}}
										</view>
										<view class="eq-status" :class="[item1.busyState==1?'online':'']">
											<text></text>{{item1.busyStateName}}
										</view>
									</view> -->
								</view>

								<view class="types flex align-center">
									<view class="types-item">
										{{item1.deviceTypeName}}
									</view>

									<view class="types-item" v-if="item1.sysPower==2">
										已断电
									</view>
									<view class="types-item" :class="[item1.netState==1?'types-show':'']" v-else>
										{{item1.netStateName}}
									</view>

									<view class="types-item" :class="[item1.busyState==1?'types-show':'']">
										{{item1.busyStateName}}
									</view>

									<view class="types-item" style="color: limegreen;" v-if="item1.isHaveTemp&&item1.tempValue!=null">
										{{item1.tempValue}}℃
									</view>
								<!-- 	<view class="types-item" v-else>
										未知
									</view> -->
								</view>

								<!-- 	<view class="eqeq-type">
									<view>
										资产编号：
									</view>
									<view>
										{{item1.mercDeviceCode||'-'}}
									</view>
								</view> -->

								<view class="eqeq-type">
									<view>
										在售/补后/容量：
									</view>
									<view>
										<text style="color: red;">{{item1.onSaleNum||'-'}}</text>/{{item1.fillNum||'-'}}/{{item1.capacity||'-'}}
									</view>
								</view>

								<view class="sale flex align-center">
									<view class="sale-item flex align-center">
										<view>
											今日交易笔数：
										</view>
										<view>
											{{item1.dayOrderNum}}
										</view>
									</view>
									<view class="sale-item flex align-center">
										<view>
											今日销售额：
										</view>
										<view>
											￥{{$xy.delMoney(item1.daySalesPrice)}}
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<!-- <view class="more" @click="$tab.navigateTo('/pages/equipment/search')">
					查看全部
				</view> -->
				</view>

				<view class="empty" v-if="list.length==0">
					<u-empty></u-empty>
				</view>
			</view>
		</scroll-view>

		<xpopup :show="screenShow" :safeAreaInsetBottom="false" @close="close" @confirm="sure" :showBtn="true" clear="清空" @clearClick="clear"
			title="筛选">
			<view class="popup-container">
				<view class='martop flex align-center'>
					<view class="pop-item-left">
						设备编号：
					</view>
					<view class="pop-item-cont">
						<u--input placeholder="设备编号" v-model="searchQuery.deviceId" border="surround"></u--input>
					</view>
				</view>
				<view class='martop flex align-center' @click="managerChange">
					<view class="pop-item-left">
						管理员：
					</view>
					<view class="pop-item-cont">
						<u--input clearable readonly suffixIcon="arrow-down" v-model="searchQuery.adminName"
							suffixIconStyle="color: #909399" placeholder="管理员" border="surround"></u--input>
					</view>
				</view>
				<view class='martop flex align-center' @click="actionsheetChange">
					<view class="pop-item-left">
						设备类型：
					</view>
					<view class="pop-item-cont">
						<u--input clearable readonly suffixIcon="arrow-down" v-model="searchQuery.deviceTypeName"
							suffixIconStyle="color: #909399" placeholder="设备类型" border="surround"></u--input>
					</view>
				</view>
				<view class='martop flex align-center'>
					<view class="pop-item-left">
						在线状态：
					</view>
					<view class="pop-item-cont">
						<u-subsection :list="['全部','在线','离线']" mode="button" activeColor="#2C6FF3"
							:current="searchQuery.onlineStatus" @change="onlineChange"></u-subsection>
					</view>
				</view>
				<view class='martop flex align-center'>
					<view class="pop-item-left">
						运营状态：
					</view>
					<view class="pop-item-cont">
						<u-subsection :list="['全部','运营','停业']" mode="button" activeColor="#2C6FF3"
							:current="searchQuery.busyStatus" @change="busyChange"></u-subsection>
					</view>
				</view>

				<!-- <view class='martop' @click="actionsheetChange('yyzt')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.busyStatus"
						border="surround" suffixIconStyle="color: #909399" placeholder="运营状态">
					</u--input>
				</view>
				<view class='martop' @click="actionsheetChange('zxzt')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.onlineStatus"
						border="surround" suffixIconStyle="color: #909399" placeholder="在线状态">
					</u--input>
				</view> -->
			</view>
		</xpopup>

		<u-action-sheet :show="actionSheetShow" :actions="actions" :title="title" @cancle="actionSheetShow=false"
			@close="actionSheetShow = false" @select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		mercHomeList,
		mercHomeStatistical,
	} from "@/api/device/device.js"
	import getDict from "@/utils/getDict.js"
	import {
		list
	} from "@/api/system/employee.js"
	export default {
		components: {},
		data() {
			return {
				online: true,
				list: [],
				totalData: {
					closedNum: 0,
					needToFillNum: 0,
					offlineNum: 0,
					onlineNum: 0,
					operatingNum: 0,
					keyWords: ''
				},

				keyWords: '',
				screenShow: false,

				searchQuery: {
					onlineStatus: 0,
					busyStatus: 0,
					deviceType: '',
					deviceName: '',
					deviceId: '',
					searchKey: '',
					adminName: '',
					deviceTypeName: ''
				},

				deviceType: '',

				actionSheetShow: false,
				actions: [],
				title: '',
				deviceTypeActions: [],
				manageerActions: [],
				fullHeight: 0,
			}
		},

		async created() {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview',0)
		},

		methods: {
			refreshData() {
				this.getTotalData()
				this.getList()
			},
			
			onlineTabChange() {
				if (this.searchQuery.onlineStatus == 2) {
					this.searchQuery.onlineStatus = 0
				} else {
					this.searchQuery.onlineStatus++
				}
				this.search()
			},
			busyTabChange() {
				if (this.searchQuery.busyStatus == 2) {
					this.searchQuery.busyStatus = 0
				} else {
					this.searchQuery.busyStatus++
				}
				this.search()
			},
			onlineChange(e) {
				this.searchQuery.onlineStatus = e
				this.search()
			},
			busyChange(e) {
				this.searchQuery.busyStatus = e
				this.search()
			},
			
			search(e) {
				this.getTotalData()
				this.getList()
			},

			//扫码
			scan() {
				this.$xy.scanDevice().then(res => {
					console.log(res)
					this.keyWords = res
					this.search()
				})
			},

			//点击筛选
			screen() {
				this.screenShow = true
				//设备类型
				getDict('device_type').then(res => {
					let actions = res.map(i => {
						return {
							type: i.value,
							name: i.msg
						}
					})
					this.deviceTypeActions = [{
						type: '',
						name: '全部'
					}, ...actions]
				})

				// 管理员
				list({}).then(res => {
					console.log(res.data)
					let data = res.data
					let actions = data.map(i => {
						return {
							type: i.name,
							name: i.name
						}
					})
					this.manageerActions = [{
						type: '',
						name: '全部'
					}, ...actions]
				})
			},

			getTotalData() {
				mercHomeStatistical({}).then(res => {
					this.totalData = res.data;
				})
			},

			getList() {
				let params = {
					onlineStatus: this.searchQuery.onlineStatus == 0 ? '' : this.searchQuery.onlineStatus,
					busyStatus: this.searchQuery.busyStatus == 0 ? '' : this.searchQuery.busyStatus,
					deviceType: this.searchQuery.deviceType,
					deviceName: this.searchQuery.deviceName,
					deviceId: this.searchQuery.deviceId,
					searchKey: this.searchQuery.searchKey,
					adminName: this.searchQuery.adminName
				}
				mercHomeList(params).then(res => {
					this.list = res.data ? res.data : [];
				})
			},

			sure() {
				this.close()
				this.search()
			},

			clear() {
				let obj1 = {
					onlineStatus: '0',
					busyStatus: '0',
					deviceType: '',
					deviceName: '',
					deviceId: '',
					adminName: '',
					deviceTypeName: ''
				}
				for (let key in obj1) {
					this.searchQuery[key] = obj1[key]
				}
				console.log(this.searchQuery)
				this.deviceType = ''
				this.screenShow = false
				this.search()
			},

			close() {
				this.screenShow = false
			},

			actionsheetChange() {
				this.actions = this.deviceTypeActions
				this.title = '请选择设备类型'
				this.actionSheetShow = true
			},

			managerChange() {
				this.actions = this.manageerActions
				this.title = '请选择管理员'
				this.actionSheetShow = true
			},

			actionsheetSelect(e) {
				if (this.title == '请选择设备类型') {
					this.searchQuery.deviceType = e.type
					this.searchQuery.deviceTypeName = e.name
				} else {
					this.searchQuery.adminName = e.type
				}
				this.$forceUpdate()
			},
		}
	}
</script>

<style lang="scss">
	.container {
		.nav-style {
			font-size: 32rpx;
			font-weight: bold;
			color: #fff;
		}


		.search-filter {
			background-color: #fff;
			padding-right: 24rpx;

			.search {
				padding: 24rpx 24rpx;
				position: relative;
				width: 614rpx;

				.scan-icon {
					position: absolute;
					right: 48rpx;
					top: 50%;
					transform: translateY(-50%);
					z-index: 2;
				}
			}

		}


		.total {
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-around;
			align-items: center;
			text-align: center;
			color: #fff;
			background-color: #2C6FF3;
			padding: 40rpx 24rpx;

			.total-item {
				.num {
					font-weight: bold;
					font-size: 52rpx;
				}

				.name {
					font-size: 26rpx;
				}
			}
		}

		.content {
			padding-bottom: calc(124rpx + env(safe-area-inset-bottom) / 2);

			.xy-card {
				margin-bottom: 24rpx;
				background-color: #F4F4F4;
			}

			.eq-line-title {
				font-size: 32rpx;
				padding-bottom: 24rpx;
				position: relative;
				font-weight: 800;
				color: #333333;
			}

			.eq-item {
				position: relative;

				&+.eq-item {
					padding-top: 12rpx;
				}

				.eq-content {

					.eq-wrap {
						border-radius: 14rpx;
						background-color: #fff;
						box-sizing: border-box;
						padding: 30rpx 30rpx 10rpx 32rpx;
						font-size: 26rpx;

						.eq-name {
							font-size: 32rpx;
							font-weight: bold;
							margin-bottom: 24rpx;
							position: relative;
							padding-left: 4rpx;

							>.eq-title {
								width: 420rpx;

								>text {
									font-size: 30rpx;
									font-weight: normal;
								}
							}
						}

						.types {
							.types-item {
								line-height: 53rpx;
								font-size: 24rpx;
								color: #777777;
								padding: 0 20rpx;
								background-color: #F7F7F7;
								border-radius: 27rpx;
								margin-right: 10rpx;

								&.types-show {
									color: #2C6FF3;
									background: #F4F8FF;
								}
							}
						}
					}

					.eqeq-type {
						display: flex;
						flex-direction: row;
						align-items: center;
						font-size: 28rpx;
						line-height: 68rpx;
						color: #333;
						padding:10rpx 0 20rpx 8rpx;

						>view:nth-child(1) {
							width: 260rpx;
						}
					}

					.sale {
						font-size: 28rpx;
						color: #333;
						padding-top:10rpx;
						border-top: 1rpx solid #f8f8f8;

						.sale-item {
							width: 50%;
							line-height: 68rpx;
						}
					}
				}
			}

			.more {
				text-align: center;
				font-size: 28rpx;
				color: #2C6FF3;
				line-height: 80rpx;
			}
		}

		.empty {
			padding-top: 40%;
		}

		.popup-container {
			padding: 20rpx;

			.martop {
				margin-top: 20rpx;
			}

			.pop-item-left {
				width: 160rpx;
			}

			.pop-item-cont {
				width: 530rpx;
			}
		}
	}
</style>