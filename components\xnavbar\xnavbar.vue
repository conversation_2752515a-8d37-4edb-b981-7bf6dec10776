<template>
	<view class="">
		<u-navbar :titleStyle="titleStyle" :autoBack="autoBack" :leftIconColor="leftIconColor" :bgColor="bgColor"
			:placeholder="true" :title="title">
			<view class="u-nav-slot flex align-center justify-between" slot="left">
				<u-icon name="arrow-left" size="19" @click="back"></u-icon>
				<u-line direction="column" :hairline="false" length="16" margin="0 8px"></u-line>
				<u-icon name="question-circle" size="20" @click="showHelpPop"></u-icon>
			</view>
		</u-navbar>
		<xhelpPopup :guideId="helpId" ref="helpPop" />
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		props: {
			title: {
				type: String,
				require: false,
				default: ''
			},
			titleStyle: {
				type: String,
				require: false,
				default: 'fontSize: 36rpx'
			},
			autoBack: {
				type: Boolean,
				require: false,
				default: true
			},
			bgColor: {
				type: String,
				require: false,
				default: '#fff'
			},
			leftIconColor: {
				type: String,
				require: false,
				default: '#333'
			},
			rightIconColor: {
				type: String,
				require: false,
				default: '#fff'
			},
			helpId: {
				type: String,
				require: false,
				default: ''
			},
		},

		methods: {
			back() {
				this.$tab.navigateBack()
			},

			showHelpPop() {
				this.$refs.helpPop.getData()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.help {
		width: 260rpx;
	}

	.u-nav-slot {
		border: 1rpx solid #cacaca;
		border-radius: 50rpx;
		padding: 8rpx 20rpx 8rpx;
	}
</style>