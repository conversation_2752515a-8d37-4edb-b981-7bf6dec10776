<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="自动续费"></u-navbar>
		<view class="content">
			<view class="input-item flex justify-between align-center">
				<view class="name">资源包预警数</view>
				<view class="value">
					<u-number-box inputWidth="65" v-model="memberTelAudit.start" :min="1"
						:max="10000000"></u-number-box>
				</view>
			</view>

			<view class="input-item flex justify-between align-center">
				<view class="name">自动续包条数</view>
				<view class="value">
					<u-number-box inputWidth="65" v-model="memberTelAudit.size" :min="1" :max="10000000"></u-number-box>
				</view>
			</view>
			<view class="input-item flex justify-between align-center">
				<view class="name">资源包少于预警条数时自动续费</view>
				<view class="value">
					<u-switch v-model="memberTelAudit.enable" activeColor="#2C6FF3"></u-switch>
				</view>
			</view>
		</view>
		<view class="btn safe-bottom">
			<xbutton size="large" width="672rpx" round="130rpx" @click="submit">保存</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		merObj,
		updateMerConfig
	} from "@/api/recharge.js"
	export default {
		data() {
			return {
				memberTelAudit: {
					enable: false,
					size: 0,
					start: 0
				}
			}
		},

		onLoad(o) {
			this.getDetail()
		},

		methods: {
			getDetail() {
				merObj().then(res => {
					let data = res.data.memberTelAudit
					this.memberTelAudit = data ? data : {
						enable: false,
						size: 100,
						start: 100
					}
				})
			},

			submit() {
				updateMerConfig({
					memberTelAudit: this.memberTelAudit
				}).then(res => {
					this.$modal.msg('保存成功')
					setTimeout(() => {
						uni.$emit('refresh')
						uni.navigateBack()
					}, 1000)
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.container {
		overflow: hidden;
		padding: 20rpx;

		.content {
			background-color: #fff;
			border-radius: 12rpx;
			padding: 20rpx 20rpx 40rpx;

			.input-item {
				line-height: 60rpx;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f3f3f3;
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
			z-index: 999;
		}
	}
</style>