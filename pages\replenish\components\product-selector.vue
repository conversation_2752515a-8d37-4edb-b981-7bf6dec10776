<template>
  <view class="product-selector">
    <!-- 选择器触发区域 -->
    <!-- <view class="selector-trigger" @tap="openProductPopup">
      <view v-if="selectedProduct" class="selected-product">
        {{ selectedProduct }}
      </view>
      <view v-else class="placeholder">
        {{ placeholder }}
      </view>
      <view class="arrow-icon">
        <u-icon name="arrow-down" size="18"></u-icon>
      </view>
    </view> -->

    <!-- 商品选择弹出框 -->
    <u-popup
      :show="showPopup"
      mode="bottom"
      round="16"
      @close="closeProductPopup"
      safe-area-inset-bottom>
      <view class="product-popup">
        <!-- 弹出框标题 -->
        <view class="popup-header">
          <view class="popup-title">选择商品</view>
          <view class="close-btn" @tap="closeProductPopup">
            <u-icon name="close" size="28"></u-icon>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-wrapper">
          <u-search
            v-model="searchText"
            placeholder="搜索商品"
            :show-action="false"
            shape="round"
            @change="handleSearch"></u-search>
        </view>

        <!-- 商品列表 -->
        <scroll-view
          scroll-y
          class="product-list-scroll"
          :style="{ height: `${scrollHeight}px` }">
          <view v-if="filteredProducts.length === 0" class="empty-tip">
            <u-empty text="没有找到相关商品" mode="search"></u-empty>
          </view>
          <view v-else class="product-grid">
            <view
              v-for="item in filteredProducts"
              :key="item.goodsId"
              class="product-item"
              @tap="selectProduct(item)">
              <image
                class="product-item-image"
                :src="item.cover"
                mode="aspectFill"></image>
              <view class="product-item-info">
                <text class="product-item-name">{{ item.name }}</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  name: "ProductSelector",
  props: {
    // 商品列表数据
    products: {
      type: Array,
      default: () => [],
    },
    // 已选择的商品
    value: {
      type: String,
      default: "",
    },
    // 占位文本
    placeholder: {
      type: String,
      default: "请选择商品",
    },
  },
  data() {
    return {
      showPopup: false,
      searchText: "",
      filteredProducts: [],
      scrollHeight: 500, // 默认高度，会在mounted中计算
    };
  },
  computed: {
    selectedProduct() {
      return this.value;
    },
  },
  watch: {
    products: {
      immediate: true,
      handler(val) {
        this.filteredProducts = val || [];
      },
    },
    searchText(val) {
      this.handleSearch();
    },
  },
  mounted() {
    // 计算列表高度，留出一定空间给其他UI元素
    uni.getSystemInfo({
      success: (res) => {
        // 计算列表高度：屏幕高度 - 弹窗头部高度 - 搜索框高度 - 底部安全区高度 - 额外边距
        this.scrollHeight = res.windowHeight * 0.6; // 使用屏幕高度的60%作为弹窗高度
      },
    });
  },
  methods: {
    // 打开选择弹窗
    openProductPopup() {
      this.showPopup = true;
    },

    // 关闭选择弹窗
    closeProductPopup() {
      this.showPopup = false;
      this.searchText = ""; // 重置搜索文本
    },

    // 选择商品
    selectProduct(product) {
      this.$emit("input", product.name);
      this.$emit("confirm", product);
      this.closeProductPopup();
    },

    // 搜索商品
    handleSearch() {
      if (!this.searchText) {
        this.filteredProducts = this.products;
      } else {
        const keyword = this.searchText.toLowerCase();
        this.filteredProducts = this.products.filter((item) =>
          item.name.toLowerCase().includes(keyword)
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.product-selector {
  width: 100%;

  .selector-trigger {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background-color: #ffffff;
    border: 1px solid #ebeef5;
    border-radius: 8rpx;
    min-height: 68rpx;

    .selected-product {
      color: #303133;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .placeholder {
      font-size: 28rpx;
      color: #909399;
    }

    .arrow-icon {
      margin-left: 16rpx;
    }
  }

  .product-popup {
    background-color: #ffffff;

    .popup-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 30rpx;
      border-bottom: 1px solid #ebeef5;

      .popup-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #303133;
      }

      .close-btn {
        padding: 10rpx;
      }
    }

    .search-wrapper {
      padding: 20rpx 30rpx;
    }

    .product-list-scroll {
      width: 100%;

      .empty-tip {
        padding: 60rpx 0;
      }

      .product-grid {
        padding: 20rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        gap: 18rpx;

        .product-item {
          width: calc((100% - 36rpx) / 3);
          margin-bottom: 0;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 16rpx;
          background-color: #fff;
          border-radius: 12rpx;
          box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

          .product-item-image {
            width: 160rpx;
            height: 160rpx;
            border-radius: 8rpx;
            margin-bottom: 16rpx;
          }

          .product-item-info {
            width: 100%;
            text-align: center;

            .product-item-name {
              font-size: 26rpx;
              color: #303133;
              display: block;
              text-align: center;
              min-height: 72rpx;
              line-height: 36rpx;
              word-break: break-all;
            }
          }
        }

        /* 添加占位元素确保最后一行对齐 */
        &::after {
          content: "";
          width: calc((100% - 36rpx) / 3);
          height: 0;
        }
      }
    }
  }
}
</style>
