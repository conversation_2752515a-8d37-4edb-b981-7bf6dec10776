import request from '@/utils/request'
/**
 * @param {Object} data
 * 列表
 */
export function couponPage(data) {
	return request({
		url: '/merc/mini-merc-coupon/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 添加或更新
 */
export function saveOrUpdate(data) {
	return request({
		url: '/merc/mini-merc-coupon/saveOrUpdate',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 编辑详情
 */
export function detail(data) {
	return request({
		url: '/merc/mini-merc-coupon/obj',
		method: 'post',
		data: data
	})
}

export function list(data) {
	return request({
		url: '/merc/mini-merc-coupon/list',
		method: 'post',
		data: data
	})
}

export function mercSend(data) {
	return request({
		url: '/merc/mini-merc-coupon/mercSend',
		method: 'post',
		data: data
	})
}

export function mercSendByTel(data) {
	return request({
		url: '/merc/mini-merc-coupon/mercSendByTel',
		method: 'post',
		data: data
	})
}

export function mercSendByMemberId (data) {
	return request({
		url: '/merc/mini-merc-coupon/mercSendByMemberId ',
		method: 'post',
		data: data
	})
}
