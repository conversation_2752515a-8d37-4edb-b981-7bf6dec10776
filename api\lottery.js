import request from '@/utils/request'
/**
 * @param {Object} data
 * 列表
 */
export function list(data) {
	return request({
		url: '/merc/mini-merc-lottery/list',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 对象查询
 */
export function obj(data) {
	return request({
		url: '/merc/mini-merc-lottery/obj',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 编辑详情
 */
export function saveOrUpdate(data) {
	return request({
		url: '/merc/mini-merc-lottery/saveOrUpdate',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 分页
 */
export function page(data) {
	return request({
		url: '/merc/mini-merc-lottery/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 奖品新增、修改
 */
export function prizeSaveOrUpdate(data) {
	return request({
		url: '/merc/mini-merc-lottery/prizeSaveOrUpdate',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 奖品删除
 */
export function prizeDel(data) {
	return request({
		url: '/merc/mini-merc-lottery/prizeDel',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 奖品查询
 */
export function prizeObj(data) {
	return request({
		url: '/merc/mini-merc-lottery/prizeObj',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 奖品列表
 */
export function prizeList(data) {
	return request({
		url: '/merc/mini-merc-lottery/prizeList',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 记录
 */
export function memberLottery(data) {
	return request({
		url: '/merc/mini-merc-lottery/memberLottery',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 优惠券
 */
export function normalMiniList(data) {
	return request({
		url: '/merc/mini-merc-coupon/normalMiniList',
		method: 'post',
		data: data
	})
}

