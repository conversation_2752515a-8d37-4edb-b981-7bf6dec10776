<template>
	<!-- 机器选择搜索下拉组件 -->
	<view class="wrap">
		<xselectInputLoading v-model="searchKey" name="name" slabel="deviceName" svalue="deviceId" placeholder="请选择设备"
			:options="searchList" @select="inputSelect" @clear="inputClear" @scrolltolower="searchScrolltolower"
			@input="inputChange" />
	</view>
</template>

<script>
	/**
	 * 机器选择搜索下拉组件
	 */
	import {
		simpleDeviceSearchPage
	} from '@/api/replenishment/replenishment.js'
	export default {
		name: "xy-deviceSelect",
		props: {

		},
		data() {
			return {
				searchPage: 1,
				searchSize: 10,
				searchKey: '',
				searchStatus: 'loadmore',
				searchList: [],
			};
		},
		watch: {
			value: {
				handler(newVal, oldVal) {
					this.changeValue = newVal
				},
			},
		},

		created() {
			this.inputChange()
		},
		methods: {
			/**
			 * 搜索框改变
			 */
			inputChange(e) {
				this.searchReset()
				this.getSearchList()
			},

			inputSelect(index, item) {
				this.searchKey = item.deviceName || item.deviceId
				this.$emit('inputSelect', item)
			},

			inputClear() {
				this.$emit('inputClear')
			},

			searchReset() {
				this.searchStatus == 'loadmore'
				this.searchPage = 1;
				this.searchList = [];
			},

			/**
			 * 搜索触底加载
			 */
			searchScrolltolower() {
				if (this.searchStatus == 'nomore') return
				this.searchPage++
				this.getSearchList()

			},

			/**
			 * 设备列表
			 */
			getSearchList() {
				simpleDeviceSearchPage({
					page: {
						current: this.searchPage,
						size: this.searchSize
					},
					searchKey: this.searchKey
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.searchStatus = "nomore"
					} else {
						this.searchStatus = "loadmore"
					}
					this.searchList = this.searchList.concat(data)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
	}

	.uni-bg {
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, .6);
		position: fixed;
		top: 0;
		left: 0;
		z-index: 998;
	}

	.uni-select-lay {
		position: relative;
		z-index: 999;
		background-color: #fff;
		border-radius: 4px;

		.uni-select-input {
			opacity: 0;
			position: absolute;
			z-index: -111;
		}

		// select部分 
		.uni-select-lay-select {
			user-select: none;
			position: relative;
			z-index: 3;
			height: 36px;
			padding: 0 30px 0 10px;
			box-sizing: border-box;
			border-radius: 4px;
			border: 1px solid rgb(229, 229, 229);
			display: flex;
			align-items: center;
			font-size: 14px;
			color: #999;

			.uni-disabled {
				position: absolute;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 19;
				cursor: no-drop;
			}

			// input 框的清除按钮
			.uni-select-lay-input-close {
				position: absolute;
				right: 35px;
				top: 0;
				height: 100%;
				width: 15px;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 3;
				cursor: pointer;

				text {
					position: relative;
					background: #fff;
					width: 13px;
					height: 13px;
					border-radius: 50%;
					border: 1px solid #bbb;

					&::before,
					&::after {
						content: "";
						position: absolute;
						left: 20%;
						top: 50%;
						height: 1px;
						width: 60%;
						transform: rotate(45deg);
						background-color: #bbb;
					}

					&::after {
						transform: rotate(-45deg);
					}
				}
			}

			.uni-select-lay-input {
				font-size: 14px;
				color: #999;
				display: block;
				width: 98%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				line-height: 30px;

				&.active {
					color: #333
				}
			}

			.clear {
				position: absolute;
				right: 10rpx;
			}

			.uni-select-lay-icon {
				cursor: pointer;
				position: absolute;
				right: 0;
				top: 0;
				height: 100%;
				width: 30px;
				display: flex;
				align-items: center;
				justify-content: center;

				&::before {
					content: "";
					width: 1px;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					background-color: #e5e5e5;
				}

				text {
					display: block;
					width: 0;
					height: 0;
					border-width: 12rpx 12rpx 0;
					border-style: solid;
					border-color: #bbb transparent transparent;
					transition: .3s;
				}
			}

			&.active .uni-select-lay-icon {
				text {
					transform: rotate(180deg);
				}
			}
		}

		// options部分
		.uni-select-lay-options {
			user-select: none;
			position: absolute;
			left: 0;
			width: 100%;
			height: 500rpx;
			overflow-y: auto;
			border-radius: 4px;
			border: 1px solid rgb(229, 229, 229);
			background: #fff;
			padding: 5px 0;
			box-sizing: border-box;
			z-index: 9;

			.uni-select-lay-item {
				padding: 0 10px;
				box-sizing: border-box;
				cursor: pointer;
				line-height: 2.5;
				transition: .3s;
				font-size: 14px;

				&.active {
					background: #007AFF;
					color: #fff;

					&:hover {
						background: #007AFF;
						color: #fff;
					}
				}

				&:hover {
					background-color: #f5f5f5;
				}
			}

			.nosearch {
				font-size: 16px;
				line-height: 3;
				text-align: center;
				color: #666;
			}
		}

		.uni-select-lay-options-top {
			bottom: calc(100% + 5px);
		}

		.uni-select-lay-options-bottom {
			top: calc(100% + 5px);
		}
	}
</style>