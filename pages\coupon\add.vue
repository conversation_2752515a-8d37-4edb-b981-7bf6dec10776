<template>
  <view class="container">
    <u-navbar
      leftIconColor="#fff"
      titleStyle="color:#fff;fontSize:36rpx;"
      :autoBack="true"
      bgColor="#2C6FF3"
      :placeholder="true"
      title="优惠券创建"></u-navbar>
    <view class="content">
      <u--form
        labelPosition="left"
        :model="form"
        errorType="toast"
        labelWidth="120">
        <view class="section-wrap">
          <u-form-item label="名称">
            <u-input
              inputAlign="right"
              v-model="form.name"
              placeholder="请输入优惠券名称"
              border="none"></u-input>
          </u-form-item>
        </view>

        <view class="section-wrap">
          <view class="tab-wrap">
            <view class="tab">
              <u-tabs
                :list="tabList"
                :activeStyle="{
                  color: '#2C6FF3',
                  fontWeight: 'bold',
                  fontSize: '28rpx',
                }"
                :inactiveStyle="{ fontSize: '28rpx' }"
                :scrollable="false"
                :current="current"
                @click="tabClick"
                lineColor="#2C6FF3">
              </u-tabs>
            </view>
          </view>

          <u-form-item label="选择设备" borderBottom>
            <u-radio-group
              v-model="form.is_all_device"
              placement="row"
              @change="actionAllDeviceRadio">
              <u-radio
                :customStyle="{ marginRight: '40rpx' }"
                label="全部"
                name="1">
              </u-radio>
              <u-radio label="指定设备" name="0"> </u-radio>
            </u-radio-group>
          </u-form-item>

          <u-form-item
            label="指定设备"
            borderBottom
            @click="chooseDev"
            v-if="form.is_all_device == 0">
            <view
              class="choose-list"
              v-if="form.deviceIds && form.deviceIds.length > 0">
              <text v-for="(item, index) in form.deviceList" :key="index">
                {{ item.deviceName }}
                <text v-if="index != form.deviceList.length - 1">，</text>
              </text>
            </view>
            <view v-else style="color: #c0c4cc; text-align: right"
              >请选择设备</view
            >
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>

          <u-form-item label="选择商品" borderBottom>
            <u-radio-group
              v-model="form.is_all_goods"
              placement="row"
              @change="actionAllGoodsRadio">
              <u-radio
                :customStyle="{ marginRight: '40rpx' }"
                label="全部"
                name="1">
              </u-radio>
              <u-radio label="指定商品" name="0"> </u-radio>
            </u-radio-group>
          </u-form-item>

          <u-form-item
            label="指定商品"
            borderBottom
            @click="chooseCom"
            v-if="form.is_all_goods != 1">
            <view
              class="choose-list"
              v-if="form.goodsIds && form.goodsIds.length > 0">
              <text v-for="(item, index) in form.goodsList" :key="index">
                {{ item.goodsName }}
                <text v-if="index != form.goodsList.length - 1">，</text>
              </text>
            </view>
            <view v-else style="color: #c0c4cc; text-align: right"
              >请选择商品</view
            >
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>

          <view class="mj-content" v-if="current == 0">
            <view class="mj-head">
              <view class="mj-title"> 满减金额(元) </view>
              <!-- 	<view class="edit" @click="addMj">
								增加
							</view> -->
            </view>
            <view class="mj-body">
              <block v-for="(item, index) in form.scheme" :key="item.id">
                <view class="mj-item flex align-center justify-between">
                  <view class="left-input flex align-center">
                    <text>满</text
                    ><u-input
                      v-model="item.limit"
                      type="digit"
                      placeholder="请输入"
                      border="none"></u-input>
                  </view>
                  <view class="left-input flex align-center">
                    <text>减</text
                    ><u-input
                      v-model="item.price"
                      type="digit"
                      placeholder="请输入"
                      border="none"></u-input>
                  </view>
                  <view class="right-switch flex align-center">
                    <text>循环</text>
                    <view
                      ><u-switch
                        :disabled="form.scheme.length > 1"
                        size="16"
                        v-model="form.scheme[index].is_loop"></u-switch>
                    </view>
                  </view>
                </view>
              </block>
            </view>
          </view>

          <u-form-item label="折扣" v-else>
            <u-input
              inputAlign="right"
              type="digit"
              v-model="form.scheme.scalar"
              placeholder="请输入不大于1的小数"
              border="none">
            </u-input>
          </u-form-item>
        </view>

        <view class="section-wrap">
          <view class="action-time">
            <u-form-item label="使用时间" borderBottom>
              <u-radio-group
                v-model="form.is_all_day"
                placement="row"
                @change="actionValidTimeRadio">
                <u-radio
                  :customStyle="{ marginRight: '40rpx' }"
                  label="有效天数"
                  name="有效天数">
                </u-radio>
                <u-radio label="固定使用时间" name="固定使用时间"> </u-radio>
              </u-radio-group>
            </u-form-item>
          </view>
          <view class="time-wrap" v-if="form.is_all_day == '有效天数'">
            <u-form-item label="有效天数">
              <u-input
                inputAlign="right"
                type="number"
                v-model="form.validDay"
                placeholder="0代表不过期"
                border="none"></u-input>
            </u-form-item>
          </view>

          <view class="time-wrap" v-if="form.is_all_day == '固定使用时间'">
            <u-form-item
              label="开始日期"
              borderBottom
              @click="chooseDate('validStartDate')">
              <view style="text-align: right" v-if="form.validStartDate">
                {{ form.validStartDate }}
              </view>
              <view v-else style="color: #c0c4cc; text-align: right"
                >请选择活动开始日期</view
              >
              <u-icon slot="right" name="arrow-right"></u-icon>
            </u-form-item>
            <u-form-item label="结束日期" @click="chooseDate('validEndDate')">
              <view style="text-align: right" v-if="form.validEndDate">
                {{ form.validEndDate }}
              </view>
              <view v-else style="color: #c0c4cc; text-align: right"
                >请选择活动结束日期</view
              >
              <u-icon slot="right" name="arrow-right"></u-icon>
            </u-form-item>
          </view>
        </view>

        <!-- 		<view class="section-wrap">
					<u-form-item label="使用门槛">
						<u-input inputAlign="right" type="number" v-model="form.useMinPrice" placeholder="0代表无门槛"
							border="none"></u-input>
					</u-form-item>
				</view> -->

        <view class="section-wrap">
          <u-form-item
            label="发放类型"
            @click="chooseType"
            :borderBottom="
              form.sendTypeName == '购物后赠送' ||
              form.sendTypeName == '优惠券页面主动领取'
            ">
            <view style="text-align: right" v-if="form.sendTypeName">
              {{ form.sendTypeName }}
            </view>
            <view v-else style="color: #c0c4cc; text-align: right"
              >请选择发放类型</view
            >
            <u-icon slot="right" name="arrow-right"></u-icon>
          </u-form-item>

          <block v-if="form.sendTypeName == '购物后赠送'">
            <u-form-item label="购物满:">
              <u-input
                inputAlign="right"
                type="digit"
                v-model="sendConfig.limitAmount"
                placeholder="赠送优惠券所需购物金额"
                border="none"></u-input>
            </u-form-item>
          </block>

          <block v-if="form.sendTypeName == '优惠券页面主动领取'">
            <u-form-item label="所需价格" borderBottom>
              <u-input
                inputAlign="right"
                type="digit"
                v-model="sendConfig.price"
                placeholder="0或不填代表不限制"
                border="none"></u-input>
            </u-form-item>

            <u-form-item label="所需积分" borderBottom>
              <u-input
                inputAlign="right"
                type="number"
                v-model="sendConfig.points"
                placeholder="0或不填代表不限制"
                border="none"></u-input>
            </u-form-item>
          </block>

          <u-form-item
            label="限领份数/人"
            v-if="
              form.sendTypeName == '优惠券页面主动领取' ||
              form.sendTypeName == '扫码领取'
            ">
            <u-input
              inputAlign="right"
              type="number"
              v-model="sendConfig.limit"
              placeholder="0或不填代表不限制"
              border="none"></u-input>
          </u-form-item>
        </view>

        <view class="section-wrap">
          <view class="action-time">
            <u-form-item label="发放日期" borderBottom>
              <u-radio-group
                v-model="form.is_all_day_send"
                placement="row"
                @change="actionSendTimeRadio">
                <u-radio
                  :customStyle="{ marginRight: '40rpx' }"
                  label="不限时间"
                  name="不限时间">
                </u-radio>
                <u-radio label="固定发放时间" name="固定发放时间"> </u-radio>
              </u-radio-group>
            </u-form-item>
          </view>

          <view class="time-wrap" v-if="form.is_all_day_send == '固定发放时间'">
            <u-form-item
              label="开始日期"
              borderBottom
              @click="chooseDate('sendStartDate')">
              <view style="text-align: right" v-if="form.sendStartDate">
                {{ form.sendStartDate }}
              </view>
              <view v-else style="color: #c0c4cc; text-align: right"
                >请选择活动开始日期</view
              >
              <u-icon slot="right" name="arrow-right"></u-icon>
            </u-form-item>
            <u-form-item label="结束日期" @click="chooseDate('sendEndDate')">
              <view style="text-align: right" v-if="form.sendEndDate">
                {{ form.sendEndDate }}
              </view>
              <view v-else style="color: #c0c4cc; text-align: right"
                >请选择活动结束日期</view
              >
              <u-icon slot="right" name="arrow-right"></u-icon>
            </u-form-item>
          </view>
        </view>
      </u--form>
    </view>
    <view class="btn">
      <xbutton size="large" @click="submit"> 保存 </xbutton>
    </view>

    <!-- 	<u-calendar :show="calendarShow" allowSameDay closeOnClickOverlay @close="calendarShow=false"
			:monthNum="calendarMonthNum" mode="range" color="#2C6FF3" @confirm="calendarConfirm"></u-calendar> -->

    <u-action-sheet
      :show="actionsShow"
      :actions="actions"
      title="发送类型选择"
      @close="actionsShow = false"
      @select="actionsSelect($event)"></u-action-sheet>

    <u-datetime-picker
      :show="calendarShow"
      v-model="date"
      confirmColor="#2C6FF3"
      mode="date"
      :closeOnClickOverlay="true"
      @close="calendarShow = false"
      @confirm="calendarConfirm"></u-datetime-picker>
  </view>
</template>

<script>
import getDict from "@/utils/getDict.js";
import { saveOrUpdate, detail } from "@/api/coupon.js";
export default {
  data() {
    return {
      form: {
        name: null,
        deviceIds: [],
        goodsIds: [],
        goodsNames: [],
        scheme: [
          {
            id: Math.random(),
            limit: null,
            price: null,
            is_loop: false,
          },
        ],
        sendStartDate: null,
        sendEndDate: null,
        useMinPrice: null,
        is_all_day: "有效天数",
        is_all_day_send: "不限时间",
        validDay: null,
        validStartDate: null,
        validEndDate: null,
        deviceList: [],
        goodsList: [],
        is_all_device: "1",
        is_all_goods: "0",
        type: "",
        typeName: "",
        sendType: "",
        sendTypeName: "",
      },

      sendConfig: {
        limitAmount: "",
        price: 0,
        points: 0,
        limit: 1,
      },

      timeType: "timeType",
      calendarShow: false,
      calendarMonthNum: "3",
      tabList: [
        {
          name: "满减",
        },
        {
          name: "折扣",
        },
      ],
      current: 0,
      timeIndex: 0,

      actions: [],
      actionsShow: false,
      date: new Date(),
      id: null,
    };
  },

  async onLoad(o) {
    await this.getSendType();
    if (o.id) {
      this.id = o.id;
      this.getDetail(o.id);
    }
  },

  onShow() {
    if (uni.getStorageSync("coupon")) {
      this.form = JSON.parse(uni.getStorageSync("coupon"));
    } else {
      uni.setStorageSync("coupon", JSON.stringify(this.form));
    }
  },

  watch: {
    form: {
      handler(newVal, oldVal) {
        console.log(newVal);
        uni.setStorageSync("coupon", JSON.stringify(newVal));
      },
      deep: true,
    },
  },

  methods: {
    getDetail(id) {
      detail({
        id: id,
      }).then((res) => {
        let data = res.data;
        let scheme = null;

        let schemeArr = JSON.parse(data.scheme);
        if (data.type == 2) {
          //满减
          scheme = [];
          console.log(schemeArr);
          for (let key in schemeArr) {
            let item = schemeArr[key];
            let obj = {};
            obj = {
              id: Math.random(),
              limit: this.$xy.delMoney(item.limit),
              price: this.$xy.delMoney(item.price),
              is_loop: item.is_loop && item.is_loop == 1,
            };
            scheme.push(obj);
          }
          console.log(scheme);
          this.current = 0;
        } else {
          scheme = {
            scalar: schemeArr.scalar,
          };
          this.current = 1;
        }
        let goodsIds = [];
        if (data.goodsList && data.goodsList.length > 0) {
          for (let key in data.goodsList) {
            let item = data.goodsList[key];
            goodsIds.push(item.goodsId);
          }
        }

        if (data.validType == "1") {
          this.form.is_all_day = "有效天数";
        } else {
          this.form.is_all_day = "固定使用时间";
        }

        let deviceIds = [];
        if (data.deviceList && data.deviceList.length > 0) {
          for (let key in data.deviceList) {
            let item = data.deviceList[key];
            deviceIds.push(item.deviceId);
          }
        }

        let sendConfig = {};
        let sendConfigObj = data.sendConfig
          ? JSON.parse(data.sendConfig)
          : data.sendConfig;
        if (data.sendType == "shopping") {
          sendConfig = {
            limitAmount: sendConfigObj?.limitAmount,
            price: 0,
            points: 0,
            limit: 1,
          };
        } else if (data.sendType == "buy") {
          sendConfig = {
            limitAmount: "",
            price: sendConfigObj?.price ?? "",
            points: sendConfigObj?.points ?? "",
            limit: sendConfigObj?.limit ?? "",
          };
        } else if (data.sendType == "qrcode") {
          sendConfig = {
            limit: sendConfigObj?.limit ?? 0,
          };
        }
        this.sendConfig = sendConfig;

        let params = {
          name: data.name,
          deviceIds: deviceIds,
          goodsIds: goodsIds,
          is_all_day: data.validType == "1" ? "有效天数" : "固定使用时间",
          is_all_day_send: data.sendStartDate ? "固定发放时间" : "不限时间",
          scheme: scheme,
          sendStartDate: data.sendStartDate,
          sendEndDate: data.sendEndDate,
          useMinPrice: data.useMinPrice,
          validDay: data.validDay,
          validStartDate: data.validStartDate,
          validEndDate: data.validEndDate,
          deviceList: data.deviceList,
          goodsList: data.goodsList,
          is_all_device:
            data.deviceList && data.deviceList.length > 0 ? "0" : "1",
          is_all_goods: data.goodsList && data.goodsList.length > 0 ? "0" : "1",
          type: data.type,
          sendType: data.sendType,
          sendTypeName: this.actions.find((i) => i.value == data.sendType).name,
        };
        this.form = JSON.parse(JSON.stringify(params));
        console.log("反显数据=======" + JSON.stringify(this.form));
      });
    },
    chooseDev() {
      this.$tab.navigateTo("/pages/coupon/chooseDevice?type=1");
    },

    chooseCom() {
      this.$tab.navigateTo("/pages/coupon/chooseGoods?type=1");
    },
    chooseDate(type) {
      this.timeType = type;
      this.calendarShow = true;
    },

    calendarConfirm(e) {
      let date = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
      this.form[this.timeType] = date;
      this.calendarShow = false;
    },

    getSendType() {
      return new Promise((resolve, reject) => {
        getDict("coupon_send_type")
          .then((res) => {
            let actions = res.map((i) => ({
              name: i.msg,
              value: i.code,
            }));
            this.actions = JSON.parse(JSON.stringify(actions));
            resolve(actions);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    tabClick(e) {
      this.current = e.index;
      if (this.current == 0) {
        this.form.scheme = [
          {
            id: Math.random(),
            limit: null,
            price: null,
            is_loop: false,
          },
        ];
      } else {
        this.form.scheme = {
          scalar: null,
        };
      }
      this.$forceUpdate();
    },

    actionAllDeviceRadio(e) {
      this.form.is_all_device = e;
    },

    actionAllGoodsRadio(e) {
      if (e == 1) {
        this.$modal
          .confirm("警告：后续新增的商品，也将全部适用当前活动！！！")
          .then((res) => {
            this.form.is_all_goods = e;
          })
          .catch((err) => {
            this.form.is_all_goods = "0";
          });
      } else {
        this.form.is_all_goods = e;
      }
    },

    actionValidTimeRadio(e) {
      this.form.is_all_day = e;
    },

    actionSendTimeRadio(e) {
      this.form.is_all_day_send = e;
      if (e == "不限时间") {
        this.form.sendStartDate = null;
        this.form.sendEndDate = null;
      }
    },

    actionsSelect(e) {
      this.form.sendType = e.value;
      this.form.sendTypeName = e.name;
    },

    chooseType() {
      this.actionsShow = true;
    },

    //表单提交
    submit() {
      let scheme = [];
      if (this.current == 0) {
        scheme = this.form.scheme.map((i) => ({
          limit: this.$xy.delMoneyL(i.limit),
          price: this.$xy.delMoneyL(i.price),
          is_loop: i.is_loop ? 1 : 0,
        }));
      } else {
        scheme = {
          scalar: this.form.scheme.scalar,
        };
      }

      let sendConfig = {};
      if (this.form.sendType == "shopping") {
        sendConfig = {
          limitAmount: this.sendConfig.limitAmount ?? 0,
        };
      } else if (this.form.sendType == "buy") {
        sendConfig = {
          price: this.sendConfig.price ?? 0,
          points: this.sendConfig.points ?? 0,
          limit: this.sendConfig.limit ?? 0,
        };
      } else if (this.form.sendType == "qrcode") {
        sendConfig = {
          limit: this.sendConfig.limit ?? 0,
        };
      }

      let params = {
        name: this.form.name,
        goodsIds: this.form.is_all_goods == 0 ? this.form.goodsIds : null,
        deviceIds: this.form.is_all_device == 0 ? this.form.deviceIds : null,
        scheme: JSON.stringify(scheme),
        validStartDate:
          this.form.is_all_day == "固定使用时间"
            ? this.form.validStartDate
            : null,
        validEndDate:
          this.form.is_all_day == "固定使用时间"
            ? this.form.validEndDate
            : null,
        validDay:
          this.form.is_all_day == "有效天数" ? this.form.validDay : null,
        useMinPrice: this.form.useMinPrice,
        type: this.current == 0 ? "2" : "1",
        sendType: this.form.sendType,
        sendStartDate: this.form.sendStartDate,
        sendEndDate: this.form.sendEndDate,
        validType: this.form.is_all_day == "有效天数" ? 1 : 2,
        sendConfig: JSON.stringify(sendConfig),
      };

      if (this.checkParams(params)) return;

      if (this.id) {
        params.id = this.id;
        saveOrUpdate(params)
          .then((res) => {
            uni.setStorageSync("coupon", ""); //清空临时存储
            this.$modal.msg("修改成功~");
            setTimeout(() => {
              this.$tab.navigateBack();
              uni.$emit("refresh");
            }, 1500);
          })
          .catch((err) => {});
      } else {
        saveOrUpdate(params)
          .then((res) => {
            uni.setStorageSync("coupon", ""); //清空临时存储
            this.$modal.msg("新建成功~");
            setTimeout(() => {
              this.$tab.navigateBack();
              uni.$emit("refresh");
            }, 1500);
          })
          .catch((err) => {});
      }
    },

    checkParams(params) {
      if (!params.name) {
        this.$modal.msg("优惠券名称不能为空");
        return true;
      }
      return false;
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .u-form-item__body {
  padding: 30rpx 0 !important;
}

.container {
  .content {
    padding-bottom: 200rpx;

    .section-wrap {
      padding: 0 30rpx;
      background-color: #fff;
      margin-bottom: 24rpx;
    }

    .input-text {
      text-align: right !important;
    }

    .tab-wrap {
      padding: 12rpx 0;
    }

    .choose-list {
      width: 418rpx;

      > text {
        word-break: break-all;
      }
    }

    .mj-content {
      padding-bottom: 30rpx;

      .edit {
        color: #2c6ff3;
        text-decoration: underline;

        &.del {
          color: red;
        }
      }

      .mj-head {
        line-height: 80rpx;
      }

      .mj-body {
        .mj-item {
          + .mj-item {
            margin-top: 24rpx;
          }

          .left-input {
            border: 1px solid #d6d7d9;
            border-radius: 8rpx;
            padding: 0 13rpx;
            line-height: 68rpx;
            width: 250rpx;

            > text {
              margin-right: 12rpx;
            }

            &.zk-left-input {
              width: 300rpx;
            }
          }

          .right-switch {
            > text {
              margin-right: 12rpx;
            }
          }
        }
      }
    }

    .fail-msg {
      color: red;
      font-size: 28rpx;
      line-height: 32rpx;
      margin-top: 12rpx;
      margin-bottom: 24rpx;
      width: 726rpx;
      margin-left: -24rpx;
      background-color: skyblue;
      padding: 24rpx 12rpx;
      box-sizing: border-box;
      border-radius: 12rpx;
    }

    .example-img {
      height: 212rpx;
      padding: 24rpx 0 12rpx;
      display: flex;
      flex-flow: row nowrap;
      justify-content: flex-start;

      > image {
        width: 160rpx;
        margin-right: 66rpx;
        border-radius: 4rpx;
      }
    }

    .img-tips {
      color: red;
      margin-top: 30rpx;
      line-height: 44rpx;
    }

    .img-upload {
      height: 212rpx;
      padding: 24rpx 0 12rpx;
    }

    .bar-code {
      position: relative;

      .scan-icon {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        z-index: 9999;
      }
    }

    .form-item {
      position: relative;

      .tips {
        position: absolute;
        right: 24rpx;
        top: 24rpx;
        color: red;
        font-size: 24rpx;
      }
    }
  }

  .priority {
    position: relative;

    .tips {
      position: absolute;
      color: red;
      font-size: 24rpx;
      left: 100rpx;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .action-time {
    position: relative;

    .edit {
      color: #2c6ff3;
      text-decoration: underline;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);

      &.del {
        color: red;
        text-decoration: underline;
      }
    }
  }

  .time-wrap {
    .time-item {
      line-height: 84rpx;

      .time-select {
      }

      .action-time-del {
        color: red;
        text-decoration: underline;
      }
    }
  }

  .btn {
    width: 724rpx;
    position: fixed;
    left: 13rpx;
    bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
    border-radius: 88rpx;
    overflow: hidden;
    z-index: 9;

    .btn-wrap {
      text-align: center;
      font-size: 36rpx;
      font-weight: 500;
    }
  }

  .popup-content {
    padding: 36rpx 24rpx;
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>
