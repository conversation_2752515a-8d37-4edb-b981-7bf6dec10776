<template>
	<!-- 余额充值 -->
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="短信充值"></u-navbar>
		<view class="content">
			<view class="section1 xy-card flex justify-between align-center">
				<view>
					余额（条）
				</view>
				<view>0</view>
			</view>

			<view class="section2 xy-card" :style="{height:fullHeight}">
				<view class="title">
					充值金额
				</view>
				<view class="recharge-list flex justify-between flex-wrap">
					<block v-for="(item,index) in rechargeList" :key="item.value">
						<view :class="[current==index?'list-item list-item-select':'list-item']"
							@click="rechargeClick(item,index)">
							{{item.label}}
						</view>
					</block>
				</view>

				<view class="recharge-input">
					￥{{$xy.delMoney(exc.money)}}
				</view>

				<view class="btn">
					<xbutton size="large" width="672rpx" round="130rpx" @click="submit">立即充值</xbutton>
					<!-- <xbutton size="large" width="672rpx" round="130rpx" bgColor="#c1c1c1" @click="submit" color="#333">立即充值</xbutton> -->
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		account
	} from "@/api/recharge.js"
	import getDict from '@/utils/getDict.js'
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
				rechargeList: [],
				fullHeight: 0,
				current: 0,
				restMoney: 0,
				exc: {
					size: 0,
					money: 0,
				}
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.section2', 20)
			getDict('sms_config').then(res => {
				let rechargeList = res.map(i => ({
					label: i.msg,
					value: i.value
				}))
				this.rechargeList = JSON.parse(JSON.stringify(rechargeList))

				this.rechargeClick(this.rechargeList[0], 0)
			})
		},

		onShow() {
			this.detail()
		},

		methods: {
			detail() {
				account({}).then(res => {
					this.restMoney = res.data.balance
				})
			},
			rechargeClick(item, index) {
				this.current = index
				this.exc = JSON.parse(item.value)
			},

			submit() {
				this.$tab.navigateTo(
					`/pages/recharge/rechargeIng?type=${JSON.stringify([8])}&chargeObj=${JSON.stringify(this.exc)}&money=${this.exc.money}`
				)
			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			padding: 0 13rpx;
			overflow: hidden;

			.section1 {
				height: 160rpx;
				margin-top: 20rpx;

				>view:nth-child(1) {
					font-size: 28rpx;
					font-weight: 500;
					color: #333333;
				}

				>view:nth-child(2) {
					font-size: 48rpx;
					font-weight: 800;
					color: #333333;
				}
			}

			.section2 {
				margin-top: 20rpx;
				position: relative;

				.title {
					font-size: 28rpx;
					font-weight: 800;
					color: #333333;
					padding: 30rpx 0;
				}

				.recharge-list {
					.list-item {
						width: 215rpx;
						height: 126rpx;
						background: #F7F7F7;
						border-radius: 10rpx;
						font-size: 32rpx;
						font-weight: 500;
						color: #333333;
						text-align: center;
						line-height: 126rpx;
						margin-bottom: 28rpx;

						&.list-item-select {
							background: #2C6FF3;
							color: #fff;
						}
					}
				}

				.recharge-input {
					margin-top: 40rpx;
					color: #333;
					font-size: 42rpx;
					font-weight: bold;
					padding-right: 30rpx;
				}

				::v-deep .input-class {
					font-size: 42rpx;
				}

				.btn {
					position: absolute;
					left: 24rpx;
					bottom: 24rpx;
				}
			}
		}
	}
</style>