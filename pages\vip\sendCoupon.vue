<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="赠送优惠券"></u-navbar>

		<view class="search">
			<u-search animation placeholder="请输入手机号码" :clearabled="true" v-model="keyword" :showAction="false"
				@search="search"></u-search>
		</view>

		<view class="flex align-center justify-between screen-container">
			<view>用户总数：{{total}}</view>
			<view class="flex align-center">
				<view class="show-zero flex justify-end align-center">
					<view>黑名单：</view>
					<view>
						<u-switch activeColor="#2C6FF3" size="14" v-model="showBlack"
							@change="showBlackChange"></u-switch>
					</view>
				</view>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="item in list" :key="item.id">
					<view class="equipment-container" @click="selectUser(item)">
						<view class="flex align-center justify-between">
							<view class="title flex align-center">
								<u-checkbox-group v-model="item.checkBoxValue" @change="checkboxItemChange(item)">
									<u-checkbox :name="item.id" @tap.stop></u-checkbox>
								</u-checkbox-group>
								<text class="ml-10">{{item.tel||'-'}}</text>
							</view>
						</view>

						<view class="flex">
							<view class="order-detail-item" style="width:48%;">
								<view>总消费金额：</view>￥{{$xy.delMoney(item.payMoney)}}
							</view>
							<view class="order-detail-item" style="width:48%;">
								<view>订单笔数：</view>{{item.size}}
							</view>
						</view>

						<view class="flex">
							<view class="order-detail-item" style="width:48%;">
								<view>黑名单：</view>{{item.isBlacklist?'是':'否'}}
							</view>
							<view class="order-detail-item" style="width:48%;">
								<view>积分：</view>{{item.points}}
							</view>
						</view>

						<view class="order-detail-item">
							<view>最后购物时间：</view>{{item.lastOrderTime||'-'}}
						</view>
						<view class="order-detail-item">
							<view>最后购物设备：</view>{{item.lastDeviceName||item.lastDeviceId||'-'}}
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<view class="btn">
			<xbutton size="large" @click="showCouponSelect">
				赠送 (已选{{selectedUsers.length}}人)
			</xbutton>
		</view>

		<!-- 优惠券选择弹框部分 -->
		<xpopup :show="couponShow" @close="couponClose" @confirm="couponConfirm" :showBtn="true" title="选择优惠券">
			<view class="popup-container">
				<view class="search">
					<u-search animation placeholder="输入优惠券名称" :clearabled="true" v-model="searchKey" :showAction="false"
						@search="searchCoupon"></u-search>
				</view>

				<scroll-view style="height: 600rpx;" scroll-y scroll-with-animation>
					<view class='coupon-scroll'>
						<u-checkbox-group style="width:100%;" v-model="selectedCoupons" @change="couponChange"
							activeColor="#2C6FF3">
							<view v-for="(item, index) in couponList" :key="item.id" style="width:100%;"
								class="coupon-item flex justify-between align-center" @click="selectCoupon(item)">
								<u-checkbox :label="item.name" :name="item.id">
								</u-checkbox>
								<view class="coupon-type">{{item.typeName}}</view>
							</view>
						</u-checkbox-group>
					</view>
				</scroll-view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		countTotalPage,
		userPage
	} from "@/api/vip.js"

	// 导入优惠券相关API
	import {
		mercSend,
		list
	} from '@/api/coupon.js'

	import getDict from '@/utils/getDict.js'

	export default {
		data() {
			return {
				keyword: null,
				list: [],
				selectedUsers: [], // 选中的用户列表
				loadmoreStatus: 'loadmore',
				page: 1, //当前分页
				size: 10, //分页数据条数
				fullHeight: 0,
				showBlack: false,
				total: 0,

				// 优惠券相关
				couponShow: false,
				searchKey: '',
				couponList: [],
				selectedCoupons: [],
				typeList: []
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			this.typeList = await this.getCouponType()

			this.search()
			this.plusTotal()
		},

		methods: {
			getCouponType() {
				return new Promise((resolve, reject) => {
					getDict('coupon_type').then(res => {
						if (res && res.length) {
							let typeList = res.map(i => ({
								name: i.msg,
								value: i.code
							}))
							resolve(typeList)
						} else {
							resolve([])
						}
					}).catch(err => {
						reject(err)
					})
				})
			},

			/**
			 * 选择用户
			 */
			selectUser(item) {
				item.checked = !item.checked;
				// 同步复选框状态
				item.checkBoxValue = item.checked ? [item.id] : [];
				this.checkboxChange();
			},

			/**
			 * 复选框项变化
			 */
			checkboxItemChange(item) {
				// 当复选框状态变化时，同步checked状态
				item.checked = item.checkBoxValue && item.checkBoxValue.length > 0;
				this.checkboxChange();
			},

			/**
			 * 复选框变化
			 */
			checkboxChange() {
				this.selectedUsers = this.list.filter(item => item.checked).map(item => ({
					id: item.id,
					memberId: item.memberId,
					tel: item.tel
				}));
			},

			/**
			 * 会员统计数据
			 */
			plusTotal() {
				countTotalPage({}).then(res => {
					let data = res.data;
					this.total = res.data.total || 0;
				})
			},

			/**
			 * 获取用户列表
			 */
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					isBlacklistSelect: this.showBlack,
					tel: this.keyword || null,
					isBalance: false
				}
				userPage(params).then(res => {
					let data = res.data.records;
					// 为每个用户添加选中状态和复选框值
					data.forEach(item => {
						item.checked = false;
						item.checkBoxValue = []; // 初始化复选框值为空数组
						// 如果已经在选中列表中，则标记为选中
						if (this.selectedUsers.some(user => user.memberId === item.memberId)) {
							item.checked = true;
							item.checkBoxValue = [item.id]; // 设置复选框值
						}
					});

					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.total = res.data.total
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus = 'loadmore' // 修正赋值操作符
				this.page = 1;
				this.size = 10;
				this.list = [];
				// 不重置已选用户
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			/**
			 * 显示优惠券选择弹框
			 */
			showCouponSelect() {
				if (this.selectedUsers.length === 0) {
					this.$u.toast('请选择至少一个用户');
					return;
				}

				this.couponShow = true;
				this.getCouponList();
			},

			/**
			 * 搜索优惠券
			 */
			searchCoupon() {
				this.getCouponList();
			},

			/**
			 * 选择优惠券
			 */
			selectCoupon(item) {
				// 检查当前优惠券是否已选中
				const index = this.selectedCoupons.indexOf(item.id);

				if (index > -1) {
					// 如果已选中，则取消选中
					this.selectedCoupons.splice(index, 1);
				} else {
					// 如果未选中，则添加到选中列表
					this.selectedCoupons.push(item.id);
				}
			},

			/**
			 * 获取优惠券列表
			 */
			getCouponList() {
				// 调用优惠券列表接口
				list({
					name: this.searchKey || null
				}).then(res => {
					if (res.data && res.data.length > 0) {
						this.couponList = res.data.map(item => ({
							id: item.id,
							name: item.name || '未命名优惠券',
							typeName: this.getCouponTypeName(item.type)
						}));
					} else {
						this.$modal.msg('暂无优惠券');
					}
				}).catch(err => {
					console.error('获取优惠券列表出错:', err);
					this.$u.toast('获取优惠券列表出错');
				});
			},

			/**
			 * 获取优惠券类型名称
			 */
			getCouponTypeName(type) {
				return this.typeList.find(item => item.value == type).name || '优惠券'
			},

			/**
			 * 优惠券选择变化
			 */
			couponChange(e) {
				console.log('选中的优惠券ID:', e);
			},

			/**
			 * 关闭优惠券弹框
			 */
			couponClose() {
				this.couponShow = false;
				this.selectedCoupons = [];
			},

			/**
			 * 确认选择优惠券
			 */
			async couponConfirm() {
				if (this.selectedCoupons.length === 0) {
					this.$u.toast('请选择至少一张优惠券');
					return false;
				}

				// 将选中的用户ID和优惠券ID传递到送券页面
				const userIds = this.selectedUsers.map(user => user.memberId);

				let sendRes = await mercSend({
					memberId: userIds,
					couponIdList: this.selectedCoupons
				})

				if (sendRes.code == 200) {
					this.$modal.msg('赠送成功');

					// 清空选中的用户
					this.selectedUsers = [];

					// 更新列表中用户的选中状态
					this.list.forEach(item => {
						item.checked = false;
						item.checkBoxValue = [];
					});
				}

				this.couponShow = false;
				this.selectedCoupons = []; // 清空选中的优惠券
				return true;
			},

			/**
			 * 黑名单开关变化
			 */
			showBlackChange() {
				this.search();
			}
		}
	}
</script>
<!-- 样式部分修改 -->
<style scoped lang="scss">
	::v-deep .u-checkbox-group {
		flex-flow: row wrap !important;
		justify-content: space-between !important;
	}

	::v-deep .u-checkbox {
		min-width: 260rpx !important;
	}

	::v-deep .u-checkbox__icon-wrap {
		margin-left: 20rpx !important;
	}

	.mr-10 {
		margin-right: 10rpx;
	}

	.ml-10 {
		margin-left: 10rpx;
	}

	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.screen-container {
			background-color: #fff;
			padding: 10rpx 13rpx 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 12rpx;
			}

			.total-list {
				>view {
					display: inline-block;
				}

				.total-item {
					padding: 0 20rpx;
				}
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 20rpx 24rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;

				>text {
					line-height: 60rpx;
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;

				>view {
					display: inline-block;
					width: 220rpx;
				}
			}
		}

		.popup-container {
			padding: 20rpx;
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}
	}


	.coupon-scroll {
		padding: 10rpx;
	}

	.coupon-item {
		padding: 20rpx 20rpx;
		border-bottom: 1px solid #f5f5f5;
	}

	.coupon-type {
		font-size: 24rpx;
		color: #777;
		background-color: #f5f8fb;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
	}
</style>