import request from '@/utils/request'

//商户订单查询 
export function page(data) {
	return request({
		url: '/merc/mini/orders/page',
		method: 'post',
		data: data
	})
}

//订单转异常
export function riskInfo(data) {
	return request({
		url: '/order/order-risk-info/riskInfo',
		method: 'post',
		data: data
	})
}

//商户订单明细查询 
export function mercOrderList(data) {
	return request({
		url: '/order/order-merc-homepage-more/orderCount/list',
		method: 'post',
		data: data
	})
}
//商户订单明细查询统计
export function mercOrderListStatic(data) {
	return request({
		url: '/order/order-merc-homepage-more/orderCount/listCount',
		method: 'post',
		data: data
	})
}
//设备订单查询 
export function deviceOrderList(data) {
	return request({
		url: '/order/order-merc-device-order/pageList',
		method: 'post',
		data: data
	})
}
//设备订单查询统计 
export function deviceOrderListStatic(data) {
	return request({
		url: '/order/order-merc-device-order/pageCount',
		method: 'post',
		data: data
	})
}


//订单详情
export function byId(data) {
	return request({
		url: '/order/order-merc-mini/byId',
		method: 'post',
		data: data
	})
}

//退款列表
export function refundList(data) {
	return request({
		url: '/order/order-refund-merc-mini/page',
		method: 'post',
		data: data
	})
}

//订单列表
export function orderPage(data) {
	return request({
		url: '/order/orders/mini/page',
		method: 'post',
		data: data
	})
}

//订单列表-统计 
export function orderPageCount(data) {
	return request({
		url: '/device/merc-mini/device/order/count',
		method: 'post',
		data: data
	})
}

//退款处理
export function hendel(data) {
	return request({
		url: '/order/order-refund/hendel',
		method: 'post',
		data: data
	})
}

//退款情况
export function refundDetail(data) {
	return request({
		url: '/order/order-refund-merc-mini/detail',
		method: 'post',
		data: data
	})
}

//订单日志
export function orderLogs(data) {
	return request({
		url: '/order/activity-info-og/list',
		method: 'post',
		data: data
	})
}

//拉黑
export function setBlacklist(data) {
	return request({
		url: '/merc/mini/member/setBlacklist',
		method: 'post',
		data: data
	})
}

//拉黑解除
export function removeBlackList(data) {
	return request({
		url: '/merc/mini/member/blackListRemove ',
		method: 'post',
		data: data
	})
}

//订单用户信息
export function userInfo(data) {
	return request({
		url: '/merc/mini/member/obj',
		method: 'post',
		data: data
	})
}

//首页角标
export function tipsCount(data) {
	return request({
		url: '/order/order-merc-homepage-mini/tipsCount',
		method: 'post',
		data: data
	})
}

//首页(本月销售)统计
export function countByMonth(data) {
	return request({
		url: '/order/order-merc-sales-count-more/countByMonth',
		method: 'post',
		data: data
	})
}

//首页(今日销售)统计
export function countByDay(data) {
	return request({
		url: '/order/order-merc-sales-count-more/dayCountDetail',
		method: 'post',
		data: data
	})
}

//订单主动退款
export function refundByMerc(data) {
	return request({
		url: '/order/order-refund-merc-mini/refundByMerc',
		method: 'post',
		data: data
	})
}

/**
 * 添加下次补单
 * @param {Object} data
 */
export function nextPayOrders(data) {
	return request({
		url: '/order/orders-extend-next/nextPayOrders',
		method: 'post',
		data: data
	})
}

/**
 * 关闭订单
 * @param {Object} data
 */
export function cancelOrder(data) {
	return request({
		url: '/order/orders/cancel',
		method: 'post',
		data: data
	})
}

/**
 * 重试扣款
 * @param {Object} data
 */
export function tradeRetry(data) {
	return request({
		url: '/order/order-merc-mini/tradeRetry',
		method: 'post',
		data: data
	})
}

/**
 *本月统计-销售统计
 * @param {Object} data
 */
export function countSalesByMonth(data) {
	return request({
		url: '/order/order-merc-sales-count-more/countSalesByMonth',
		method: 'post',
		data: data
	})
}

/**
 *客诉角标
 * @param {Object} data
 */
export function complaintCount(data) {
	return request({
		url: '/merc/merc-complaint/count',
		method: 'post',
		data: data
	})
}

/**
 *支付宝设备捞取视频
 * @param {Object} data
 */
export function alipayGetVideo(data) {
	return request({
		url: '/order/orders/alipayGetVideo',
		method: 'post',
		data: data
	})
}

/**
 *支付宝设备捞取视频
 * @param {Object} data
 */
export function alipayQueryVideo(data) {
	return request({
		url: '/order/orders/alipayQueryVideo',
		method: 'post',
		data: data
	})
}

/**
 *PLUS会员总收入
 * @param {Object} data
 */
export function totalByMerc(data) {
	return request({
		url: '/order/order-plus-member-mini/totalByMerc',
		method: 'post',
		data: data
	})
}

/**
 *PLUS会员总收入按天分组
 * @param {Object} data
 */
export function totalGroupDay(data) {
	return request({
		url: '/order/order-plus-member-mini/totalGroupDay',
		method: 'post',
		data: data
	})
}

/**
 *会员总收入按天分组
 * @param {Object} data
 */
export function totalGroupDayRec(data) {
	return request({
		url: '/order/order-member-money-mini/totalGroupDay',
		method: 'post',
		data: data
	})
}

/**
 *会员总收入
 * @param {Object} data
 */
export function totalByMercRec(data) {
	return request({
		url: '/order/order-member-money-mini/totalByMerc',
		method: 'post',
		data: data
	})
}










