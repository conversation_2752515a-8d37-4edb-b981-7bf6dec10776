<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="销售排行"></u-navbar>
		<view class="content">
			<view class="card">
				<view class="select">
					<view class="time-tab flex justify-between">
						<block v-for="(item,index) in timeList" :key="item.id">
							<view class="time-tab-item" :class="[timeTabCurrent==item.id?'time-tab-show':'']"
								@click="timeTabClick(item.id)">
								{{item.name}}
							</view>
						</block>
					</view>
					<view class="time-select flex justify-around">
						<view class="flex  align-center">
							<view @click="timePickerShow('start')">
								{{timeStart?timeStart:'开始时间'}}
							</view>
							<view>
								一
							</view>
							<view @click="timePickerShow('end')">
								{{timeEnd?timeEnd:'结束时间'}}
							</view>
						</view>
					</view>
				</view>
				<view class="total flex justify-around">
					<view class="flex flex-direction align-center">
						<view class="flex align-center justify-center total-name">
							<text style="margin-right: 6rpx;">成交额(元)</text>
							<u-icon name="question-circle-fill" size="14" @click="showToast(0)"></u-icon>
						</view>
						<view class="total-num" style="color: #2C6FF3;">
							{{$xy.delMoney(total.salesMoney)}}
						</view>
					</view>
					<view class="flex flex-direction align-center">
						<view class="flex align-center justify-center total-name">
							<text style="margin-right: 6rpx;">成交笔数(笔)</text>
							<u-icon name="question-circle-fill" size="14" @click="showToast(1)"></u-icon>
						</view>
						<view class="total-num" style="color: #F9B237;">
							{{total.salesCount||0}}
						</view>
					</view>
					<view class="flex flex-direction align-center">
						<view class="total-name">
							客单价(元)
						</view>
						<view class="total-num" v-if="total.salesCount>0" style="color: #E94F4F;">
							{{($xy.delMoney(total.salesMoney)/total.salesCount).toFixed(2)||0}}
						</view>
						<view class="total-num" v-else style="color:#E94F4F;">
							0
						</view>
					</view>
				</view>
			</view>

			<view class="card">
				<!-- <view class="chart-tab">
					<view
						:class="[chartCurrent==1?'chart-tab-item flex align-center chart-tab-item-show1':'chart-tab-item flex align-center']"
						@click="chartTabChange(1)">
						销售金额
					</view>
					<view
						:class="[chartCurrent==0?'chart-tab-item flex align-center chart-tab-item-show2':'chart-tab-item flex align-center']"
						@click="chartTabChange(0)">
						销售笔数
					</view>
				</view> -->

				<view class="table-title flex justify-between align-center" style="border-bottom: 1rpx solid #fff;">
					<view class="title">
						成交额/成交笔数
					</view>
					<view class="sort-type flex">
						<view :class="[chartCurrent==1?'sort-type-item sort-type-show':'sort-type-item']"
							@click="chartTabChange(1)">
							成交金额
						</view>
						<view :class="[chartCurrent==0?'sort-type-item sort-type-show':'sort-type-item']"
							@click="chartTabChange(0)">
							成交笔数
						</view>
					</view>
				</view>

				<view class="chart" style="height: 680rpx;">
					<qiun-data-charts type="column" canvasId="canvasString" :ontouch="true" :canvas2d="true"
						:opts="opts" :chartData="chartData" :errorMessage="errorMessage" />
				</view>
			</view>
			<view class="device-table card">
				<view class="table">
					<view class="table-title flex justify-between align-center">
						<view class="title">
							设备销售排行
							<!-- <text style="font-size: 24rpx;color:#a6a6a6;font-weight: normal;"
								v-if="timeTabCurrent!=1">(不含当日)</text> -->
						</view>
						<view class="sort-type flex">
							<view :class="[deviceSortType==0?'sort-type-item sort-type-show':'sort-type-item']"
								@click="deviceSort(0)">
								成交额
							</view>
							<view :class="[deviceSortType==1?'sort-type-item sort-type-show':'sort-type-item']"
								@click="deviceSort(1)">
								成交笔数
							</view>
						</view>
					</view>
					<uni-table :border="false" :stripe="false" emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th align="center" width="52">排名</uni-th>
							<uni-th align="center" width="110">上榜设备</uni-th>
							<uni-th align="center" width="92">成交额</uni-th>
							<uni-th align="center" width="92">成交笔数</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(item,index) in list1" :key="item.deviceId">
							<uni-td v-if="index==0">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/first-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else-if="index==1">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/second-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else-if="index==2">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/third-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else>
								<view class="table-td">
									{{index+1}}
								</view>
							</uni-td>
							<uni-td>
								<view class="table-td table-td-name">{{item.deviceName||item.deviceId}}</view>
							</uni-td>
							<uni-td>
								<view class="table-td">￥{{$xy.delMoney(item.salesMoney)}}</view>
							</uni-td>
							<uni-td>
								<view class="table-td">{{item.salesCount}}笔</view>
							</uni-td>
						</uni-tr>
					</uni-table>
				</view>
				<view class="more" @click="more('device')" v-if="list1.length==5">
					查看更多<view><u-icon name="arrow-right" color="#2C6FF3"></u-icon></view>
				</view>
			</view>

			<view class="goods-table card">
				<view class="table">
					<view class="table-title flex justify-between align-center">
						<view class="title">
							商品销售排行
							<!-- <text style="font-size: 24rpx;color:#a6a6a6;font-weight: normal;"
								v-if="timeTabCurrent!=1">(不含当日)</text> -->
						</view>
						<view class="sort-type flex">
							<view :class="[goodsSortType==0?'sort-type-item sort-type-show':'sort-type-item']"
								@click="goodsSort(0)">
								成交额
							</view>
							<view :class="[goodsSortType==1?'sort-type-item sort-type-show':'sort-type-item']"
								@click="goodsSort(1)">
								销量
							</view>
						</view>
					</view>
					<uni-table :border="false" :stripe="false" emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th align="center" width="52">排名</uni-th>
							<uni-th align="center" width="110">上榜商品</uni-th>
							<uni-th align="center" width="92">成交额</uni-th>
							<uni-th align="center" width="92">销量</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(item,index) in list2" :key="item.goodsId">
							<uni-td v-if="index==0">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/first-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else-if="index==1">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/second-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else-if="index==2">
								<view class="table-td">
									<image class="table-img" src="../../static/images/global/third-class.png"
										mode="widthFix"></image>
								</view>
							</uni-td>
							<uni-td v-else>
								<view class="table-td">
									{{index+1}}
								</view>
							</uni-td>
							<uni-td>
								<view class="table-td table-td-name">{{item.goodsName}}</view>
							</uni-td>
							<uni-td>
								<view class="table-td">￥{{$xy.delMoney(item.salesMoney)}}</view>
							</uni-td>
							<uni-td>
								<view class="table-td">{{item.goodsCount}}件</view>
							</uni-td>
						</uni-tr>
					</uni-table>
				</view>
				<view class="more" @click="more('goods')" v-if="list2.length==5">
					查看更多<view><u-icon name="arrow-right" color="#2C6FF3"></u-icon></view>
				</view>
			</view>
		</view>

		<u-datetime-picker :show="timeShow" :mode="timeMode" v-model="time" @confirm="confirm"
			:closeOnClickOverlay="true" @close="close" @cancel="close"></u-datetime-picker>
		<u-picker :show="pickerShow" :columns="columns" @confirm="pickerComfirm" :closeOnClickOverlay="true"
			@close="pickerClose" @cancel="pickerClose"></u-picker>
	</view>
</template>

<script>
	import {
		sumPage
	} from "@/api/commodity/goods.js"

	import {
		sumCount as deviceSumCount,
		sumPage as deviceSumPage,
		salesData
	} from "@/api/device/device.js"

	import uCharts from "@/uni_modules/qiun-data-charts/js_sdk/u-charts/u-charts.js"

	export default {
		data() {
			return {
				timeType: ['日', '月', '年'],
				timeCurrent: 0,
				timeStart: '',
				timeEnd: '',
				deviceSortType: 0,
				goodsSortType: 0,
				timeShow: false,
				timeMode: 'date',
				startOrEnd: '',
				time: new Date(),
				pickerType: 'year',

				pickerShow: false,
				// typeColumns: [
				// 	['销售额从高到低', '销售额从低到高', '销售数量从高到低', '销售数量从低到高']
				// ],

				typeColumns: [
					['成交额', '成交笔数']
				],

				page: 1, //商品分页
				size: 10,

				status: 'loadmore', //加载更多
				list1: [], //设备列表
				list2: [], //商品列表
				fullHeight: 0,

				total: {
					goodsCount: 0,
					salesMoney: 0
				},


				tabCurrent: 0,
				timeList: [{
						id: 0,
						name: '昨天'
					},
					{
						id: 1,
						name: '今天'
					},
					{
						id: 2,
						name: '近7日'
					},
					{
						id: 3,
						name: '近30日'
					},
					{
						id: 4,
						name: '本月'
					},
				],
				timeTabCurrent: 1,
				title: '设备成交额排行',

				opts: {
					// touchMoveLimit: 24,
					legend: {
						show: false,
						position: 'top',
						float: 'right',
						padding: 20,
						itemGap: 20
					},
					padding: [20, 20, 10, 20],
					xAxis: {
						labelCount: 5,
					},
					color: [],
					yAxis: {
						gridType: "solid",
						dashLength: 2,
						showTitle: true,
						data: [{
							position: "left",
							title: "/元",
							axisLineColor: '#fff',
							min: 0
						}]
					},
				},

				errorMessage: '无数据',
				chartData: {
					categories: [],
					series: []
				},

				tempChartData: {
					categories: [],
					series: []
				},

				chartCurrent: 1,
				color: ["#F9B237", "#2C6FF3"],
			}
		},

		watch: {
			// tabCurrent: {
			// 	handler(newVal, oldVal) {
			// 		if (newVal == 0) {
			// 			this.title = '设备销售额排行'
			// 		}
			// 		if (newVal == 1) {
			// 			this.title = '商品销售额排行'
			// 		}

			// 	},
			// 	deep: true
			// }
		},

		onShow() {
			let timeObj = this.setResetTime(this.timeTabCurrent)
			this.timeStart = timeObj.start
			this.timeEnd = timeObj.end
			this.getData()
		},
		methods: {
			// chartComplete(e) {
			// 	console.log(e)
			// 	let uChartsInstance = {}
			// 	if(this.chartData.categories&&this.chartData.categories.length>20){
			// 		uChartsInstance[e.id] = new uCharts(e.opts);
			// 		uChartsInstance[e.id].translate(-1000)
			// 	}
			// },
			chartTabChange(type) {
				console.log(type)
				this.chartCurrent = type;
				let tempData = {}
				if (this.tempChartData.categories && this.tempChartData.categories.length > 0) {
					tempData = {
						categories: this.tempChartData.categories,
						series: [this.tempChartData.series[type]]
					}
				} else {
					tempData = {
						categories: [],
						series: []
					}
				}
				this.chartData = JSON.parse(JSON.stringify(tempData))
				this.opts.color = [this.color[type]]
				this.opts.yAxis.data[0].title = type == 0 ? "/笔" : "/元"
			},

			getData() {
				this.getList1(this.deviceSortType)
				this.getList2(this.goodsSortType)
				this.getDeviceTotal(this.deviceSortType)
				this.getCountData()
			},

			timeTabClick(e) {
				this.timeTabCurrent = e
				let timeObj = this.setResetTime(this.timeTabCurrent)
				this.timeStart = timeObj.start
				this.timeEnd = timeObj.end
				this.getData()
			},


			setResetTime(type) {
				let date = new Date()
				let time = {
					start: uni.$u.timeFormat(date, 'yyyy-mm-dd'),
					end: uni.$u.timeFormat(date, 'yyyy-mm-dd')
				}
				switch (type) {
					case 0: //昨天
						time = {
							start: uni.$u.timeFormat(date - 24 * 60 * 60 * 1000, 'yyyy-mm-dd'),
							end: uni.$u.timeFormat(date - 24 * 60 * 60 * 1000, 'yyyy-mm-dd')
						}
						break;
					case 1: //今天
						time = {
							start: uni.$u.timeFormat(date, 'yyyy-mm-dd'),
							end: uni.$u.timeFormat(date, 'yyyy-mm-dd')
						}
						break;
					case 2: //近7日
						time = {
							start: uni.$u.timeFormat(date - 7 * 24 * 60 * 60 * 1000, 'yyyy-mm-dd'),
							end: uni.$u.timeFormat(date, 'yyyy-mm-dd')
						}
						break;
					case 3: //近30日
						time = {
							start: uni.$u.timeFrom(date - 30 * 24 * 60 * 60 * 1000, 'yyyy-mm-dd'),
							end: uni.$u.timeFrom(date, 'yyyy-mm-dd')
						}
						break;
					case 4: //本月
						let start = uni.$u.timeFrom(date, 'yyyy-mm-dd')
						time = {
							start: start.substr(0, 8) + '01',
							end: uni.$u.timeFrom(date, 'yyyy-mm-dd')
						}
						break;
					default:
						break;
				}
				return time
			},

			timePickerShow(type) {
				this.pickerType = 'year';
				this.startOrEnd = type
				if (this.timeCurrent == 2) {
					this.columns = yearList
					this.pickerShow = true
				} else {
					this.timeShow = true;
				}
			},

			deviceSort(type) {
				this.deviceSortType = type
				this.getList1(type)
			},

			goodsSort(type) {
				this.goodsSortType = type
				this.getList2(type)
			},

			close() {
				this.timeShow = false
			},

			confirm(e) {
				let time = '';
				time = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')

				if (this.startOrEnd == 'start') {
					this.timeStart = time
				}
				if (this.startOrEnd == 'end') {
					this.timeEnd = time
				}

				//开始时间大于结束时间
				if (this.timeStart && this.timeEnd && new Date(this.timeStart) > new Date(this.timeEnd)) {
					this.$modal.msg('开始时间不能大于结束时间');
					return
				}
				this.getData()
				this.timeShow = false;
			},

			pickerClose() {
				this.pickerShow = false
			},

			//获取统计数据
			getCountData(type) {
				salesData({
					type: "day",
					beginDate: this.timeStart,
					endDate: this.timeEnd
				}).then(res => {
					let data = res.data;
					this.delData(data);
					this.tempChartData.series = data.series ? data.series : [];
					this.tempChartData.categories = data.categories ? data.categories : [];
					this.chartTabChange(1)
				}).catch(err => {

				})
			},

			// 处理统计图表数据
			delData(data) {
				let arr = data.categories.map(item => {
					if (item.length < 3) {
						item = item + '点'
					} else {
						item = item.substr(4, 2) + '月' + item.substr(6, 2) + '日'
					}
					return item
				})

				data.categories = arr
			},

			getParams(type) {
				let orderByKey = "";
				let orderBy = "";
				switch (type) {
					case '成交额':
						orderBy = 'desc';
						orderByKey = 'sales_money';
						break;
					case '成交笔数':
						orderBy = 'desc';
						orderByKey = 'sales_count';
						break;
					case '销量':
						orderBy = 'desc';
						orderByKey = 'goods_count';
						break;
					default:
						break;
				}

				let params = {
					type: 'day',
					orderByKey: orderByKey,
					orderBy: orderBy,
					beginDate: this.timeStart,
					endDate: this.timeEnd
				}
				return params
			},

			//设备统计总数居
			getDeviceTotal(type) {
				let params = this.getParams(type)
				deviceSumCount(params).then(res => {
					if (res.data) {
						this.total = res.data
					}
				})
			},

			//获取设备排行
			getList1(e) {
				let type = e == 0 ? '成交额' : '成交笔数';
				let dataParams = this.getParams(type)
				let pageParams = {
					page: {
						current: 1,
						size: 5
					}
				}
				let params = Object.assign(dataParams, pageParams)
				deviceSumPage(params).then(res => {
					if (res.data) {
						this.list1 = res.data.records;
					} else {
						this.list1 = []
					}
				})
			},

			//获取商品排行
			getList2(e) {
				let type = e == 0 ? '成交额' : '销量';
				let dataParams = this.getParams(type)
				let pageParams = {
					page: {
						current: 1,
						size: 5
					}
				}
				let params = Object.assign(dataParams, pageParams)
				sumPage(params).then(res => {
					if (res.data) {
						this.list2 = res.data.records;
					} else {
						this.list2 = []
					}
				})
			},

			more(type) {
				let sortType = type == 'device' ? this.deviceSortType : this.goodsSortType
				this.$tab.navigateTo(
					`/pages/globalPages/statisticsMore?type=${type}&&timeStart=${this.timeStart}&&timeEnd=${this.timeEnd}&&sortType=${sortType}`
				)
			},

			showToast(index) {
				let msg=['成交额不包含退款金额!','成交笔数不包含零元单！']
				this.$modal.msg(msg[index])
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {
			padding-bottom: 24rpx;

			.card {
				width: 724rpx;
				margin-left: 13rpx;
				background-color: #fff;
				margin-top: 20rpx;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
				border-radius: 14rpx;
				overflow: hidden;
			}

			.tab {
				padding: 24rpx;
				// background-color: #fff;
			}

			.select {
				padding-top: 34rpx;

				.time-tab {
					padding: 0 36rpx;

					.time-tab-item {
						width: 20%;
						border: 1rpx solid #CCCCCC;
						border-left: none;
						text-align: center;
						font-size: 26rpx;
						line-height: 58rpx;
						color: #777777;

						&:first-of-type {
							border-left: 1rpx solid #CCCCCC;
							border-radius: 6rpx 0px 0px 6rpx;
						}

						&:last-of-type {
							border-radius: 0px 6rpx 6rpx 0px;
						}

						&.time-tab-show {
							background: #F4F8FF;
							color: #2C6FF3;
						}
					}
				}

				.time-select {
					padding: 34rpx 36rpx;
					color: rgb(144, 144, 144);
					font-size: 28rpx;

					>view {
						>view:nth-child(1) {
							width: 160rpx;
							text-align: right;
						}

						>view:nth-child(2) {
							width: 80rpx;
							padding: 0 24rpx;
						}
					}
				}
			}

			.total {
				padding: 12rpx 0;
				background-color: #fff;

				.total-name {
					color: #333;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 48rpx;
				}

				.total-num {
					color: #eab09a;
					font-size: 42rpx;
					font-weight: bold;
					line-height: 41rpx;
					margin-top: 24rpx;
				}
			}

			.search {
				padding: 24rpx 24rpx;
				background-color: #fff;
				position: relative;
				font-size: 26rpx;

				.time-type {
					width: 180rpx;
				}

				.time-select {
					width: 340rpx;
					line-height: 64rpx;
					background-color: #eeeeef;
					border-radius: 6rpx;
				}


			}

			.chart {
				background-color: #fff;
			}

			.chart-tab {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				margin-right: 24rpx;
				margin-top: 20rpx;

				.chart-tab-item {
					font-size: 24rpx;
					color: #676767;
					margin-left: 24rpx;
					line-height: 44rpx;

					&::before {
						content: '';
						width: 30rpx;
						height: 20rpx;
						background-color: #909090;
						margin-right: 14rpx;
					}

					&.chart-tab-item-show1::before {
						background-color: #F9B237;
					}

					&.chart-tab-item-show2::before {
						background-color: #2C6FF3;
					}
				}
			}

			.table-title {
				font-size: 32rpx;
				line-height: 50rpx;
				padding: 24rpx;
				font-weight: 800;
				color: #333333;
				border-bottom: 1rpx solid #ebeef5;
			}

			.sort-type {
				width: 250rpx;
				height: 50rpx;
				line-height: 48rpx;
				text-align: center;
				font-weight: normal;

				.sort-type-item {
					width: 50%;
					font-size: 26rpx;
					color: #555555;
					background-color: #fff;

					&.sort-type-item:nth-child(1) {
						border-top-left-radius: 6rpx;
						border-bottom-left-radius: 6rpx;
						border-top: 1rpx solid #CCCCCC;
						border-left: 1rpx solid #CCCCCC;
						border-bottom: 1rpx solid #CCCCCC;

						&.sort-type-show {
							color: #fff;
							border-top: 1rpx solid #2C6FF3;
							border-left: 1rpx solid #2C6FF3;
							border-bottom: 1rpx solid #2C6FF3;
							background-color: #2C6FF3;
						}
					}

					&.sort-type-item:nth-child(2) {
						border-top-right-radius: 6rpx;
						border-bottom-right-radius: 6rpx;
						border-top: 1rpx solid #CCCCCC;
						border-right: 1rpx solid #CCCCCC;
						border-bottom: 1rpx solid #CCCCCC;

						&.sort-type-show {
							color: #fff;
							border-top: 1rpx solid #2C6FF3;
							border-right: 1rpx solid #2C6FF3;
							border-bottom: 1rpx solid #2C6FF3;
							background-color: #2C6FF3;
						}
					}
				}

			}

			.table {
				width: 724rpx;
				background-color: #fff;
				color: #333;



				.table-td {
					width: 100%;
					text-align: center;


					&.table-td-name {
						width: 230rpx;
						white-space: wrap;
					}
				}

				.table-img {
					width: 40rpx;
					height: 40rpx;
					position: relative;
				}

				::v-deep.uni-table-th {
					color: #333;
					font-weight: normal;
				}

				::v-deep.uni-table-td {
					vertical-align: middle;
				}
			}

			.more {
				text-align: right;
				padding-right: 40rpx;
				position: relative;
				color: #2C6FF3;
				line-height: 80rpx;
				background-color: #fff;

				>view {
					position: absolute;
					right: 12rpx;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
	}
</style>