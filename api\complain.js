import request from '@/utils/request'
//列表
export function page(data) {
	return request({
		url: '/merc/merc-complaint/page',
		method: 'post',
		data: data
	})
}

//处理
export function handle(data) {
	return request({
		url: '/merc/merc-complaint/handle',
		method: 'post',
		data: data
	})
}

//互动记录
export function logInfo(data) {
	return request({
		url: '/merc/merc-complaint/logInfo',
		method: 'post',
		data: data
	})
}

//统计
export function count(data) {
	return request({
		url: '/merc/merc-complaint/count',
		method: 'post',
		data: data
	})
}

//退款
export function refundForComplaint(data) {
	return request({
		url: '/order/order-refund-merc-mini/refundForComplaint',
		method: 'post',
		data: data
	})
}
