<template>
	<view class="container">
		<image src="https://ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/rech-successful.png"
			mode="widthFix"></image>
		<view class="tips">
			{{title}}成功
		</view>
		<view class="num">
			￥<text>{{$xy.delMoney(money)}}</text>
		</view>

		<view class="btn safe-bottom">
			<xbutton size="large" width="672rpx" round="130rpx" @click="submit">完成</xbutton>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				money: 0,
				type: null
			}
		},
		onLoad(o) {
			this.type = o.type
			this.money = o.money
		},
		computed: {
			title() {
				let str = '';
				switch (this.type) { // 1设备管理费，2算法服务费(按笔)，3算法服务费(包月)，4流量卡缴费，5余额充值，6会员功能激活
					case '1':
						str = '设备管理费用充值'
						break;
					case '2':
						str = '算法服务费充值'
						break;
					case '3':
						str = '算法服务费充值'
						break;
					case '4':
						str = '流量卡缴费'
						break;
					case '5':
						str = '余额充值'
						break;
					case '6':
						str = '会员功能激活'
						break;
					case '7':
						str = '积分功能激活'
						break;
					case '8':
						str = '短信充值'
						break;
					case '9':
						str = '会员手机号充值'
						break;
					case '10':
						str = '流量卡购买'
						break;
					default:
						break;
				}
				return str
			}
		},
		methods: {
			submit() {
				uni.$emit('refresh')
				uni.navigateBack({
					delta: this.getCompletePage()
				})
			},

			//获取返回的页面层级
			getCompletePage() {
				//返回页面维护
				let backPages = [
					'pages/globalPages/home',
					'pages/recharge/devManaFee',
					'pages/recharge/devSimFee',
					'pages/recharge/payRecord',
					'pages/activeDevice/deviceManage',
					'pages/vip/vipSetting',
					'pages/integral/integralSet',
					'pages/recharge/smsRecord',
					'pages/recharge/telRecord',
					'pages/recharge/dataCardBuy'
				]
				var pages = getCurrentPages()
				let pageIndex = 0
				pages.forEach((item, index) => {
					if (backPages.findIndex(i => i == item.route) != -1) {
						pageIndex = index + 1
					}
				})
				return pages.length - pageIndex
			},
		}
	}
</script>

<style scoped lang="scss">
	.container {
		text-align: center;
		overflow: hidden;

		>image {
			width: 80rpx;
			height: 80rpx;
			margin-top: 350rpx;
		}

		.tips {
			color: #333;
			font-size: 32rpx;
			margin-top: 100rpx;
		}

		.num {
			font-size: 70rpx;
			font-weight: bold;
			margin-top: 40rpx;
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
			z-index: 999;
		}
	}
</style>