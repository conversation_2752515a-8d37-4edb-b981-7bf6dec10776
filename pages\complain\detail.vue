<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="title"></u-navbar>
		<view class="content safe-bottom">
			<view class="title">
				{{detail.problemDescription||'-'}}
			</view>
			<view class="rows">
				投诉单号：{{detail.id}}<text class="under-line-text" @click.stop="$xy.copy(detail.id)">复制</text>
			</view>
			<view class="rows">
				会员手机：{{detail.memberTel||'-'}}<text class="under-line-text"
					@click.stop="$xy.copy(detail.memberTel)">复制</text>
			</view>
			<view class="rows">
				投诉时间：{{detail.complaintTime||''}}
			</view>
			<view class="rows">
				投诉来源：{{detail.source==1?"微信":"支付宝"}}
			</view>
			<view class="rows">
				用户留言：<text style="color:red;">{{detail.userDetail||'-'}}</text>
			</view>
			<view class="rows">
				问题类型：{{detail.problemType||'-'}}
			</view>
			<view class="rows">
				订单号：{{detail.orderId}}<text class="under-line-text"
					@click.stop="$xy.copy(detail.orderId)">复制</text><text class="under-line-text red"
					@click.stop="orderDetail(detail.orderId)">查看订单</text>
			</view>
			<view class="rows">
				商户单号：{{detail.payQueryOrderId}}<text class="under-line-text"
					@click.stop="$xy.copy(detail.payQueryOrderId)">复制</text>
			</view>
			<view class="rows">
				交易单号：{{detail.payBuyOrderId}}<text class="under-line-text"
					@click.stop="$xy.copy(detail.payBuyOrderId)">复制</text>
			</view>

			<view class="rows">
				申请退款金额：{{$xy.delMoney(detail.refundAmount)}}
			</view>

			<view class="rows">
				关联设备：{{detail.deviceId||'-'}}
			</view>

			<view class="rows">
				处理备注：{{detail.handleMsg||'-'}}
			</view>

			<view class="rows">
				归属商户：{{detail.mercName||'-'}}
			</view>
		</view>

		<view class="btn safe-bottom">
			<xbutton v-if="checkPermi(['complain:delwith'])" size="large" width="702rpx" round="130rpx" @click="del">处理</xbutton>
		</view>

		<xpopup :show="show" @close="close" :showBtn="false" mode="center">
			<view class="popup-content flex flex-direction align-center">
				<view class="tab" v-if="show">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'30rpx'}"
						:inactiveStyle="{fontSize:'28rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>

				<view class="tab-content">
					<scroll-view style="height:600rpx;" scroll-y scroll-with-animation>
						<block v-if="current==0">
							<view>
								<block v-for="(item,index) in msg" :key="item.logId">
									<xtimeLine :leftTime="item.operateTime">
										<view class="tripItem">
											<view class="title">发起人：{{item.operator}}</view>
											<view class="tips">内容：{{item.operateDetails}}</view>
										</view>
									</xtimeLine>
								</block>
							</view>
						</block>
						<block v-else>
							<view class="refund-detail" style="padding:0 20rpx;">
								<view class="refund-item">
									会员手机：{{detail.memberTel||'-'}}
								</view>
								<view class="refund-item">
									问题说明：<text style="color:red;">{{detail.problemDescription||'-'}}</text>
								</view>
								<view class="refund-item">
									用户留言：<text style="color:red;">{{detail.userDetail||'-'}}</text>
								</view>
								<view class="refund-item">
									申请退款金额：
									<u--input placeholder="请输入退款金额" border="surround" type="digit"
										v-model="money"></u--input>
								</view>
							</view>
						</block>
					</scroll-view>
					<view class="del-back">
						<view v-if="current==0">
							<view class="flex">
								<view class="back">回复： </view>
								<u--textarea v-model="handleMsg" placeholder="请输入内容"></u--textarea>
							</view>
							<view class="btn-box flex justify-end">

								<xbutton @click="sure" delay="2000" width="130rpx">确定
								</xbutton>
								<xbutton @click="close" width="130rpx" bgColor="#fff" borderColor="#2C6FF3"
									style="margin-left: 12rpx;" color="#2C6FF3">关闭</xbutton>
							</view>
						</view>
						<view v-else>
							<view class="flex">
								<view class="back">回复： </view>
								<u--textarea v-model="handleMsg" placeholder="请输入内容"></u--textarea>
							</view>

							<view class="btn-box flex justify-end">
								<xbutton @click="refuse" bgColor="red" borderColor="red" color="#fff" delay="2000"
									width="130rpx" style="margin-left: 12rpx;">拒绝退款
								</xbutton>
								<xbutton @click="refund" delay="2000" width="130rpx" style="margin-left: 12rpx;">确认退款
								</xbutton>
								<xbutton @click="close" width="130rpx" bgColor="#fff" borderColor="#2C6FF3"
									style="margin-left: 12rpx;" color="#2C6FF3">退出</xbutton>
							</view>
						</view>
					</view>
				</view>
			</view>
		</xpopup>

	</view>
</template>

<script>
	import {
		page,
		logInfo,
		handle,
		refundForComplaint
	} from "@/api/complain.js"
	export default {
		data() {
			return {
				id: null,
				title: '',
				detail: {
					refundAmount: 0
				},
				options: [],
				show: false,
				tabList: [{
						name: '互动记录'
					},
					{
						name: '退款处理'
					},
				],
				current: 0,
				msg: [],
				handleMsg: '',
				money: 0
			}
		},
		async onLoad(o) {
			this.title = o.title
			this.id = o.id

			await this.getDict('complaints_config_problem_type').then(res => {
				let newData = []
				res.forEach(item => {
					newData.push({
						name: item.msg,
						code: item.code
					})
				})
				this.options = newData;
			})
			this.getDetail()
		},
		methods: {
			getDetail() {
				page({
					page: {
						current: 1,
						size: 1
					},
					id: this.id,
				}).then(res => {
					let data = res.data.records[0];
					for (let item of this.options) {
						if (item.code == data.problemType) {
							data.problemType = item.name;
							break
						}
					}
					this.detail = data
				})
			},

			del() {
				this.getMsg()
				this.show = true
			},



			tabClick(e) {
				this.current = e.index
				this.handleMsg = ''
				this.money = this.$xy.delMoney(this.detail.refundAmount)
			},

			getMsg() {
				logInfo({
					id: this.id
				}).then(res => {
					this.msg = res.data
				})
			},

			close() {
				this.show = false
			},

			sure() {
				handle({
					id: this.id,
					handleMsg: this.handleMsg,
					reject: false
				}).then(res => {
					uni.$emit('refresh')
					setTimeout(() => {
						this.$modal.msg('回复成功')
					}, 1000)
					this.close()
				})
			},

			refund() {
				refundForComplaint({
					"orderId": this.detail.orderId,
					"refundMoney": this.$xy.delMoneyL(this.money),
					"memberId": this.detail.memberId
				}).then(res => {
					uni.$emit('refresh')
					setTimeout(() => {
						this.$modal.msg('提交成功')
					}, 1000)
					this.close()

				})
			},

			refuse() {
				handle({
					id: this.id,
					handleMsg: this.handleMsg,
					reject: true
				}).then(res => {
					uni.$emit('refresh')
					setTimeout(() => {
						this.$modal.msg('处理成功')
					}, 1000)
					this.close()
				})
			},

			orderDetail(id) {
				this.$tab.navigateTo('/pages/order/orderDetails?id=' + id)
			},
		}
	}
</script>

<style scoped lang="scss">
	.container {
		padding: 20rpx 20rpx 0;

		.content {
			padding: 24rpx;
			background-color: #fff;
			border-radius: 12rpx;

			.title {
				font-size: 30rpx;
				font-weight: bold;
			}

			.rows {
				margin-top: 16rpx;
				padding-left: 12rpx;
			}

			.under-line-text {
				font-size: 26rpx !important;
				font-weight: 500;
				font-style: italic;
				text-decoration: underline;
				color: #2C6FF3 !important;
				margin-left: 12rpx;

				&.red {
					color: red !important;
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
		}

		.popup-content {
			width: 702rpx;
			padding: 20rpx;

			.tab {
				width: 100%;
				padding-bottom: 30rpx;
			}

			.tab-content {
				width: 100%;
			}

			.refund-detail {
				.refund-item {
					line-height: 50rpx;
				}
			}

			.del-back {
				padding: 20rpx 0;
				width: 100%;

				.back {
					line-height: 50rpx;
					font-weight: bold;
					padding-left: 12rpx;
				}

				.btn-box {
					margin-top: 20rpx;
				}
			}
		}
	}
</style>