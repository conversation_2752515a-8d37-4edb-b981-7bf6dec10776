<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="数据报表"></u-navbar>
		<view class="content">
			<view class="btn flex justify-between">
				<xhelpPopup guideId="BB0001" />
				<xbutton padding="15rpx 40rpx" @click="add">新增</xbutton>
			</view>
			<view class="list-warp">
				<block v-for="(item, index) in list" :key="item.type">
					<view class="item-wrap xy-card"
						@click="$tab.navigateTo(`/pages/report/listDetail?type=${item.type}&&typeName=${item.msg}`)">
						<view class="item-head flex justify-between">
							<view class="title">
								{{item.msg||'无'}}
							</view>
							<view class="status">
								<view class="com-status" style="color: #bdbdbd;" v-if="item.reportInfo.status==1">生成中
								</view>
								<view class="com-status" style="color: #00aa00;" v-if="item.reportInfo.status==2">生成完成
								</view>
								<view class="com-status" style="color: #FF1E1E;" v-if="item.reportInfo.status==3">生成超时
								</view>
							</view>
						</view>
						<view class="item-body" v-if="item.reportInfo.status">
							<view class="item-body-sec">
								<view>报表名称：</view>{{item.reportInfo.name||'无'}}
							</view>
							<view class="item-body-sec">
								<view>创建时间：</view>{{item.reportInfo.createTime||'无'}}
							</view>
						</view>
						<view class="item-body-none" v-else>
							暂无任何报表~
						</view>
						<view class="item-foot flex justify-end">
							<view class="view flex align-center">
								<text>查看</text>
								<u-icon name="arrow-right" color="#2C6FF3" size="16px"></u-icon>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		typs,
		reportNews
	} from '@/api/report.js'
	export default {
		data() {
			return {
				list: []
			}
		},

		onShow() {
			this.loadData()
		},

		methods: {
			async loadData() {
				let list = await this.getList()
				let news = await this.getNews()
				let newList = list.map(i => {
					for (let j = 0; j < news.length; j++) {
						let item = news[j]
						if (item.type == i.type) {
							i.reportInfo = item.reportInfo
							return i
						}
					}
				})
				this.list = newList
			},

			getList() {
				return new Promise((resolve, reject) => {
					typs().then(res => {
						resolve(res.data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			getNews() {
				return new Promise((resolve, reject) => {
					reportNews().then(res => {
						resolve(res.data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			add() {
				this.$tab.navigateTo('/pages/report/createReport')
			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #555;

		.content {
			overflow: hidden;
			padding: 0 13rpx;

			.btn {
				margin-top: 20rpx;
			}

			.list-warp {
				margin-top: 10rpx;

				.item-wrap {
					+.item-wrap {
						margin-top: 20rpx;
					}

					.item-head {
						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}

						.status {
							font-size: 28rpx;
						}
					}

					.item-body {
						margin-top: 22rpx;

						.item-body-sec {
							line-height: 44rpx;

							>view {
								display: inline-block;
								min-width: 100rpx;
							}
						}
					}

					.item-body-none {
						height: 88rpx;
						line-height: 88rpx;
					}

					.item-foot {
						margin-top: 16rpx;

						.view {
							color: #2C6FF3;
						}
					}
				}
			}
		}
	}
</style>