import request from '@/utils/request'

import {
	list
} from '@/api/dict.js'

/**
 * @param {Object} type 父code搜索，支持数组
 * @param {Object} code code值搜索
 */
const getDict = function getDict(type, code) {
	return new Promise((resolve, reject) => {
		let params = Array.isArray(type) ? {
			paterCodes: type,
			code: code
		} : {
			paterCode: type,
			code: code
		}

		list(params).then(res => {
			resolve(res.data)
		}).catch(error => {
			reject(error)
		})
	})
}


/**
 * @param {Object} type 父code搜索，支持数组
 */
export function dict(type) {
	return new Promise((resolve, reject) => {
		let params = Array.isArray(type) ? {
			paterCodes: type
		} : {
			paterCode: type
		}

		list(params).then(res => {
			let data = res.data
			let actions = []
			if (data && data.length > 0) {
				actions = data.map(i => ({
					id: i.code,
					name: i.value
				}))
			}
			resolve(actions)
		}).catch(error => {
			reject(error)
		})
	})
}
export default getDict