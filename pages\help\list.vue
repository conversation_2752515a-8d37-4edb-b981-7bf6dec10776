<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="帮助手册"></u-navbar>

		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
					:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
					lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>

		<view class="search">
			<u-search animation placeholder="请输入" :clearabled="true" v-model="keyword" :showAction="false"
				@search="search"></u-search>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="item flex align-center justify-between" @click="detail(item)">
						<view>
							{{item.title}}
						</view>
						<view>
							<u-icon name="arrow-right" size="16"></u-icon>
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>10" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>


		<u-action-sheet :show="actionSheetShow" :actions="actions" title="套餐类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		page
	} from "@/api/help.js"
	import config from "@/config.js"

	export default {
		data() {
			return {
				keyword: null,
				list: [],
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				title: '',
				fullHeight: 0,

				tabList: [{
						name: '文档教程'
					},
					{
						name: '视频教程'
					},
				],
				current: 0,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			this.search()
		},

		methods: {
			tabClick(e) {
				this.current = e.index
				this.reset()
				this.getpage()
			},

			//获取订单列表
			getpage() {
				page({
					page: {
						current: this.page,
						size: this.size
					},
					title: this.keyword,
					category: this.current == 0 ? '1' : 2
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			detail(item) {
				if (this.current == 0) {
					this.$tab.navigateTo(`/pages/help/detail?title=${item.title}&id=${item.id}`)
				} else {
					let src = config.baseUrl == 'https://api.mxrvending.com:9050' ?
						`/pages/globalPages/webView?src=https://ai.mxrvending.com/html/help.html&id=${item.id}` :
						`/pages/globalPages/webView?src=http://aitest.mxrvending.com:9099/html/help.html&id=${item.id}`
					this.$tab.navigateTo(src)
				}
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		}
	}
</script>
<style scoped lang="scss">
	.container {
		background-color: #fff;

		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.scrollview {
			padding: 0 12rpx;
		}

		.item {
			font-size: 30rpx;
			background-color: #e2e7f0;
			margin-bottom: 20rpx;
			line-height: 80rpx;
			padding: 0 20rpx;
			border-radius: 12rpx;
		}
	}
</style>