<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="添加奖品"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="100">
				<view class="section-wrap">
					<u-form-item label="奖品图片">
						<!-- <u--image radius='14rpx' width="120rpx" height="120rpx" @click="$xy.previewImg()"
							src="https://static.xynetweb.com/sysFile/defaultgoods.png" mode="aspectFit"
							:lazy-load="true">
						</u--image> -->
						<ximgUpload :capture="['camera']" maxCount='1' v-model="form.image" />
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="奖项" @click="actionShow('awards')">
						<view style="text-align: right;">{{form.awardsName}}
						</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="奖品名称">
						<u-input inputAlign="right" v-model="form.name" placeholder="请输入奖品名称" border="none"></u-input>
					</u-form-item>
				</view>
				<view class="section-wrap">
					<u-form-item label="奖品数量">
						<u-input type="number" inputAlign="right" v-model="form.limitNum" placeholder="请输入奖品数量"
							border="none"></u-input>
					</u-form-item>
				</view>
				<view class="section-wrap">
					<u-form-item label="单人限制">
						<u-input type="number" inputAlign="right" v-model="form.limitWinNum" placeholder="请输入单人限制"
							border="none"></u-input>
					</u-form-item>
				</view>
				<view class="section-wrap">
					<u-form-item label="中奖概率">
						<u-input type="number" inputAlign="right" v-model="form.percentage" placeholder="请输入中奖概率"
							border="none">
							<template slot="suffix">
								<text>%</text>
							</template>
						</u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="奖品">
						<u-radio-group v-model="form.type" placement="row" @change="actionRadio">
							<u-radio :customStyle="{marginRight: '12rpx'}" label="优惠券" name="1">
							</u-radio>
							<u-radio :customStyle="{marginRight: '12rpx'}" label="积分" name="2">
							</u-radio>
							<u-radio :customStyle="{marginRight: '12rpx'}" label="商品" name="3">
							</u-radio>
							<u-radio label="自有" name="4">
							</u-radio>
						</u-radio-group>
					</u-form-item>
				</view>

				<view class="section-wrap" v-if="form.type==1">
					<u-form-item label="优惠券" @click="actionShow('coupon')">
						<view style="text-align: right;">{{form.typeConfigName||'请选择优惠券'}}
						</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap" v-if="form.type==2">
					<u-form-item label="积分">
						<u-input inputAlign="right" type="number" v-model="form.typeConfig.points" placeholder="请输入积分"
							border="none"></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap" v-if="form.type==3">
					<u-form-item label="商品" @click="actionShow('goods')">
						<view style="text-align: right;">{{form.typeConfigName||'请选择商品'}}
						</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap" v-if="form.type==4">
					<u-form-item label="自定义">
						<u-input inputAlign="right" v-model="form.typeConfig.custom" placeholder="请输入"
							border="none"></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="序号">
						<u-input inputAlign="right" type="number" v-model="form.indexNum" placeholder="请输入序号"
							border="none"></u-input>
					</u-form-item>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>
		<u-action-sheet :show="actionsShow" :actions="actions" :title="actionTitle" @close="actionsShow = false"
			@select="actionsSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		dict
	} from "@/utils/getDict.js"
	import {
		prizeSaveOrUpdate,
		prizeObj,
		normalMiniList
	} from "@/api/lottery.js"
	import {
		ownerGoodsList
	} from "@/api/commodity/mercGoods.js"
	//验证规则
	let rulesObj = {
		type: 'string',
		required: true,
		message: '必填项',
		trigger: ['change']
	}

	export default {
		data() {
			return {
				form: {
					"id": null,
					"image": "",
					"name": "",
					"percentage": null,
					"limitWinNum": null,
					"limitNum": null,
					"type": "1",
					"typeConfig": {
						"couponId": null,
						"goodsId": null,
						"points": null,
						"custom": null
					},
					"typeConfigName": "",
					"awards": null,
					"awardsName": "",
					"indexNum": null,

				},

				calendarShow: false,
				calendarMonthNum: '3',

				actions: [{
						name: '会员折扣优先'
					},
					{
						name: '活动优惠优先'
					},
					{
						name: '会员折扣叠加'
					}
				],
				actionsShow: false,
				actionTitle: null,

				prizeId: null,
				lotteryId: null,

			}
		},

		async onLoad(o) {
			this.lotteryId = o.lotteryId
			if (o.prizeId) {
				this.prizeId = o.prizeId
				this.actions = await this.getAwards()
				this.getDetail()
			}
		},

		methods: {
			getDetail(id) {
				prizeObj({
					"lotteryId": this.lotteryId,
					"prizeId": this.prizeId
				}).then(res => {
					let data = res.data;
					data.image = [data.image]
					data.awardsName = this.actions.find(item => item.id == data.awards).name
					data.typeConfigName = data.typeConfigName
					this.form = data
				})
			},

			async actionShow(type) {
				if (type == 'awards') {
					this.actionTitle = '奖项'
					this.actions = await this.getAwards()
				} else if (type == 'coupon') {
					this.actionTitle = '优惠券'
					this.actions = await this.getCoupon()
				} else if (type == 'goods') {
					this.actionTitle = '商品'
					this.actions = await this.getGoods()
				}
				this.actionsShow = true
			},

			actionsSelect(e) {
				if (this.actionTitle == '奖项') {
					this.form.awards = e.id
					this.form.awardsName = e.name
				} else if (this.actionTitle == '优惠券') {
					this.form.typeConfig = {
						couponId: e.id
					}
					this.form.typeConfigName = e.name
				} else if (this.actionTitle == '商品') {
					this.form.typeConfig = {
						goodsId: e.id
					}
					this.form.typeConfigName = e.name
				}
			},

			getAwards() {
				return new Promise((resolve, reject) => {
					dict('lottery_prize_awards').then(res => {
						console.log(res)
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			getCoupon() {
				return new Promise((resolve, reject) => {
					normalMiniList({}).then(res => {
						let data = res.data;
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			getGoods() {
				return new Promise((resolve, reject) => {
					ownerGoodsList({
						page: {
							current: 1,
							size: 1000
						},
						status: '1', //0下架1上架
					}).then(res => {
						let data = res.data.records;
						let options = data.map(item => ({
							id: item.goodsId,
							name: item.goodsName
						}))
						resolve(options)
					}).catch(err => {
						reject(err)
					})
				})
			},

			actionRadio(e) {
				this.form.type = e
				this.form.typeConfigName = ''
				this.form.typeConfig = {
					"couponId": null,
					"goodsId": null,
					"points": null,
					"custom": null
				}
			},

			//表单提交
			submit() {
				let params = {
					"id": this.form.id,
					"image": this.form.image[0],
					"name": this.form.name,
					"percentage": this.form.percentage,
					"limitWinNum": this.form.limitWinNum,
					"limitNum": this.form.limitNum,
					"type": this.form.type,
					"typeConfig": this.form.typeConfig,
					"awards": this.form.awards,
					"indexNum": this.form.indexNum
				}

				// if (this.checkParams(params)) return

				prizeSaveOrUpdate({
					lotteryId: this.lotteryId,
					prize: params
				}).then(res => {
					this.$modal.msg('成功~')
					setTimeout(() => {
						uni.$emit('updatePrize')
						this.$tab.navigateBack()
					}, 500)
				}).catch(err => {

				})
			},

			checkParams(params) {
				if (this.current == 1) {
					if (params.price.scalar > 1) {
						this.$modal.msg('折扣必须是0-1之间的小数')
						return true
					}
				}
				return false
			},
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
			}

			.input-text {
				text-align: right !important;
			}

			.tab-wrap {
				padding: 12rpx 0;
			}

			.choose-list {
				width: 418rpx;

				>text {
					word-break: break-all;
				}
			}

			.rule-tips {
				padding-top: 20rpx;
				color: red;
			}

			.mj-content {
				padding-bottom: 30rpx;

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 230rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.fail-msg {
				color: red;
				font-size: 28rpx;
				line-height: 32rpx;
				margin-top: 12rpx;
				margin-bottom: 24rpx;
				width: 726rpx;
				margin-left: -24rpx;
				background-color: skyblue;
				padding: 24rpx 12rpx;
				box-sizing: border-box;
				border-radius: 12rpx;
			}

			.example-img {
				height: 212rpx;
				padding: 24rpx 0 12rpx;
				display: flex;
				flex-flow: row nowrap;
				justify-content: flex-start;

				>image {
					width: 160rpx;
					margin-right: 66rpx;
					border-radius: 4rpx;
				}
			}

			.img-tips {
				color: red;
				margin-top: 30rpx;
				line-height: 44rpx;
			}

			.img-upload {
				height: 212rpx;
				padding: 24rpx 0 12rpx;
			}

			.bar-code {
				position: relative;

				.scan-icon {
					position: absolute;
					right: 0;
					top: 50%;
					transform: translateY(-50%);
					z-index: 9999;
				}
			}

			.form-item {
				position: relative;

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.priority {
			position: relative;

			.tips {
				position: absolute;
				color: red;
				font-size: 24rpx;
				left: 100rpx;
				top: 50%;
				transform: translateY(-50%);
			}
		}

		.action-time {
			position: relative;

			.edit {
				color: #2C6FF3;
				text-decoration: underline;
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);

				&.del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.time-wrap {
			padding-bottom: 20rpx;

			.time-item {
				line-height: 84rpx;

				.time-select {}

				.action-time-del {
					color: red;
					text-decoration: underline;
				}
			}

			.time-input {
				width: 160rpx;
				padding: 0 12rpx;
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 9;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>