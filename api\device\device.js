import request from '@/utils/request'
// 设备列表
export function mercHomeList(data) {
	return request({
		url: '/merc/mini/device/mercHomeList',
		method: 'post',
		data: data
	})
}

// 商户设备首页统计
export function mercHomeStatistical(data) {
	return request({
		url: '/device/merc-mini/device/mercHomeStatistical',
		method: 'post',
		data: data
	})
}

// 增加商品到设备
export function bindDevice(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/bindDevice',
		method: 'post',
		data: data
	})
}

// 设备商品列表
export function goodsList(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/page',
		method: 'post',
		data: data
	})
}

// 商户设备列表
export function searchPage(data) {
	return request({
		url: '/device/merc-mini/device/searchPage',
		method: 'post',
		data: data
	})
}

// 商户设备详情
export function detail(data) {
	return request({
		url: '/device/merc-mini/device/detail',
		method: 'post',
		data: data
	})
}

// 数据统计
export function dataCount(data, loading = true) {
	return request({
		url: '/device/merc-mini/device/dataCount',
		method: 'post',
		data: data,
		loading: loading
	})
}

// 设备操作记录
export function indexDeviceRecords(data) {
	return request({
		url: '/device/merc-mini/deviceRecords/indexDeviceRecords',
		method: 'post',
		data: data
	})
}

// 设备联网记录
export function indexDeviceNetRecords(data) {
	return request({
		url: '/device/merc-mini/deviceRecords/indexDeviceNetRecords',
		method: 'post',
		data: data
	})
}

// 设备故障情况
export function indexDeviceError(data) {
	return request({
		url: '/device/merc-mini/deviceRecords/indexDeviceError',
		method: 'post',
		data: data
	})
}

// 设备温度记录
export function indexDeviceTempRecords(data) {
	return request({
		url: '/device/merc-mini/deviceRecords/indexDeviceTempRecords ',
		method: 'post',
		data: data
	})
}

// 设备激活
export function deviceActive(data) {
	return request({
		url: '/device/merc-mini/device/active',
		method: 'post',
		data: data
	})
}

// 设备详情
export function deviceDetail(data) {
	return request({
		url: '/device/device-info/obj',
		method: 'post',
		data: data
	})
}

// 运营状态修改
export function modifyBusyStage(data) {
	return request({
		url: '/device/merc-mini/device/modifyBusyStage',
		method: 'post',
		data: data
	})
}

// 首页统计数据
export function allCount(data) {
	return request({
		url: '/order/order-merc-sales-count-more/count',
		method: 'post',
		data: data
	})
}

// 设备商品分类
export function categoryList(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/categoryList',
		method: 'post',
		data: data
	})
}

// 修改设备信息
export function updateInfo(data) {
	return request({
		url: '/device/merc-mini/device/updateInfo',
		method: 'post',
		data: data
	})
}

export function sendCommand(data) {
	return request({
		url: '/device/mqtt/senCommand',
		method: 'post',
		data: data
	})
}
//查询指令操作结果
export function queryCommandResult(data) {
	return request({
		url: '/device/mqtt/snByCmdAndResult',
		method: 'post',
		data: data
	})
}

//销售统计汇总
export function sumCount(data) {
	return request({
		url: '/order/ordersTop/data',
		method: 'post',
		data: data
	})
}

//销售统计列表
export function sumPage(data) {
	return request({
		url: '/order/ordersTop/deviceSaleTop',
		method: 'post',
		data: data
	})
}

//设备激活情况
export function getActiveInfo(data) {
	return request({
		url: '/device/merc-mini/device/getActiveInfo',
		method: 'post',
		data: data
	})
}

//设备是否属于商户
export function isMerc(data) {
	return request({
		url: '/device/merc-mini/device/isMerc',
		method: 'post',
		data: data
	})
}


//设备二维码
export function getQrCode(data) {
	return request({
		url: '/device/device-create-ids/getQrCode',
		method: 'post',
		data: data
	})
}

//导出设备二维码
export function exportQrCode(data) {
	return request({
		url: '/device/device-create-ids/exportQrCode',
		method: 'post',
		data: data
	})
}

//清除故障
export function abort(data) {
	return request({
		url: '/order/activity-info/abort',
		method: 'post',
		data: data
	})
}

//统计数据图表
export function salesData(data) {
	return request({
		url: '/order/ordersTop/salesData',
		method: 'post',
		data: data
	})
}

//支付宝设备激活分页
export function aliDeviceActivePage(data) {
	return request({
		url: '/merc/mini/device/aliDeviceActivePage',
		method: 'post',
		data: data
	})
}

//获取商家信息
export function userInfoBySelf(data) {
	return request({
		url: '/merc/mini/mercUser/userInfoBySelf',
		method: 'post',
		data: data
	})
}

//绑定支付宝
export function updateByAli(data) {
	return request({
		url: '/merc/mini/mercUser/updateByAli',
		method: 'post',
		data: data
	})
}

//支付宝设备详情查询
export function aliDeviceObj(data) {
	return request({
		url: '/merc//mini/device/aliDeviceObj',
		method: 'post',
		data: data
	})
}

export function mercDeviceList(data) {
	return request({
		url: '/merc/mini/mercUserDevice/mercDeviceList',
		method: 'post',
		data: data
	})
}

export function authDevicePage(data) {
	return request({
		url: '/merc/mini/mercUserDevice/authDevicePage',
		method: 'post',
		data: data
	})
}

export function addDevice(data) {
	return request({
		url: '/merc/mini/mercUserDevice/add',
		method: 'post',
		data: data
	})
}

export function userDeviceList(data) {
	return request({
		url: '/merc/mini/mercUserDevice/userDeviceList',
		method: 'post',
		data: data
	})
}

export function delDevice(data) {
	return request({
		url: '/merc/mini/mercUserDevice/del',
		method: 'post',
		data: data
	})
}

export function listOfIdName(data) {
	return request({
		url: '/device/merc-mini/device/listOfIdName',
		method: 'post',
		data: data
	})
}

export function activeAlipayDevice(data) {
	return request({
		url: '/device/api/device/deviceActive',
		method: 'post',
		data: data
	})
}

/**
 * 设备温度反显
 * @param {Object} data
 */
export function getLastTem(data) {
	return request({
		url: '/device/deviceTempSet/getLastOne',
		method: 'post',
		data: data
	})
}

// 设备活动状态
export function deviceActiveState(data) {
	return request({
		url: '/order/activity-info/infoByRedis',
		method: 'post',
		data: data
	})
}

// 设备事件列表
export function eventList(data) {
	return request({
		url: '/device/merc-mini/device-event/page',
		method: 'post',
		data: data
	})
}

// 设备告警温度设置
export function batchUpdate(data) {
	return request({
		url: '/device/device-config/batchUpdate',
		method: 'post',
		data: data
	})
}

// sn查询设备id
export function snGetDeviceId(data) {
	return request({
		url: '/device/device-sysinfo/obj',
		method: 'post',
		data: data
	})
}

// 蓝牙授权
export function genCode(data) {
	return request({
		url: '/device/merc-mini/device/genCode',
		method: 'post',
		data: data
	})
}

// 加热丝反显
export function getLastJrs(data) {
	return request({
		url: '/device/device-sysinfo/obj',
		method: 'post',
		data: data
	})
}

// 选择设备列表
export function groupByAdminName(data) {
	return request({
		url: '/merc/mini/device/groupByAdminName',
		method: 'post',
		data: data
	})
}

// 流量卡缴费角标
export function pageCount(data) {
	return request({
		url: '/device/merc-mini/device-sim/pageCount',
		method: 'post',
		data: data
	})
}

// 设置反显
export function lastOne(data) {
	return request({
		url: '/device/device-set-records/lastOne',
		method: 'post',
		data: data
	})
}

export function updateSn(data) {
	return request({
		url: '/device/device-register/updateSn',
		method: 'post',
		data: data
	})
}


export function queryUpInfoTask(data) {
	return request({
		url: '/device/device-version-up/queryUpInfoTask',
		method: 'post',
		data: data
	})
}

export function snByCmdAndResult(data) {
	return request({
		url: '/device/mqtt/snByCmdAndResult',
		method: 'post',
		data: data,
		hiddenLoading: true
	})
}




