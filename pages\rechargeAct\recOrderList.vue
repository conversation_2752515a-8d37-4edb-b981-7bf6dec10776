<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="充值订单列表"></u-navbar>
		<view class="search flex align-center justify-between">
			<view class="search-input"><u-search animation placeholder="请输入手机号码" :clearabled="true" v-model="keyword"
					:showAction="false" @search="search"></u-search></view>
			<!-- <view class="flex align-center justify-end screen" style="width:230rpx;" @tap="screen">
				<view>{{meal.name||'套餐类型'}}</view>
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png" mode="widthFix">
				</image>
			</view> -->
			<view class="flex align-center time" @click="show=true">
				<view>
					{{ params.time || "时间" }}
				</view>
				<view>
					<u-icon class="arrow" name="arrow-down-fill" size="12" color="#333"></u-icon>
				</view>
			</view>
		</view>

		<!-- 	<view class="flex align-center justify-between screen-container">
			<view class="total-list flex">
				<view>总数:{{10||'-'}}</view>
				<view class="total-item">充值金额:{{10||'-'}}</view>
				<view>赠送金额:{{10||'-'}}</view>
			</view>
			
		</view> -->

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="equipment-container" v-if="current==0">
						<view class="flex align-center">
							<view class="title">
								订单号：{{item.id||'-'}}
							</view>
							<text class="under-line-text" @tap="$xy.copy(item.id)">复制</text>
						</view>

						<view class="order-detail-item">
							<view>手机号：</view>{{item.memberTel||'-'}}
						</view>

						<view class="order-detail-item">
							<view>套餐：</view>{{item.rechargeName||'-'}}
						</view>
						<view class="order-detail-item">
							<view>金额：</view>￥{{$xy.delMoney(item.amount)}}
						</view>
						<view class="order-detail-item">
							<view>到账金额：</view>￥{{$xy.delMoney(item.receiptAmount)}}
						</view>
						<view class="order-detail-item">
							<view>支付时间：</view>{{item.payTime||'-'}}
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<u-datetime-picker :show="show" v-model="time" confirmColor="#2C6FF3" mode="date" :closeOnClickOverlay="true"
			@cancel="cancle" @confirm="confirm" @close="show = false"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		recOrderList
	} from "@/api/rechargeAct.js"

	export default {
		data() {
			return {
				keyword: null,
				list: [],
				orderCount: {},
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				mode: 'single',
				screenShow: false,
				actionSheetShow: false,
				actions: [],
				title: '',

				pickerType: null,

				fullHeight: 1000,

				tabList: [{
						name: '全部用户'
					},
					{
						name: 'vip会员'
					},
				],
				current: 0,
				showBlack: false,

				countData: [],

				meal: {
					id: null,
					name: null
				},
				total: 0,
				params: {
					time: null
				},

				show: false,
				time: new Date(),
				memberId: null
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			this.memberId = o.memberId
			this.search()
		},

		methods: {
			selecTime() {
				this.show = true
			},

			confirm(e) {
				this.params.time = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
				this.show = false;
				this.search()
			},

			cancle() {
				this.show = false
				this.params.time = null
				this.search()
			},

			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					memberId: this.memberId,
					beginCreateTime: this.params.time ? this.params.time + ' 00:00:00' : null,
					endCreateTime: this.params.time ? this.params.time + ' 23:59:59' : null
				}
				recOrderList(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.total = res.data.total
					this.list = this.list.concat(data)
				})

			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		}
	}
</script>
<style scoped lang="scss">
	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;

			.search-input {
				width: 500rpx;
			}

			.screen {
				padding-right: 12rpx;
			}

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 12rpx;
			}

			.time {
				fontsize: 24rpx;
				line-height: 45rpx;
				background: #F2F2F2;
				border-radius: 14rpx;
				padding: 0 16rpx;
				position: relative;
				color: #333;

				.arrow {
					margin-left: 16rpx;
				}
			}
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.screen-container {
			background-color: #fff;
			padding: 10rpx 13rpx 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;

			.total-list {
				font-size: 24rpx;

				.total-item {
					padding: 0 20rpx;
				}
			}

			.time {
				fontsize: 24rpx;
				line-height: 45rpx;
				background: #F2F2F2;
				border-radius: 14rpx;
				padding: 0 16rpx;
				position: relative;
				color: #333;

				.arrow {
					margin-left: 16rpx;
				}
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 26rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.under-line-text {
				font-size: 26rpx !important;

				font-weight: 500;
				font-style: italic;
				text-decoration: underline;
				color: #2C6FF3 !important;
				margin-left: 24rpx;
				background-color: #fff !important;
			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;
				padding-left: 8rpx;

				>view {
					display: inline-block;
					width: 170rpx;
				}
			}
		}
	}
</style>