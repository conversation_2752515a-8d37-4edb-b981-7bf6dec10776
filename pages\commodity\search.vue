<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="商品私库"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search animation placeholder="商品搜索" :clearabled="false" v-model="keyword" :showAction="false"
					@search="search"></u-search>
				<view class="scan-icon" @click="scan">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>

				<view class="search-history flex flex-wrap flex-start">
					<view class="history-item" v-for="(item,index) in historyList" :key="index"
						@click="searchFast(item)">
						{{item}}
					</view>
				</view>
			</view>
			<view class="list">
				<u-list @scrolltolower="scrolltolower" :height="fullHeight">
					<u-list-item class="list-item" v-for="(item, index) in commList" :key="index">
						<view class="comm-item" @click.stop="$tab.navigateTo('/pages/commodity/comEdit?id='+item.id)">
							<view style="width:110rpx;height:100rpx;" @click.stop="$xy.previewImg(item.goodsCover)">
								<u--image radius="4" width="110rpx" height="110rpx" :src="item.goodsCover" mode="widthFix"
									:lazy-lord="true"></u--image>
							</view>
							<view class="item-content">
								<view class="name">
									{{item.goodsName}}
								</view>
								<view>
									69码:{{item.goodsBarcode}}
								</view>
								<view>
									商品类型:{{item.categoryCode}}
								</view>
							</view>
							<view class="status">
								<u-tag text="已上架" type="success" plain v-if="item.status=='1'"> </u-tag>
								<u-tag text="已下架" type="error" plain v-else></u-tag>
							</view>
						</view>
					</u-list-item>
					<u-loadmore :status="status" v-if="commList.length>0" />
				</u-list>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		ownerGoodsList
	} from "@/api/commodity/mercGoods.js"
	import {
		saveKeyWord
	} from '@/utils/common.js'
	export default {
		data() {
			return {
				keyword: '',
				page: 1, //商品分页
				size: 10,

				status: 'loadmore', //加载更多
				commList: [], //商品列表
				fullHeight: 0,
				historyList: []
			}
		},
		
		async onLoad() {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.list',0)

			if (uni.getStorageSync('goods')) {
				this.historyList = JSON.parse(uni.getStorageSync('goods'))
			}
			
			this.search()
			
			uni.$on('refreshData', res => {
				this.search();
			})
		},

		methods: {
			search(val) {
				if(val){
					this.historyList = saveKeyWord('goods', this.keyword)
				}
				this.reset();
				this.getCommList()
			},

			searchFast(e) {
				this.keyword = e
				this.search()
			},

			//扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.keyword = res.result;
						this.getCommList()
					}
				});
			},

			//根据类目获取商品列表
			getCommList(id) {
				ownerGoodsList({
					page: {
						current: this.page,
						size: this.size
					},
					keyword: this.keyword
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.commList = this.commList.concat(data)
				})
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},

			scrolltolower() {
				if (this.status == 'nomore') return
				this.page++
				this.getCommList()
			},
		},
		
		onUnload() {
			uni.$off('refreshData')
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {
			.search {
				padding: 24rpx 24rpx 12rpx;
				background-color: #fff;
				position: relative;

				.scan-icon {
					position: absolute;
					right: 36rpx;
					top: 38rpx;
					z-index: 2;
				}

				.search-history {

					.history-item {
						margin-right: 12rpx;
						padding: 0 12rpx;
						background-color: #f2f2f2;
						color: #333;
						font-size: 24rpx;
						line-height: 40rpx;
						border-radius: 40rpx;
						margin-top: 12rpx;
					}
				}
			}

			.list {
				width: 100%;
				padding: 12rpx 24rpx;

				.comm-item {
					display: flex;
					flex-direction: row;
					justify-content: flex-start;
					align-items: center;
					background-color: #fff;
					margin-bottom: 12rpx;
					border-radius: 12rpx;
					box-sizing: border-box;
					padding: 12rpx;
					position: relative;

					.status {
						position: absolute;
						right: 12rpx;
						top: 12rpx;
					}

					.image {
						width: 110rpx;
					}

					.item-content {
						padding-left: 24rpx;
						color: #999;

						>view:nth-child(1) {
							font-size: 28rpx;
							font-weight: bold;
							color: #333;
							width:400rpx;
						}

						>view:nth-child(2) {
							font-size: 26rpx;
							padding: 12rpx 0;
						}

						>view:nth-child(3) {
							font-size: 26rpx;
						}
					}
				}
			}
		}
	}
</style>