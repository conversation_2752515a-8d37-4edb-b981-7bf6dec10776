<template>
  <xpopup
    :show="show"
    @close="$emit('close')"
    @confirm="handleConfirm"
    :showBtn="true"
    title="换绑SN">
    <view class="popup-content blueTe">
      <view class="info">
        <view>更换设备的SN号码，请谨慎操作！</view>
      </view>
      <view class="blueTe-msg">
        <view>当前SN：</view>
        <view>{{ deviceDetail.deviceSysinfo?.deviceSn || "-" }}</view>
      </view>
      <view class="blueTe-msg">
        <view>新SN码：</view>
        <view>
          <u--input
            placeholder="请输入新的SN码"
            border="surround"
            v-model="localSnCode"
            @input="handleSnCodeChange">
          </u--input>
        </view>
      </view>
    </view>
  </xpopup>
</template>

<script>
export default {
  name: 'SnBindingPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    deviceDetail: {
      type: Object,
      default: () => ({})
    },
    snCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localSnCode: ''
    }
  },
  watch: {
    snCode: {
      handler(newVal) {
        this.localSnCode = newVal
      },
      immediate: true
    }
  },
  methods: {
    handleConfirm() {
      if (!this.localSnCode) {
        this.$modal.msg("请输入新的SN码！");
        return;
      }
      this.$emit('confirm', this.localSnCode);
    },
    handleSnCodeChange(value) {
      this.localSnCode = value;
      this.$emit('updateSnCode', value);
    }
  }
}
</script>

<style lang="scss" scoped>
.popup-content.blueTe {
  padding: 20rpx;
}

.info {
  background: #fff2f0;
  border: 1rpx solid #ffccc7;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
  
  view {
    font-size: 26rpx;
    color: #ff4d4f;
    line-height: 1.5;
  }
}

.blueTe-msg {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  
  > view:first-child {
    width: 120rpx;
    font-size: 28rpx;
    color: #333;
  }
  
  > view:last-child {
    flex: 1;
    font-size: 28rpx;
    color: #666;
  }
}
</style>
