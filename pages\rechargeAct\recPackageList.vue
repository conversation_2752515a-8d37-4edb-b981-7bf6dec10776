<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="储值套餐列表"></u-navbar>
		<view class="content">
			<xhelpPopup guideId="CZ0001" />
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view v-if="!memberRechargeDiscountType" style="padding:20rpx;">
					储值会员与其他优惠：<text style="color:red;">不叠加！！！</text>
				</view>
				<view v-if="list.length>0" class="content">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="equipment-container">
							<view class="list-item flex">
								<view class="item-name">套餐名称：</view>
								<view class="item-val">{{item.rechargeName}}</view>
							</view>
							<view class="list-item flex">
								<view class="item-name">套餐类型：</view>
								<view class="item-val" :class="[item.type=='1'?'long-val item-val':'item-val']">
									{{item.typeName}}
								</view>
							</view>
							<view class="list-item flex">
								<view class="item-name">适用设备：</view>
								<view class="item-val">
									{{item.isAllDevice?'全部设备':'部分设备'}}
								</view>
							</view>

							<view class="list-item flex" v-if="item.type==2">
								<view class="item-name">有效日期：</view>
								<view class="item-val">{{item.startTime.split(' ')[0]}}~{{item.endTime.split(' ')[0]}}
								</view>
							</view>
							<view class="list-item flex">
								<view class="item-name">套餐内容：</view>
								<view>
									<view v-for="(item1,index) in item.itemList" :key="item1.id" class="flex rec-list">
										<view>充值：<text>{{$xy.delMoney(item1.amount)}}</text></view>
										<view>到账：<text>{{$xy.delMoney(item1.receiptAmount)}}</text></view>
									</view>
								</view>
							</view>

							<view class="item-foot flex justify-end align-center">
								<view class="foot-right flex">
									<view style="margin-right: 16rpx;">
										<xbutton width="114rpx" bgColor="#F4F8FF" color="#2C6FF3" borderColor="#F4F8FF"
											round="14rpx" @click="qrcode(item.id)">
											二维码
										</xbutton>
									</view>
									<view style="margin-right: 16rpx;">
										<xbutton width="114rpx" bgColor="#F4F8FF" color="#2C6FF3" borderColor="#F4F8FF"
											round="14rpx" @click="del(item)">
											删除
										</xbutton>
									</view>
									<view>
										<xbutton width="114rpx" round="14rpx"
											@click="$tab.navigateTo('/pages/rechargeAct/add?id='+item.id)">修改</xbutton>
									</view>
								</view>
							</view>

							<view class="default-tips" v-if="item.type==1">
								默认
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

			<view class="btn">
				<xbutton size="large" @click="$tab.navigateTo('/pages/rechargeAct/add')">
					新增套餐
				</xbutton>
			</view>
		</view>

		<view class="canvas-box">
			<canvas style="width: 185px; height:185px;" canvas-id="myQrcode"></canvas>
		</view>

		<xpopup :show="qrcodeShow" round="12" @close="qrcodeClose" :safeAreaInsetBottom="false" :showBtn="false"
			mode="center">
			<view class="qrcode-content">
				<view class="qrcode">
					<image style="width: 185px; height:185px;" :src="qrImg" mode="widthFix"
						:show-menu-by-longpress="true"></image>
				</view>
				<view class="tips flex flex-start">
					<view class="tips-before">提示1：</view>
					<view class="tips-content">请长按图片或截屏转发给他人扫码购买！</view>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		rechargeList,
		remove,
		merObj
	} from "@/api/rechargeAct.js"
	import drawQrcode from '@/static/js/weapp.qrcode.esm.js'
	import config from "../../config"

	export default {
		data() {
			return {
				list: [],
				typeOptions: [],
				qrcodeShow: false,
				id: null,
				qrImg: null,
				memberRechargeDiscountType: true
			}
		},

		async onLoad() {
			await getDict('member_recharge_type').then(res => {
				console.log(res)
				this.typeOptions = res.map(i => ({
					label: i.msg,
					value: i.value
				}))
			})
			this.getStatus()
			this.getList();

			uni.$on('refresh', res => {
				this.getList()
			})
		},

		methods: {
			getStatus() {
				merObj({}).then(res => {
					this.memberRechargeDiscountType = res.data.memberRechargeDiscountType
				})
			},
			getList() {
				rechargeList({}).then(res => {
					let data = res.data;
					for (let item of data) {
						for (let type of this.typeOptions) {
							if (item.type == type.value) {
								item.typeName = type.label;
								break
							}
						}
					}
					this.list = data
				})
			},

			del(item) {
				this.$modal.oldConfirm('确认删除？').then(res => {
					remove({
						id: item.id
					}).then(response => {
						this.$modal.msg('删除成功');
						setTimeout(() => {
							this.getList();
						}, 1000)
					})
				})
			},

			qrcodeClose() {
				this.qrcodeShow = false
			},

			qrcode(id) {
				this.qrcodeShow = true
				this.drawQrcode(id)
			},

			//绘制二维码
			drawQrcode(id) {
				let url = `${config.baseUrl}?type=1&rechargeId=${id}`
				drawQrcode({
					width: 185,
					height: 185,
					canvasId: 'myQrcode',
					text: url,
				})

				// 绘制结束,生成本地图片链接
				setTimeout(() => {
					this.saveImg()
				}, 1000)
			},

			//二维码保存转发
			saveImg() {
				console.log('转换图片')
				uni.canvasToTempFilePath({
					x: 0,
					y: 0,
					width: 185,
					height: 185,
					destWidth: 185, //画布宽高*dpr 以iphone6为准
					destHeight: 185,
					canvasId: 'myQrcode',
					success: res => {
						//console.log(res.tempFilePath) //生成的临时图片路径
						this.qrImg = res.tempFilePath
						console.log('临时地址======' + this.qrImg)
					},
				})
			},
		},

		onUnload() {
			uni.$off('refresh');
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;

		.content {
			overflow: hidden;

			.menu-item {
				text-align: center;
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-top: 24rpx;

				>image {
					width: 57rpx;
					height: 57rpx;
				}

				>view {
					color: #333;
					font-size: 24rpx;
					line-height: 24rpx;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 16rpx;
				}
			}

			.empty {
				padding-top: 40%;
			}

			.martop {
				margin-top: 20rpx;
			}

			.tab-wrap {
				padding: 12rpx 0;

				.tab {
					// width: 40%;
					width: 70%;
				}
			}

			.tab-list {
				width: 100%;
				background-color: #fff;
				padding: 24rpx 26rpx 0;

				.tab-item {
					padding: 0 20rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					line-height: 62rpx;
					margin-bottom: 14rpx;
					text-align: center;
					margin-right: 12rpx;

					&.tab-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}
			}

			.marleft {
				margin-left: 10rpx;
			}

			.scrollview {
				.content {
					padding-bottom: 90rpx;
					overflow: hidden;
				}
			}

			.equipment-container {
				margin: 13rpx 13rpx 0;
				padding: 24rpx 30rpx 40rpx;
				border-radius: 14rpx;
				background-color: #fff;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
				position: relative;

				.list-item {
					font-size: 30rpx;
					line-height: 56rpx;

					.item-name {
						width: 160rpx;
					}

					.rec-list {
						>view {
							width: 240rpx;

							>text {
								color: red;
							}
						}
					}

					.item-val {
						&.long-val {
							color: orange;
						}
					}

				}

				.item-foot {
					margin-top: 20rpx;
				}

				.default-tips {
					position: absolute;
					right: 30rpx;
					top: 20rpx;
					width: 106rpx;
					height: 38rpx;
					background: #F8D5AD;
					border-radius: 19rpx;
					font-size: 22rpx;
					color: #500000;
					text-align: center;
					line-height: 38rpx;
				}
			}

			.btn {
				width: 724rpx;
				position: fixed;
				left: 13rpx;
				bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
				border-radius: 88rpx;
				overflow: hidden;

				.btn-wrap {
					text-align: center;
					font-size: 36rpx;
					font-weight: 500;
				}
			}
		}

		.qrcode-content {
			width: 616rpx;
			border-radius: 18rpx;
			padding: 50rpx 110rpx;
			text-align: center;

			.qrcode {
				width: 370rpx;
				height: 370rpx;
				margin-top: 43rpx;
				position: relative;
				left: 50%;
				transform: translateX(-50%);
			}

			.tips {
				font-size: 22rpx;
				font-weight: 500;
				color: #999999;
				line-height: 30rpx;
				text-align: left;
				margin-bottom: 12rpx;

				.tips-before {
					width: 110rpx;
				}

				.tips-content {
					width: 390rpx;

					>text {
						color: #2C6FF3;
						font-style: italic;
						text-decoration: underline;
					}
				}
			}

			.tips1 {
				font-size: 22rpx;
				color: #999999;
				line-height: 30rpx;
				// margin-top: 65rpx;
				margin-top: 24rpx;
			}
		}

		.canvas-box {
			position: relative;
			left: -2000rpx;
		}
	}
</style>