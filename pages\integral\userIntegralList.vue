<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="积分列表"></u-navbar>

		<view class="search">
			<u-search animation placeholder="请输入" :clearabled="true" v-model="keyword" :showAction="false"
				@search="getList"></u-search>
		</view>

		<view class="total flex">
			<view>积分会员总数：100</view><view>积分剩余总数：100000</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="equipment-container">
						<view class="flex align-center justify-between">
							<view class="title">
								手机号：<text>19967171798</text>
							</view>
							<xbutton round='25rpx' padding='0 20rpx' size="mini" bgColor="#DBE8FF"
								color="#2C6FF3">积分记录</xbutton>
						</view>
						<view class="flex justify-between">
							<view class="order-detail-item">
								<view>总积分：</view>200
							</view>
							<view class="order-detail-item" style="width:50%;">
								<view>剩余积分：：</view>200
							</view>
						</view>
						<view class="order-detail-item">
							<view>总消费金额：</view>￥{{$xy.delMoney(item.payMoney)}}
						</view>
						<view class="order-detail-item">
							<view>总订单笔数：</view>{{item.memberTel}}笔
						</view>
						
						
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>
		<u-action-sheet :show="actionSheetShow" :actions="actions" title="套餐类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		buyPlusRec,
		listByMercId
	} from "@/api/vip.js"

	export default {
		data() {
			return {
				keyword: '',
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				actionSheetShow: false,
				actions: [],
				title: '',

				pickerType: null,

				fullHeight: 3000,
				list: [],
				meal: {
					id: null,
					name: null
				},
			}
		},
		async onLoad(o) {
			// let _this = this;
			// _this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			this.search()
		},

		methods: {
			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					mealId: this.meal.id
				}
				buyPlusRec(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
					console.log(this.list)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		}
	}
</script>
<style scoped lang="scss">
	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}
		
		.total{
			font-size: 24rpx;
			color: #777777;
			line-height: 80rpx;
			padding:0 15rpx;
			background-color: #fff;
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 15rpx 34rpx 30rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
			}

			.order-detail-item {
				font-size: 26rpx;
				margin-top: 16rpx;
				color: #777;

				>view {
					display: inline-block;
					width: 170rpx;
				}
			}
		}
	}
</style>