import request from '@/utils/request'

// 商品分页
export function ownerGoodsList(data) {
	return request({
		url: '/goods/merc-mini/goods/page',
		method: 'post',
		data: data
	})
}

// 商品类目
export function goodsCategory(data, loading = true) {
	return request({
		url: '/goods/merc-mini/goods/categoryLevel1 ',
		method: 'post',
		data: data,
		loading: loading
	})
}

// 列表(内部服务调用) 
export function listById(data) {
	return request({
		url: '/mini/mercLine/listById',
		method: 'post',
		data: data
	})
}

// 列表
export function lineListByMerc(data) {
	return request({
		url: '/mini/mercLine/listByMerc',
		method: 'post',
		data: data
	})
}

// 删除
export function lineDel(data) {
	return request({
		url: '/mini/mercLine/del',
		method: 'post',
		data: data
	})
}
//设备 分类列表
export function categoryList(data, loading = true) {
	return request({
		url: '/goods/merc-mini/goodsMerc/categoryList',
		method: 'post',
		data: data,
		loading: loading
	})
}
//设备  商品类别
export function list(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/list',
		method: 'post',
		data: data
	})
}

//销售统计汇总
export function sumCount(data) {
	return request({
		url: '/goods/merc-mini/goodsData/sumCount',
		method: 'post',
		data: data
	})
}

//销售统计列表
export function sumPage(data) {
	return request({
		url: '/order/ordersTop/goodsSaleTop',
		method: 'post',
		data: data
	})
}

//设备商品公库分页
export function pageByGoods(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/pageByGoods',
		method: 'post',
		data: data
	})
}

//设备商品私库分页
export function pageByGoodsMerc(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/pageByGoodsMerc',
		method: 'post',
		data: data
	})
}

//私库到设备
export function bindDeviceByMercGoods(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/bindDeviceByMercGoods',
		method: 'post',
		data: data
	})
}

//公库到设备
export function bindDeviceByGoods(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/bindDeviceByGoods',
		method: 'post',
		data: data
	})
}

//h5私库到设备
export function supplyH5AddGoods(data) {
	return request({
		url: '/goods/merc-mini/goodsDeviceAisle/supplyH5AddGoods',
		method: 'post',
		data: data
	})
}

//设备商品
export function deviceGoods(data) {
	return request({
		url: '/goods/goodsDevice/list',
		method: 'post',
		data: data
	})
}

/**
 * 批量修改设备商品价格
 * @param {Object} data
 */
export function updateBySupply(data) {
	return request({
		url: '/goods/goodsDevice/updateBySupply',
		method: 'post',
		data: data
	})
}

/**
 * 商家算法列表
 * @param {Object} data
 */
export function mercAiList(data) {
	return request({
		url: '/goods/merc-mini/goodsDevice/mercAiList',
		method: 'post',
		data: data,
		hiddenLoading: true
	})
}

/**
 * 商品复购占比
 * @param {Object} data
 */
export function goodsDrawRePayPer(data) {
	return request({
		url: '/goods/goods-draw-re-pay/goodsDrawRePayPer',
		method: 'post',
		data: data
	})
}

/**
 * 商品冲突组列表
 * @param {Object} data
 */
export function goodsConflictList(data) {
	return request({
		url: '/goods/merc-mini/goodsConflict/list',
		method: 'post',
		data: data
	})
}

/**
 * 更新商品冲突组列表
 * @param {Object} data
 */
export function goodsConflictUpdate(data) {
	return request({
		url: '/goods/merc-mini/goodsConflict/update',
		method: 'post',
		data: data
	})
}

/**
 * 新增商品冲突组列表
 * @param {Object} data
 */
export function goodsConflictSave(data) {
	return request({
		url: '/goods/merc-mini/goodsConflict/save',
		method: 'post',
		data: data
	})
}

/**
 * 删除商品冲突组列表
 * @param {Object} data
 */
export function goodsConflictDel(data) {
	return request({
		url: '/goods/merc-mini/goodsConflict/del',
		method: 'post',
		data: data
	})
}