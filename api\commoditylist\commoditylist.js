import request from '@/utils/request'

// 分页查询
export function page(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/page',
		method: 'post',
		data: data
	})
}
//清单关联商品列表
export function refGoods(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/refGoods',
		method: 'post',
		data: data
	})
}
// 新增商品清单 
export function save(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/save',
		method: 'post',
		data: data
	})
}
// 商品清单应用设备
export function saveGoodsDevice(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/saveGoodsDevice',
		method: 'post',
		data: data
	})
}
// 修改商品清单 
export function update(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/update',
		method: 'post',
		data: data
	})
}
// 保存清单商品（每次传全量数据）
export function saveListingGoods(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/saveListingGoods',
		method: 'post',
		data: data
	})
}

//获取区域下设备列表
export function regionDevices(data) {
	return request({
		url: '/merc/mini/mercLine/regionDevices',
		method: 'post',
		data: data
	})
}
//获取商户线路列表
export function mercLineDevices(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/mercLineDevices',
		method: 'post',
		data: data
	})
}

//商品清单应用到设备
export function goodsListApply(data) {
	return request({
		url: '/goods/goodsDevice/save',
		method: 'post',
		data: data
	})
}

//删除清单
export function del(data) {
	return request({
		url: '/goods/goods-merc-model/del',
		method: 'post',
		data: data
	})
}

//保存设备商品为清单
export function backUpDeviceGoods(data) {
	return request({
		url: '/goods/merc-mini/goodsMercModel/backUpDeviceGoods',
		method: 'post',
		data: data
	})
}


