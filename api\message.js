import request from '@/utils/request'
//消息
export function myMsg(data) {
	return request({
		url: '/sys/msg-user-message/myMsg',
		method: 'post',
		data: data
	})
}

export function myMsgCount(data) {
	return request({
		url: '/sys/msg-user-message/myMsgCount',
		method: 'post',
		data: data
	})
}

//消息详情
export function msgDetail(data) {
	return request({
		url: '/sys/msg-user-message/obj',
		method: 'post',
		data: data
	})
}

//未读消息
export function unRead(data) {
	return request({
		url: '/sys/msg-user-message/unRead',
		method: 'post',
		data: data
	})
}

//所有消息标记已读
export function readAll(data) {
	return request({
		url: '/sys/msg-user-message/readAll',
		method: 'post',
		data: data
	})
}

//系统公告
export function notifyList(data) {
	return request({
		url: '/merc/mini/mercSysNotify/getSysNotifyByCode',
		method: 'post',
		data: data
	})
}

/**
 * 消息通知
 * @param {Object} data
 */
export function myMsgPopUp(data) {
	return request({
		url: '/sys/msg-user-message/myMsgPopUp',
		method: 'post',
		data: data
	})
}

/**
 * 消息通知标记已读
 * @param {Object} data
 */
export function readById(data) {
	return request({
		url: '/sys/msg-user-message/readById',
		method: 'post',
		data: data
	})
}

/**
 * 算法费欠费通知
 * @param {Object} data
 */
export function algoLow(data) {
	return request({
		url: '/merc/merc-account/obj',
		method: 'post',
		data: data
	})
}

/**
 * 算法费欠费通知
 * @param {Object} data
 */
export function arrearageCount(data) {
	return request({
		url: '/merc/be/merc/arrearageCount',
		method: 'post',
		data: data
	})
}

/**
 * 每日一图
 * @param {Object} data
 */
export function dayImg(data) {
	return request({
		url: '/merc/merc-teach-day-imgs/randomImgs',
		method: 'post',
		data: data
	})
}



