<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="流量卡续费明细"></u-navbar>
		<view class="content">
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view class="scroll-content" v-if="list&&list.length>0">
					<block v-for="(item,index) in list" :key="item.placeLineId">
						<view class="xy-card section">
							<view class="flex justify-between align-center"
								style="font-size: 32rpx; line-height: 68rpx;margin-bottom: 12rpx;">
								<view class="row-item flex align-center"
									style="font-size: 32rpx; line-height: 68rpx;font-weight: bold;">
									<view>
										缴费单号（123123213）
									</view>
									<!-- <view>
									123123213
								</view> -->
								</view>
								<view>
									<!-- <text>已缴费</text> -->
									<text style="color: #FF0000;">已退款</text>
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									动态视觉柜
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									动态视觉柜
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									动态视觉柜
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									动态视觉柜
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									动态视觉柜
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									动态视觉柜
								</view>
							</view>
						</view>
					</block>
				</view>

				<view v-else style="padding-top: 40%;">
					<u-empty text="没有数据!"></u-empty>
				</view>
			</scroll-view>

		</view>
	</view>
</template>

<script>
	// import {
	// 	costSimDetailList,
	// } from "@/api/recharge.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
				loadmoreStatus: 'loadmore',
				fullHeight: 0,
				page: 1,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')

			// this.deviceId = o.deviceId
			// this.search()
		},

		methods: {
			search() {
				this.reset()
				this.getList()
			},
			reset() {
				this.page = 1;
				this.list = [];
				this.loadmoreStatus = "loadmore"
			},

			getList(id) {
				costSimDetailList({
					deviceId: this.deviceId,
					page: {
						current: this.page,
						size: 10
					}
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data);
				})
			},

			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getList()
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			padding: 0 13rpx;
			overflow: hidden;

			.search {
				margin-top: 30rpx;
			}

			.scroll-content {
				overflow: hidden;
			}

			.section {
				margin-top: 20rpx;
				padding: 24rpx 30rpx 24rpx 48rpx;

				.row-item {
					font-size: 28rpx;
					color: #555555;
					line-height: 56rpx;

					.row-name {
						min-width: 140rpx;
					}

					.row-val {}
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}
	}
</style>