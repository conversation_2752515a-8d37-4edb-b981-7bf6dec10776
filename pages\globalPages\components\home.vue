<template>
	<scroll-view class="scroll-view" :scroll-with-animation="true" scroll-y>
		<view class="container">
			<u-navbar bgColor="#fff" :placeholder="true" @leftClick="scan" :fixed="false">
				<view slot="left">
					<u-icon name="scan" color="#333" size="32"></u-icon>
				</view>
				<view slot="center" style="color:#333;font-size: 36rpx;">
					开门柜管理
				</view>
			</u-navbar>

			<!-- 统计信息 -->
			<view class="total" v-if='totalMenu'>
				<view class="total-sale flex flex-start" v-if="checkPermi(['indSta:moreData'])">
					<view class="sale-item">
						<view class="sale-name">
							今日总收益（元）
						</view>
						<view class="sale-num">
							<u-count-to :startVal="0" :endVal="$xy.delMoney(total.day.salesPrice)" :decimals="2"
								separator="," color="#fff" bold></u-count-to>
						</view>
					</view>
					<view class="sale-item">
						<view class="sale-name">
							本月总收益（元）
						</view>
						<view class="sale-num">
							<u-count-to :startVal="0" :endVal="$xy.delMoney(total.month.salesPrice)" :decimals="2"
								separator="," color="#fff" bold></u-count-to>
						</view>
					</view>

					<view class="sale-more" @click="moreData">
						更多数据
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/home/<USER>"
							mode="widthFix"></image>
					</view>

					<view @click="update" :class="[load?'update load':'update']">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/home/<USER>"
							mode="heightFix"></image>
					</view>
				</view>
				<view class="refund-order flex justify-between" v-if="checkPermi(['indSta:moreData'])">
					<view class="refund-item" @click="$tab.navigateTo('/pages/order/refundList?type=1')">
						<view class="refund-num">
							￥<text>{{$xy.delMoney(total.day.refundMoney)}}</text>
						</view>
						<view class="refund-name">
							今日退款
						</view>
					</view>
					<view class="refund-item" @click="$tab.navigateTo('/pages/order/orderQuery?type=1')">
						<view class="refund-num">
							<text>{{total.day.orderNum||0}}</text>
						</view>
						<view class="refund-name">
							今日订单数
						</view>
					</view>
					<view class="refund-item" @click="$emit('typeClick',1)">
						<view class="refund-num">
							<text>{{onlineDevice||0}}</text>
						</view>
						<view class="refund-name">
							在线设备数
						</view>
					</view>
					<view class="refund-item">
						<view class="refund-num">
							<text>{{total.day.grossProfitMargin||0}}</text>%
						</view>
						<view class="refund-name">
							今日毛利率
						</view>
					</view>
				</view>

				<!-- 滚动通知 -->
				<view class="notice" @click="goNotice" v-if="checkPermi(['indSta:notice'])">
					<view>
						<u-notice-bar :text="notice" color="#CB7506" bgColor="#fff" speed="80" mode="link"
							@close="noticeClose">
						</u-notice-bar>
					</view>
					<view class="notice-tips flex justify-around align-center" v-if="noticeNum>0">
						<u-badge numberType="overflow" bgColor="#E60012" max="99" :value="noticeNum"></u-badge>
					</view>
				</view>
			</view>

			<!-- 常用菜单 -->
			<view class="useful-menu" v-if="operMenu&&operMenu.length>0">
				<view class="flex justify-between flex-wrap">
					<view class="useful-menu-item flex justify-between align-center" v-for="(item,index) in operMenu"
						:key="item.id" @click="menuClick(item)">
						<view class="image">
							<u-image width="75rpx" height="75rpx" :src="item.miniMenuIcon" mode="widthFix"
								:lazy-load="true">
							</u-image>
						</view>
						<view class="menu-name">{{item.name}}</view>
						<!-- <view class="tips flex justify-around align-center" v-if="tips(item.name)>0">
							<view>
								{{tips(item.name)}}
							</view>
						</view> -->
						<u-badge absolute :offset="[-2,-2]" is-dot bgColor="#E60012" v-if="tips(item.name)>0"></u-badge>
						<!-- <u-badge numberType="overflow" :absolute="true" :offset="[-4,-4]" bgColor="#E60012" max="99" :value="tips(item.name)"></u-badge> -->
					</view>
				</view>
			</view>

			<!-- 广告 -->
			<!-- <view class="ids" v-if="$xy.appId()!='wxe875ab8368fdbe57'">
				<u-image width="722rpx" height="355rpx" radius="14rpx"
					src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/ids-ny.png"
					mode="widthFix" :lazy-load="true" @click="idsClick">
				</u-image>
			</view> -->
			
			<!-- 新功能 -->
			<view class="menu" v-if="newMenu&&newMenu.length>0">
				<view class="menu-title">
					新功能
				</view>
				<view class="menu-content flex justify-start">
					<view class="menu-item" v-for="(item,index) in newMenu" :key="item.id" @click="menuClick(item)">
						<view class="image">
							<u-image width="57rpx" height="57rpx" :src="item.miniMenuIcon" mode="widthFix"
								:lazy-load="true">
							</u-image>
							<u-badge absolute :offset="[-2,-2]" is-dot bgColor="#E60012"></u-badge>
						</view>
						<view>{{item.name}}</view>
					</view>
				</view>
			</view>

			<!--便捷功能 -->
			<view class="menu" v-if="usefMenu&&usefMenu.length>0">
				<view class="menu-title">
					便捷功能
				</view>
				<view class="menu-content flex justify-start">
					<view class="menu-item" v-for="(item,index) in usefMenu" :key="item.id" @click="menuClick(item)">
						<view class="image">
							<u-image width="57rpx" height="57rpx" :src="item.miniMenuIcon" mode="widthFix"
								:lazy-load="true">
							</u-image>
						</view>
						<view>{{item.name}}</view>
					</view>
				</view>
			</view>
			
			<!-- 营销功能 -->
			<view class="menu" v-if="marketMenu&&marketMenu.length>0">
				<view class="menu-title">
					营销功能
				</view>
				<view class="menu-content flex justify-start">
					<view class="menu-item" v-for="(item,index) in marketMenu" :key="item.id" @click="menuClick(item)">
						<view class="image">
							<u-image width="57rpx" height="57rpx" :src="item.miniMenuIcon" mode="widthFix"
								:lazy-load="true">
							</u-image>
							<u-badge absolute :offset="[-2,-2]" is-dot bgColor="#E60012" v-if="newMenu.find(i=>i.name==item.name)"></u-badge>
						</view>
						<view>{{item.name}}</view>
					</view>
				</view>
			</view>
		</view>
	</scroll-view>
</template>

<script>
	import {
		allCount,
		snGetDeviceId,
		mercHomeStatistical
	} from "@/api/device/device.js"
	import {
		tipsCount,
		complaintCount
	} from "@/api/order/order.js"
	import storage from '@/utils/storage'
	import constant from '@/utils/constant'
	import {
		unRead
	} from "@/api/message.js"

	export default {
		data() {
			return {
				tabArr: [{
						name: '今日',
						id: 0
					},
					{
						name: '本月',
						id: 1
					}
				],
				current: 0,
				tempTotal: {},

				total: {
					day: {
						orderNum: 0,
						refundMoney: 0,
						salesPrice: 0,
						grossProfitMargin:0
					},
					month: {
						orderNum: 0,
						refundMoney: 0,
						salesPrice: 0
					}
				},

				riskNum: 0,
				noticeShow: false,
				load: false,
				timer: null,
				tipsCount: {
					refundNum: 0,
					complaintsNum: 0,
					outOfStockNum: 0,
					riskOrderNum: 0
				},
				totalMenu: null,
				operMenu: [],
				usefMenu: [],
				marketMenu: [],
				newMenu:[],

				noticeNum: 0,
				onlineDevice: 0
			}
		},

		computed: {
			//消息
			notice() {
				let msg = '您有新的消息~'
				if (this.noticeNum > 0) {
					msg = `您有${this.noticeNum}条新的通知消息待查看!`
				} else {
					msg = '无新增通知~'
				}
				return msg
			}
		},

		async created() {
			this.totalMenu = this.getTotalMenu('首页', '统计数据')
			this.operMenu = this.getMenu('首页', '运营任务')
			this.usefMenu = this.getMenu('首页', '便捷功能')
			this.marketMenu = this.getMenu('首页', '营销工具')
			this.newMenu = this.getMenu('首页', '新功能')
		},

		methods: {
			refreshData() {
				this.getTotalData() //顶部统计数据
				this.getOnlineDevice() //在线设备数
				this.getNotice() //新增通知消息
				this.getNum() //常用菜单角标
				this.getComplaintsNum() //客诉角标
			},

			//更新统计数据
			update() {
				if (this.timer) {
					clearTimeout(this.timer)
				}
				this.load = true;
				this.timer = setTimeout(() => {
					this.load = false;
				}, 1000)
				this.getTotalData()
				this.getNum() //常用菜单角标
			},

			//统计数据
			getTotalData() {
				allCount().then(res => {
					this.total = res.data
				})
			},

			//在线设备数
			getOnlineDevice() {
				mercHomeStatistical({}).then(res => {
					this.onlineDevice = res.data.onlineNum ?? '-'
				})
			},

			//通知消息角标
			getNotice() {
				unRead().then(res => {
					this.noticeNum = res.data
				})
			},

			//常用菜单角标数字
			tips(name) {
				let num = 0;
				switch (name) {
					case '退款审核':
						num = this.tipsCount.refundNum
						break;
					case '异常订单':
						num = this.tipsCount.riskOrderNum
						break;
					case '客诉处理':
						num = this.tipsCount.complaintsNum
						break;
					case '补货管理':
						num = this.tipsCount.outOfStockNum
						break;
					default:
						break;
				}
				return num
			},

			//常用功能角标数据
			getNum() {
				tipsCount().then(res => {
					this.tipsCount.refundNum = res.data.refundNum;
					this.tipsCount.outOfStockNum = res.data.outOfStockNum;
					this.tipsCount.riskOrderNum = res.data.riskOrderNum;
				}).catch(err => {
					this.tipsCount = {
						refundNum: 0,
						outOfStockNum: 0,
						riskOrderNum: 0
					}
				})
			},

			//客诉处理角标
			getComplaintsNum() {
				complaintCount({}).then(res => {
					this.tipsCount.complaintsNum = res.data.untreatedCount
				})
			},

			getTotalMenu(menu1, menu2) {
				let permissions_menu = this.$store.state.permission.permissions_menu
				if (permissions_menu && permissions_menu != '[]') {
					let allMenu = JSON.parse(permissions_menu);
					if (allMenu.find(i => i.name == menu1) && allMenu.find(i => i.name == menu1).children) {
						let homeMenu = allMenu.find(i => i.name == menu1).children;
						if (homeMenu.find(i => i.name == menu2)) {
							return true
						}
					}
				}
				return false
			},

			getMenu(menu1, menu2) {
				let menu = []
				let permissions_menu = this.$store.state.permission.permissions_menu
				if (permissions_menu && permissions_menu != '[]') {
					let allMenu = JSON.parse(permissions_menu);
					if (allMenu.find(i => i.name == menu1) && allMenu.find(i => i.name == menu1).children) {
						let homeMenu = allMenu.find(i => i.name == menu1).children;
						console.log(homeMenu)
						if (homeMenu.find(i => i.name == menu2) && homeMenu.find(i => i.name == menu2).children) {
							menu = homeMenu.find(i => i.name == menu2).children
						}
					}
				}
				return menu
			},

			scan() {
				uni.scanCode({
					success: (res) => {
						let deviceId = res.result.split('=')[1]
						if (deviceId) {
							this.$tab.navigateTo(`/pages/activeDevice/bindDevice?id=${deviceId}`)
						} else {
							this.$modal.msg('该二维码无效~')
						}
					}
				});
			},
			tabChange(e) {
				this.current = e.id
				this.switchTotal(this.current)
			},

			// 更多数据
			moreData() {
				if (this.checkPermi(['indSta:moreData'])) {
					this.$tab.navigateTo('/pages/globalPages/moreData')
				} else {
					this.$modal.msg('您没有该权限！')
				}
			},

			menuClick(e) {
				if (e.route.indexOf('/') != -1) {
					this.$tab.navigateTo(`/pages/${e.route}`)
				} else {
					this.$modal.msg('功能开发中，尽请期待~')
				}
				// this.$tab.navigateTo(`/pages/collection/home`)
			},

			goComplain() {
				wx.navigateToMiniProgram({
					appId: 'wx49625208931d29ec',
					path: 'subpackages/order_complaint/index/index',
					// extraData: {
					// 	mercId: data.mercId,
					// 	mercName: data.mercName,
					// 	token: data.mercSatoken,
					// 	type: 1
					// },
					// envVersion:'trial',//跳转体验版
					success(res) {
						console.log(res);
					}
				})
			},

			clearStor() {
				try {
					uni.clearStorageSync();
				} catch (e) {
					console.log(e)
				}
			},

			noticeClose() {
				this.noticeShow = false
			},

			goNotice() {
				this.$tab.navigateTo('/pages/globalPages/notice')
			},


			/**
			 * 广告跳转
			 */
			idsClick() {
				wx.openEmbeddedMiniProgram({
					appId: 'wxad14149b49adf86b',
					path: 'pages/square/index?inviterCode=HB77096712&source=9&playletId=1764555233606189057',
					complete: () => {
						console.log('跳转成功！')
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(359deg);
		}
	}

	::v-deep.u-notice-bar {
		padding: 9px 12rpx !important;
	}

	.scroll-view {
		height: 100%;
	}

	.container {
		position: relative;
		padding-bottom: 40rpx;

		.nav-style {
			font-size: 32rpx;
			font-weight: bold;
			color: #fff;
		}

		// 统计信息
		.total {
			width: 724rpx;
			margin-left: 14rpx;
			overflow: hidden;
			border-radius: 14rpx;
			background-color: #fff;
			margin-top: 10rpx;

			.total-sale {
				width: 724rpx;
				height: 205rpx;
				background: url('https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/home/<USER>') no-repeat left top;
				background-size: 100% 100%;
				position: relative;

				.sale-item {
					margin-top: 46rpx;
					padding-left: 30rpx;

					.sale-name {
						font-size: 26rpx;
						font-weight: 500;
						color: #EEEDFF;
						line-height: 26rpx;
					}

					.sale-num {
						line-height: 50rpx;
						font-size: 50rpx;
						font-weight: 800;
						color: #FFFFFF;
						margin-top: 55rpx;
					}
				}

				.sale-more {
					font-size: 26rpx;
					font-weight: 500;
					color: #FFFFFF;
					padding-right: 18rpx;
					position: absolute;
					right: 12rpx;
					top: 22rpx;
					line-height: 26rpx;

					image {
						width: 24rpx;
						height: 24rpx;
						position: absolute;
						right: 0;
						top: 0;
					}
				}

				.update {
					width: 50rpx;
					height: 50rpx;
					position: absolute;
					right: 16rpx;
					bottom: 16rpx;

					&.load {
						animation: rotate 1s linear;
						animation-iteration-count: 3;
					}

					image {
						width: 50rpx;
						height: 50rpx;
					}
				}
			}

			.refund-order {
				padding:0 34rpx 26rpx;
				overflow: hidden;

				.refund-item {
					text-align: center;
					color: #2E2E2E;
					margin-top: 68rpx;
					// margin-right: 70rpx;

					// &:nth-child(3) {
					// 	margin-right: 0;
					// }

					.refund-num {
						line-height: 32rpx;
						font-size: 28rpx;
						text{
							font-size: 36rpx;
							font-weight: 800;
						}
					}

					.refund-name {
						font-size: 28rpx;
						font-weight: 500;
						line-height: 28rpx;
						margin-top: 27rpx;
					}
				}
			}

			.notice {
				width: 100%;
				position: relative;

				// .notice-tips {
				// 	padding: 10rpx;
				// 	color: #fff;
				// 	font-size: 24rpx;
				// 	text-align: center;
				// 	position: absolute;
				// 	right: 56rpx;
				// 	top: 50%;
				// 	transform: translateY(-50%);
				// 	z-index: 999;
				// 	background-color: #fff;

				// 	>view {
				// 		width: 36rpx;
				// 		height: 36rpx;
				// 		background-color: #E60012;
				// 		border-radius: 36rpx;
				// 		line-height: 36rpx;
				// 	}
				// }

				.notice-tips {
					padding: 10rpx;
					text-align: center;
					position: absolute;
					right: 40rpx;
					top: 50%;
					transform: translateY(-54%);
					z-index: 999;
					background-color: #fff;
				}
			}
		}

		//常用菜单
		.useful-menu {
			width: 724rpx;
			margin-left: 14rpx;

			.useful-menu-item {
				margin-top: 20rpx;
				width: 352rpx;
				height: 168rpx;
				background: #FFFFFF;
				box-shadow: 0px 0px 10px 0px rgba(174, 201, 255, 0.2);
				border-radius: 14rpx;
				padding: 0 66rpx 0 46rpx;
				position: relative;

				>.image {
					width: 75rpx;
					height: 75rpx;
				}

				>.menu-name {
					font-size: 32rpx;
					font-weight: 500;
					color: #333333;
				}

				.tips {
					// width: 40rpx;
					// height: 40rpx;
					// background-color: #E60012;
					// color: #fff;
					// font-size: 24rpx;
					// text-align: center;
					// line-height: 28rpx;
					// border-radius: 40rpx;
					// position: absolute;
					// right: -8rpx;
					// top: -8rpx;
					// padding: 6rpx;

					width: 40rpx;
					height: 40rpx;
					position: absolute;
					right: -8rpx;
					top: -8rpx;
					padding: 6rpx;
				}
			}
		}

		// 广告
		.ids {
			width: 100%;
			padding: 22rpx 14rpx 0;
		}

		// 便捷功能
		.menu {
			width: 724rpx;
			margin-left: 14rpx;
			background: #FFFFFF;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
			border-radius: 14rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-around;
			margin-top: 20rpx;
			position: relative;
			padding: 101rpx 36rpx 0rpx;

			.menu-title {
				font-size: 28rpx;
				font-weight: 800;
				color: #000;
				position: absolute;
				left: 28rpx;
				top: 28rpx;
			}

			.menu-item {
				margin-bottom: 48rpx;
			}

			.menu-content {
				width: 100%;
				flex-flow: row wrap;
			}

			.menu-item {
				text-align: center;
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;

				>.image {
					width: 57rpx;
					height: 57rpx;
					position: relative;
				}

				>view {
					color: #333;
					font-size: 24rpx;
					line-height: 24rpx;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 16rpx;
				}
			}
		}
	}
</style>