import request from '@/utils/request'

/**
 * 促销活动列表
 * @param {Object} data
 */
export function proPage(data) {
	return request({
		url: '/act/home/<USER>/page',
		method: 'post',
		data: data
	})
}

export function addPro(data) {
	return request({
		url: '/act/home/<USER>/add',
		method: 'post',
		data: data
	})
}

export function proDetail(data) {
	return request({
		url: '/act/home/<USER>/obj',
		method: 'post',
		data: data
	})
}

export function proEdit(data) {
	return request({
		url: '/act/home/<USER>/update',
		method: 'post',
		data: data
	})
}

export function addSpe(data) {
	return request({
		url: '/act/home/<USER>/add',
		method: 'post',
		data: data
	})
}

export function speDetail(data) {
	return request({
		url: '/act/home/<USER>/obj',
		method: 'post',
		data: data
	})
}

export function speEdit(data) {
	return request({
		url: '/act/home/<USER>/update',
		method: 'post',
		data: data
	})
}

export function proStatus(data) {
	return request({
		url: '/act/home/<USER>/update_enable_status',
		method: 'post',
		data: data
	})
}

export function sendStatus(data) {
	return request({
		url: '/act/home/<USER>/update_enable_status',
		method: 'post',
		data: data
	})
}

export function speStatus(data) {
	return request({
		url: '/act/home/<USER>/update_enable_status',
		method: 'post',
		data: data
	})
}



export function addSend(data) {
	return request({
		url: '/act/home/<USER>/add',
		method: 'post',
		data: data
	})
}

export function sendDetail(data) {
	return request({
		url: '/act/home/<USER>/obj',
		method: 'post',
		data: data
	})
}

export function sendEdit(data) {
	return request({
		url: '/act/home/<USER>/update',
		method: 'post',
		data: data
	})
}


