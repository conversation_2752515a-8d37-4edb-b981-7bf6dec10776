<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="编辑套餐"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap" style="padding:0 30rpx;">
					<u-form-item label="名称" borderBottom>
						<u--input inputAlign="right" v-model="form.mealName" placeholder="请输入" border="none"></u--input>
					</u-form-item>
					<u-form-item label="是否永久套餐" borderBottom>
						<view class="flex justify-end">
							<u-switch activeColor="#2C6FF3" size="20" v-model="form.forever"
								@change
								="switchChange"></u-switch>
						</view>
					</u-form-item>
					<block v-if="!form.forever">
						<u-form-item label="天数" borderBottom>
							<u--input inputAlign="right" type="number" v-model="form.days" placeholder="请输入"
								border="none"></u--input>
						</u-form-item>
						<u-form-item label="天数别名" borderBottom>
							<u--input inputAlign="right" v-model="form.daysName" placeholder="例如:一个月"
								border="none"></u--input>
						</u-form-item>
					</block>
					<u-form-item label="价格" borderBottom>
						<u--input inputAlign="right" v-model="form.price" placeholder="请输入" border="none"></u--input>
					</u-form-item>
					<u-form-item label="默认折扣" borderBottom>
						<u--input inputAlign="right" type="digit" v-model="form.discount" placeholder="输入不能大于1"
							border="none"></u--input>
					</u-form-item>
					<view class="action-time">
						<u-form-item label="类型" borderBottom>
							<u-radio-group v-model="form.memberType" placement="row" @change="actionStatusRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="全部">
								</u-radio>
								<u-radio label="仅限新用户" name="仅限新用户">
								</u-radio>
							</u-radio-group>
						</u-form-item>
					</view>
					<u-form-item label="排序(套餐顺序按从小到大排列)" borderBottom>
						<u--input inputAlign="right" type="number" v-model="form.weigh" placeholder="请输入"
							border="none"></u--input>
					</u-form-item>
					<view class="action-time">
						<u-form-item label="状态" borderBottom>
							<u-radio-group v-model="form.status" placement="row" @change="actionStatusRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="正常" name="正常">
								</u-radio>
								<u-radio label="下架" name="下架">
								</u-radio>
							</u-radio-group>
						</u-form-item>
					</view>

					<u-form-item label="权益说明">
						<u--textarea autoHeight border="none" v-model="form.remark" placeholder="请输入"></u--textarea>
					</u-form-item>
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<!-- <u-form-item label="选择设备" borderBottom>
						<u-radio-group v-model="form.isAllDevice" placement="row" @change="actionAllDeviceRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
							</u-radio>
							<u-radio label="指定设备" name="0">
							</u-radio>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="指定设备" borderBottom @click="chooseDev" v-if="form.isAllDevice==0">
						<view class="choose-list" style="text-align: right;"
							v-if="form.deviceList&&form.deviceList.length>0">
							<text v-for="(item,index) in form.deviceList" :key="item.deviceId">
								{{item.deviceName||item.deviceId}}
								<text v-if="index!=form.deviceList.length-1">,</text>
							</text>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择设备</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item> -->

					<u-form-item label="选择商品" borderBottom>
						<u-radio-group v-model="form.isAllGoods" placement="row" @change="actionAllGoodsRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
							</u-radio>
							<u-radio label="指定商品" name="0">
							</u-radio>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="指定商品" borderBottom @click="chooseCom" v-if="form.isAllGoods==0">
						<view class="choose-list flex align-center flex-wrap"
							v-if="form.goodsList&&form.goodsList.length>0">
							<view v-for="(item,index) in form.goodsList" :key="index">
								{{item.goodsName||item.name}}
								<text v-if="index!=form.goodsList.length-1">,</text>
							</view>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择商品</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap" style="padding:0 13rpx 24rpx;" v-if="form.isAllGoods==0">
					<view class="spe-list">
						<view class="spe-head flex justify-between align-center" style="line-height: 104rpx;">
							<view>特价设置</view>
							<view v-if="this.form.isAllGoods=='0'">
								<xbutton width="134rpx" round="14rpx" @click="chooseCom">增加商品</xbutton>
							</view>
						</view>
						<view class="spe-body" v-if="form.goodsList&&form.goodsList.length>0">
							<block v-for="(item,index) in form.goodsList" :key="item.goodsId">
								<view class="com-item flex align-center">
									<view class="com-img">
										<u--image radius="4" width="124rpx" height="150rpx" :src="item.cover"
											mode="aspectFit" :lazy-lord="true"></u--image>
									</view>
									<view class="com-content">
										<view class="com-name">
											{{istem.goodsName||item.name}}
										</view>
										<view class="com-num-spe flex justify-between">
											<view class="com-num flex align-center">
												<view>折扣：</view>
												<view class="com-input flex align-center justify-center">
													<u--input v-model="item.discount" placeholder="请输入"
														border="none"></u--input>
												</view>
											</view>
										</view>
									</view>

									<view class="close-btn" @click="delCom(index)">
										<u-icon name="close" color="#333" size="16"></u-icon>
									</view>
								</view>
							</block>
						</view>
						<view class="spe-body" v-else style="text-align: center;line-height: 100rpx;">
							请选择商品！
						</view>
					</view>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		save,
		vipMsg,
		update
	} from "@/api/vip.js"

	export default {
		data() {
			return {
				form: {
					"mealName": null,
					"price": null,
					"discount": null,
					"days": null,
					"goodsList": [],
					// "deviceList": [],
					"weigh": null,
					"daysName": null,
					// "isAllDevice": '1',
					"isAllGoods": '1',
					"memberType": '全部',
					"remark": null,
					"status": '正常',
					"forever": false
				},
			}
		},

		onLoad(o) {
			if (o.id) {
				this.getDetail(o.id)
			}

			uni.$on('updateCom', res => {
				this.form.goodsList = res
			})
		},

		methods: {
			getDetail(id) {
				vipMsg({
					id: id
				}).then(res => {
					let obj = res.data
					let goodsList = []
					if (obj.goodsList && obj.goodsList.length > 0) {
						goodsList = obj.goodsList.map(i => ({
							goodsId: i.id,
							goodsName: i.name,
							discount: i.discount,
							cover: i.cover
						}))
					}
					let newForm = {
						"id": obj.id,
						"mealName": obj.mealName,
						"price": this.$xy.delMoney(obj.price),
						"discount": obj.discount,
						"days": obj.days,
						"goodsList": goodsList,
						// "deviceList": obj.deviceList ? obj.deviceList : [],
						"weigh": obj.weigh,
						"daysName": obj.daysName,
						// "isAllDevice": obj.isAllDevice ? '1' : '0',
						"isAllGoods": obj.isAllGoods ? '1' : '0',
						"memberType": obj.memberType,
						"remark": obj.remark,
						"status": obj.status
					}
					this.form = JSON.parse(JSON.stringify(newForm))
					this.$forceUpdate()
				})
			},

			switchChange(e) {
				if (e) {
					this.form.days = 99999;
					this.form.daysName = ""
				} else {
					this.form.days = "";
					this.form.daysName = ""
				}
			},

			chooseDev() {
				let ids = []
				if (this.form.deviceList && this.form.deviceList.length > 0) {
					ids = this.form.deviceList.map(i => i.deviceId)
				}
				this.$tab.navigateTo(`/pages/vip/chooseDevice?ids=${JSON.stringify(ids)}`)
			},

			chooseCom() {
				let goodsList = JSON.stringify(this.form.goodsList)
				uni.setStorageSync('vipSto', goodsList)
				this.$tab.navigateTo(`/pages/vip/chooseGoods`)
			},

			delCom(index) {
				this.form.goodsList.splice(index, 1)
				this.$forceUpdate()
			},

			actionAllDeviceRadio(e) {
				this.form.isAllDevice = e
			},

			actionAllGoodsRadio(e) {
				if (e == 1) {
					this.$modal.confirm('警告：后续新增的商品，也将全部适用当前活动！！！').then(res => {
						this.form.isAllGoods = e
					}).catch(err => {
						this.form.isAllGoods = '0'
					})
				} else {
					this.form.isAllGoods = e
				}
			},

			actionStatusRadio(e) {
				this.form.enable_status = e
			},

			priceSet(value, oldPrice) {
				if (value != null) {
					if (value <= 0 || value * 100 >= oldPrice) {
						this.$modal.msg('特价价格需低于原价，特价不能为0')
					}
				}
			},

			//表单提交
			submit() {
				console.log(this.form)
				if (this.checkParams(this.form)) return
				let goodsList = []
				if (this.form.isAllGoods == 0) {
					goodsList = this.form.goodsList.map(item => ({
						goodsId: item.goodsId,
						discount: item.discount
					}))
				}
				// let deviceIdList = []
				// if (this.form.isAllDevice == 0 && this.form.deviceList?.length > 0) {
				// 	deviceIdList = this.form.deviceList.map(i => i.deviceId)
				// }
				let params = {
					"mealName": this.form.mealName,
					"price": this.$xy.delMoneyL(this.form.price),
					"discount": this.form.discount,
					"days": this.form.days,
					"goodsList": goodsList,
					// "deviceIdList": deviceIdList,
					"weigh": this.form.weigh,
					"daysName": this.form.daysName,
					// "isAllDevice": this.form.isAllDevice == 1,
					"isAllGoods": this.form.isAllGoods == 1,
					"memberType": this.form.memberType,
					"remark": this.form.remark,
					"status": this.form.status
				}
				if (this.form.id) {
					params.id = this.form.id
					update(params).then(res => {
						this.$modal.msg('修改成功~')
						uni.$emit('updateList')
						setTimeout(() => {
							this.$tab.navigateBack()
						}, 1000)
					}).catch(err => {

					})
				} else {
					save(params).then(res => {
						this.$modal.msg('新建成功~')
						uni.$emit('updateList')
						setTimeout(() => {
							this.$tab.navigateBack()
						}, 1000)
					}).catch(err => {

					})
				}
			},

			checkParams(params) {
				if (!params.mealName) {
					console.log(123)
					this.$modal.msg('请输入套餐名称')
					return true
				}
				if (params.price == null) {
					this.$modal.msg('请输入套餐价格')
					return true
				}
				if (params.days == null) {
					this.$modal.msg('请输入天数')
					return true
				}
				if (params.discount == null) {
					this.$modal.msg('请输入默认折扣')
					return true
				}
				if (!/^(0\.(0[1-9]|[1-9]{1,2}|[1-9]0)$)|^1$/.test(params.discount)) {
					this.$modal.msg('折扣请输入0-1最多两位小数')
					return true
				}
				// if (params.isAllDevice == 0 && !params.deviceList.length > 0) {
				// 	this.$modal.msg('请输入选择设备')
				// 	return true
				// }
				if (params.isAllGoods == 0 && (params.goodsList == 0 && !params.goodsList.length > 0)) {
					this.$modal.msg('请输入选择商品')
					return true
				}
				for (let item of params.goodsList) {
					if (item.discount == null || item.discount == '') {
						this.$modal.msg('请输入商品折扣')
						return true
					}
				}
				return false
			},
		},
		onUnload() {
			uni.$off('updateCom')
		}
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
			}

			.input-text {
				text-align: right !important;
			}

			.tab-wrap {
				padding: 12rpx 0;
			}

			.choose-list {
				width: 418rpx;
				line-height: 40rpx;
			}

			.mj-content {

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 230rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.form-item {
				position: relative;

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.action-time {
			position: relative;

			.edit {
				color: #2C6FF3;
				text-decoration: underline;
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);

				&.del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.priority {
			position: relative;

			.tips {
				position: absolute;
				color: red;
				font-size: 24rpx;
				left: 100rpx;
				top: 50%;
				transform: translateY(-50%);
			}
		}


		.time-wrap {
			.time-item {
				line-height: 84rpx;

				.time-select {}

				.action-time-del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.spe-list {
			.spe-head {
				padding: 0 17rpx;
			}

			.spe-body {
				.com-item {
					padding: 25rpx 25rpx 30rpx;
					box-shadow: 0 6px 18px #1d293924;
					position: relative;

					+.com-item {
						margin-top: 20rpx;
					}

					background: #FFFFFF;
					border-radius: 14rpx;
					box-shadow: #333333;

					.com-img {
						margin-right: 26rpx;
					}

					.com-content {
						.com-name {
							font-size: 28rpx;
							font-weight: 800;
							color: #333333;
							width: 460rpx;
						}

						.com-old-price {
							font-size: 26rpx;
							color: #555555;
							margin-top: 20rpx;
						}

						.com-num-spe {
							font-size: 26rpx;
							margin-top: 20rpx;

							.com-num,
							.com-spe {
								.com-input {
									width: 139rpx;
									height: 68rpx;
									border: 1px solid #D8D8D8;
									border-radius: 8rpx;
									padding: 0 12rpx;
								}
							}

							.com-num {
								margin-right: 58rpx;
							}
						}
					}

					.close-btn {
						position: absolute;
						right: 0;
						top: 0;
						padding: 24rpx;
					}
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>