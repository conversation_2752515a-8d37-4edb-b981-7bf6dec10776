<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="用户想买"></u-navbar>
		<view class="content">
			<scroll-view @scrolltolower="loadMore" class="scrollview" :scroll-with-animation="true" scroll-y
				lower-threshold="100" :style="{height:fullHeight}">
				<view class="list" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="index">
						<view class="comm-main">
							<view class="item-head flex justify-between">
								<view class="title">
									{{item.content||'无'}}
								</view>
							</view>
							<view class="item-body">
								<view class="item-body-sec">
									<view style="width:150rpx;">设备id：</view>
									<view>{{item.deviceId||'无'}}</view>
								</view>
								<view class="item-body-sec">
									<view style="width:150rpx;">创建时间：</view>
									<view>{{item.createTime||'无'}}</view>
								</view>
								<view class="item-body-sec">
									<view style="width:150rpx;">用户手机：</view>
									<view>{{item.tel||'无'}}</view>
								</view>
							</view>
							<view class="flex align-center" style="margin-top: 12rpx;" v-if="item.imgUrls">
								<view v-for="(item1,index1) in item.imgUrls" :key="index1"
									style="margin:0 24rpx 24rpx 0;">
									<u--image radius="4" width="130rpx" height="130rpx" :src="item1"
										@click="$xy.previewImg(item.imgUrls)" mode="aspectFit"
										:lazy-lord="true"></u--image>
								</view>
							</view>
						</view>
					</block>
					<view class="more" style="overflow: hidden;">
						<u-loadmore :status="status" v-if="list.length>=1" />
					</view>
				</view>
				<view class="empty" v-if="list.length==0">
					<u-empty mode="list" text="没有任何通知!"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		list
	} from "@/api/wantEat.js"
	export default {
		data() {
			return {
				list: [], //列表
				fullHeight: 0,
				page: 1, //当前分页
				size: 10, //分页数据条数
				status: 'loadmore', //加载更多
			}
		},

		async onLoad() {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)
			this.getList()
		},

		methods: {
			//消息中心
			getList() {
				list({
					page: {
						current: this.page,
						size: this.size,
						orders: [{
							column: 'create_time',
							asc: false
						}]
					}
				}).then(res => {
					let newData = res.data.records;
					for (var i = 0; i < newData.length; i++) {
						let item = newData[i]
						if (item.imgUrls) {
							item.imgUrls = item.imgUrls?.split(',')
						}
					}
					if (newData.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.list = this.list.concat(newData)
				})
			},

			//重置数据
			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			//上拉加载
			loadMore(e) {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			detail(item) {
				this.$tab.navigateTo('/pages/customer/wantEatDetail?id=' + item.infoId)

			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.u-nav-slot {
			.nav-title {
				font-size: 32rpx;
				color: #fff;
			}

			.clear-img {
				width: 50rpx;
				height: 50rpx;
				border-radius: 50rpx;
				background-color: rgba(0, 0, 0, 0.2);
				margin-left: 44rpx;

				>image {
					width: 36rpx;
					height: 36rpx;
				}
			}
		}

		.content {

			.list {
				width: 100%;
				padding: 0rpx 13rpx 12rpx;
				padding-bottom: calc(110rpx + env(safe-area-inset-bottom) / 2);
				overflow: hidden;

				.comm-img {
					width: 130rpx;
					height: 130rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-around;

					image {
						width: 130rpx;
						height: 130rpx;
					}
				}

				.comm-main {
					padding: 20rpx 30rpx;
					background-color: #fff;
					border-radius: 12rpx;
					margin-top: 12rpx;
					box-sizing: border-box;
					color: #999;
					line-height: 60rpx;
					position: relative;

					.item-head {
						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}

						.status {
							font-size: 28rpx;
						}

						.read-status {
							width: 20rpx;
							height: 20rpx;
							border-radius: 20rpx;
							background-color: red;
						}
					}

					.item-body {
						margin-top: 22rpx;
						color: #333;

						.item-body-sec {
							line-height: 44rpx;
							overflow: hidden;

							>view:nth-child(1) {
								float: left;
							}

							>view:nth-child(2) {
								padding-left: 150rpx;
								word-break: break-all;
							}
						}
					}
				}
			}
		}
	}

	.empty {
		padding-top: 40%;
	}
</style>