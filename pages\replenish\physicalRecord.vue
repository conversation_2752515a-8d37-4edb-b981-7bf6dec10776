<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="盘点"></u-navbar>
		<view class="content">
			<view class="search">
				<xdeviceSelect @inputSelect="inputSelect" @inputClear="inputClear" />
			</view>
			<scroll-view @scrolltolower="loadMore" class="scrollview" :scroll-with-animation="true" scroll-y
				lower-threshold="100" :style="{height:fullHeight}">

				<view class="list-warp" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="item.deviceId">
						<view class="item-wrap xy-card">
							<view class="item-head">
								<view class="title">
									<view v-if="item.deviceName">{{item.deviceName}}({{item.deviceId}})</view>
									<view v-else>{{item.deviceId}}</view>
								</view>
							</view>
							<view class="item-body flex justify-between align-center">
								<view class="item-body-sec">
									<view>盘点时间：</view>{{item.inventoryDate}}
								</view>
								<view class="view flex align-center justify-end">
									<xbutton padding="15rpx 40rpx" delay="2000" bgColor="#F4F8FF" color="#2C6FF3"
										@click="detail(item)">明细</xbutton>
								</view>
							</view>
						</view>
					</block>
				</view>

				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

		</view>
	</view>
</template>

<script>
	import {
		pageByGroupDate
	} from '@/api/replenishment/replenishment.js'
	export default {
		data() {
			return {
				status: 'loadmore',
				list: [],
				page: 1,
				size: 10,
				fullHeight: 0,
				deviceId: ''
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			this.deviceId = o.deviceId ? o.deviceId : ''
			_this.search()
			_this.inputChange()
		},

		methods: {
			search() {
				this.reset()
				this.getList()
			},

			getList() {
				pageByGroupDate({
					page: {
						current: this.page,
						size: this.size
					},
					deviceId: this.deviceId
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.list = [];
			},

			//上拉加载
			loadMore(e) {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			/**
			 * @param {Object} item
			 * 明细
			 */
			detail(item) {
				console.log(item)
				this.$tab.navigateTo(
					`/pages/replenish/physicalRecordDevice?id=${item.deviceId}&time=${item.inventoryDate}`)
			},

			inputSelect(item) {
				this.deviceId = item.deviceId
				this.search()
			},

			inputClear() {
				this.deviceId = ''
				this.search()
			},
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #555;

		.content {
			overflow: hidden;
			padding: 0 13rpx;

			.search {
				width: 750rpx;
				padding: 20rpx 12rpx;
				margin-left: -12rpx;
				background-color: #fff;
			}

			.btn {
				margin-top: 20rpx;
				padding-bottom: 24rpx;
			}

			.list-warp {
				margin-top: 10rpx;

				.item-wrap {
					+.item-wrap {
						margin-top: 20rpx;
					}

					.item-head {
						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}

						.status {
							font-size: 28rpx;
						}
					}

					.item-body {
						margin-top: 22rpx;

						.item-body-sec {
							line-height: 44rpx;

							>view {
								display: inline-block;
								min-width: 100rpx;
							}
						}
					}

					.item-foot {
						margin-top: 16rpx;

						.view {
							width: 500rpx;
						}
					}
				}
			}

			.empty {
				padding-top: 40%;
			}
		}

		.popup-content {
			width: 600rpx;
			border-radius: 12rpx;
			overflow: hidden;

			.txt {
				padding: 24rpx;
				text-align: center;
				word-break: break-all;
				line-height: 60rpx;
			}

			.txt1 {
				padding: 24rpx;
				text-align: left;
				word-break: break-all;
			}

			.button {
				text-align: center;
				line-height: 80rpx;
				border-top: 1rpx solid #dfdfdf;

				>view {
					flex: 1;
					border-right: 1rpx solid #dfdfdf;
				}

				>navigator {
					flex: 1;
					color: #2C6FF3;
				}
			}
		}
	}
</style>