<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="补货记录"></u-navbar>
		<view class="search">
			<u-search :clearabled="true" v-model="page.deviceId" placeholder='输入设备ID进行搜索' :showAction="true"
				actionText="搜索" @custom='custom'></u-search>
		</view>
		<view class="flex align-center justify-between margin">
			<view class="flex align-center">
				<view class="flex align-center time">
					<view class="" @tap="replenishmentdate('start')">{{dateStart?dateStart:'开始时间'}}</view>
					<view class="" style="padding:0 24rpx;">至</view>
					<view class="" @tap="replenishmentdate('end')">{{dateend?dateend:'结束时间'}}</view>
				</view>
				<image class="date-image" src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/date.png" mode="widthFix"></image>
			</view>
			<view class="flex align-center" @tap="pickershow=true">
				<view class="" style="font-size: 26rpx;">{{isopen?isopen:'是否开门'}}</view>
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/selectIcon.png" mode="aspectFit"
					style="width:24rpx;height: 24rpx;margin-left: 8rpx;"></image>

				<!-- <view class="filter">筛选</view> -->
			</view>
		</view>
		<scroll-view @scrolltolower="loadMore" class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100" :style="{'height':fullHeight}">
			<view class="scroll-content" v-if="replenishmentList.length>0">
				<view class="replenishmentrecord-container" v-for="item in replenishmentList" :key="item.id"  @tap="details(item)">
					<view class="flex align-center justify-between">
						<view class="">商户ID：{{item.mercId}}</view>
						
						<view class="">
							<u-tag v-if="!item.isOpenDoor" text="补货" type="error"></u-tag>
							<u-tag v-else text="开门补货"></u-tag>
						</view>
					</view>
					<view class="flex">设备ID：{{item.deviceId}}</view>
					<view class="flex" v-if="item.urlVideo0">视频路径：{{item.urlVideo0}}</view>
					<view class="flex" v-if="item.urlVideo1">视频路径：{{item.urlVideo1}}</view>
					<view class="flex">创建人姓名：{{item.createUserName}}</view>
					<view class="flex">创建时间：{{item.createTime}}</view>
				</view>
				<view class="load-more">
					<u-loadmore :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<u-picker title='是否开门' :show="pickershow" :columns="isOpenDoor" keyName="value" @confirm="pickerconfirm"
			@cancel='pickerclose'></u-picker>


		<u-datetime-picker :show="show" :closeOnClickOverlay="true" mode="date" @confirm="confirm" @cancel="close" v-model="timeStamp"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		page
	} from "@/api/replenishment/replenishment.js"
	export default {
		data() {
			return {
				page: {
					deviceId: '',
					isOpenDoor: '',
					beginCreateTime: '',
					endCreateTime: ''
				},
				dateStart: '',
				dateend: '',
				type: '',
				replenishmentList: [],
				mode: 'range',
				show: false,
				pickershow: false,
				date: [],
				index: 1,
				isopen: '',
				isOpenDoor: [
					[{
							type: '',
							value: '全部'
						},
						{
							type: true,
							value: '是'
						}, {
							type: false,
							value: '否'
						}
					],
				],
				timeStamp: new Date(), //时间picker显示时间
				
				current:1,
				loadmoreStatus:'loadmore',
				fullHeight:0,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')
			
			if(o.id){
				this.page.deviceId=o.id
			}
			// this.currentDate = new Date();
			// this.dateStart = uni.$u.timeFormat(this.currentDate, 'yyyy-mm-dd')
			// this.dateend = uni.$u.timeFormat(this.currentDate, 'yyyy-mm-dd')

			// this.page.beginCreateTime = this.dateStart + ' 00:00:00'
			// this.page.endCreateTime = this.dateend + ' 23:59:59'
			this.getPage()
		},
		methods: {
			franchiseePickerChange(e) {
				this.index = e.detail.value
			},
			replenishmentdate(type) {
				if (type == 'start') {
					this.type = type
				}
				if (type == 'end') {
					this.type = type
				}
				this.show = true
			},
			close() {
				this.show = false
			},
			confirm(e) {
				if (this.type == 'start') {
					this.dateStart = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
					this.page.beginCreateTime = this.dateStart + ' 00:00:00'
				}
				if (this.type == 'end') {
					this.dateend = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
					this.page.endCreateTime = this.dateend+ ' 23:59:59'
				}
				
				if(this.dateStart>this.dateend){
					uni.$u.toast('开始日期不能大于结束日期')
					return;
				}
				
				this.getPage()
				this.show = false
			},
			custom() {
				this.getPage()
			},
			pickerconfirm(e) {
				this.isopen = e.value[0].value

				this.page.isOpenDoor = e.value[0].type

				this.getPage()
				this.pickershow = false
			},
			pickerclose() {
				this.pickershow = false
			},
			// 补货详情
			details(val) {
				this.$tab.navigateTo('replenishmentRecordDetails?id=' + val.id)
			},
			getPage() {
				page({
					page: {
						current:this.current,
						size: 10,
					},
					"deviceId": this.page.deviceId,
					"isOpenDoor": this.page.isOpenDoor,
					"beginCreateTime": this.page.beginCreateTime,
					"endCreateTime": this.page.endCreateTime
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.replenishmentList = this.replenishmentList.concat(data)
				})
			},
			
			loadMore(e) {
				if (this.loadmoreStatus == 'nomore') return
				this.current++
				this.getPage()
			},
		}
	}
</script>
<style>
	page {
		background-color: #f7f7f7;
	}
</style>
<style lang="scss" scoped>
	.container {
		.margintop {
			margin-top: 20rpx;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.scrollview {
			overflow: hidden;
		}

		.empty {
			margin-top: 120rpx
		}

		.search {
			padding: 24rpx 24rpx;
			background-color: #fff;
		}

		.time {
			font-size: 26rpx;
			font-weight: 400;
			color: #333440;


		}

		.date-image {
			width: 30rpx;
			margin-left: 10rpx;
		}

		.filter {
			font-size: 26rpx;
			font-weight: 400;
			color: #000000;
			margin-left: 22rpx;
		}



		.date {
			margin-left: 10rpx;
			padding: 15rpx;
			border-radius: 15rpx;
			background-color: #fff;
		}

		.magin {
			margin: 10rpx 20rpx;
		}

		.replenishmentrecord-container {
			background-color: #fff;
			padding: 20rpx;
			border-radius: 15rpx;
			margin:0 20rpx 20rpx 20rpx;
			box-shadow: 0 5rpx 4rpx rgba(179, 179, 179, 0.3);
		}
	}
</style>
