<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="选择商品"></u-navbar>
		<view class="content">
		<!-- 	<view class="search">
				<u-search animation placeholder="商品搜索" v-model="keyword" :showAction="false" @search="search"></u-search>
			</view> -->
			<view class="list" v-if="commList&&commList.length>0">
				<view class="thumb-box" v-for="(item, index) in commList" :key="item.id"
					@click.stop="commItemSelect(item)">
					<view>
						<image class="select-img"
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
							mode="widthFix" v-show="item.checked"></image>
						<image class="select-img"
							src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
							mode="widthFix" v-show="!item.checked"></image>
					</view>
					<view class="check-content">
						<view class="comm-img">
							<u--image width="130rpx" height="130rpx" :src="item.cover" mode="aspectFit"
								:lazy-load="true"></u--image>
						</view>
						<view class="comm-main">
							<view>
								{{item.goodsName}}
							</view>
						<!-- 	<view>
								条形码：{{item.barcode}}
							</view> -->
							<view>
								商品ID：{{item.id}}
							</view>
							<view>
                算法SKUID：{{item.skuId}}
							</view>
							<!-- <view class="c-cx">
								促销活动：
							</view> -->
							<view class="c-pri">
								价格：<text>￥{{$xy.delMoney(item.price)}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="empty" v-else>
				<u-empty></u-empty>
			</view>
		</view>

		<view class="btn safe-bottom">
			<xbutton width="724rpx" size="large" round="50rpx" @click="sure">选择商品</xbutton>
		</view>

	</view>
</template>

<script>
	import {
		deviceGoods
	} from "@/api/commodity/goods.js"

	export default {
		data() {
			return {
				keyword: '',
				commList: [],
				deviceIds: null, //设备id
				goodsIds: [],
				goodsNames:[],
			}
		},

		onLoad(o) {
			this.deviceIds = JSON.parse(uni.getStorageSync('report')).deviceIds
			this.goodsIds = JSON.parse(uni.getStorageSync('report')).goodsIds
			this.goodsNames = JSON.parse(uni.getStorageSync('report')).goodsNames
			console.log('good',this.goodsIds)
			this.getList()
		},

		methods: {
			search(val) {
				this.getList()
			},
			// 商品选中状态改变
			commItemSelect(item) {
				if (item.checked) {
					item.checked = false
					this.goodsIds.splice(this.goodsIds.indexOf(item.goodsId), 1)
					this.goodsNames.splice(this.goodsNames.indexOf(item.goodsName), 1)
				} else {
					item.checked = true
					this.goodsIds.push(item.goodsId.toString())
					this.goodsNames.push(item.goodsName)
				}
				this.$forceUpdate()
			},

			sure() {
				let report = JSON.parse(uni.getStorageSync('report'))
				report.goodsIds = this.goodsIds
				report.goodsNames = this.goodsNames
				uni.setStorageSync('report', JSON.stringify(report))
				uni.$emit('update', report)
				this.$tab.navigateBack()
			},

			getList() {
				deviceGoods({
					deviceIds: this.deviceIds,
					keyword: this.keyword
				}).then(res => {
					let data = res.data;
					if(this.goodsIds){
						for (let i = 0; i < data.length; i++) {
							let item = data[i];
							if (this.goodsIds.indexOf(item.goodsId.toString())!=-1) {
								item.checked = true
							}else{
								item.checked = false
							}
						}
					}
					this.commList = data
					console.log('commList',this.commList)
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {
			padding-bottom: 88rpx;

			.search {
				padding: 24rpx 24rpx;
				background-color: #fff;
			}

			.list {
				width: 100%;
				padding: 12rpx 24rpx;

				.thumb-box {
					margin-bottom: 12rpx;
					border-bottom: 1rpx solid #f4f4f4;
					display: flex;
					flex-flow: row nowrap;
					padding: 12rpx 12rpx;
					align-items: center;
					background-color: #fff;
					border-radius: 12rpx;
				}

				.select-img {
					width: 34rpx;
					height: 34rpx;
				}

				.check-content {
					width: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					padding-left: 12rpx;
					position: relative;

					.comm-img {
						width: 130rpx;
						height: 130rpx;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-around;
						border-radius:8rpx;
						overflow: hidden;
						image {
							width: 100%;
						}
					}

					.comm-main {
						box-sizing: border-box;
						padding-left: 18rpx;
						color: #999;

						>view {
							padding: 8rpx 0;
							// width: 330rpx;
						}

						>view:nth-child(1) {
							font-size: 28rpx;
							font-weight: bold;
							color: #333;
							padding: 12rpx 0 8rpx 0;
						}

						>view:nth-child(2) {
							width: 450rpx;
							font-size: 26rpx;
						}

						>view:nth-child(3) {
							width: 450rpx;
							font-size: 26rpx;
						}

						>view:nth-child(4) {
							width: 450rpx;
							font-size: 26rpx;
							padding: 0;
							line-height: 40rpx;
						}

						>.c-cx {
							font-size: 26rpx;
						}

						>.c-pri {
							font-size: 26rpx;

							text {
								font-weight: bold;
								color: red;
								font-size: 30rpx;
							}

							>view {
								display: inline-block;
								font-size: 26rpx;
								color: #2C6FF3;
								text-decoration: underline;
								margin-left: 12rpx;
							}
						}
					}

					.status {
						width: 120rpx;
						height: 120rpx;
						box-sizing: border-box;
						border-radius: 120rpx;
						// border: 6rpx solid #2C6FF3;
						text-align: center;
						display: flex;
						flex-flow: column;
						justify-content: space-around;
						align-items: center;
						position: absolute;
						right: 12rpx;
						top: -12rpx;

						.s-name {
							font-size: 24rpx;
							padding-top: 16rpx;
							font-weight: bold;
						}

						.s-num {
							font-size: 28rpx;
							padding-bottom: 20rpx;
						}
					}

					.sale {
						position: absolute;
						right: 12rpx;
						bottom: 12rpx;
						text-align: center;
						color: #999;

						>view:nth-child(1) {
							font-size: 24rpx;
						}

						>view:nth-child(2) {
							font-size: 32rpx;
							color: #2C6FF3;
							font-weight: bold;
						}
					}
				}
			}
		}

		.empty {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.refresh {
			width: 80rpx;
			height: 80rpx;
			border-radius: 80rpx;
			text-align: center;
			line-height: 80rpx;
			position: fixed;
			right: 24rpx;
			bottom: 230rpx;
			background-color: #999;
			color: #fff;
			opacity: .8;
		}

		.btn {
			width: 100%;
			position: fixed;
			bottom: 24rpx;
			left: 0;
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-between;
			padding: 0 24rpx;

			.cu-btn {
				background-color: #2C6FF3;
				color: #fff;
				width: 48%;
			}
		}

		.pop-content {
			padding: 24rpx;
		}
	}
</style>
