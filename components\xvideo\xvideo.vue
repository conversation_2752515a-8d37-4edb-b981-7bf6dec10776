<template>
	<view class="sunny-video">
		<video class="video" :style="{height:elVideoHeight.strNum}" id="sunnyVideo" ref="sunnyVideo" :title="title"
			:src="src" :show-center-play-btn="false" :controls="true" @timeupdate="timeupdate" enable-play-gesture
			:show-mute-btn="showMuteBtn" @play="play" @pause="pause" @ended="ended" @error="playError"
			@fullscreenchange="fullscreenchange">
			<!-- <view class="video-view">
				<cover-image class="img" src="static/left.png" @click="onBack"></cover-image>
			</view> -->
			<view v-if="!isPlay" class="banner-view" :style="{height:elVideoHeight.strNum}" @click="onPlayChange">
				<image v-if="poster" class="banner" :style="{height:elVideoHeight.strNum}" :src="poster" mode=""
					@click="onPlayChange"></image>
				<!-- <image class="imgPal"
					:style="{width:playBtnHeight.strNum,height:playBtnHeight.strNum,top:`${elVideoHeight.intNum/2}px`,transform:`translate(-${playBtnHeight.intNum/2}px,-${playBtnHeight.intNum/2}px)`}"
					:src="playImg"></image> -->
			</view>

			<view v-if="isPlay&&!isShowRateBox" class="speedText"
				:style="{top:`${isFullScreen?(screenInfo.width/2) - 14.5 +'px':(elVideoHeight.intNum/2)- 14.5+'px'}`}"
				@click="isShowRateBox = true">
				<!-- #ifndef MP-WEIXIN || APP-NVUE -->
				<view class="text" @click="isShowRateBox = true">{{rateText}}X</view>
				<!-- #endif -->
				<!-- #ifdef APP-NVUE -->
				<text class="text">{{rateText}}X</text>
				<!-- #endif -->
				<!-- #ifdef MP-WEIXIN -->
				{{rateText}}X
				<!-- #endif -->
			</view>

			<view v-if="isFullScreen&&isShowRateBox" class="vertical vertical-full"
				:style="{width:`${screenInfo.height}px`,height:`${isShowRateBox? screenInfo.width : screenInfo.width-40}px`}">
				<!-- #ifdef APP-NVUE -->
				<view class="options"
					:style="{width:`${screenInfo.height - screenInfo.options}px`,height:`${screenInfo.width}px`}"
					@click="isShowRateBox=false"></view>
				<view class="speed-box" :style="{height:`${screenInfo.width}px`}">
					<text class="speeds" :class="{act:item.isTo}" v-for="(item,index) in rateList" :key="item.name"
						@click="changeRate(item,index)">{{item.name}}X</text>
				</view>
				<!-- #endif -->
				<!-- #ifndef APP-NVUE -->
				<view class="options"
					:style="{width:`${screenInfo.height - screenInfo.options}px`,height:`${screenInfo.width}px`}"
					@click="isShowRateBox=false"></view>
				<view class="speed-box" :style="{height:`${screenInfo.width}px`}">
					<view class="speeds" :class="{act:item.isTo}" v-for="(item,index) in rateList"
						:key="item.name" @click="changeRate(item,index)">{{item.name}}X</view>
				</view>
				<!-- #endif -->
			</view>
			<view v-if="!isFullScreen&&isShowRateBox" class="vertical"
				:style="{height:`${isShowRateBox ? elVideoHeight.intNum:elVideoHeight.intNum - 44}px`}">
				<!-- #ifdef APP-NVUE -->
				<view class="options" :style="{height:elVideoHeight.strNum}" @click="isShowRateBox=false"></view>
				<view class="speed-box" :style="{height:elVideoHeight.strNum}">
					<text class="speeds" :class="{act:item.isTo}" v-for="(item,index) in rateList" :key="item.name"
						@click="changeRate(item,index)">{{item.name}}X</text>
				</view>
				<!-- #endif -->
				<!-- #ifndef APP-NVUE -->
				<view class="options" :style="{height:elVideoHeight.strNum}"
					@click="isShowRateBox=false"></view>
				<view class="speed-box" :style="{height:elVideoHeight.strNum}">
					<view class="speeds" :class="{act:item.isTo}" v-for="(item,index) in rateList"
						:key="item.name" @click="changeRate(item,index)">{{item.name}}X</view>
				</view>
				<!-- #endif -->
			</view>
		</video>
	</view>
</template>

<script>
	import {
		getPx,
		addUnit,
		sys,
		playImgs
	} from './function.js'
	export default {
		props: {
			// 视频地址
			src: {
				type: String,
				default: ''
			},
			// 视频标题
			title: {
				type: String,
				default: ''
			},
			// 视频封面
			poster: {
				type: String,
				default: ''
			},
			// 视频高度
			videoHeight: {
				type: [String, Number],
				default: '218'
			},
			// 播放图片按钮宽高
			playImgHeight: {
				type: [String, Number],
				default: '70rpx'
			},
			// 暂停按钮
			playImg: {
				type: String,
				default: playImgs
			},
			// 是否显示静音按钮
			showMuteBtn: {
				type: Boolean,
				default: true
			},
			// 播放完毕是否退出全屏
			isExitFullScreen: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				videoCtx: '', //视频上下文
				isPlay: false, // 视频是否播放过
				isShowRateBox: false, // 是否显示倍速盒子
				rateText: '1.0', // 当前倍速
				rateList: [{
						name: '0.5',
						isTo: false
					}, {
						name: '0.8',
						isTo: false
					}, {
						name: '1.0',
						isTo: true
					}, {
						name: '1.25',
						isTo: false
					}, {
						name: '1.5',
						isTo: false
					}
					// #ifdef MP-WEIXIN
					, {
						name: '2.0',
						isTo: false
					}
					// #endif
				], //倍数
				isFullScreen: false, //全屏状态
				// 屏幕信息
				screenInfo: {
					width: 0,
					height: 0,
					options: getPx('160rpx'), // 遮罩要减去的高度
				}
			}
		},
		computed: {
			// 视频单位高度信息
			elVideoHeight() {
				return {
					intNum: getPx(this.videoHeight ? this.videoHeight : 230),
					strNum: addUnit(this.videoHeight ? this.videoHeight : 230)
				}
			},
			// 播放图片按钮宽高信息
			playBtnHeight() {
				return {
					intNum: getPx(this.playImgHeight ? this.playImgHeight : '70rpx'),
					strNum: addUnit(this.playImgHeight ? this.playImgHeight : '70rpx')
				}
			}
		},
		watch: {
			src: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.isPlay = false
						//初始化播放速度
						this.changeRate({
							name: '1.0',
							isTo: false
						}, 2)
					}
				},
				immediate: false,
			}
		},
		mounted() {
			this.videoCtx = uni.createVideoContext('sunnyVideo', this)
			this.getScreenInfo()
		},
		methods: {
			// 获取元素信息
			getDomInfo(elId) {
				return new Promise(resolve => {
					const view = uni.createSelectorQuery().select(`#${elId}`)
					view.boundingClientRect(data => {
						resolve(data.height)
					}).exec()
				})
			},
			//监听开始播放
			play(e) {
				this.isPlay = true
				this.$emit('play')
				this.videoCtx.playbackRate(this.rateText * 1)
			},
			// 监听视频暂停
			pause() {
				this.isPlay = false
				this.$emit('pause')
			},
			// 视屏播放出错
			playError(e) {
				this.$emit('playError', e)
			},
			// 监听视频结束
			ended() {
				this.isPlay = false
				this.$emit('videoEnded')
				if (!this.isExitFullScreen) return
				this.videoCtx.exitFullScreen(); //退出全屏
			},
			onBack() {
				uni.navigateBack()
			},
			// 播放进度
			timeupdate(e) {
				this.$emit('timeupdate', e)
			},
			// 切换倍速
			changeRate(item, index) {
				if (item.isTo) return this.isShowRateBox = false
				this.rateList.forEach((v, i) => {
					i == index ? v.isTo = true : v.isTo = false
				})
				this.videoCtx.playbackRate(item.name * 1)
				this.rateText = item.name
				this.isShowRateBox = false
			},
			// 播放视频
			onPlayChange() {
				this.$nextTick(() => {
					this.videoCtx.play()
					this.videoCtx.playbackRate(this.rateText * 1)
				})
			},
			// 全屏操作
			fullscreenchange() {
				this.isFullScreen = !this.isFullScreen
				this.$emit('fullscreenchange', this.isFullScreen)
			},
			// 获取屏幕信息
			getScreenInfo() {
				const res = sys()
				this.screenInfo.width = res.screenWidth
				this.screenInfo.height = res.screenHeight
			},
		}
	}
</script>

<style lang="scss">
	$sunny-primary: #5C91EF;

	.sunny-video {
		position: relative;
		width: 100%;
		z-index: 0;

		.speedText {
			position: absolute;
			top: 240rpx;
			right: 30rpx;
			z-index: 5;
			padding: 10rpx;
			padding-right: 0;
			font-size: 30rpx;
			color: $sunny-primary;
			/* #ifndef APP-NVUE */
			box-sizing: border-box;

			/* #endif */
			.text {
				font-size: 30rpx;
				color: $sunny-primary;
			}
		}
	}

	.video {
		width: 100%;
	}

	.video-view {
		position: absolute;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		/* #ifndef APP-NVUE */
		padding: var(--status-bar-height) 32rpx 0;
		/* #endif */

		.img {
			width: 56rpx;
			height: 56rpx;
		}
	}

	.banner-view {
		position: relative;
		width: 100%;
		height: 372px;

		.banner {
			width: 100%;
			height: 372px;
		}

		.imgPal {
			position: absolute;
			z-index: 2;
			top: 240rpx;
			left: 50%;
			// transform: translate(-50%, -35rpx);
			width: 70rpx;
			height: 70rpx;
		}
	}

	.vertical {
		position: relative;
		width: 100%;
		height: 410rpx;
		/* #ifndef APP-NVUE */
		display: flex;

		/* #endif */
		&.vertical-full {
			position: relative;

			.speed-box {
				padding: 50rpx 0;
			}
		}

		.options {
			width: 590rpx;
		}

		.speed-text {
			position: absolute;
			top: 240rpx;
			right: 30rpx;
			font-size: 30rpx;
			color: #fff;
		}

		.speed-box {
			position: absolute;
			right: 0;
			top: 0;
			width: 160rpx;
			height: 372px;
			/* #ifndef APP-NVUE */
			display: flex;
			box-sizing: border-box;
			/* #endif */
			flex-direction: column;
			align-items: center;
			justify-content: space-between;
			padding: 10px 0;
			background-color: rgba(0, 0, 0, 0.7);

			.speeds {
				/* #ifndef APP-NVUE */
				box-sizing: border-box;
				/* #endif */
				width: 160rpx;
				text-align: center;
				font-size: 28rpx;
				color: #fff;

				&.act {
					color: $sunny-primary;
				}
			}
		}
	}
</style>