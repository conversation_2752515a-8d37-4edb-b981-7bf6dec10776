<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="渠道类型管理"></u-navbar>
		<view class="content">
			<view v-if="list&&list.length>0">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="item xy-card flex justify-between align-center">
						<view class="title">{{item.name}}</view>
						<view class="flex">
							<xbutton size='medium' bgColor='red' round='25rpx' padding='0rpx 20rpx' width='100rpx'
								@tap="del(item)">删除
							</xbutton>
							<xbutton style="margin-left: 12rpx;" size='medium' round='25rpx' padding='0rpx 20rpx'
								width='100rpx' @tap="edit(item)">编辑
							</xbutton>
						</view>
					</view>
				</block>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</view>
		<view class="btn">
			<xbutton size="large" @click="add">
				新增
			</xbutton>
		</view>

		<!-- 弹框 -->
		<xpopup :show="popShow" @close="close" @confirm="submit" showBtn :title="title">
			<view class="popup-content">
				<view>渠道名称：</view>
				<u--input type="text" placeholder="渠道名称" border="surround" v-model="channelName"></u--input>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		channelType,
		saveOrUpdateType,
		delChannelType
	} from "@/api/channel.js"
	import {
		getMercId
	} from '@/utils/auth'

	export default {
		data() {
			return {
				popShow: false,
				channelName: null,
				channelId: null,
				list: [],
				title:'新增'
			}
		},

		onLoad(o) {
			this.getList()
		},

		methods: {
			getList(id) {
				channelType({
					page: {
						current: 1,
						size: 100
					}
				}).then(res => {
					let data = res.data.records
					this.list = data
				})
			},

			close() {
				this.reset()
				this.popShow = false
			},

			submit() {
				let params = this.channelId ? {
					mercId: getMercId(),
					id: this.channelId,
					name: this.channelName
				} : {
					mercId: getMercId(),
					name: this.channelName
				}
				saveOrUpdateType(params).then(res => {
					this.popShow = false
					let msg = this.channelId ? '修改成功！！！' : '新建成功！！！'
					this.$modal.msg(msg)
					setTimeout(()=>{
						this.getList()
					},1000)
				})
			},

			add() {
				this.title = '新增'
				this.reset()
				this.popShow = true
			},

			reset() {
				this.channelId = null
				this.channelName = null
			},

			del(item) {
				this.$modal.confirm('是否删除当前类型').then(res => {
					delChannelType({
						idList: [item.id]
					}).then(res => {
						this.$modal.msg('删除成功！！！')
						setTimeout(()=>{
							this.getList()
						},1000)
					})
				})
			},

			edit(item) {
				this.title = '编辑'
				this.popShow = true
				this.channelId = item.id
				this.channelName = item.name
			}
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		padding: 20rpx;

		.item {
			margin-bottom: 20rpx;

			.title {
				font-size: 32rpx;
				font-weight: bold;
			}
		}
		
		.empty {
			padding-top: 40%;
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}
	}
</style>