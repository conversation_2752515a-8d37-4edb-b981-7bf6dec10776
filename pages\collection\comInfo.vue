<template>
	<view class="add-goods">
		<!-- 顶部导航栏 -->
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff"
			:placeholder="true" title="商品信息"></u-navbar>

		<scroll-view scroll-y class="form-container" :style="{ paddingBottom: '120rpx' }">
			<!-- 基本信息 -->
			<view class="section">
				<view class="section-title">
					<view class="title-bar"></view>
					<text>基本信息</text>
				</view>

				<!-- 商品名称点击触发弹窗 -->
				<view class="form-item" @click="showGoodsSelect">
					<text class="label required">商品名称:</text>
					<view class="input-wrapper">
						<input class="input with-arrow" v-model="formData.name" placeholder="请选择商品" disabled />
						<u-icon name="arrow-right" size="18" color="#999"></u-icon>
					</view>
				</view>

				<view class="form-item justify-between">
					<view class="flex align-center">
						<text class="label">条形码:</text>
						<input class="input" v-model="formData.barCode" placeholder="请输入条形码"
							:disabled="!!selectedGoods" />
					</view>
					<xbutton size="mini" @click="scan">扫一扫</xbutton>
				</view>

				<view class="form-item">
					<text class="label required">规格:</text>
					<input class="input" v-model="formData.specs" placeholder="请输入规格" :disabled="!!selectedGoods" />
				</view>

				<!-- <view class="form-item">
          <text class="label required">商品分类:</text>
          <view class="input-wrapper">
            <input
              class="input with-arrow"
              v-model="formData.catName"
              placeholder="请选择商品分类"
              disabled
              @click="showCategoryPicker = true" />
            <u-icon name="arrow-right" size="18" color="#999"></u-icon>
          </view>
        </view> -->

				<view class="form-item">
					<text class="label required">品牌名称:</text>
					<input class="input" v-model="formData.brandName" placeholder="请填写品牌名称"
						:disabled="!!selectedGoods" />
				</view>

				<view class="form-item">
					<text class="label required">是否可售:</text>
					<u-radio-group v-model="formData.canSale" placement="row">
						<u-radio :customStyle="{ marginRight: '40rpx' }" label="是" name="yes">
						</u-radio>
						<u-radio label="否" name="no"> </u-radio>
					</u-radio-group>
				</view>

				<view class="form-item">
					<text class="label required">颜色:</text>
					<view class="input-wrapper">
						<input class="input with-arrow" :value="getColorText(formData.color)" placeholder="请选择颜色"
							disabled @click="showColorPicker = true" />
						<u-icon name="arrow-right" size="18" color="#999"></u-icon>
					</view>
				</view>

				<view class="form-item">
					<text class="label required">包装:</text>
					<view class="input-wrapper">
						<input class="input with-arrow" :value="getCategoryText(formData.category)" placeholder="请选择包装"
							disabled @click="showCategoryPicker = true" />
						<u-icon name="arrow-right" size="18" color="#999"></u-icon>
					</view>
				</view>
			</view>

			<!-- 价格信息 -->
			<view class="section">
				<view class="section-title">
					<view class="title-bar"></view>
					<text>价格信息</text>
				</view>

				<view class="form-item">
					<text class="label required">进价:</text>
					<input class="input" type="digit" v-model="formData.purchasePrice" placeholder="请输入进价" />
				</view>

				<view class="form-item">
					<text class="label required">零售价:</text>
					<input class="input" type="digit" v-model="formData.salePrice" placeholder="请输入零售价" />
				</view>
			</view>

			<!-- 商品展示图 -->
			<view class="section">
				<view class="section-title">
					<view class="title-bar"></view>
					<text>商品展示图</text>
				</view>

				<view class="form-item" style="padding-bottom: 20rpx">
					<text class="label required">展示图:</text>
					<view class="upload-container">
						<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" :maxCount="1"
							width="200" height="200" :previewFullImage="true"></u-upload>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 固定在底部的保存按钮 -->
		<view class="fixed-bottom-bar">
			<button class="save-btn" @click="submitForm">保存</button>
		</view>

		<!-- 选择器组件 -->
		<u-picker :show="showCategoryPicker" :columns="[categoryOptions]" @confirm="onCategoryConfirm"
			@cancel="showCategoryPicker = false">
		</u-picker>

		<u-picker :show="showColorPicker" :columns="[colorOptions]" @confirm="onColorConfirm"
			@cancel="showColorPicker = false">
		</u-picker>

		<u-picker :show="showPackagePicker" :columns="[packageOptions]" @confirm="onPackageConfirm"
			@cancel="showPackagePicker = false">
		</u-picker>

		<!-- 商品选择弹窗 -->
		<xpopup :show="showGoodsPopup" :mode="'center'" :title="'选择商品'" :showBtn="false" :round="'12'"
			@close="closeGoodsPopup">
			<view class="goods-popup">
				<!-- 搜索框 -->
				<view class="search-box">
					<u-search v-model="searchKeyword" placeholder="请输入商品名称搜索" actionText="确定" :show-action="true"
						:clearabled="true" @input="handleSearch" @clear="clearSearch" @search="searchSubmit" @custom="searchSubmit"
						height="70rpx">
					</u-search>
				</view>

				<!-- 商品列表容器 -->
				<view class="goods-list-container">
					<scroll-view scroll-y class="goods-list">
						<view class="goods-item" v-for="(item, index) in goodsList" :key="index"
							@click="selectGoods(item)">
							<image class="goods-image" :src="item.mainImg || '/static/images/default-goods.png'"
								mode="aspectFill" />
							<view class="goods-info">
								<text class="goods-name">{{ item.name }}</text>
								<text class="goods-spec">规格：{{ item.specs }}</text>
								<text class="goods-brand">品牌：{{ item.brandName }}</text>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		getSkuList,
		createProduct
	} from "@/api/collection.js";
	import config from '@/config'

	export default {
		data() {
			return {
				showGoodsPopup: false,
				searchKeyword: "",
				goodsList: [],
				selectedGoods: null,
				showCategoryPicker: false,
				showColorPicker: false,
				showPackagePicker: false,
				searchTimer: null,
				categoryOptions: [{
						text: "瓶装",
						value: "bottle"
					},
					{
						text: "盒装",
						value: "box"
					},
					{
						text: "袋装",
						value: "bag"
					},
					{
						text: "碗装",
						value: "bowl"
					},
					{
						text: "鸡蛋",
						value: "egg"
					},
				],
				colorOptions: [{
						text: "BK-黑色",
						value: "1"
					},
					{
						text: "WT-白色",
						value: "2"
					},
					{
						text: "RD-红色",
						value: "3"
					},
					{
						text: "GN-绿色",
						value: "4"
					},
					{
						text: "BU-蓝色",
						value: "5"
					},
					{
						text: "YL-黄色",
						value: "6"
					},
					{
						text: "OG-橙色",
						value: "7"
					},
					{
						text: "PL-紫色",
						value: "8"
					},
					{
						text: "GY-灰色",
						value: "9"
					},
					{
						text: "CR-透明",
						value: "10"
					},
				],
				formData: {
					name: "",
					specs: "",
					canSale: "yes",
					catId: "835",
					catName: "",
					brandName: "",
					purchasePrice: "",
					salePrice: "",
					mainImg: "", // 确保这个字段名称正确
					color: "",
					category: "",
					barCode: "",
				},
				fileList: [],
			};
		},

		watch: {
			// 监听formData.mainImg变化，同步到fileList
			'formData.mainImg': {
				handler(newVal, oldVal) {
					console.log('newval', newVal)
					if (newVal && this.fileList.length === 0) {
						this.fileList = [{
							url: newVal,
							status: 'success',
							message: '上传成功'
						}];
					}
				},
				immediate: true,
				deep: true,
			}
		},

		methods: {
			/**
			 * 处理搜索输入，使用防抖优化
			 * @param {string} value - 搜索关键词
			 */
			handleSearch(value) {
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				this.searchTimer = setTimeout(() => {
					this.searchGoods();
				}, 300);
			},

			searchSubmit(value) {
				console.log('搜索提交', value)
				this.formData.name = value
				this.closeGoodsPopup();
			},

			/**
			 * 显示商品选择弹窗
			 */
			showGoodsSelect() {
				this.showGoodsPopup = true;
			},

			//扫条码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.formData.barCode = res.result;
					}
				});
			},

			/**
			 * 根据关键词搜索商品
			 */
			searchGoods() {
				if (this.searchKeyword == "") return;
				getSkuList({
					name: this.searchKeyword,
				}).then((res) => {
					if (res.code === 200) {
						this.goodsList = res.data || [];
					}
				});
			},

			/**
			 * 清空搜索内容和结果
			 */
			clearSearch() {
				this.searchKeyword = "";
				this.goodsList = [];
			},

			/**
			 * 关闭商品选择弹窗
			 */
			closeGoodsPopup() {
				this.showGoodsPopup = false;
				this.searchKeyword = "";
				this.goodsList = [];
			},

			/**
			 * 图片选择后的回调
			 */
			afterRead(event) {
				const {
					file
				} = event;

				// 显示上传中
				uni.showLoading({
					title: '上传中...'
				});

				// 更新文件状态为上传中
				this.fileList[0] = {
					...file,
					status: 'uploading',
					message: '上传中'
				};

				// 上传图片到服务器
				uni.uploadFile({
					url: config.baseUrl + '/goods/merc-mini/jiangYiApi/uploadImage',
					filePath: file.url,
					name: 'file',
					header: {
						'satoken': uni.getStorageSync('App-Token') || ''
					},
					success: (uploadRes) => {
						try {
							// 解析返回的JSON数据
							const data = JSON.parse(uploadRes.data);
							if (data.code === 200) {
								// 确保设置正确的图片URL到表单数据
								this.$set(this.formData, 'mainImg', data.data);

								// 更新文件状态为上传成功
								this.fileList[0] = {
									...this.fileList[0],
									url: data.data,
									status: 'success',
									message: '上传成功'
								};

								console.log('this.fileList', this.fileList)

								uni.showToast({
									title: '上传成功',
									icon: 'success'
								});
							} else {
								this.handleUploadFail(data.msg || '上传失败');
							}
						} catch (e) {
							console.error('解析上传响应失败:', e);
							this.handleUploadFail('上传失败，返回数据格式错误');
						}
					},
					fail: (err) => {
						console.error('上传请求失败:', err);
						this.handleUploadFail('上传失败，请检查网络');
					},
					complete: () => {
						uni.hideLoading();
					}
				});
			},

			/**
			 * 处理上传失败
			 */
			handleUploadFail(message) {
				// 更新文件状态为上传失败
				this.fileList[0] = {
					...this.fileList[0],
					status: 'failed',
					message: message
				};

				uni.showToast({
					title: message,
					icon: 'none'
				});
			},

			/**
			 * 删除图片
			 */
			deletePic() {
				this.formData.mainImg = '';
				this.fileList = [];
			},

			/**
			 * 选择商品并填充表单
			 * @param {Object} item - 选中的商品信息
			 */
			selectGoods(item) {
				this.formData = {
					name: item.name,
					specs: item.specs,
					brandName: item.brandName,
					barCode: item.barCode,
					category: item.category,
					mainImg: item.mainImg,
				};
				this.$forceUpdate()

				this.closeGoodsPopup();
				uni.showToast({
					title: "已选择商品",
					icon: "success",
				});
			},

			/**
			 * 获取包装类型的显示文本
			 * @param {string} value - 包装类型的值
			 * @returns {string} 包装类型的显示文本
			 */
			getCategoryText(value) {
				const option = this.categoryOptions.find((opt) => opt.value === value);
				return option ? option.text : "";
			},

			/**
			 * 确认包装类型选择
			 * @param {Object} e - 选择器事件对象
			 */
			onCategoryConfirm(e) {
				const item = e.value[0];
				this.formData.category = item.value;
				this.showCategoryPicker = false;
			},

			/**
			 * 确认颜色选择
			 * @param {Object} e - 选择器事件对象
			 */
			onColorConfirm(e) {
				const item = e.value[0];
				this.formData.color = item.value;
				this.showColorPicker = false;
			},

			/**
			 * 获取颜色的显示文本
			 * @param {string} value - 颜色的值
			 * @returns {string} 颜色的显示文本
			 */
			getColorText(value) {
				const option = this.colorOptions.find((opt) => opt.value === value);
				return option ? option.text : "";
			},

			/**
			 * 验证表单数据
			 * @returns {boolean} 验证是否通过
			 */
			validateForm() {
				const required = [{
						fieId: "name",
						name: "商品名称"
					},
					{
						fieId: "specs",
						name: "规格"
					},
					{
						fieId: "catId",
						name: "商品分类"
					},
					{
						fieId: "brandName",
						name: "品牌名称"
					},
					{
						fieId: "color",
						name: "颜色"
					},
					{
						fieId: "category",
						name: "包装"
					},
					{
						fieId: "purchasePrice",
						name: "进价"
					},
					{
						fieId: "salePrice",
						name: "零售价"
					},
					{
						fieId: "mainImg",
						name: "展示图"
					},
				];

				console.log("提交的表单数据:", this.formData);

				for (const item of required) {
					if (!this.formData[item.fieId]) {
						uni.showToast({
							title: `请填写${item.name}`,
							icon: "none",
						});
						return false;
					}
				}

				if (
					isNaN(this.formData.purchasePrice) ||
					this.formData.purchasePrice <= 0
				) {
					uni.showToast({
						title: "请输入正确的进货价格",
						icon: "none",
					});
					return false;
				}

				if (isNaN(this.formData.salePrice) || this.formData.salePrice <= 0) {
					uni.showToast({
						title: "请输入正确的零售价格",
						icon: "none",
					});
					return false;
				}

				return true;
			},

			/**
			 * 提交表单数据
			 */
			submitForm() {
				if (!this.validateForm()) return;

				// 确保mainImg字段有值
				if (!this.formData.mainImg) {
					uni.showToast({
						title: "请上传商品展示图",
						icon: "none",
					});
					return;
				}

				// 创建一个新对象进行提交，确保数据正确
				const submitData = {
					...this.formData,
					mainImg: this.formData.mainImg
				};

				console.log("准备提交的数据:", submitData);

				createProduct(submitData).then((res) => {
					if (res.code === 200) {
						uni.showToast({
							title: "保存成功",
							icon: "success",
						});
						// 延迟返回上一页
						setTimeout(() => {
							uni.$emit("refresh");
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: res.msg || "保存失败",
							icon: "none",
						});
					}
				}).catch(err => {
					console.error("提交失败:", err);
					uni.showToast({
						title: "提交失败，请重试",
						icon: "none",
					});
				});
			},

			// 选择器确认事件
			onPackageConfirm(e) {
				const item = this.packageOptions[e.value[0]];
				this.formData.category = item.value;
				this.showPackagePicker = false;
			},
		},
	};
</script>

<style lang="scss">
	.add-goods {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
		position: relative;

		.search-section {
			padding: 20rpx;
			background-color: #fff;
		}

		.search-results {
			flex: 1;
			background-color: #fff;

			.result-item {
				padding: 20rpx;
				border-bottom: 1px solid #f5f5f5;

				.goods-info {
					display: flex;
					align-items: center;

					.goods-image {
						width: 120rpx;
						height: 120rpx;
						border-radius: 8rpx;
						margin-right: 20rpx;
					}

					.goods-details {
						flex: 1;
						display: flex;
						flex-direction: column;

						.goods-name {
							font-size: 28rpx;
							color: #333;
							margin-bottom: 10rpx;
						}

						.goods-spec,
						.goods-brand {
							font-size: 24rpx;
							color: #666;
							margin-top: 6rpx;
						}
					}
				}
			}
		}

		.form-container {
			flex: 1;
			padding: 20rpx;
		}

		.section {
			background-color: #fff;
			border-radius: 12rpx;
			margin-bottom: 20rpx;
			padding: 0 30rpx;

			.section-title {
				display: flex;
				align-items: center;
				height: 88rpx;

				.title-bar {
					width: 6rpx;
					height: 32rpx;
					background-color: #2C6FF3;
					margin-right: 20rpx;
				}

				text {
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
				}
			}
		}

		.form-item {
			display: flex;
			align-items: center;
			min-height: 88rpx;
			border-bottom: 1rpx solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}

			.label {
				width: 180rpx;
				font-size: 28rpx;
				color: #333;

				&.required::before {
					content: "*";
					color: #ff3b30;
					margin-right: 4rpx;
				}
			}

			.input-wrapper {
				flex: 1;
				display: flex;
				align-items: center;

				.input {
					flex: 1;
				}
			}

			.input {
				height: 88rpx;
				font-size: 28rpx;
				color: #333;

				&.with-arrow {
					padding-right: 40rpx;
				}

				&::placeholder {
					color: #999;
				}
			}

			.radio-group {
				flex: 1;
				display: flex;
				align-items: center;

				.radio-item {
					display: flex;
					align-items: center;
					margin-right: 40rpx;

					.radio-circle {
						width: 40rpx;
						height: 40rpx;
						border: 2rpx solid #ddd;
						border-radius: 50%;
						margin-right: 10rpx;
						display: flex;
						align-items: center;
						justify-content: center;

						&.active {
							border-color: #4177fb;
							background-color: #4177fb;

							&::after {
								content: "";
								width: 20rpx;
								height: 20rpx;
								background-color: #fff;
								border-radius: 50%;
							}
						}
					}

					text {
						font-size: 28rpx;
						color: #333;
					}
				}
			}

			.upload-image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 8rpx;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}

				.upload-placeholder {
					width: 100%;
					height: 100%;
					background-color: #f5f5f5;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					text {
						font-size: 24rpx;
						color: #999;
						margin-top: 10rpx;
					}
				}
			}
		}

		.fixed-bottom-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			background-color: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.save-btn {
				height: 88rpx;
				background-color: #4177fb;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0;
			}
		}

		// 商品选择弹窗样式
		.goods-popup {
			width: 600rpx;
			height: 800rpx;
			background-color: #fff;
			display: flex;
			flex-direction: column;

			.search-box {
				padding: 20rpx;
				background-color: #fff;
				border-bottom: 1rpx solid #f5f5f5;
				flex-shrink: 0;
			}

			.goods-list-container {
				flex: 1;
				overflow: hidden;

				.goods-list {
					height: 100%;

					.goods-item {
						display: flex;
						padding: 20rpx;
						border-bottom: 1rpx solid #f5f5f5;
						background-color: #fff;

						&:active {
							background-color: #f5f5f5;
						}

						.goods-image {
							width: 120rpx;
							height: 120rpx;
							border-radius: 8rpx;
							margin-right: 20rpx;
							flex-shrink: 0;
						}

						.goods-info {
							flex: 1;
							display: flex;
							flex-direction: column;
							justify-content: space-around;

							.goods-name {
								font-size: 28rpx;
								color: #333;
								font-weight: 500;
							}

							.goods-spec,
							.goods-brand {
								font-size: 24rpx;
								color: #666;
							}
						}
					}
				}
			}
		}
	}

	// 修改弹窗内容区域的样式
	:deep(.pop-content) {
		height: 800rpx;
		display: flex;
		flex-direction: column;

		.head {
			flex-shrink: 0;
		}
	}
</style>