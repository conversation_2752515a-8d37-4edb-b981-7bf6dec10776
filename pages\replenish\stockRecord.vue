<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="库存变化记录"></u-navbar>
		<view class="content">
			<view class="section" style="border-radius: 0;">
				<view class="item flex justify-between align-center">
					<view class="item-left">
						选择设备
					</view>
					<view class="item-right flex align-center" @click="chooseDevice">
						<text :class="[params.deviceId?'item-val':'item-val-show']">{{params.deviceId||'请选择设备'}}</text>
						<u-icon name="arrow-right" color="#777" size="16px"></u-icon>
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left">
						商品名称
					</view>
					<view class="item-right flex align-center" style="500rpx;">
						<xselectInput :value="params.goodsId" name="name" slabel="name" svalue="id" @clear="clearInput"
							placeholder="请选择商品" :options="goodsList" @select="selectGoods"
							@inputChange="inputChange" />
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-lefst">
						起始时间
					</view>
					<view class="item-right flex align-center">
						<view :class="[params.startTime?'start-time':'start-time-show']"
							@click="selectTime('startTime')">
							{{params.startTime||'开始日期'}}
						</view>
						<view style="padding:0 12rpx;">
							一
						</view>
						<view :class="[params.endTime?'end-time':'end-time-show']" @click="selectTime('endTime')">
							{{params.endTime||'结束日期'}}
						</view>
						<u-icon name="arrow-right" color="#777" size="16px"></u-icon>
					</view>
				</view>
			</view>

			<view class="section stock-content" style="margin-top: 20rpx; padding:6 30rpx 0;">
				<view class="device-msg flex justify-between align-center">
					<view class="device-name">
						{{list.goodsName}}
					</view>
				</view>
				<view class="rep-msg">
					<view class="rep-msg-item flex align-center">
						<view>
							设备名称：
						</view>
						<view v-if="list.deviceName">
							{{list.deviceName}}（{{list.deviceId}}）
						</view>
						<view v-else>
							{{list.deviceId}}
						</view>
					</view>
					<view class="rep-msg-item flex align-center">
						<view>
							补货时间：
						</view>
						<view class="flex align-center">
							<view>
								{{params.startTime}}~{{params.endTime}}
							</view>
						</view>
					</view>


				</view>
				<view class="rep-content" v-if="list&&list.dataList&&list.dataList.length>0">
					<block v-for="(item,index) in list.dataList" :key="index">
						<view class="goods-item">
							<view class="goods-detail flex align-center">
								<view class="goods-image">
									<u--image radius="4" width="81rpx" height="81rpx" v-if="item.changeType=='补货'"
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/bh.png"
										mode="aspectFit" :lazy-lord="true"></u--image>
									<u--image radius="4" width="81rpx" height="81rpx" v-if="item.changeType=='盘点'"
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/pd.png"
										mode="aspectFit" :lazy-lord="true"></u--image>
									<u--image radius="4" width="81rpx" height="81rpx" v-if="item.changeType=='销售'"
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/xs.png"
										mode="aspectFit" :lazy-lord="true"></u--image>
								</view>
								<view class="goods-msg">
									<view class="flex align-center">
										<view class="goods-msg-item" style="margin-right: 50rpx;">
											原库存：{{item.oldStock}}
										</view>
										<view class="goods-msg-item">
											新库存：{{item.newStock}}
										</view>
									</view>
									<view class="stock-time">
										订单id：{{item.orderId||'-'}}
									</view>
									<view class="flex justify-between align-center">
										<view class="stock-time">
											{{item.createTime}}
										</view>
										<view>
											<xbutton bgColor="#F4F8FF" color="#2C6FF3" @tap="showVideoView(item)">交易视频
											</xbutton>
										</view>
									</view>
									<view class="stock-num">
										<view>
											库存数量
										</view>
										<view>
											{{item.numStr}}
										</view>
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>

				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</view>
		</view>

		<u-picker :show="typeShow" :columns="actions" confirmColor="#2C6FF3" :closeOnClickOverlay="true"
			keyName="deviceName" @close="typeShow=false" @confirm="actionSelect" @cancel="typeShow = false">
		</u-picker>

		<u-picker :show="employeeShow" :columns="employeeActions" confirmColor="#2C6FF3" :closeOnClickOverlay="true"
			keyName="name" @close="employeeShow=false" @confirm="employeeSelect" @cancel="employeeShow = false">
		</u-picker>

		<u-datetime-picker :show="show" v-model="time" confirmColor="#2C6FF3" mode="date" :closeOnClickOverlay="true"
			@close="show=false" @cancel="show=false" @confirm="confirm"></u-datetime-picker>

		<xvideoPopup :show="videoShow" :showMuteBtn="false" :urls="videoUrls" :deviceId="params.deviceId" :poster="poster" @timeupdate="timeupdate"
			@close="videoClose" @open="videoOpen"></xvideoPopup>
	</view>
</template>

<script>
	import {
		listOfIdName
	} from "@/api/device/device.js"

	import {
		goodsRep,
		listByDeviceGoods
	} from "@/api/replenishment/replenishment.js"
	import {
		ownerGoodsList,
	} from "@/api/commodity/goods.js"

	import {
		byId
	} from "@/api/order/order.js"

	export default {
		data() {
			return {
				show: false,
				time: new Date(),
				timeType: 'startTime',
				list: [{
						id: 1,
						icon: 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/bh.png'
					},
					{
						id: 2,
						icon: 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/pd.png'
					},
					{
						id: 3,
						icon: 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/xs.png'
					}
				],
				actions: [],
				typeShow: false,
				params: {
					deviceId: null,
					startTime: null,
					endTime: null,
					goodsName: null,
					goodsId: null,
				},


				employeeShow: false,
				employeeActions: [],

				mercTree: [],

				goodsList: [],

				videoShow: false,

				poster: '',
				videoUrls: [],
			}
		},

		async onLoad(o) {
			await this.inputChange(decodeURIComponent(o.goodsName), 50)
			this.params = {
				deviceId: o.deviceId,
				startTime: o.startTime,
				endTime: o.endTime,
				goodsName: decodeURIComponent(o.goodsName),
				goodsId: o.goodsId,
			}
		},

		watch: {
			params: {
				handler(newVal, oldVal) {
					if (newVal) {
						console.log(this.params)
						this.list = []
						this.getData()
					}
				},
				deep: true,
				immediate: false
			}
		},

		methods: {
			selectTime(type) {
				this.timeType = type
				this.show = true
			},

			detail(id) {
				if (!id) return
				this.$tab.navigateTo(
					`/pages/order/orderDetails?id=${id}`)
			},

			inputChange(e, size) {
				return new Promise((resolve, reject) => {
					ownerGoodsList({
						page: {
							current: 1,
							size: size ? size : 20
						},
						keyword: e
					}).then(res => {
						let data = res.data.records;
						let goodsList = data.map(i => ({
							id: i.id,
							name: i.name
						}))
						this.goodsList = goodsList
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			selectGoods(index, item) {
				console.log(index)
				if (index == -1) {
					this.params.goodsId = null
					this.params.goodsName = null
					return
				}
				this.params.goodsId = item.id
				this.params.goodsName = item.name
				this.$forceUpdate()
			},

			clearInput() {
				this.params.goodsId = null;
				this.params.goodsName = null;
			},

			confirm(e) {
				this.params[this.timeType] = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.show = false
			},

			chooseDevice() {
				listOfIdName({}).then(res => {
					this.actions = [res.data]
					this.typeShow = true
				})
			},

			// 下拉选择
			actionSelect(e) {
				console.log(e)
				this.params.deviceId = e.value[0].deviceId
				this.typeShow = false
			},

			getData() {
				if (this.checkParams()) return
				let params = {
					"deviceId": this.params.deviceId,
					"goodsId": this.params.goodsId,
					"startTime": this.params.startTime + ' 00:00:00',
					"endTime": this.params.endTime + ' 23:59:59',
				}
				listByDeviceGoods(params).then(res => {
					this.list = res.data
				})
			},

			checkParams() {
				if (!this.params.startTime) {
					return true
				}
				if (!this.params.endTime) {
					return true
				}
				if (!this.params.deviceId) {
					return true
				}
				if (!this.params.goodsId) {
					return true
				}
				return false
			},

			timeupdate(e) {

			},

			videoClose() {
				this.videoShow = false
			},

			videoOpen() {
				this.videoShow = true
			},

			showVideoView(item) {
				if (!item.orderId) {
					this.$modal.msg('暂无视频')
					return
				}
				byId({
					id: item.orderId
				}).then(res => {
					if (res.code == 200 && res.data != null) {
						var urls = res.data.video.split(',');
						this.videoUrls = [
							this.$xy.cdnUrl(urls[0]),
							this.$xy.cdnUrl(urls[1])
						]
						console.log(this.videoUrls)
						this.videoOpen()
					} else {
						this.$modal.msg('暂无视频')
					}
				})
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		::v-deep .u-border {
			border-color: transparent !important;
			text-align: right;
		}

		.content {

			.section {
				padding: 6rpx 20rpx 0;
				border-radius: 14rpx;
				background-color: #fff;

				&.stock-content {
					width: 724rpx;
					margin-left: 13rpx;
				}

				.item {
					line-height: 28rpx;
					padding: 10rpx 17rpx 10rpx 23rpx;
					height: 88rpx;

					&:not(:last-child) {
						border-bottom: 1rpx solid #eaeaea;
					}

					.item-left {
						line-height: 50rpx;
					}

					.item-right {
						line-height: 50rpx;

						.item-val {
							color: #333;
							display: inline-block;
							max-width: 362rpx;
							text-align: right;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.item-val-show {
							color: #c6c6c6;
						}

						.report-type {
							text-decoration: underline;
							color: #2C6FF3;
							font-style: oblique;
						}

						.device {
							margin-right: 24rpx;
						}

						.start-time,
						.end-time {
							padding: 0 12rpx;
						}

						.start-time-show,
						.end-time-show {
							color: #c6c6c6;
						}
					}
				}

				.device-msg {
					font-size: 32rpx;
					padding: 26rpx 0 14rpx;

					.device-name {
						font-weight: 800;
						color: #333333;
					}
				}

				.rep-msg {
					padding: 12rpx 0;

					.rep-msg-item {
						line-height: 56rpx;

						>view:nth-child(1) {
							width: 160rpx;
						}
					}
				}

				.rep-content {
					.goods-item {
						background: #F6F7FA;
						border-radius: 14rpx;
						padding: 27rpx 25rpx 28rpx 27rpx;
						margin-top: 20rpx;

						.goods-detail {
							.goods-image {
								width: 81rpx;
								height: 81rpx;
							}

							.goods-msg {
								position: relative;
								margin-left: 27rpx;
								width: 600rpx;

								.goods-name {
									font-size: 26rpx;
									font-weight: 800;
									padding-bottom: 12rpx;
								}

								.goods-msg-item {
									font-size: 28rpx;
									color: #555555;
									line-height: 54rpx;
								}

								.stock-time {
									font-size: 26rpx;
									font-weight: 500;
									line-height: 40rpx;
									color: #999999;
								}

								.stock-num {
									font-size: 28rpx;
									color: #FF0000;
									text-align: right;
									line-height: 44rpx;
									position: absolute;
									right: 0;
									top: 0;

									>view:nth-child(2) {
										font-weight: bold;
									}
								}
							}
						}
					}
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}

		.popup-content {
			padding: 0 36rpx;
		}

		.empty {
			padding: 300rpx 0;
		}
	}
</style>