{"name": "喵星人智能柜商家端", "appid": "__UNI__70676CE", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "wxde6edfb4b2c1e7db", "setting": {"checkSiteMap": false, "urlCheck": false, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true, "lazyCodeLoading": "requiredComponents", "permission": {"scope.userLocation": {"desc": "上传机器坐标位置"}}}, "vueVersion": "2", "h5": {"template": "static/index.html", "devServer": {"port": 9090, "https": false}, "title": "喵星人商户端", "router": {"mode": "hash", "base": "./"}, "optimization": {"treeShaking": {"enable": true}}}, "sassImplementationName": "node-sass"}