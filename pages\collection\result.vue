<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff"
			:placeholder="true" title="已采集商品">
		</u-navbar>

		<view class="content">
			<!-- 商品列表 -->
			<view class="goods-list" v-if="collectedGoods.length > 0">
				<view class="goods-item" v-for="(item, index) in collectedGoods" :key="index">
					<view class="goods-image-container">
						<image class="goods-image" :src="item.image || '/static/images/default-goods.png'" mode="aspectFill"></image>
					</view>
					<view class="goods-info">
						<text class="goods-name">{{item.name}}</text>
						<text class="goods-spec">{{item.spec || '暂无规格'}}</text>
						<view class="goods-actions">
							<text class="goods-status" :class="{'success': item.status === 1, 'fail': item.status === 2}">
								{{item.status === 1 ? '采集成功' : item.status === 2 ? '采集失败' : '采集中'}}
							</text>
							<view class="action-btns">
								<u-button size="mini" type="primary" text="重新采集" @click="recollectGoods(item)" v-if="item.status === 2"></u-button>
								<u-button size="mini" type="error" text="删除" @click="deleteGoods(index)"></u-button>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<u-empty mode="data" text="暂无采集商品"></u-empty>
			</view>
		</view>

		<!-- 固定在底部的操作按钮 -->
		<view class="bottom-bar">
			<xbutton class="btn-primary" bgColor="#2979ff" color="#fff" @click="addNewGoods">继续采集</xbutton>
			<xbutton class="btn-secondary" bgColor="#f5f5f5" color="#333" @click="submitSelected">完成采集</xbutton>
		</view>
		
		<!-- 手机号码输入弹框 -->
		<u-popup :show="showPhonePopup" mode="center" @close="closePhonePopup" round="10">
			<view class="phone-popup">
				<view class="popup-title">请输入手机号码</view>
				<view class="popup-content">
					<view class="input-item">
						<u-input
							v-model="phoneNumber"
							type="number"
							placeholder="请输入手机号码"
							maxlength="11"
						></u-input>
					</view>
					<view class="popup-tips">完成采集后，将通过短信通知您</view>
				</view>
				<view class="popup-buttons">
					<xbutton width="260rpx" bgColor="" color="#333" @click="closePhonePopup">取消</xbutton>
					<xbutton width="260rpx" @click="confirmPhone">确定</xbutton>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		endAll,
		goOnGather
	} from "@/api/collection.js";
	export default {
		data() {
			return {
				showGoodsPopup: false,
				searchKeyword: "",
				goodsList: [],
				selectedGoods: null,
				goodsName: '',
				goodsId: null,
				collectedGoods: [], // 已采集的商品列表
				loading: false,
				deviceId: '',
				showPhonePopup: false,
				phoneNumber: ''
			};
		},

		onLoad(o) {
			this.deviceId = o.deviceId;
			this.loadCollectedGoods();
		},
		
		onShow() {
			// 页面显示时刷新数据
			this.loadCollectedGoods();
		},

		methods: {
			/**
			 * 加载已采集的商品列表
			 */
			loadCollectedGoods() {
				this.loading = true;
				
				// 使用模拟数据进行测试
				this.collectedGoods = [
					{
						id: 1,
						name: '可口可乐 330ml',
						image: '/static/images/default-goods.png',
						spec: '330ml/瓶',
						status: 1 // 1成功，2失败
					},
					{
						id: 2,
						name: '百事可乐 500ml',
						image: '/static/images/default-goods.png',
						spec: '500ml/瓶',
						status: 1
					},
					{
						id: 3,
						name: '农夫山泉 550ml',
						image: '/static/images/default-goods.png',
						spec: '550ml/瓶',
						status: 2
					},
					{
						id: 4,
						name: '康师傅方便面 红烧牛肉味',
						image: '/static/images/default-goods.png',
						spec: '105g/包',
						status: 1
					},
					{
						id: 5,
						name: '统一老坛酸菜牛肉面',
						image: '/static/images/default-goods.png',
						spec: '120g/包',
						status: 0
					}
				];
				this.collectedGoods=[...this.collectedGoods,...this.collectedGoods,...this.collectedGoods,...this.collectedGoods]
				this.loading = false;
				
				// 注释掉API调用代码，以便使用测试数据
				/*
				gatherProducts({
					"identifier": this.deviceId,
					"productInfo": "string",//商品信息中文
					"id": 0 //采集商品信息id
				}).then(res => {
					if (res.code === 200) {
						this.collectedGoods = res.data || [];
					} else {
						uni.showToast({
							title: res.message || '获取商品列表失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					console.error('获取商品列表失败', err);
					uni.showToast({
						title: '获取商品列表失败',
						icon: 'none'
					});
				}).finally(() => {
					this.loading = false;
				});
				*/
			},
			
			/**
			 * 删除商品
			 * @param {number} index - 商品索引
			 */
			deleteGoods(index) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该商品吗？',
					success: res => {
						if (res.confirm) {
							// 这里应该调用删除API
							// 暂时使用本地删除
							this.collectedGoods.splice(index, 1);
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			/**
			 * 重新采集商品
			 * @param {Object} item - 商品信息
			 */
			recollectGoods(item) {
				uni.navigateTo({
					url: `/pages/collection/collecting?goodsId=${item.id}&deviceId=${this.deviceId}`
				});
			},
			
			/**
			 * 添加新商品
			 */
			async addNewGoods() {
				//查询是否可继续采集
				let goOnGatherRes=await this.goOnGatherFun();
				if(goOnGatherRes.code!=200) return this.$modal.msg('继续采集失败');

				// 直接跳转到商品选择页面
				uni.navigateTo({
						url: `/pages/collection/chooseGoods?deviceId=${this.deviceId}`
					});
			},

			goOnGatherFun(){
				return new Promise((resolve, reject) => {
					goOnGather({
						"identifier": this.deviceId
					}).then(res => {
						if (res.code === 200) {
							resolve(res);
						} else {
							reject(res);
						}
					})
				})
			},
			
			/**
			 * 提交已选商品
			 */
			submitSelected() {
				if (this.collectedGoods.length === 0) {
					uni.showToast({
						title: '请至少采集一个商品',
						icon: 'none'
					});
					return;
				}
				
				// 显示手机号码输入弹框
				this.showPhonePopup = true;
			},
			
			/**
			 * 关闭手机号码输入弹框
			 */
			closePhonePopup() {
				this.showPhonePopup = false;
			},
			
			/**
			 * 确认手机号码并提交
			 */
			async confirmPhone() {
				// 验证手机号码
				if (!this.phoneNumber || this.phoneNumber.length !== 11) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none'
					});
					return;
				}
				
				// 关闭弹框
				this.closePhonePopup();

				// 结束采集
				let endAllRes=await this.endAllFun();
				if(endAllRes.code!=200) return this.$modal.msg('结束采集失败');

				this.$tab.navigateTo('/pages/collection/colletionList');
			},

			endAllFun(){
				return new Promise((resolve, reject) => {
					endAll({
						identifier: this.deviceId,
						phoneNum: this.phoneNumber
					}).then(res => {
						if (res.code === 200) {
							resolve(res);
						} else {
							reject(res);
						}
					})
				})
			},
		},
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #fff;
		position: relative;
		padding: 0 20rpx;

		.content {
			flex: 1;
			padding: 20rpx 0;
			padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
			
			// 商品列表
			.goods-list {
				.goods-item {
					display: flex;
					padding: 20rpx;
					margin-bottom: 20rpx;
					background-color: #fff;
					border-radius: 12rpx;
					box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
					
					.goods-image-container {
						width: 160rpx;
						height: 160rpx;
						margin-right: 20rpx;
						flex-shrink: 0;
						
						.goods-image {
							width: 100%;
							height: 100%;
							border-radius: 8rpx;
							background-color: #f5f5f5;
						}
					}
					
					.goods-info {
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						
						.goods-name {
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
							margin-bottom: 10rpx;
						}
						
						.goods-spec {
							font-size: 24rpx;
							color: #666;
							margin-bottom: 20rpx;
						}
						
						.goods-actions {
							display: flex;
							justify-content: space-between;
							align-items: center;
							
							.goods-status {
								font-size: 24rpx;
								color: #666;
								
								&.success {
									color: #19be6b;
								}
								
								&.fail {
									color: #fa3534;
								}
							}
							
							.action-btns {
								display: flex;
								gap: 10rpx;
							}
						}
					}
				}
			}
			
			// 空状态
			.empty-state {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 100rpx 0;
			}
		}

		.form-item {
			display: flex;
			align-items: center;
			min-height: 88rpx;
			margin-top: 50rpx;

			&:last-child {
				border-bottom: none;
			}

			.label {
				width: 130rpx;
				font-size: 28rpx;
				color: #333;

				&.required::before {
					content: "*";
					color: #ff3b30;
					margin-right: 4rpx;
				}
			}

			.input-wrapper {
				flex: 1;
				display: flex;
				align-items: center;
				border: 1rpx solid #f5f5f5;
				padding: 0 20rpx;
				border-radius: 12rpx;

				.input {
					flex: 1;
				}
			}

			.input {
				height: 88rpx;
				font-size: 28rpx;
				color: #333;

				&.with-arrow {
					padding-right: 40rpx;
				}

				&::placeholder {
					color: #999;
				}
			}
		}

		// 商品选择弹窗样式
		.goods-popup {
			width: 600rpx;
			height: 800rpx;
			background-color: #fff;
			display: flex;
			flex-direction: column;

			.search-box {
				padding: 20rpx;
				background-color: #fff;
				border-bottom: 1rpx solid #f5f5f5;
				flex-shrink: 0;
			}

			.goods-list-container {
				flex: 1;
				overflow: hidden;

				.goods-list {
					height: 100%;

					.goods-item {
						display: flex;
						padding: 20rpx;
						border-bottom: 1rpx solid #f5f5f5;
						background-color: #fff;

						&:active {
							background-color: #f5f5f5;
						}

						.goods-image {
							width: 120rpx;
							height: 120rpx;
							border-radius: 8rpx;
							margin-right: 20rpx;
							flex-shrink: 0;
						}

						.goods-info {
							flex: 1;
							display: flex;
							flex-direction: column;
							justify-content: space-around;

							.goods-name {
								font-size: 28rpx;
								color: #333;
								font-weight: 500;
							}

							.goods-spec,
							.goods-brand {
								font-size: 24rpx;
								color: #666;
							}
						}
					}
				}
			}
		}

		.bottom-bar {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			background-color: #ffffff;
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.btn-primary,
			.btn-secondary {
				height: 80rpx;
				border-radius: 6px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
			}
		}
	}

	// 修改弹窗内容区域的样式
	:deep(.pop-content) {
		height: 800rpx;
		display: flex;
		flex-direction: column;

		.head {
			flex-shrink: 0;
		}
	}

	// 手机号码输入弹框样式
	.phone-popup {
		width:600rpx;
		padding: 30rpx;
		
		.popup-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
			text-align: center;
			margin-bottom: 30rpx;
		}
		
		.popup-content {
			margin-bottom: 30rpx;
			
			.input-item {
				margin-bottom: 20rpx;
			}
			
			.popup-tips {
				font-size: 24rpx;
				color: #999;
				padding: 0 10rpx;
			}
		}
		
		.popup-buttons {
			display: flex;
			justify-content: space-between;
			
			button {
				flex: 1;
				height: 80rpx;
				font-size: 28rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 20rpx;
			}
		}
	}
</style>