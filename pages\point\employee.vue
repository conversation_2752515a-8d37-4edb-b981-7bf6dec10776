<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="人员管理"></u-navbar>
		<view class="content">

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				:style="{height:fullHeight}">
				<view class="list" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="index">
						<view class="comm-main flex justify-between">
							<view @click.stop="detail(item)">
								<view class="flex align-center">
									<view>
										{{item.name}}
									</view>
									<view>
										{{item.roleNames[0]||'无'}}
									</view>
								</view>
								<view class="phone-user flex align-center">
									<image class="image"
										src="https://ossfile.mxrvending.com/assets/xy_merc_mini/images/system/tel.png"
										mode="widthFix"></image>
									<view class="tel">
										{{item.tel}}
									</view>
								</view>
							</view>

						<!-- 	<view class="stop" v-if="checkPermi(['employee:stop'])">
								<u-switch activeColor="#2C6FF3" size="20" v-model="item.status"
									@change="stop(item)"></u-switch>
							</view> -->
						</view>
					</block>
				</view>
				<view class="empty" v-if="list.length==0">
					<u-empty mode="list" text="没有用户!"></u-empty>
				</view>
			</scroll-view>


			<!-- <view class="btn safe-bottom" v-if="checkPermi(['employee:add'])">
				<xbutton size="large" round="82rpx" @click="add">添加人员</xbutton>
			</view> -->
		</view>
	</view>
</template>

<script>
	import {
		list,
		update
	} from "@/api/system/employee.js"
	export default {
		data() {
			return {
				list: [], //列表
				fullHeight: 0,
				type: null
			}
		},

		async onLoad(o) {
			this.type = o.type
			
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')
		},

		onShow() {
			this.getList()
		},

		methods: {
			//获取分页
			getList() {
				list({}).then(res => {
					this.list = res.data
				})
			},

			detail(item) {
				if (!this.checkPermis(['employee:edit'])) return
				let obj = {
					type: this.type,
					id: item.userId,
					tel: item.tel,
					name: item.name
				}
				uni.$emit('chooseEmp', obj)
				this.$tab.navigateBack()
			},

			stop(item) {
				let msg = item.status ? '启用' : '禁用'
				uni.showModal({
					title: '提示',
					content: `是否确认${msg}`,
					success: res => {
						if (res.confirm) {
							update({
								userId: item.userInfoId,
								status: item.status
							}).then(res => {
								this.getList()
								setTimeout(() => {
									this.$modal.showToast(`${msg}成功~`)
								}, 500)
							}).catch(err => {

							})
						} else if (res.cancel) {
							item.status = !item.status
						}
					}
				});
			},

			//添加人员
			add() {
				this.$tab.navigateTo('/pages/employee/addEmployee')
			},

			//拨打电话
			phone(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.content {

			.list {
				width: 100%;
				padding: 0rpx 13rpx 12rpx;
				padding-bottom: calc(110rpx + env(safe-area-inset-bottom) / 2);
				overflow: hidden;

				.comm-img {
					width: 130rpx;
					height: 130rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-around;

					image {
						width: 130rpx;
						height: 130rpx;
					}
				}

				.comm-main {
					padding: 20rpx 30rpx;
					background-color: #fff;
					border-radius: 12rpx;
					margin-top: 12rpx;
					box-sizing: border-box;
					color: #999;
					line-height: 60rpx;
					position: relative;

					.image {
						width: 23rpx;
						height: 23rpx;
						margin-right: 12rpx;
					}

					>view:nth-child(1) {
						width: 580rpx;

						>view {
							padding: 4rpx 0;
						}

						>view:nth-child(1) {

							>view:nth-child(1) {
								font-size: 30rpx;
								font-weight: bold;
								color: #333;
							}

							>view:nth-child(2) {
								font-size: 28rpx;
								margin-left: 24rpx;
							}
						}

						>view:nth-child(2) {
							font-size: 28rpx;
						}
					}

					.phone-user {
						width: 250rpx;
					}

					.stop {
						// position: absolute;
						// right: 30rpx;
						// top: 26rpx;
					}
				}
			}
		}
	}

	.empty {
		margin-top: 40%;
	}

	.btn {
		width: 100%;
		position: fixed;
		left: 0;
		padding: 0 24rpx;
		bottom: $iphone-safe-bottom;
	}

	.popup-content {
		padding: 36rpx 24rpx;
		display: flex;
		flex-flow: row nowrap;
		justify-content: flex-start;
		align-items: center;

		input {
			border: 1rpx solid #999;
			border-radius: 6rpx;
			width: 530rpx;
			padding: 0 24rpx;
		}

		&.edit-point {
			flex-direction: column;
			align-items: flex-start;

			.edit-point-item {
				width: 100%;
				padding-left: 170rpx;
				position: relative;

				&+.edit-point-item {
					margin-top: 12rpx;
				}

				>view:nth-child(1) {
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
				}
			}
		}
	}

	.del-popup-content {
		padding: 36rpx 24rpx;
		text-align: center;
		font-size: 32rpx;
	}
</style>