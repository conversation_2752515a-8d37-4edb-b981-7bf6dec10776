export default {
	//金额分转换为元
	delMoney(money) {
		let newMoney = '';
		if (money) {
			if (typeof(money) == Number) {
				newMoney = (money / 100).toFixed(2)
			} else {
				newMoney = ((Number(money) / 100)).toFixed(2)
			}
		} else {
			newMoney = 0
		}
		return newMoney
	},

	//金额元转分,解决浮点数精度问题
	delMoneyL(money) {
		return Math.round(Number(money) * 100)
	},
	weight2Mg(weight) {
		return Math.round(Number(weight) * 1000)
	},

	//支付方式反显
	getPayType(type) {
		let value = '/'
		switch (type) {
			case 3:
				value = '微信支付'
				break;
			case 2:
				value = '支付宝支付'
				break;
			case 10:
				value = '取货'
				break;
			case 11:
				value = '会员卡'
				break;
			default:
				break;
		}
		return value
	},

	testPrice(val) {
		let reg = /^\d+(\.([0-9]|\d[0-9]))?$/
		return reg.test(val)
	},

	/**
	 * 扫描机器二维码
	 */
	scanDevice() {
		return new Promise((resolve, reject) => {
			uni.scanCode({
				success: res => {
					let data = decodeURIComponent(res.result)
					console.log('data', data)
					let splitArr = data.split("?");
					//链接后面可能存在多个？得情况
					let paramsArr = []
					for (var i = 1; i < splitArr.length; i++) {
						let item = splitArr[i]
						let itemArr = item.split('&')
						paramsArr = paramsArr.concat(itemArr)
					}
					let obj = {}
					for (var i = 0; i < paramsArr.length; i++) {
						let item = paramsArr[i]
						let itemArr = item.split('=')
						obj[itemArr[0]] = itemArr[1]
					}
					console.log('解析二维码========' + JSON.stringify(obj))
					if (!obj.deviceId && !obj.terminalId) {
						uni.showToast({
							icon: 'none',
							title: '该二维码无效~'
						})
						reject('该二维码无效~')
					} else {
						resolve(obj)
					}
				},
				fail: err => {
					reject(err)
				}
			});
		})
	},

	/**
	 * 转发视频
	 */
	shareVideo(url) {
		uni.showLoading({
			title: '转发中~'
		});
		setTimeout(() => {
			uni.hideLoading();
		}, 2000)
		uni.downloadFile({
			url: url,
			success: (res) => {
				// 下载完成后转发
				uni.shareVideoMessage({
					videoPath: res.tempFilePath,
					success() {
						console.log('转发成功！')
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: '转发成功~'
						})
					},
					fail: err => {
						console.log('转发失败！')
						uni.hideLoading();
						uni.showToast({
							icon: 'none',
							title: '转发失败'
						})
					},
				})
			},
			fail: err => {
				console.log('转发失败！')
				uni.hideLoading();
				uni.showToast({
					icon: 'none',
					title: '转发失败~'
				})
			},
		})
	},

	/**
	 * @param {Object} url
	 * 导出
	 */
	exportDoc(url) {
		uni.downloadFile({
			url: url,
			success: (res) => {
				if (res.statusCode === 200) {
					uni.showToast({
						icon: 'none',
						title: '导出成功~'
					})
					var filePath = res.tempFilePath;
					console.log(filePath);
					uni.openDocument({
						filePath: filePath,
						showMenu: true,
						fileType: 'xlsx',
						success: function(res) {
							console.log(' 打开文档成功 ')
						},
						fail: function(res) {
							console.log(res);
						},
						complete: function(res) {
							console.log(res);
						}
					})
				}
			}
		})
	},

	/**
	 * 预览图片
	 */
	previewImg(data) {
		let currentUrl = null
		if (typeof data == 'object') {
			currentUrl = data
		} else {
			currentUrl = [data]
		}
		uni.previewImage({
			urls: currentUrl // 需要预览的图片http链接列表
		})
		getApp().globalData.isOnShow = false
	},

	/**
	 * 视频地址插入cdn
	 */
	cdnUrl(url) {
		let newUrl = '';
		if (typeof url === 'string') {
			if (url.indexOf('https://ossfile.mxrvending.com') != -1) {
				newUrl = url.slice(0, 8) + 'cdn.' + url.slice(8)
			} else {
				newUrl = url
			}
		}
		return newUrl
	},

	/**
	 * scroll滚动高度适配
	 * @param {Object} _this this
	 * @param {Object} cla 滚动标签class
	 * @param {Object} bot 滚动区域距离底部的距离
	 */
	scrollHeight(_this, cla, bot = 0) {
		return new Promise((resolve, reject) => {
			const query = uni.createSelectorQuery().in(_this);
			let fullHeight = 0;
			query.select(cla).boundingClientRect((data) => {
				uni.getSystemInfo({
					success(res) {
						console.log(res)
						// 针对iPhone X等机型底部安全距离做适配
						const model = res.model;
						const modelInclude = [
							"iPhone X",
							'iPhone XR',
							"iPhone XS",
							"iPhone XS MAX",
							"iPhone 12/13 mini",
							"iPhone 12/13 (Pro)",
							"iPhone 12/13 Pro Max",
							"iPhone 14 Pro Max"
						];
						let safeDistance = modelInclude.includes(model)
						//动态设置滚动区域高度
						console.log(res.windowHeight, data.top, bot)
						if (safeDistance) {
							console.log(res.windowHeight - data.top - 20 - bot + 'px')
							fullHeight = res.windowHeight - data.top - 20 - bot + 'px';
						} else {
							fullHeight = res.windowHeight - data.top - bot + 'px';
						}
						resolve(fullHeight)
					},
					fail(err) {
						reject(err)
					}
				});
			}).exec();
		})
	},

	/**
	 * 获取appid
	 */
	appId() {
		return uni.getAccountInfoSync().miniProgram.appId
	},

	delPhone(phone) {
		return phone ? phone.substring(0, 3) + '****' + phone.substring(7) : ''
	},

	copy(str) {
		uni.setClipboardData({
			data: str,
			success: (data) => {
				uni.showToast({
					title: '复制成功'
				})
			}
		})
	},


	/**
	 * @param {Object} val
	 * 正则校验0-1之间保留两位小数
	 */
	regScalar(val) {
		return /^(0(\.\d{1,2})?|1(\.0{1,2})?)$/.test(val)
	},
	
	
	/**
	 * @param {Object} val
	 * 正则校验保留两位小数
	 */
	regdoitTwo(val) {
		return /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(val)
	},
	
	
	/**
	 * 支付链接获取订单号
	 * @param {Object} url
	 */
	urlToOrderId(url) {
		let order = ''
		if (url.indexOf('=') != -1) {
			if (url.indexOf('order=') != -1) {
				order = url.split('order=')[1]
			} else if (url.indexOf('ordersId=') != -1) {
				order = url.split('ordersId=')[1]
			} else {
				order = url.split('state=')[1]
			}
		} else {
			order = url.substr(url.lastIndexOf(':') + 1)
		}
		return order
	}
}