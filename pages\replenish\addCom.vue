<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" bgColor="#2C6FF3" :placeholder="true"
			title="添加商品">
			<view class="u-nav-slot flex align-center" slot="left">
				<u-icon name="arrow-left" color="#fff" size="19" @click="$tab.navigateBack()"></u-icon>
				<!-- 	<view class="com-type" @click="changeType">{{algoTypeName}}</view> -->
			</view>
		</u-navbar>
		<!-- <view class="search" @click.native="searchClick">
			<u-search animation placeholder="商品搜索" disabled v-model="keyword" :showAction="false" search="search">
			</u-search>
		</view> -->

		<view class="search" @click="searchComm">
			<view class="search-input">
				<u-search placeholder="search" actionText="cancle" :actionStyle="{color:'#2C6FF3'}"
					:showAction="!leftShow" :clearabled="false" v-model="keyword" @search="search"
					@custom="cancle"></u-search>
				<view @click="scan" :class="[leftShow?'scan-icon scan-left-show':'scan-icon scan-left-hidden']">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>
			</view>

			<view class="search-history flex flex-wrap flex-start" v-if="!leftShow">
				<view class="history-item" v-for="(item,index) in historyList" :key="index" @click="searchFast(item)">
					{{item}}
				</view>
			</view>
		</view>
		<view class="content">
			<view class="swiperitem-content" v-if="current==0">
				<view class="classify-wrap">
					<classify storeName="perStor" :showId="true" :tabList="perTabList" :status="perStatus"
						:commList="perCommList" @switchMenu="perSwitchMenu" @lowerBottom="perLowerBottom"
						:isModal="true" :height="fullHeight" @comClick="comClick" :leftShow="leftShow" />
				</view>
			</view>
			<view class="swiperitem-content" v-if="current==1">
				<view class="classify-wrap">
					<classify storeName="pubStor" :showId="true" :tabList="pubTabList" :status="pubStatus"
						:commList="pubCommList" @switchMenu="pubSwitchMenu" @lowerBottom="pubLowerBottom"
						:height="fullHeight" :leftShow="leftShow" />
				</view>
			</view>
		</view>

		<u-action-sheet :show="actionSheetShow" :actions="actions" title="change commodity type"
			@close="actionSheetShow = false" @select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		detail
	} from "@/api/device/device.js"
	import {
		goodsCategory as perGoodsCategory,
	} from "@/api/commodity/mercGoods.js"
	import {
		goodsCategory as pubGoodsCategory,
		pageByGoods,
		pageByGoodsMerc,
		supplyH5AddGoods,
		mercAiList
	} from "@/api/commodity/goods.js"
	export default {
		data() {
			return {
				current: 0,
				fullHeight: "0",
				// 私库
				perTabList: [], //商品类目
				perCommList: [], //商品列表
				perPage: 1, //商品分页
				perSize: 10,
				perStatus: 'loadmore', //加载更多

				// 公库
				pubTabList: [], //商品类目
				pubCommList: [], //商品列表
				pubPage: 1, //商品分页
				pubSize: 10,
				pubStatus: 'loadmore', //加载更多

				id: null, //设备id
				categoryCode: null,

				leftShow: true,
				historyList: [],
				keyword: '',
				algoType: '1',
				algoTypeName: '切换商品库',
				actionSheetShow: false,
				actions: [],
				algorithmId: null,
				aisleNo: null,
				aisleId: null
			}
		},

		async onLoad(o) {
			let _this=this
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.classify-wrap',0)
			
			if (o.id) {
				this.id = o.id;
			}
			if (o.aisleNo) {
				this.aisleNo = o.aisleNo
				this.aisleId = o.aisleId
			}

			if (uni.getStorageSync('goods')) {
				this.historyList = JSON.parse(uni.getStorageSync('goods'))
			}

			// 算法
			await this.getAlgo()

			// 类目tab
			this.getPubCategory()
			await this.getPerCategory()

			// 商品列表
			this.search()
		},

		methods: {
			// 获取当前设备算法类型
			getAlgo() {
				return new Promise((resolve, reject) => {
					detail({
						deviceId: this.id,
						isSysinfo: false,
						isStatus: false,
						isRegister: false
					}).then(res => {
						this.algorithmId = res.data.algorithmId
						resolve(res.data.algorithmId)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//商家算法列表
			getAlgoList() {
				mercAiList({}).then(res => {
					let data = res.data
					let newData = data.map(i => {
						return {
							type: i.id,
							name: i.alias
						}
					})
					this.actions = newData
				})
			},

			changeType() {
				this.actionSheetShow = true
			},

			actionsheetSelect(e) {
				this.algoType = e.type
				this.algoTypeName = e.name
				this.search()
			},

			searchComm() {
				this.leftShow = false
			},

			search(val) {
				this.saveKeyWord('goods', this.keyword)
				if (this.current == 0) { //私库
					this.categoryCode = this.perTabList[0].categoryCode
					this.perReset();
					this.getPerCommList()
				} else { //公库
					this.categoryCode = this.pubTabList[0].categoryCode
					this.pubReset();
					this.getPubCommList()
				}
			},

			cancle(val) {
				this.keyword = ''
				this.leftShow = true
				this.search()
			},

			saveKeyWord(type, val) {
				if (val) {
					let arr = []
					if (uni.getStorageSync(type)) {
						let arr = JSON.parse(uni.getStorageSync(type))
						if (arr.indexOf(val) != -1) {
							console.log('arr.indexOf(val)', arr.indexOf(val))
							arr.splice(arr.indexOf(val), 1)
						}
						arr.unshift(val)
						if (arr.length > 6) {
							arr.pop()
						}
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					} else {
						arr.unshift(val)
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					}
				} else {
					return
				}
			},

			searchFast(e) {
				this.keyword = e
				this.search()
			},

			//扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.keyword = res.result;
						this.search()
					}
				});
			},

			/************************ 私库方法 ********************************/
			// 搜索商品
			searchClick() {
				let storeName = this.current == 0 ? 'perStor' : 'pubStor'
				this.$tab.navigateTo('/pages/commodity/allGoodsSearch?type=' + this.current + '&storeName=' + storeName +
					'&id=' +
					this.id)
			},

			//获取类目列表
			getPerCategory() {
				return new Promise((resolve, reject) => {
					perGoodsCategory({
						algorithmId: this.algorithmId
					}, false).then(res => {
						if (res.data && res.data.length > 0) {
							this.perTabList = res.data.map(i => {
								if (i.categoryCode == null) {
									i.categoryName = 'undefined'
									return i
								} else {
									return i
								}
							});
						}
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//商品类目切换
			perSwitchMenu(item) {
				this.categoryCode = item.categoryCode
				this.perReset()
				this.getPerCommList()
			},

			//根据类目获取商品列表
			getPerCommList() {
				let params = {}
				if (this.leftShow) { //搜索
					params = {
						categoryCode: this.categoryCode,
						page: {
							current: this.perPage,
							size: this.perSize
						},
						deviceId: this.id,
						algorithmId: this.algorithmId
					}
				} else { //非搜索
					params = {
						page: {
							current: this.perPage,
							size: this.perSize
						},
						deviceId: this.id,
						keyword: this.keyword,
						algorithmId: this.algorithmId
					}
				}
				return new Promise((resolve, reject) => {
					pageByGoodsMerc(params).then(res => {
						let data = res.data.records;
						if (data && data.length > 0) {
							data = data.map(i => {
								i.name = i.goodsName;
								i.barcode = i.goodsBarcode;
								i.cover = i.goodsCover;
								i.price = i.price != null ? Number(i.price) / 100 : null;
								i.categoryName = i.categoryCodeName;
								i.showId = i.goodsId;
								return i
							})
						}
						if (data.length < 10) {
							this.perStatus = "nomore"
						} else {
							this.perStatus = "loadmore"
						}
						this.perCommList = this.perCommList.concat(data)

						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//触底加载更多
			perLowerBottom() {
				if (this.perStatus == 'nomore') return
				this.perPage++
				this.getPerCommList()
			},

			//重置
			perReset() {
				this.perStatus == 'loadmore'
				this.perPage = 1;
				this.perSize = 10;
				this.perCommList = [];
			},

			//新建模块商品先需要设置价格
			comClick(e) {
				console.log(e)
				this.$modal.oldConfirm(`是否确定添加当前商品到【${this.aisleNo}】货道?`).then(res => {
					this.addCom(e.goodsId)
				}).catch(err => {

				})
			},

			/**
			 * 添加商品
			 * 私库添加到设备,不需要设置参数
			 */
			addCom(goodsId) {
				supplyH5AddGoods({
					aisleId: this.aisleId,
					deviceId: this.id,
					goodsId: goodsId
				}).then(res => {
					if (res.code == 200) {
						this.$modal.msg('成功~')
						setTimeout(() => {
							uni.$emit('refresh')
							uni.navigateBack({
								delta: 1
							})
						}, 1000)
					}
				})
			},


			/********************** 公库方法 *************************/
			//获取类目列表
			getPubCategory() {
				return new Promise((resolve, reject) => {
					pubGoodsCategory({
						deviceId: this.id,
						algorithmId: this.algorithmId,
					}, false).then(res => {
						if (res.data && res.data.length > 0) {
							this.pubTabList = res.data.map(i => {
								if (i.categoryCode == null) {
									i.categoryName = '未定义'
									return i
								} else {
									return i
								}
							});
						}
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//商品类目切换
			pubSwitchMenu(item) {
				this.categoryCode = item.categoryCode
				this.pubReset()
				this.getPubCommList()
			},

			//根据类目获取商品列表
			getPubCommList() {
				let params = {}
				if (this.leftShow) { //搜索
					params = {
						categoryCodeLevel1: this.categoryCode,
						page: {
							current: this.pubPage,
							size: this.pubSize
						},
						deviceId: this.id,
						algorithmId: this.algorithmId
					}
				} else { //非搜索
					params = {
						page: {
							current: this.pubPage,
							size: this.pubSize
						},
						deviceId: this.id,
						keyword: this.keyword,
						algorithmId: this.algorithmId
					}
				}
				return new Promise((resolve, reject) => {
					pageByGoods(params).then(res => {
						let data = res.data.records;
						let newData = data.map(i => {
							i.categoryCode = i.categoryCodeLevel1;
							i.showId = i.id;
							return i
						})
						if (data.length < 10) {
							this.pubStatus = "nomore"
						} else {
							this.pubStatus = "loadmore"
						}
						this.pubCommList = this.pubCommList.concat(newData)
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//触底加载更多
			pubLowerBottom() {
				if (this.pubStatus == 'nomore') return
				this.pubPage++
				this.getPubCommList()
			},

			//重置
			pubReset() {
				this.pubStatus == 'loadmore'
				this.pubPage = 1;
				this.pubSize = 10;
				this.pubCommList = [];
			},

			tabClick(e) {
				this.current = e.index
				this.search()
			},

			change(e) {
				this.current = e.detail.current
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: calc(100vh);
		/* #ifdef H5 */
		height: calc(100vh - var(--window-top));
		/* #endif */
		display: flex;
		flex-direction: column;

		.u-nav-slot {
			color: #fff;

			.com-type {
				margin-left: 24rpx;
			}
		}

		.search {
			padding: 24rpx 24rpx;
			background-color: #fff;

			.search-input {
				position: relative;

				.scan-icon {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					z-index: 2;

					&.scan-left-show {
						right: 36rpx;
					}

					&.scan-left-hidden {
						right: 100rpx;
					}
				}
			}

			.search-history {

				.history-item {
					margin-right: 24rpx;
					padding: 0 12rpx;
					background-color: #f2f2f2;
					color: #333;
					font-size: 24rpx;
					line-height: 40rpx;
					border-radius: 40rpx;
					margin-top: 24rpx;
				}
			}
		}

		.tab-list {
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-around;

			.tab-left {
				width: 60%;
			}

			.use-list {
				font-size: 30rpx;
				color: #606266;
				line-height: 88rpx;
				padding-right: 24rpx;
			}
		}

		.content {}
	}

	.swiperitem-content {
		// position: relative;
	}

	.btn {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 24rpx;
		padding: 0 24rpx;
	}
</style>