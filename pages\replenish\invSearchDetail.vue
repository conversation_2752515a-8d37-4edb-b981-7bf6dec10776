<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="title"></u-navbar>
		<view class="content">
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view v-if="list.length>0">
					<block v-for="(item,index) in list" :key="index">
						<view class="equipment-container">
							<view class="flex align-center justify-between">
								<view class="title" v-if="type==0">
									{{item.deviceName||item.deviceId}}
								</view>
								<view class="title" v-else>{{item.goodsName}}</view>
							</view>
							
							<view class="flex align-center">
								<view class="" v-if="type==1" style="margin-right: 30rpx;margin-top: 20rpx;">
									<u--image width="110rpx" height="110rpx" :src="item.cover" mode="widthFix" :lazy-lord="true">
									</u--image>
								</view>
								<view class="">
									<view class="order-detail-item">
										<view>缺货：</view>{{item.lackSum}}
									</view>
														
									<view class="order-detail-item">
										<view>库存：</view>{{item.stockSum}}
									</view>
														
									<view class="order-detail-item">
										<view>容量：</view>{{item.capacitySum}}
									</view>
														
									<view class="order-detail-item percent">
										<view>缺货占比：</view>{{item.percent}}%
									</view>
								</view>
							</view>
						</view>
					</block>
				</view>
				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		stockByGoodsDetail,
		stockByDeviceDetail
	} from '@/api/replenishment/replenishment.js'

	export default {
		data() {
			return {
				fullHeight: 0,
				id: null,
				type: null,
				list: {},
				title:null
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')

			this.id = o.id
			this.type = o.type
			this.title=o.title
			if (this.type == 0) {
				this.getDetailByGoods()
			} else {
				this.getDetailByDev()
			}
		},
		methods: {
			getDetailByGoods() {
				stockByGoodsDetail({
					goodsId: this.id
				}).then(res => {
					this.list = res.data
				})
			},
			getDetailByDev() {
				stockByDeviceDetail({
					deviceId: this.id
				}).then(res => {
					this.list = res.data
				})
			}
		}
	}
</script>
<style scoped lang="scss">
	.container {
		.content {
			.equipment-container {
				margin: 13rpx 13rpx 0;
				padding: 12rpx 20rpx 24rpx;
				border-radius: 14rpx;
				background-color: #fff;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
				position: relative;
			
				.sb-box {
					padding: 24rpx 18rpx;
					background-color: #f5f8fb;
					border-radius: 8rpx;
					margin-top: 12rpx;
				}
			
				.title {
					height: 60rpx;
					line-height: 60rpx;
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
			
				.order-detail-item {
					font-size: 28rpx;
					margin-top: 12rpx;
					color: #777;
			
					>view {
						display: inline-block;
						width: 170rpx;
					}
				}
			
				.percent {
					position: absolute;
					right: 40rpx;
					bottom: 20rpx;
					line-height: 140rpx;
					color: red;
			
					>view {
						color: #777;
						width: 150rpx;
					}
				}
			}
		}
	}
</style>