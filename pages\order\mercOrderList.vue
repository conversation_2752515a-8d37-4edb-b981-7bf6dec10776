<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="viewTitle"></u-navbar>
		<!-- 		<view class="device-name">
			{{name}}/{{id}}
		</view> -->
		<view class="time-choose flex align-center justify-between">
			<view class="flex align-center justify-between top">
				<view class="con-btn" @tap="thedaybefore()">
					前一天 </view>
				<view class="flex align-center justify-between date" @tap="timeShow=true">
					<view class="">{{searchQuery.date}}</view>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/date.png" mode="widthFix">
					</image>
				</view>
				<view class="con-btn" @tap="thenextday()">后一天</view>
			</view>
		</view>
		<!-- <view class="tab-list flex flex-wrap">
			<block v-for="(item,index) in typeList" :key="item.name">
				<view :class="[searchQuery.queryType==item.value?'tab-item tab-show':'tab-item']" @click="tabClick(item.value)">
					{{item.name}}
				</view>
			</block>
		</view> -->

		<!-- <view class="search">
			<u-search placeholder='请输入订单号' v-model="searchQuery.searchKey" :showAction="false" @search="search">
			</u-search>
		</view> -->

		<view class="flex align-center justify-between screen-container">
			<view class="total">共{{orderCount.orderNum||0}}个订单，合计:￥{{$xy.delMoney(orderCount.orderTotalAmount)}}
			</view>
			<view class="flex align-center">
				<view class="show-zero flex justify-end align-center">
				</view>
				<!-- <view class="flex align-center justify-center" @tap="screen">
					<view class="" style="font-size: 28rpx;font-weight: 500;color: #333333;">导出</view>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png"
						style="width: 32rpx;height: 32rpx;margin-left: 12rpx;" mode="widthFix"></image>
				</view> -->
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="orderList.length>0">
				<block v-for="(item,index) in orderList" :key="item.orderId">
					<view class="equipment-container" @tap="details(item)">
						<view class="order-detail-item flex justify-between align-start">
							<view class="flex">
								<view class="label">订单号：</view>
								<view class="order-id">{{item.orderId}}</view>
							</view>

							<view class="order-status flex">{{getOrderStatus(item.status)}}</view>
							<view class="arrow">
								<u-icon name="arrow-right" size="14"></u-icon>
							</view>
						</view>
						<!-- 	<view class="order-detail-item flex justify-between align-start">
							<view class="flex">
								<view class="label">支付方式：</view>
								<view class="text">{{$xy.getPayType(item.payType)}}</view>
							</view>
						</view> -->
						<view class="order-detail-item flex justify-between align-start">
							<view class="flex">
								<view class="label">交易时间：</view>
								<view class="text">{{item.createTime}}</view>
							</view>
						</view>
						<!-- 		<view class="order-detail-item flex justify-between align-start">
							<view class="flex">
								<view class="label">订单商品：</view>
								<view class="text">{{item.goodsName?item.goodsName:'/'}}</view>
							</view>
							<view class="money flex">金额￥{{(item.money/100.0).toFixed(2)}}</view>
						</view> -->
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="orderList.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>


		<!-- 时间选择 -->
		<u-datetime-picker :show="timeShow" :closeOnClickOverlay="true" @close="timeShow=false" @confirm="timeSubmit"
			@cancel="timeShow=false" v-model="timeStamp" mode="date"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		mercOrderList,
		mercOrderListStatic,
	} from "@/api/order/order.js"
	import {
		areaTree
	} from "@/api/point/area"
	import {
		linePage,
	} from "@/api/point/line"

	export default {
		data() {
			return {
				id: 0,
				viewTitle: '-',
				typeList: [{
					name: '全部',
					value: 0
				}, {
					name: '已完成',
					value: 1
				}, {
					name: '已补扣',
					value: 2
				}, {
					name: '0元单',
					value: 3
				}, {
					name: '罚款',
					value: 4
				}, {
					name: '支付中',
					value: 5
				}, {
					name: '补单',
					value: 6
				}, {
					name: '待确认',
					value: 7
				}, {
					name: '已退款',
					value: 8
				}],
				orderStatus: [{
						value: 1,
						label: '交易中'
					},
					{
						value: 2,
						label: '交易异常'
					},
					{
						value: 3,
						label: '交易取消'
					},
					{
						value: 4,
						label: '交易完成'
					},
					{
						value: 5,
						label: '交易关闭'
					},
					{
						value: 6,
						label: '下次补单'
					},
					{
						value: 7,
						label: '支付中'
					},
				],
				keyword: '',
				orderListlength: '',
				orderList: [],
				orderCount: {
					orderNum:0,
					orderTotalAmount:0
				},
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				mode: 'single',
				screenShow: false,
				actionSheetShow: false,
				currentDate: '',
				nextDate: '',
				preDate: '',
				date: '',
				actions: [],
				title: '',
				zeroOrderName: true,
				zeroOrderList: [{
					type: false,
					name: '否'
				}, {
					type: true,
					name: '是'
				}],
				equipmentTypename: '',
				equipmentTypeList: [{
					type: '',
					name: '全部'
				}, {
					type: 1,
					name: '开门柜'
				}, {
					type: 2,
					name: '重力柜'
				}],
				administrativeDivisionname: '',
				administrativeDivisionList: [{
					type: 'xzqy',
					name: '全部'
				}, {
					type: 'xzqy',
					name: '省市区三级联动'
				}],
				routename: '',
				routeList: [{
					type: 'lx',
					name: '全部'
				}, {
					type: 'lx',
					name: '路线1'
				}, {
					type: 'lx',
					name: '路线2'
				}],
				orderStatusname: '',
				orderStatusList: [{
						type: '',
						name: '全部'
					}, {
						type: 1,
						name: '交易中'
					}, {
						type: 2,
						name: '交易异常'
					},
					{
						type: 3,
						name: '交易取消'
					},
					{
						type: 4,
						name: '交易完成'
					},
					{
						type: 5,
						name: '交易关闭'
					},
					{
						type: 6,
						name: '下次补单'
					},
					{
						type: 7,
						name: '支付中'
					}
				],
				abnormalStatename: '',
				abnormalStateList: [{
						type: '',
						name: '全部'
					}, {
						type: 2,
						name: '发起识别失败'
					}, {
						type: 3,
						name: '未拿商品'
					}, {
						type: 4,
						name: '无法识别'
					},
					{
						type: 5,
						name: '故意遮挡'
					}, {
						type: 6,
						name: '柜中无此sku'
					}, {
						type: 9,
						name: '异物拿放'
					}, {
						type: 10,
						name: '疑似设备故障'
					},
					{
						type: 11,
						name: '支付失败'
					}, {
						type: 12,
						name: '超时'
					},
					{
						type: 8,
						name: '其他告警'
					}
				],


				searchQuery: {
					date: '', //日期
					type: 0,
				},


				pickerType: null,
				minDate: null,

				timeShow: false, //时间选择弹框
				timeStamp: new Date(), //时间picker显示时间

				dayNum: 0, //日期

				fullHeight: 0,

				current: 0,
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			this.searchQuery.date = o.date;
			this.searchQuery.type = o.type;
			switch (o.type) {
				case '1':
					this.viewTitle = '订单明细';
					break;
				case '2':
					this.viewTitle = '退款订单明细';
					break;
				case '3':
					this.viewTitle = '挂账订单明细';
					break;
				case '4':
					this.viewTitle = '待确认订单明细';
					break;
				case '5':
					this.viewTitle = '异常订单明细';
					break;
				case '6':
					this.viewTitle = '今日补收明细';
					break;
				case '7':
					this.viewTitle = '今日补退明细';
					break;
				case '8':
					this.viewTitle = '待处理风险订单明细';
					break;
				case '9':
					this.viewTitle = '今日新增风险订单明细';
					break;
				case '10':
					this.viewTitle = '今日处理风险订单明细';
					break;
			}
			this.currentDate = new Date();
			this.minDate = '2023-03-10'
			this.search()
		},
		methods: {
			getOrderStatus(o) {
				for (let i = 0; i < this.orderStatus.length; i++) {
					if (this.orderStatus[i].value == o) return this.orderStatus[i].label;
				}
				return '-';
			},
			// 时间选择
			timeSubmit(e) {
				this.searchQuery.date = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.timeShow = false
				this.reset()
				this.getpage()
				//改为分页接口获取 this.getCountData()
			},


			details(item) {
				this.$tab.navigateTo(`/pages/order/orderDetails?id=${item.orderId}&createTime=${item.createTime}`)
			},

			confirm(e) {
				this.searchQuery.date = e[0]
				this.show = false
				this.search()
				//改为分页接口获取 this.getCountData()
			},

			thedaybefore() {
				this.dayNum--
				this.preDate = new Date(this.currentDate.getTime() + this.dayNum * (24 * 60 * 60 * 1000)); //前一天
				this.searchQuery.date = uni.$u.timeFormat(this.preDate, 'yyyy-mm-dd')
				//改为分页接口获取 this.getCountData()
				this.search()
			},
			thenextday() {
				this.dayNum++
				this.nextDate = new Date(this.currentDate.getTime() + this.dayNum * (24 * 60 * 60 * 1000)); //后一天
				this.searchQuery.date = uni.$u.timeFormat(this.nextDate, 'yyyy-mm-dd')
				//改为分页接口获取 this.getCountData()
				this.search()
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					}
				}
				if (this.current == 0) {
					Object.assign(params, this.searchQuery)
				} else {
					let obj = JSON.parse(JSON.stringify(this.searchQuery))
					obj.date = ''

					Object.assign(params, obj)
				}
				
				mercOrderListStatic(params).then(res => {
					this.orderCount.orderNum = res.data.orderNum;
					this.orderCount.orderTotalAmount = res.data.orderTotalMoney;
				});
				
				
				mercOrderList(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}

					this.orderList = this.orderList.concat(data)
				})

			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.orderList = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			sure() {
				this.reset()
				this.getpage()
				this.screenShow = false;
			}
		}
	}
</script>
<style scoped lang="scss">
	.container {
		background-color: #fff;

		.tab-list {
			width: 100%;
			background-color: #fff;
			padding: 0 26rpx;

			.tab-item {
				padding: 0 20rpx;
				width: 120rpx;
				height: 62rpx;
				background: #F7F7F7;
				border-radius: 10rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #777777;
				margin-right: 20rpx;
				line-height: 62rpx;
				margin-bottom: 14rpx;

				&.tab-show {
					background: #F4F8FF;
					color: #2C6FF3;
				}
			}
		}

		.empty {
			margin-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			margin: 0 10rpx;
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.total {
			margin: 0 20rpx 10rpx 20rpx;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
		}

		.tab-wrap {
			background-color: #fff;

			.tab {
				// width: 40%;
			}
		}

		.marleft {
			margin-left: 10rpx;
		}

		.scrollview {
			overflow: hidden;
			background-color: #f6f6f6;
		}

		.device-name {
			display: inline-block;
			width: 100%;
			height: 60rpx;
			border-radius: 0px 0px 50rpx 50rpx;

			background-color: #2C6FF3;
			color: #fff;
			text-align: center;

		}

		.time-choose {
			width: 100%;

			.top {
				width: 100%;
				height: 80rpx;
				margin-top: 10rpx;
				color: #fff;
				padding: 0 40rpx;

				.con-btn {
					height: 60rpx;
					padding: 10rpx 36rpx;
					background-color: #2C6FF3;
					border-radius: 14rpx;
				}

				.date {
					height: 60rpx;
					width: 300rpx;
					margin: 0rpx 20rpx;
					border-radius: 10rpx;
					background-color: #f6f6f6;
					color: #000;
					padding: 0 20rpx;

					image {
						width: 30rpx;
						height: 30rpx;
					}
				}


			}
		}

		.buttons {
			padding: 0 20rpx;

			.btn {
				margin: 6rpx 10rpx;
			}
		}

		.screen-container {
			background-color: #fff;
			padding: 0 13rpx;

			>view:nth-child(1) {
				font-size: 28rpx;
				font-weight: 500;
				color: #777777;
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 20rpx 24rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.goods-container {
				min-height: 154rpx;
				margin-top: 14rpx;
				padding: 12rpx 12rpx 12rpx 164rpx;
				box-sizing: border-box;
				position: relative;

				.image-container {
					height: 130rpx;
					width: 130rpx;
					position: absolute;
					left: 12rpx;
					top: 50%;
					transform: translateY(-50%);
				}

				.details-container {
					position: relative;
					padding: 12rpx;

					.goods-name-num {
						.goods-name {
							font-size: 26rpx;
							color: #333;
							font-weight: bold;
						}

						.goods-num {
							font-size: 26rpx;
							color: red;
							margin-right: 40rpx;
							font-weight: bold;
						}
					}

					.goods-price {
						font-size: 28rpx;
					}

					.good-act {
						margin-top: 12rpx;
					}

					.gp-item {
						width: 50%;
						margin-top: 12rpx;
					}

					.goodf-act {
						font-size: 28rpx;
						color: #333;
						margin-top: 12rpx;
					}

					.goods-btn {
						margin-top: 12rpx;
					}

					.refund {
						position: absolute;
						right: 0;
						top: 0;
						background-color: red;
						color: #fff;
						border-radius: 4rpx;
						padding: 0 12rpx;
						line-height: 40rpx;
						font-size: 24rpx;
					}
				}

			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;
				line-height: 40rpx;

				.label {
					width: 150rpx;
				}

				.order-id {
					font-weight: 700;

				}

				.text {
					font-weight: 400;
				}

				.money {
					color: red;
					font-weight: 700;
				}

				.order-status {
					margin-right: 6rpx;
				}

				.arrow {
					margin-top: 8rpx;
				}
			}
		}

		.popup-container {
			padding: 20rpx;
		}

		.popup-content {
			padding: 0 24rpx;
		}
	}
</style>