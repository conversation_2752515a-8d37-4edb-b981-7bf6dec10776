<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="设备卡包"></u-navbar>
		<view class="content">
			<view class="device-name">
				设备编号：{{deviceId}}
			</view>
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view class="scroll-content" v-if="list&&list.length>0">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="xy-card section">
							<view class="flex justify-between"
								style="font-size: 32rpx; line-height: 68rpx;margin-bottom: 12rpx;">
								<view class="row-item flex align-center"
									style="font-size: 32rpx; line-height: 68rpx;font-weight: bold;">
									<view>
										<!-- {{item.name}} -->
										设备激活算法包
									</view>
								</view>
								<view class="flex flex-direction align-center rest">
									<view>
										剩余：<text style="font-size: 40rpx;font-weight: bold;">{{item.unusedSize}}次</text>
									</view>
									<view style="color: #999999;font-size: 24rpx;">
										已用：<text>{{item.makeSize}}次</text>
									</view>
									<view style="color: #999999;font-size: 24rpx;">
										总计：<text>{{item.makeSize+item.unusedSize}}次</text>
									</view>
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									可用算法：
								</view>
								<view class="row-val">
									{{getAlgorithmName(item.algorithmId)}}
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									生效时间：
								</view>
								<view class="row-val">
									{{item.beginTime}}
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									到期时间：
								</view>
								<view class="row-val">
									{{item.timeout==-1?'永久':item.timeout }}
								</view>
							</view>
						</view>
					</block>
				</view>

				<view v-else style="padding-top: 40%;">
					<u-empty text="没有数据!"></u-empty>
				</view>
			</scroll-view>

		</view>
	</view>
</template>

<script>
	import {
		devCardPackList
	} from "@/api/recharge.js"
	import getDict from '@/utils/getDict.js'
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
				loadmoreStatus: 'loadmore',
				fullHeight: 0,
				page: 1,
				algorithm_types: []
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')
			
			getDict('algorithm_types').then(res => {
				this.algorithm_types = res
				this.deviceId = o.deviceId
				this.search()
			})
		},
		methods: {
			search() {
				this.reset()
				this.getList()
			},

			reset() {
				this.page = 1;
				this.list = [];
				this.loadmoreStatus = "loadmore"
			},

			getList() {
				devCardPackList({
					deviceId: this.deviceId,
					page: {
						current: this.page,
						size: 10
					}
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data);
				})
			},

			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			getAlgorithmName(type) {
				let name = ''
				for (var i = 0; i < this.algorithm_types.length; i++) {
					let item = this.algorithm_types[i]
					if (type == item.code) {
						name = item.msg
						break
					}
				}
				return name
			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			overflow: hidden;

			.device-name {
				height: 84rpx;
				width: 100%;
				text-align: center;
				font-size: 32rpx;
				line-height: 84rpx;
				background: #2C6FF3;
				color: #fff;
				border-radius: 0px 0px 50rpx 50rpx;
			}

			.search {
				margin-top: 30rpx;
			}

			.scroll-content {
				overflow: hidden;
				padding: 0 13rpx;
			}

			.section {
				margin-top: 20rpx;
				padding: 24rpx 30rpx 24rpx 48rpx;
				position: relative;

				.row-item {
					font-size: 28rpx;
					color: #555555;
					line-height: 56rpx;
					position: relative;

					.row-name {
						min-width: 140rpx;
					}

					.row-val {}
				}

				.rest {
					color: red;
					position: absolute;
					right: 24rpx;
					top: 80rpx;
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}
	}
</style>