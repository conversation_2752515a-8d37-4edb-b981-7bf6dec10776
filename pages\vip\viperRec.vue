<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="套餐记录"></u-navbar>

<!-- 		<view class="search">
			<u-search animation placeholder="请输入" :clearabled="true" v-model="keyword" :showAction="false"
				@search="getList"></u-search>
		</view> -->

		<view class="flex align-center justify-end screen-container">
			<view class="flex align-center justify-center" @tap="screen">
				<view>{{meal.name||'套餐类型'}}</view>
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png" mode="widthFix">
				</image>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="equipment-container">
						<view class="flex align-center justify-between">
							<view class="title">
								{{item.mealName}}<text>({{Number(item.mealDiscount)*10}}折)</text>
							</view>
						</view>
						<view class="order-detail-item">
							<view>订单号：</view>{{item.id}}
						</view>
						<view class="order-detail-item">
							<view>手机号：</view>{{item.memberTel||'-'}}
						</view>
						<view class="order-detail-item">
							<view>购买时间：</view>{{item.payTime||'-'}}
						</view>
						<view class="order-detail-item">
							<view>到期时间：</view>{{item.timeout||'-'}}
						</view>
						<view class="order-detail-item">
							<view>支付方式：</view>{{$xy.getPayType(item.payType)}}
						</view>
						<view class="order-detail-item">
							<view>金额：</view>￥{{$xy.delMoney(item.payMoney)}}
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>
		<u-action-sheet :show="actionSheetShow" :actions="actions" title="套餐类型" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		orderPlusMember,
		listByMercId
	} from "@/api/vip.js"

	export default {
		data() {
			return {
				keyword: '',
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				actionSheetShow: false,
				actions: [],
				title: '',

				pickerType: null,

				fullHeight: 0,
				list: [],
				meal: {
					id: null,
					name: null
				},
				memberId:null
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			
			this.memberId=o.memberId
			this.screenList()
			this.search()
		},

		methods: {
			/**
			 * 套餐筛选
			 */
			screenList() {
				listByMercId({}).then(res => {
					let data = res.data;
					let actions = data.map(i => ({
						name: i.mealName,
						type: i.id
					}))
					this.actions = [{
						name: '全部',
						type: ''
					}, ...actions]
				})
			},

			actionsheetSelect(e) {
				this.meal = {
					name: e.name,
					id: e.type
				}

				this.search()
			},

			screen() {
				this.actionSheetShow = true;
			},

			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					mealId: this.meal.id,
					memberId:this.memberId
				}
				orderPlusMember(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		}
	}
</script>
<style scoped lang="scss">
	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.screen-container {
			background-color: #fff;
			padding: 10rpx 13rpx 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 12rpx;
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 20rpx 24rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;

				>view {
					display: inline-block;
					width: 170rpx;
				}
			}
		}
	}
</style>