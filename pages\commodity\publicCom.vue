<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="官方商品库"></u-navbar>
		<!-- <view class="u-search-box" @click="searchComm">
			<view class="u-search-inner">
				<u-icon name="search" color="#909399" :size="28"></u-icon>
				<text class="u-search-text">搜索商品</text>
			</view>
		</view> -->
		<view class="search" @click="searchComm">
			<view class="search-input">
				<u-search placeholder="商品搜索" actionText="取消" :actionStyle="{color:'#2C6FF3'}" :showAction="!leftShow"
					:clearabled="false" v-model="keyword" @search="search" @custom="cancle"></u-search>
				<view @click="scan" :class="[leftShow?'scan-icon scan-left-show':'scan-icon scan-left-hidden']">
					<u-icon name="scan" size="22" color="#909399"></u-icon>
				</view>
			</view>

			<view class="search-history flex flex-wrap flex-start" v-if="!leftShow">
				<view class="history-item" v-for="(item,index) in historyList" :key="index" @click="searchFast(item)">
					{{item}}
				</view>
			</view>
		</view>

		<view class="algo flex justify-end">
			<view class="flex align-center filter-algo" @click="changeType">
				<view style="margin-right: 6rpx;">{{algoTypeName}}</view>
				<view><u-icon name="arrow-down-fill" size="10" color="#6b778b"></u-icon></view>
			</view>
		</view>

		<view class="classify-wrap">
			<classify ref="classify" :storeName="storeName" :selectShow="true" :tabList="tabList" :status="status"
				:commList="commList" @switchMenu="switchMenu" @comClick='detail' @lowerBottom="lowerBottom"
				:height="fullHeight" :leftShow="leftShow" />
		</view>
		<view class="btn safe-bottom">
			<xbutton round="88rpx" delay="1500" size="large" @click="addToPer">添加到商品私库</xbutton>
		</view>

		<u-action-sheet :show="actionSheetShow" :actions="actions" title="切换商品种类" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import {
		goodsCategory,
		ownerGoodsList,
		mercAiList
	} from "@/api/commodity/goods.js"
	export default {
		data() {
			return {
				fullHeight: '0',
				tabList: [], //商品类目
				commList: [], //商品列表
				page: 1, //商品分页
				size: 10,
				status: 'loadmore', //加载更多
				storeName: 'pubToPerStor', //存储名称
				keyword: '', //搜索
				leftShow: true,
				historyList: [],

				actionSheetShow: false,
				actions: [],
				algoType: '',
				algoTypeName: '切换商品库',
			}
		},

		async onLoad() {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.classify-wrap', 50)

			if (uni.getStorageSync('goods')) {
				this.historyList = JSON.parse(uni.getStorageSync('goods'))
			}
			
			uni.setStorageSync('pubToPerStor', '')
			
			this.getAlgoList().then(res => {
				this.getCategory()
			})
		},

		onShow() {
			//搜索页来回跳转，刷新已选商品数据
			this.$refs.classify.onshow()
		},

		methods: {
			// 搜索商品
			// searchComm() {
			// 	this.$tab.navigateTo('/pages/commodity/publicSearch?type=0&storeName=' + this.storeName)
			// },

			changeType() {
				this.actionSheetShow = true
				console.log(this.actionSheetShow)
			},

			actionsheetSelect(e) {
				this.algoType = e.type
				this.algoTypeName = e.name
				this.search()
			},

			//商家算法列表
			getAlgoList() {
				return new Promise((resolve, reject) => {
					mercAiList({}).then(res => {
						let data = res.data
						let newData = data.map(i => {
							return {
								type: i.id,
								name: i.alias
							}
						})
						this.actions = newData
						this.algoType = data[0].id
						this.algoTypeName = data[0].alias
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			search(val) {
				this.saveKeyWord('goods', this.keyword)
				this.reset();
				this.getCommList()
			},

			cancle(val) {
				this.keyword = ''
				this.leftShow = true
				this.search()
			},

			saveKeyWord(type, val) {
				if (val) {
					let arr = []
					if (uni.getStorageSync(type)) {
						let arr = JSON.parse(uni.getStorageSync(type))
						if (arr.indexOf(val) != -1) {
							console.log('arr.indexOf(val)', arr.indexOf(val))
							arr.splice(arr.indexOf(val), 1)
						}
						arr.unshift(val)
						if (arr.length > 6) {
							arr.pop()
						}
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					} else {
						arr.unshift(val)
						this.historyList = JSON.parse(JSON.stringify(arr))
						uni.setStorageSync(type, JSON.stringify(arr))
					}
				} else {
					return
				}
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},

			searchFast(e) {
				this.keyword = e
				this.search()
			},

			//扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.keyword = res.result;
						this.search()
					}
				});
			},

			searchComm() {
				this.leftShow = false
			},

			//获取类目列表
			getCategory() {
				goodsCategory({
					deviceId: '',
					algorithmId: this.algoType
				}).then(res => {
					this.tabList = res.data
					if (this.tabList && this.tabList.length > 0) {
						this.switchMenu(this.tabList[0])
					}
				})
			},

			//商品类目切换
			switchMenu(item) {
				this.reset()
				this.getCommList(item.categoryCode)
			},

			//根据类目获取商品列表
			getCommList(id) {
				let params = {}
				if (this.leftShow) { //搜索
					params = {
						categoryCode: id,
						algorithmId: this.algoType,
						page: {
							current: this.page,
							size: this.size
						}
					}
				} else { //非搜索
					params = {
						page: {
							current: this.page,
							size: this.size
						},
						algorithmId: this.algoType,
						keyword: this.keyword
					}
				}
				ownerGoodsList(params).then(res => {
					let data = res.data.records;
					let newData = data.map(i => {
						i.noSelect = i.mercGoodsId != null ? true : false;
						i.categoryCode = i.categoryCodeLevel1;
						return i
					})
					if (newData.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.commList = this.commList.concat(newData)
				})
			},

			//触底加载更多
			lowerBottom() {
				if (this.status == 'nomore') return
				this.page++
				this.getCommList()
			},

			//添加商品到私库
			addToPer() {
				if (uni.getStorageSync(this.storeName) && JSON.parse(uni.getStorageSync(
						this.storeName)).length > 0) {
					this.$tab.navigateTo(`/pages/equipment/addComList?storeName=${this.storeName}&type=0`)
				} else {
					uni.$u.toast('请先选择商品！')
				}
			}
		},
		
		onUnload() {
			uni.setStorageSync('pubToPerStor', '')
		}	
	}
</script>

<style lang="scss" scoped>
	.container {
		height: calc(100vh);
		/* #ifdef H5 */
		height: calc(100vh - var(--window-top));
		/* #endif */
		display: flex;
		flex-direction: column;

		.search {
			padding: 24rpx 24rpx;
			background-color: #fff;

			.search-input {
				position: relative;

				.scan-icon {
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					z-index: 2;

					&.scan-left-show {
						right: 36rpx;
					}

					&.scan-left-hidden {
						right: 100rpx;
					}
				}
			}

			.search-history {

				.history-item {
					margin-right: 24rpx;
					padding: 0 12rpx;
					background-color: #f2f2f2;
					color: #333;
					font-size: 24rpx;
					line-height: 40rpx;
					border-radius: 40rpx;
					margin-top: 24rpx;
				}
			}
		}
	}

	.algo {
		font-size: 28rpx;
		color: #0D0D0D;
		background-color: #fff;
		padding: 0 24rpx 20rpx;

		.filter-algo {
			padding: 0 35rpx;
			line-height: 46rpx;
			background: #F2F2F2;
			border-radius: 14rpx;
		}
	}

	.classify-wrap {
		// padding-bottom: 200rpx;
	}

	.btn {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 24rpx;
		padding: 0 24rpx;
	}
</style>