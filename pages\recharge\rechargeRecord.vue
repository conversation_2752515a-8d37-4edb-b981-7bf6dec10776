<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="设备管理费记录"></u-navbar>
		<view class="content">
			<block v-for="(item,index) in list" :key="item.placeLineId">
				<view class="xy-card section">
					<view class="flex justify-between align-center">
						<view class="row-item flex align-center">
							<view class="row-name">
								缴费单号：
							</view>
							<view class="row-val">
								动态视觉柜
							</view>
						</view>
						<view  style="color: #2C6FF3;" @click="detail(item)">
							查看明细
						</view>
					</view>
					<view class="row-item flex align-center">
						<view class="row-name">
							机器类型：
						</view>
						<view class="row-val">
							动态视觉柜
						</view>
					</view>
					<view class="row-item flex align-center">
						<view class="row-name">
							机器类型：
						</view>
						<view class="row-val">
							动态视觉柜
						</view>
					</view>
					<view class="row-item flex align-center">
						<view class="row-name">
							机器类型：
						</view>
						<view class="row-val">
							动态视觉柜
						</view>
					</view>
					<view class="row-item flex align-center">
						<view class="row-name">
							机器类型：
						</view>
						<view class="row-val">
							动态视觉柜
						</view>
					</view>
					<view class="row-item flex align-center">
						<view class="row-name">
							机器类型：
						</view>
						<view class="row-val">
							动态视觉柜
						</view>
					</view>
					<view class="row-item flex align-center">
						<view class="row-name">
							机器类型：
						</view>
						<view class="row-val">
							动态视觉柜
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		mercHomeList,
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
			}
		},
		onLoad(o) {
			this.search()
		},
		methods: {
			detail(){
				
			},
			
			search() {
				this.getList()
			},

			getList(id) {
				mercHomeList({
					memercId: id,
					deviceName: '',
					deviceId: this.deviceId
				}).then(res => {
					let data = res.data;
					if (this.deviceIds) {
						for (let i = 0; i < data.length; i++) {
							let par = data[i];
							par.checked = true
							for (let j = 0; j < par.deviceInfos.length; j++) {
								let child = par.deviceInfos[j]
								if (this.deviceIds.indexOf(child.deviceId.toString()) != -1) {
									child.checked = true
								} else {
									child.checked = false
									par.checked = false
								}
							}
						}
					}
					this.list = data;
				})
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			padding: 0 13rpx;
			overflow: hidden;

			.search {
				margin-top: 30rpx;
			}

			.section {
				margin-top: 20rpx;
				padding:24rpx 30rpx 24rpx 48rpx;

				.row-item {
					font-size: 28rpx;
					color: #555555;
					line-height: 56rpx;
				
					.row-name {
						min-width: 140rpx;
					}
				
					.row-val {}
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}
	}
</style>