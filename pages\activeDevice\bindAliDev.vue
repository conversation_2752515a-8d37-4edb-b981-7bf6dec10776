<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="设备激活">
		</u-navbar>

		<view class="device-img flex flex-direction">
			<view class="tips flex justify-between align-center" v-if="!isBindAli">
				<view class="bind-tip">
					您还没绑定支付宝
				</view>
				<xbutton round="50rpx" width="160rpx" @click="bindAliAcc">立即绑定</xbutton>
			</view>
			<view class="img">
				<u-image width="147rpx" src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/device.png"
					mode="widthFix" :lazy-load="true">
				</u-image>
			</view>
		</view>

		<view class="device-msg">
			<view class="msg-item">
				<view class="">
					激活状态
				</view>
				<view class="">
					{{device.activeState==1?'已激活':'未激活'}}
				</view>
			</view>
			<view class="msg-item">
				<view class="">
					工控SN
				</view>
				<view class="">
					{{device.deviceSn}}
				</view>
			</view>
			<view class="msg-item">
				<view class="">
					商户前缀
				</view>
				<view class="">
					{{device.mercPrefix}}
				</view>
			</view>
			<view class="msg-item">
				<view class="">
					设备编号
				</view>
				<view class="">
					{{device.deviceId}}
				</view>
			</view>

		</view>

		<view class="btn flex justify-between safe-bottom">
			<view style="width:48%">
				<xbutton size="large" round="82rpx" @click="refesh">刷新状态</xbutton>
			</view>
			<!--      <view style="width:48%">-->
			<!--        <xbutton size="large" round="82rpx" @click="help">激活帮助</xbutton>-->
			<!--      </view>-->
			<view style="width:48%">
				<xbutton size="large" round="82rpx" @click="scan">扫一扫 激活</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		userInfoBySelf,
		aliDeviceObj,
		activeAlipayDevice
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				isBindAli: true,
				deviceId: null,
				device: {

				}
			}
		},
		onLoad(o) {
			this.deviceId = o.id;
		},

		onShow() {
			this.getUserMsg()
			this.getDeviceMsg()
		},

		methods: {
			getDeviceMsg() {
				aliDeviceObj({
					deviceId: this.deviceId
				}).then(res => {
					this.device = res.data
				}).catch(err => {

				})
			},

			getUserMsg() {
				userInfoBySelf().then(res => {
					let data = res.data
					this.isBindAli = data.aliUserId ? true : false;
				})
			},

			refesh() {
				this.getDeviceMsg()
			},

			help() {

			},

			scan() {
				uni.scanCode({
					success: (res) => {
						console.log(res);
						let sn = res.result;
						return new Promise((resolve, reject) => {
							activeAlipayDevice({
								sn: sn,
								deviceId: this.deviceId,
							}).then(res => {
								resolve(res)
							}).catch(err => {
								reject(res)
							})
						})
						console.log("支付宝设备激活扫一扫SN:" + sn);
					}
				});
			},

			bindAliAcc() {
				this.$tab.navigateTo('/pages/activeDevice/bindAliAcc')
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
		text-align: center;



		.device-img {
			width: 724rpx;
			margin-left: 13rpx;
			background-color: #F5F5F5;
			border-radius: 14rpx;
			margin-top: 20rpx;
			padding-bottom: 40rpx;

			.tips {
				border-top-left-radius: 14rpx;
				border-top-right-radius: 14rpx;
				line-height: 80rpx;
				font-size: 26rpx;
				height: 80rpx;
				padding: 0 40rpx;
				background: linear-gradient(180deg, #DEE9FF, rgba(217, 231, 255, 1));
			}

			.img {
				width: 147rpx;
				height: 281rpx;
				margin-top: 75rpx;
				margin-left: 292rpx;
			}
		}

		.device-msg {
			width: 724rpx;
			margin-left: 13rpx;
			margin-top: 20rpx;
			background-color: #F5F5F5;
			border-radius: 14rpx;
			padding: 0 37rpx;

			.msg-item {
				line-height: 92rpx;
				font-size: 28rpx;
				color: #333;
				display: flex;
				justify-content: space-between;

				&+.msg-item {
					border-top: 1rpx solid #E2E2E2;
				}

				>view:nth-child(1) {}

				>view:nth-child(2) {
					color: #777777;
				}
			}
		}

		.device-code {
			font-size: 54rpx;
			font-weight: bold;
			margin-top: 46%;
			color: #333;
		}

		.device-tips {
			font-size: 28rpx;
			margin-top: 30rpx;
		}

		.device-err {
			font-size: 22rpx;
			color: red;
			margin-top: 40rpx;
		}

		.scan {
			margin-top: 36rpx;
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
		}
	}
</style>