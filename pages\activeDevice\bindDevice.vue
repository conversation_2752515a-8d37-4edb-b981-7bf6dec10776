<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="设备激活">
		</u-navbar>

		<view class="img">
			<u-image width="147rpx" src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/device.png"
				mode="widthFix" :lazy-load="true">
			</u-image>
		</view>

		<view class="device-msg">
			<view class="msg-item">
				<view class="">
					设备编号
				</view>
				<view class="">
					{{deviceId}}
				</view>
			</view>
			<view class="msg-item">
				<view class="">
					机器状态
				</view>
				<view class="" style="color: #2C6FF3;">
					{{err}}
				</view>
			</view>
		</view>

		<view class="btn safe-bottom" v-if="!isActive">
			<xbutton size="large" @click="activeDeviceNow">激活货柜</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		deviceActive,
		getActiveInfo
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				deviceId: '请扫描二维码',
				err: '未激活',
				isActive: false //true已激活false未激活
			}
		},
		onLoad(o) {
			this.deviceId = o.id;
			this.getDeviceStatus()
		},
		methods: {
			getDeviceStatus() {
				getActiveInfo({
					deviceId: this.deviceId
				}).then(res => {
					this.isActive = false
					this.err = '未激活'
				}).catch(err => {
					this.isActive = true
					if (err.length > 10) {
						this.err = '请重新扫码~'
					} else {
						this.err = err
					}
				})
			},

			scan() {
				uni.scanCode({
					success: (res) => {
						console.log('条码内容：' + res.result);
						let deviceId = res.result.split('=')[1]
						this.deviceId = deviceId
					}
				});
			},

			activeDeviceNow() {
				deviceActive({
					deviceId: [this.deviceId]
				}).then(res => {
					let data = res.data;
					if (data && data.length) { //有值则需要缴费
						let code = data[0].code
						let msg = code == 202 ? '管理费' : code == 203 ? '算法服务费' : '管理费、算法服务费'
						let deviceIds = JSON.stringify([this.deviceId])
						this.$modal.oldConfirm(`激活设备需要预缴${msg}，确定缴费激活设备？`).then(res => {
							this.$tab.navigateTo(
								`/pages/recharge/devRechargeIng?devices=${JSON.stringify(data)}`
							)
						})
					} else {
						this.$modal.msg('激活成功~')
						setTimeout(() => {
							this.$tab.redirectTo(`/pages/equipment/detail/index?id=${this.deviceId}`)
						}, 1500)
					}
				}).catch(err => {
					this.err = '获取失败'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;
		text-align: center;

		.img {
			width: 147rpx;
			height: 281rpx;
			margin-top: 75rpx;
			margin-left: 292rpx;
		}

		.device-msg {
			width: 724rpx;
			margin-left: 13rpx;
			margin-top: 115rpx;
			background-color: #F5F5F5;
			border-radius: 14rpx;
			padding: 0 37rpx;

			.msg-item {
				line-height: 92rpx;
				font-size: 28rpx;
				color: #333;
				display: flex;
				justify-content: space-between;

				&+.msg-item {
					border-top: 1rpx solid #E2E2E2;
				}

				>view:nth-child(1) {}

				>view:nth-child(2) {
					color: #777777;
				}
			}
		}

		.device-code {
			font-size: 54rpx;
			font-weight: bold;
			margin-top: 46%;
			color: #333;
		}

		.device-tips {
			font-size: 28rpx;
			margin-top: 30rpx;
		}

		.device-err {
			font-size: 22rpx;
			color: red;
			margin-top: 40rpx;
		}

		.scan {
			margin-top: 36rpx;
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
		}
	}
</style>