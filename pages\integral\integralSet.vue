<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="active?'积分':'积分激活'"></u-navbar>
		<view class="content safe-bottom">
			<view class="" v-if="active">
				<view class="flex justify-between" style="padding:20rpx;">
					<xhelpPopup guideId="JF0001" />
					<xbutton  @click="$tab.navigateTo('/pages/integral/integralExcSet')">
						积分兑换商品设置
					</xbutton>
				</view>
				<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
					<view class="section-wrap" style="padding:0 30rpx;">
						<view class="action-time">
							<u-form-item label="积分有效期设置" prop="categoryName">
								<u-radio-group v-model="form.clear_type" placement="col" @change="usefulRadio">
									<u-radio :customStyle="{marginBottom: '20rpx'}" label="不清零" name="不清零">
									</u-radio>
									<u-radio label="自然年清零（1月1日0点）" name="自然年">
									</u-radio>
								</u-radio-group>
							</u-form-item>
						</view>
					</view>

					<view class="section-wrap" style="padding:0 30rpx;">
						<u-form-item label="选择设备" borderBottom>
							<u-radio-group v-model="form.is_all_device" placement="row" @change="actionAllDeviceRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
								</u-radio>
								<u-radio label="指定设备" name="0">
								</u-radio>
							</u-radio-group>
						</u-form-item>

						<u-form-item label="指定设备" borderBottom @click="chooseDev" v-if="form.is_all_device==0">
							<view class="choose-list" v-if="form.device_list&&form.device_list.length>0">
								<text v-for="(item,index) in form.device_list" :key="item.device_id">
									{{item.device_name||item.device_id}}
									<text v-if="index!=form.device_list.length-1">，</text>
								</text>
							</view>
							<view v-else style="color: #c0c4cc;text-align: right;">请选择设备</view>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>

						<u-form-item label="选择商品">
							<u-radio-group v-model="form.is_all_goods" placement="row" @change="actionAllGoodsRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
								</u-radio>
								<u-radio label="指定商品" name="0">
								</u-radio>
							</u-radio-group>
						</u-form-item>

						<u-form-item label="指定商品" borderBottom @click="chooseCom" v-if="form.is_all_goods==0">
							<view class="choose-list" v-if="form.goods_list&&form.goods_list.length>0">
								<text v-for="(item,index) in form.goods_list" :key="index">
									{{item.goods_name||item.name}}
									<text v-if="index!=form.goods_list.length-1">，</text>
								</text>
							</view>
							<view v-else style="color: #c0c4cc;text-align: right;">请选择商品</view>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
					</view>

					<view class="section-wrap" style="padding:0 30rpx;">
						<view class="rules-title">
							积分赠送规则
						</view>

						<view class="action-time">
							<u-form-item prop="categoryName" labelWidth="0" borderBottom>
								<view class="flex justify-between align-center">
									<view style="padding-left: 44rpx;">
										基础积分
									</view>
									<view class="flex align-center">
										消费满1元 赠送
										<view style="width:160rpx;padding:0 20rpx;">
											<u-input placeholder="请输入内容" type="number"
												v-model="form.item_base"></u-input>
										</view>
										积分
									</view>
								</view>
							</u-form-item>
						</view>

						<u-checkbox-group placement="column" v-model="form.item_multiple_selected"
							@change="multipleCheck">
							<view class="action-time action-time-else">
								<u-form-item prop="categoryName" labelWidth="0">
									<view class="flex justify-between">
										<u-checkbox shape="circle" label="限时翻倍" name="1"></u-checkbox>
										<view class="flex align-center">
											<text>消费积分翻</text>
											<view style="width:160rpx;padding:0 20rpx;"><u-input type="digit"
													placeholder="请输入内容" v-model="form.item_multiple.multiple"></u-input>
											</view>
											<text>倍</text>
										</view>
									</view>
								</u-form-item>
								<u-form-item prop="categoryName" labelWidth="0">
									<view class="flex justify-between">
										<view class="time-name">
											开始时间
										</view>
										<view class="flex align-center" @click="chooseDate('start_time')">
											<view style="text-align: right;" v-if="form.item_multiple.start_time">
												{{form.item_multiple.start_time}}
											</view>
											<view v-else style="color: #c0c4cc;text-align: right;">请选择日期</view>
											<u-icon slot="right" name="arrow-right"></u-icon>
										</view>
									</view>
								</u-form-item>
								<u-form-item prop="categoryName" labelWidth="0">
									<view class="flex justify-between">
										<view class="time-name">
											结束时间
										</view>
										<view class="flex align-center" @click="chooseDate('end_time')">
											<view style="text-align: right;" v-if="form.item_multiple.end_time">
												{{form.item_multiple.end_time}}
											</view>
											<view v-else style="color: #c0c4cc;text-align: right;">请选择日期</view>
											<u-icon slot="right" name="arrow-right"></u-icon>
										</view>
									</view>
								</u-form-item>
							</view>
						</u-checkbox-group>
					</view>
				</u--form>

				<view class="btn">
					<xbutton size="large" @click="submit">
						保存
					</xbutton>
				</view>
			</view>

			<view class="active-content" v-else>
				<!-- <view class="active-bg">
					<u--image width="724rpx" height="370rpx"
						src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/vip/active-bg.png"
						mode="widthFix" :lazy-load="true"></u--image>
				</view> -->

				<view class="btn flex justify-around">
					<xbutton size="large" width="720rpx" height="90rpx" round="90rpx" fontSize="32rpx"
						@click="goActive">
						立即激活（{{$xy.delMoney(activeMoney)}}元）</xbutton>
				</view>
			</view>
		</view>

		<u-datetime-picker :show="calendarShow" v-model="time" mode="datetime" confirmColor="#2C6FF3"
			@confirm="timeSelect"></u-datetime-picker>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		objs
	} from '@/api/vip.js'
	import {
		configSave,
		configObj
	} from "@/api/integral.js"

	export default {
		data() {
			return {
				active: true,
				activeMoney: 0,
				form: {
					clear_type: '不清零',
					is_all_device: '1',
					is_all_goods: '1',
					device_ids: [],
					goods_ids: [],
					item_base_selected: '1',
					item_multiple_selected: [],
					item_base: '',
					item_multiple: {
						multiple: '', //倍数
						start_time: '',
						end_time: ''
					},
					goods_list: [],
					device_list: []
				},
				time: new Date(),
				calendarShow: false,
				timeType: 'start_time'
			}
		},

		onLoad(o) {
			this.getData()

			uni.$on('updateCom', res => {
				if (res && res.length > 0) {
					let data = res.map(i => ({
						goods_id: i.goodsId,
						goods_name: i.goodsName,
						cover: i.cover
					}))
					this.form.goods_list = data
				} else {
					this.form.is_all_goods = '1'
					this.form.goods_list = []
				}
			})
			uni.$on('updateDev', res => {
				if (res && res.length > 0) {
					let data = res.map(i => ({
						device_id: i.deviceId,
						device_name: i.deviceName
					}))
					this.form.device_list = data
				} else {
					this.form.is_all_device = '1'
					this.form.device_list = []
				}
			})

			uni.$on('refresh', res => {
				this.getData()
			})
		},

		methods: {
			/**
			 * 获取数据
			 */
			getData() {
				this.getDetail().then(res => {

				}).catch(err => {
					this.getActiveMoney()
				})
			},

			/**
			 * 激活费用
			 */
			getActiveMoney() {
				getDict('merc_points_config').then(res => {
					this.activeMoney = res[0].value
				})
			},

			/**
			 * 前往激活
			 */
			goActive() {
				let type = [7]
				let url =
					`/pages/recharge/rechargeIng?type=${JSON.stringify(type)}&money=${this.activeMoney}`
				this.$tab.navigateTo(url)
			},

			/**
			 * 详情反显
			 */
			getDetail() {
				return new Promise((resolve, reject) => {
					configObj({}).then(res => {
						if (res.data && res.data.active_state) {
							this.active = true
							let obj = res.data
							let newForm = {
								clear_type: obj.clear_type,
								is_all_device: obj.is_all_device ? '1' : '0',
								is_all_goods: obj.is_all_goods ? '1' : '0',
								type: obj.item_base_selected ? '基础积分' : '限时翻倍',
								item_base_selected: obj.item_base_selected ? '1' : '0',
								item_multiple_selected: obj.item_multiple_selected ? ['1'] : [],
								item_base: obj.item_base,
								item_multiple: obj.item_multiple_selected ? {
									multiple: obj.item_multiple.multiple, //倍数
									start_time: obj.item_multiple.start_time,
									end_time: obj.item_multiple.end_time
								} : {
									multiple: '', //倍数
									start_time: '',
									end_time: ''
								},
								goods_list: obj.goods_list,
								device_list: obj.device_list
							}
							this.form = JSON.parse(JSON.stringify(newForm))
							console.log(this.form)
							this.$forceUpdate()
							resolve(res)
						} else {
							this.active = false
							reject(res)
						}
					})
				})
			},

			/**
			 * 选择设备
			 */
			chooseDev() {
				let ids = []
				if (this.form.device_list?.length > 0) {
					ids = this.form.device_list.map(i => i.device_id.toString())
				}
				this.$tab.navigateTo(`/pages/integral/chooseDevice?ids=${JSON.stringify(ids)}`)
			},

			/**
			 * 选择商品
			 */
			chooseCom() {
				let data = this.form.goods_list.map(i => ({
					goodsId: i.goods_id,
					goodsName: i.goods_name,
					cover: i.cover
				}))
				let goodsList = JSON.stringify(data)
				uni.setStorageSync('integralSto', goodsList)
				this.$tab.navigateTo(`/pages/integral/chooseGoods`)
			},


			actionAllDeviceRadio(e) {
				this.form.is_all_device = e
			},

			actionAllGoodsRadio(e) {
				if (e == 1) {
					this.$modal.oldConfirm('警告：后续新增的商品，也将全部适用当前活动！！！').then(res => {
						this.form.is_all_goods = e
					}).catch(err => {
						this.form.is_all_goods = '0'
					})
				} else {
					this.form.is_all_goods = e
				}
			},

			/**
			 * 积分有效期设置radio
			 */
			usefulRadio(e) {
				console.log(e)
			},

			/**
			 * @param {Object} 
			 * 是否限时翻倍
			 */
			multipleCheck(e) {
				console.log(e)
				this.form.item_multiple_selected = e
			},

			/**
			 * 时间选择弹框
			 */
			chooseDate(type) {
				this.timeType = type
				this.calendarShow = true
			},

			/**
			 * @param {Object} 
			 * 时间选择确认
			 */
			timeSelect(e) {
				let time = uni.$u.timeFormat(e.value, 'yyyy-mm-dd hh:MM:ss')
				console.log(time)
				this.form.item_multiple[this.timeType] = time
				console.log(this.form)
				this.calendarShow = false
			},

			//表单提交
			submit() {
				console.log(this.form)
				if (this.checkParams(this.form)) return
				let goods_ids = []
				if (this.form.is_all_goods == 0) {
					goods_ids = this.form.goods_list.map(item => item.goods_id)
				}
				let device_ids = []
				if (this.form.is_all_device == 0 && this.form.device_list?.length > 0) {
					device_ids = this.form.device_list.map(i => i.device_id)
				}
				let params = {
					goods_ids: goods_ids,
					device_ids: device_ids,
					clear_type: this.form.clear_type,
					is_all_device: this.form.is_all_device,
					is_all_goods: this.form.is_all_goods,
					item_base_selected: '1',
					item_base: this.form.item_base,
					item_multiple_selected: this.form.item_multiple_selected[0] == '1' ? '1' : '0',
					item_multiple: {
						multiple: this.form.item_multiple.multiple || 0, //倍数
						start_time: this.form.item_multiple.start_time,
						end_time: this.form.item_multiple.end_time
					}
				}
				configSave(params).then(res => {
					this.$modal.msg('更新成功~')
				}).catch(err => {

				})
			},

			checkParams(params) {
				if (!params.item_base) {
					this.$modal.msg('请输入赠送积分~')
					return true
				}

				if (params.item_multiple_selected?.length > 0) {
					if (!params.item_multiple.start_time) {
						this.$modal.msg('请输入积分翻倍开始时间~')
						return true
					}
					if (!params.item_multiple.end_time) {
						this.$modal.msg('请输入积分翻倍结束时间~')
						return true
					}
				}
				return false
			},
		},
		onUnload() {
			uni.$off(['updateDev', 'refresh', 'updateCom'])
		}
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
			}

			.input-text {
				text-align: right !important;
			}

			.tab-wrap {
				padding: 12rpx 0;
			}

			.choose-list {
				width: 418rpx;
				line-height: 40rpx;

				>text {
					word-break: break-all;
				}
			}

			.rules-title {
				padding-top: 30rpx;
			}

			.time-name {
				padding-left: 40rpx;
				color: #606266;
			}

			.form-item {
				position: relative;

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.action-time {
			position: relative;

			&.action-time-else {
				::v-deep .u-form-item__body {
					padding: 20rpx 0 !important;
				}
			}

			.edit {
				color: #2C6FF3;
				text-decoration: underline;
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);

				&.del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}

	.active-content {
		height: 100vh;
		width: 100%;
		background-color: #fff;
		padding: 20rpx 13rpx;
		text-align: center;

		.active-bg {
			height: 370rpx;
			position: relative;
		}

		.btn {
			margin-top: 87rpx;
		}
	}
</style>