<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="deviceName"></u-navbar>
		<view class="title">常用商品清单</view>
		<scroll-view class="scrollview" scroll-y="true" scroll-with-animation="true" lower-threshold="100"
			@scrolltolower="loadMore" :style="{height:fullHeight}">
			<view class="scroll-content" v-if="commodityList&&commodityList.length>0">
				<block v-for="(item,index) in commodityList" :key="item.modelId">
					<view class="commoditylist-container xy-card">
						<view class="commoditylist-content flex flex-direction justify-between">
							<view class="flex-sub list-name">{{item.name}}</view>
							<view class="flex-sub">设备类型：{{item.deviceTypeName}}</view>
							<view class="flex-sub">关联商品：{{item.goodsNum}}件</view>
						</view>
						<view class="flex align-center justify-end btn-box martop">
							<view class='marleft' v-if="checkPermi(['comList:del'])">
								<xbutton size="medium" bgColor="red" round='25rpx' padding='0rpx 20rpx'
									@click='deleted(item)'>删除</xbutton>
							</view>
							<view class='marleft' v-if="checkPermi(['comList:update'])">
								<xbutton size="medium" round='25rpx' padding='0rpx 20rpx' @click='modify(item)'>修改
								</xbutton>
							</view>
							<view class='marleft' v-if="checkPermi(['comList:addCom'])">
								<xbutton size="medium" round='25rpx' padding='0rpx 20rpx' @click="relatedgoods(item)">
									关联商品
								</xbutton>
							</view>
							<view class='marleft' v-if="checkPermi(['comList:applyDev'])">
								<xbutton size="medium" round='25rpx' padding='0rpx 20rpx' @click="application(item)">
									应用到设备</xbutton>
							</view>
						</view>
					</view>
				</block>
				<u-loadmore :status="loadmoreStatus" />
			</view>

			<view style="margin-top: 120rpx;" v-else>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<view class="btn safe-bottom">
			<xbutton width="340rpx" round="88rpx" size="large" @click="saveDevComList">保存设备商品为清单</xbutton>
			<xbutton width="340rpx" round="88rpx" size="large" @click="addcommoditylist">新增商品清单
			</xbutton>
		</view>

		<xpopup :show="addcommoditylistShow" @open="open" @close="cancel" @confirm="submit" :title="popTitle">
			<view class="addcommoditylist-container">

				<!-- 	<u--input class='martop' clearable suffixIcon="arrow-down" v-model="equipmentTypename"
					suffixIconStyle="color: #909399" placeholder="设备类型" border="surround" @focus="actionSheetShow=true">
				</u--input> -->
				<view class='martop'>
					<u--input placeholder="请输入商品清单名称" v-model="name" border="surround"></u--input>
				</view>
			</view>
		</xpopup>

		<!-- 选择左右门 -->
		<u-modal :show="doorShow" @confirm="right" :showCancelButton="true" confirmText="当前设备" cancelText="其他设备"
			:closeOnClickOverlay="true" @close="doorShow=false" confirmColor="#2C6FF3" cancelColor="#2C6FF3"
			@cancel="left" title="提示" content="请选择应用到当前设备还是其他设备~">
		</u-modal>

		<!-- <u-action-sheet :show="actionSheetShow" :actions="equipmentTypeList" title="请选择设备类型"
			@close="actionSheetShow = false" @select="actionsheetSelect($event)"></u-action-sheet> -->
	</view>
</template>

<script>
	import request from '@/utils/request'
	import {
		page,
		save,
		update,
		saveGoodsDevice,
		del,
		backUpDeviceGoods
	} from "@/api/commoditylist/commoditylist.js"
	import {
		detail
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				fullHeight: 0,
				commodityList: [], //商品清单
				name: '',
				loadmoreStatus: 'loadmore',
				popTitle: '新增清单',
				type: '',
				equipmentTypename: '',
				page: 1, //当前分页
				size: 10, //分页数据条数
				deviceType: '',
				actionSheetShow: false,
				addcommoditylistShow: false,

				deviceId: null,
				algorithmId: null,
				deviceName: '',

				equipmentTypeList: [],
				doorShow: false,
				modelId: null
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 70)
			if (o.id) {
				this.deviceId = o.id
				await this.getDeviceType()
			}
			this.reset()
			this.getpage()
		},

		methods: {
			getDeviceType() {
				return new Promise((resolve, reject) => {
					detail({
						deviceId: this.deviceId,
						isSysinfo: false,
						isStatus: false,
						isRegister: false
					}).then(res => {
						this.deviceType = res.data.deviceType
						this.deviceName = res.data.deviceName
						this.algorithmId = res.data.algorithmId
						resolve(res)
					}).catch(err => {
						resolve(err)
					})
				})
			},

			//分页查询
			getpage() {
				page({
					page: {
						current: this.page,
						size: this.size,
					},
					deviceType: this.deviceType,
					algorithmId: this.algorithmId
				}).then(res => {
					let dicts = res.data.dicts[0]
					for (let key in dicts) {
						let item = dicts[key]
						this.equipmentTypeList.push(item)
					}
					let newList = res.data.records
					newList.forEach(i => {
						this.equipmentTypeList.forEach(j => {
							if (i.deviceType == j.value) {
								i.deviceTypeName = j.msg
							}
						})
					})

					this.commodityList = this.commodityList.concat(newList)
					console.log(this.commodityList)
					if (newList.length < 10) {
						this.loadmoreStatus = 'nomore'
					} else {
						this.loadmoreStatus = 'loadmore'
					}
				})
			},

			reset() {
				this.page = 1
				this.commodityList = []
			},

			loadMore(e) {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},


			open() {
				this.addcommoditylistShow = true
			},

			//新增商品清单
			addcommoditylist() {
				this.popTitle = '新增清单'
				this.addcommoditylistShow = true
			},

			actionsheetSelect(e) {
				this.equipmentTypename = e.name
				this.deviceType = e.deviceType
			},
			cancel() {
				this.addcommoditylistShow = false
			},
			deleted(item) {
				uni.showModal({
					title: '提示',
					content: '是否确认删除',
					success: res => {
						if (res.confirm) {
							del({
								ids: [item.modelId]
							}).then(res => {
								if (res.code == 200) {
									this.addcommoditylistShow = false
									this.reset()
									this.getpage()
									setTimeout(() => {
										this.$modal.msg('删除成功~')
									}, 500)
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},

			modify(item) {
				this.popTitle = '修改清单'
				this.name = item.name
				this.modelId = item.modelId
				this.addcommoditylistShow = true
			},

			saveDevComList() {
				backUpDeviceGoods({
					deviceId: this.deviceId
				}).then(res => {
					this.$modal.msg('保存成功~')
					setTimeout(() => {
						this.reset()
						this.getpage()
					}, 1000)
				})
			},

			// 新增商品清单
			submit() {
				if (!this.name) {
					uni.$u.toast('请填写清单名称')
					return;
				}
				if (this.popTitle == '新增清单') {
					save({
						name: this.name, //清单名称
						deviceType: this.deviceType, //设备类型
						algorithmId: this.algorithmId, //设备算法
					}).then(res => {
						if (res.code == 200) {
							this.addcommoditylistShow = false
							this.reset()
							this.getpage()
							setTimeout(() => {
								this.$modal.msg('新增成功~')
							}, 500)
						}
					})
				}
				if (this.popTitle == '修改清单') {
					update({
						modelId: this.modelId, //id
						name: this.name, //清单名称
						deviceType: this.deviceType //设备类型
					}).then(res => {
						if (res.code == 200) {
							this.addcommoditylistShow = false
							this.reset()
							this.getpage()
							setTimeout(() => {
								this.$modal.msg('修改成功~')
							}, 500)
						}
					})
				}

			},

			//清单关联商品
			relatedgoods(item) {
				this.$tab.navigateTo(
					`comListEdit?modelId=${item.modelId}&title=${item.name}&algorithmId=${this.algorithmId}`)
			},

			//清单应用到设备
			application(item) {
				this.modelId = item.modelId
				this.doorShow = true
			},

			// 应用到其他设备
			left() {
				this.doorShow = false
				this.$tab.navigateTo(`chooseDevice?modelId=${this.modelId}&algorithmId=${this.algorithmId}`)
			},

			// 应用到当前设备
			right() {
				saveGoodsDevice({
					deviceIds: [this.deviceId], //设备信息id
					modelId: this.modelId //商品清单ID
				}).then(res => {
					this.$modal.msg('应用成功!')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000)
				}).catch(err => {
					this.doorShow = false
				})
			}
		}
	}
</script>
<style lang="scss" scoped>
	.container {
		.martop {
			margin-top: 20rpx;
		}

		.marleft {
			margin-left: 20rpx;
		}

		.title {
			margin: 20rpx;
		}

		.scroll-content {
			overflow: hidden;
			padding-bottom: calc(120rpx + env(safe-area-inset-bottom) / 2);
		}

		.commoditylist-container {
			margin: 0 13rpx;

			&+.commoditylist-container {
				margin-top: 20rpx;
			}

			.commoditylist-content {
				padding: 0 20rpx 20rpx;

				>view {
					line-height: 50rpx;
					color: #555;
				}

				.list-name {
					font-size: 32rpx;
					line-height: 60rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 20rpx;
				}
			}

			.btn-box {}
		}

		.btn {
			width: 100%;
			position: fixed;
			bottom: 24rpx;
			left: 0;
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-between;
			padding: 24rpx 24rpx 0;
		}

		.addcommoditylist-container {
			padding: 20rpx 30rpx 40rpx;
		}
	}
</style>