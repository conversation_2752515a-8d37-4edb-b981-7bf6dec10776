<script>
import config from "./config";
import { getToken, setToken, removeToken } from "@/utils/auth";

export default {
  globalData: {
    isManagerUser: false, //是否为平台用户
    defaultPwd: false, //是否默认密码
    isOnShow: true, //是否需要出发小程序onShow事件。在调用相机、地图等api时会触发onShow事件，但此时不需要触发onShow事件
  },
  onLaunch(options) {
    // options.referrerInfo.extraData.type为0顾客端进入，为1平台小程序进入
    // options.scene != 1037
    if (options.scene != 1037 || options.referrerInfo.extraData.type != "1") {
      //非管理平台进入
      // 处理进入小程序参数
      this.elseEnter(options);
    }
  },
  onShow(options) {
    console.log("options=========" + JSON.stringify(options));
    // 是否有版本更新
    this.isUpdate();

    //管理平台进入
    if (options.scene == 1037 && options.referrerInfo.extraData.type == "1") {
      //管理平台进入
      if (this.globalData.isOnShow) {
        //非相机、地图调用需要触发onShow
        console.log("管理平台小程序跳转！");
        console.log(
          "管理平台传入参数" + JSON.stringify(options.referrerInfo.extraData)
        );
        this.globalData.isManagerUser = true; //标记为管理平台用户
        // 处理进入小程序参数
        this.manaEnter(options);
      } else {
        //在调用相机、地图等api时不需要触发onShow事件
        this.globalData.isManagerUser = true;
      }
    } else {
      //其他方式进入,之前登录过管理平台用户登录过，则先清除管理平台登录记录，重新登录
      if (this.globalData.isManagerUser) {
        this.globalData.isManagerUser = false;
        this.$store.dispatch("LogOut").then((res) => {
          this.$tab.reLaunch("/pages/login");
        });
      }
    }
  },
  methods: {
    /**
     * 管理小程序进入
     * @param {Object} options
     */
    manaEnter(options) {
      console.log("管理平台小程序进入=========", JSON.stringify(options));
      //平台端管理小程序跳转进入，免登录
      this.$store.dispatch("manageLogin", options);
      //初始化小程序
      this.initApp();
    },

    /**
     * 其他方式进入小程序
     * @param {Object} options
     */
    elseEnter(options) {
      this.initApp();
    },

    // 初始化应用
    initApp(options) {
      console.log("初始化应用开始~");
      // 初始化应用配置
      this.initConfig();
      // 免登录
      this.checkLogin();
    },

    initConfig() {
      this.globalData.config = config;
    },
    async checkLogin() {
      console.log("检测是否登录开始！");
      if (getToken()) {
        console.log("已登录！");
        await this.$store
          .dispatch("GetPermis")
          .then((res) => {
            //判断用户是否有任一菜单权限
            if (res && res.length > 0) {
              if (process.env.NODE_ENV == "production") {
                this.$tab.reLaunch("/pages/globalPages/home");
              }
            } else {
              this.$modal.msg("该用户无权限~");
            }
          })
          .catch((err) => {});
      } else {
        console.log("未登录！");
      }
    },

    isUpdate() {
      console.log("检测更新开始~");
      const updateManager = wx.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        // 请求完新版本信息的回调
        console.log(res.hasUpdate);
      });

      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: "更新提示",
          content: "新版本已经准备好，是否重启应用？",
          success(res) {
            if (res.confirm) {
              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
              updateManager.applyUpdate();
            }
          },
        });
      });

      updateManager.onUpdateFailed(function () {
        // 新版本下载失败
      });
    },
  },
};
</script>

<style lang="scss">
@import "@/uni_modules/uview-ui/index.scss";
@import "@/static/scss/index.scss";
</style>
