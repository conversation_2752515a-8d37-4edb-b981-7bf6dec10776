<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="中奖名单"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search animation placeholder="请输入" :clearabled="true" v-model="keyword" :showAction="false"
					@search="search"></u-search>
			</view>
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view v-if="list.length>0" class="content">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="list-wrap">
							<view class="item-row">
								<text class="item-label">活动名称：</text><text
									class="item-value">{{item.lotteryTitle}}</text>
							</view>
							<view class="item-row">
								<text class="item-label">手机号：</text><text class="item-value">{{item.tel}}</text>
							</view>
							<view class="item-row">
								<text class="item-label">奖项：</text><text
									class="item-value">{{item.lotteryAwardsName}}</text>
							</view>
							<view class="item-row">
								<text class="item-label">奖品：</text><text class="item-value">{{item.lotteryPrize}}</text>
							</view>
							<view class="item-row">
								<text class="item-label">中奖时间：</text><text class="item-value">{{item.createTime}}</text>
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

			<view class="btn">
				<xbutton size="large" @click="exp">
					导出
				</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		memberLottery
	} from "@/api/lottery.js"
	export default {
		data() {
			return {
				list: [],
				loadmoreStatus: 'loadmore',
				page: 1, //当前分页
				size: 10, //分页数据条数
				fullHeight: 0,

				tabList: [{
						name: '进行中'
					},
					{
						name: '已禁用'
					},
					{
						name: '已结束'
					},
				],
				current: 0,
				status: '进行中',
				type: 'cx',
				typeList: [{
						name: '促销',
						value: 'cx'
					},
					{
						name: '特价',
						value: 'tj'
					},
					{
						name: '买赠',
						value: 'mz'
					}
				],
				keyword: null,
				id: null
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)
			this.id = o.id;
			this.search()
		},

		methods: {

			menuTab(type) {
				this.type = type
				this.search()
			},

			switchBtn(item) {
				let _this = this
				this.$modal.oldConfirm(`是否确定删除？`).then(res => {
					if (this.type == 'mz') {
						sendStatus({
							id: item.id,
							enable_status: status
						}).then(res => {
							_this.$modal.msg('成功~')
							item.enable_status = status
							_this.$forceUpdate()

						})
					} else {
						proStatus({
							id: item.id,
							enable_status: status
						}).then(res => {
							_this.$modal.msg('成功~')
							item.enable_status = status
							_this.$forceUpdate()

						})
					}
				})
			},

			edit(item) {
				let url = ''
				console.log(this.type)
				switch (this.type) {
					case 'cx':
						url = `/pages/market/addPro?id=${item.id}`
						break;
					case 'tj':
						url = `/pages/market/addSpe?id=${item.id}`
						break;
					case 'mz':
						url = `/pages/market/addSend?id=${item.id}`
						break;
					default:
						break;
				}
				this.$tab.navigateTo(url)
			},

			tabClick(e) {
				console.log(e)
				this.current = e.index
				this.status = e.name
				this.reset()
				this.getpage()
			},

			//获取订单列表
			getpage() {
				memberLottery({
					page: {
						current: this.page,
						size: 10
					},
					tel: this.keyword,
					lotteryId: this.id
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			exp() {

			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;

		.content {
			overflow: hidden;

			.search {
				background: #fff;
				padding: 20rpx;
			}

			.menu-item {
				text-align: center;
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-top: 24rpx;

				>image {
					width: 57rpx;
					height: 57rpx;
				}

				>view {
					color: #333;
					font-size: 24rpx;
					line-height: 24rpx;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 16rpx;
				}
			}

			.empty {
				padding-top: 40%;
			}

			.martop {
				margin-top: 20rpx;
			}

			.tab-wrap {
				padding: 12rpx 0;

				.tab {
					// width: 40%;
					width: 70%;
				}
			}

			.tab-list {
				width: 100%;
				background-color: #fff;
				padding: 24rpx 26rpx 0;

				.tab-item {
					padding: 0 20rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					line-height: 62rpx;
					margin-bottom: 14rpx;
					text-align: center;
					margin-right: 12rpx;

					&.tab-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}
			}

			.marleft {
				margin-left: 10rpx;
			}

			.scrollview {
				.content {
					padding-bottom: 90rpx;
					overflow: hidden;
				}
			}

			.list-wrap {
				margin: 13rpx 13rpx 0;
				padding: 24rpx 30rpx 40rpx;
				border-radius: 14rpx;
				background-color: #fff;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

				.item-row {
					font-size: 26rpx;
					margin-top: 16rpx;
				}
			}

			.btn {
				width: 724rpx;
				position: fixed;
				left: 13rpx;
				bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
				border-radius: 88rpx;
				overflow: hidden;

				.btn-wrap {
					text-align: center;
					font-size: 36rpx;
					font-weight: 500;
				}
			}
		}
	}
</style>