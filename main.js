import Vue from 'vue'
import App from './App'
import store from './store' // store
import plugins from './plugins' // plugins
import './permission' // permission
import {
	checkPermi,
	checkPermis
} from '@/utils/permission.js'

import getdict from './utils/getDict.js'
Vue.use(plugins)

// 引入:uView-UI
import uView from '@/uni_modules/uview-ui';
Vue.use(uView);

import share from './utils/share.js'
Vue.mixin(share)

Vue.prototype.getDict = getdict;
Vue.prototype.$store = store
Vue.prototype.checkPermi = checkPermi
Vue.prototype.checkPermis = checkPermis

Vue.config.productionTip = false

App.mpType = 'app'

const app = new Vue({
	...App
})

app.$mount()