<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="选择商品"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search animation placeholder="商品搜索" v-model="keyword" :showAction="false"
					@search="search"></u-search>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view class="list">
					<view v-if="commList.length>0">
						<block v-for="(item, index) in commList" :key="item.id">
							<view class="thumb-box" @click.stop="commItemSelect(item)">
								<view>
									<image class="select-img"
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
										mode="widthFix" v-show="item.checked"></image>
									<image class="select-img"
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
										mode="widthFix" v-show="!item.checked"></image>
								</view>
								<view class="check-content">
									<view class="comm-img" @click.stop="$xy.previewImg(item.goodsCover)">
										<u--image width="130rpx" height="130rpx" :src="item.goodsCover" mode="aspectFit"
											:lazy-load="true"></u--image>
									</view>
									<view class="comm-main">
										<view>
											{{item.goodsName}}
										</view>
										<!-- 	<view>
												条形码：{{item.barcode}}
											</view> -->
										<view>
											商品ID：{{item.id}}
										</view>
										<!-- 	<view>
											算法SKUID：{{item.skuId}}
										</view> -->
										<!-- <view class="c-cx">
												促销活动：
											</view> -->
										<view class="c-pri">
											价格：<text>￥{{$xy.delMoney(item.price)}}</text>
										</view>
									</view>
								</view>
							</view>
						</block>
					</view>
					<view class="empty" v-else>
						<u-empty></u-empty>
					</view>
				</view>
			</scroll-view>
		</view>

		<view class="btn flex justify-between align-center">
			<view class="all-select flex align-center" @click="selectAll">
				<view class="select-line-img">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
						mode="widthFix" v-show="allChecked"></image>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
						mode="widthFix" v-show="!allChecked"></image>
				</view>
				<view>
					全选
				</view>
			</view>
			<view class="sure-btn">
				<xbutton width="180rpx" size="large" round="50rpx" @click="sure">选择商品</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		deviceGoods
	} from "@/api/commodity/goods.js"

	import {
		ownerGoodsList
	} from "@/api/commodity/mercGoods.js"

	export default {
		data() {
			return {
				keyword: '',
				commList: [],
				goods_list: [],
				page: 1, //商品分页
				size: 10,
				status: 'loadmore', //加载更多
				fullHeight: 0,
				allChecked: false
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.content')

			this.goods_list = JSON.parse(uni.getStorageSync('refundList')) ? JSON.parse(uni.getStorageSync(
				'refundList')) : [];
			this.getList()
		},

		methods: {
			search(val) {
				this.reset()
				this.getList()
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.commList = [];
			},

			// 商品选中状态改变
			commItemSelect(item) {
				item.checked = !item.checked
				this.updateSto(item)
				this.allChecked = this.isAllChecked(this.commList)
				this.$forceUpdate()
			},

			updateSto(item) {
				console.log(item)
				if (item.checked) {
					this.goods_list.push({
						id: item.goodsId.toString(),
						goodsName: item.goodsName,
						sellPrice: item.price,
						totalNumber: 1,
						type: '0',
						selected : true
					})
				} else {
					this.goods_list.splice(this.goods_list.findIndex(i => i.id == item.goodsId.toString()), 1)
				}
			},

			async selectAll() {
				let status = this.allChecked
				if (!this.allChecked) {
					this.reset()
					this.size = 10000
					await this.getList()
				}
				for (var i = 0; i < this.commList.length; i++) {
					let item = this.commList[i]
					if (item.checked == status) {
						this.commItemSelect(item)
					}
				}
				this.$forceUpdate()
			},

			isAllChecked(list) {
				if (list && list.length > 0) {
					return list.every(i => i.checked)
				} else {
					return false
				}
			},

			sure() {
				uni.$emit('addGoods',this.goods_list)
				// let url = ''
				// switch (this.type) {
				// 	case 1:
				// 		url = `/pages/market/addPro`
				// 		break;
				// 	case 2:
				// 		url = `/pages/market/addSpe`
				// 		break;
				// 	default:
				// 		break;
				// }
				this.$tab.navigateBack()
			},

			getList() {
				return new Promise((resolve, reject) => {
					let params = {
						page: {
							current: this.page,
							size: this.size
						},
						// algorithmId: this.algoType,
						status: '1', //0下架1上架
						keyword: this.keyword
					}
					ownerGoodsList(params).then(res => {
						let data = res.data.records;
						for (let i = 0; i < data.length; i++) {
							let item = data[i];
							if (this.allChecked) {
								item.checked = true
							} else {
								let goods_ids = []
								if (this.goods_list.length > 0) {
									goods_ids = this.goods_list.map(i => {
										return i.id
									})
								}
								if (goods_ids.indexOf(item.goodsId.toString()) != -1 || goods_ids
									.indexOf(Number(item.goodsId)) != -1) {
									item.checked = true
								} else {
									item.checked = false
								}
							}
						}
						if (data.length < 10) {
							this.status = "nomore"
						} else {
							this.status = "loadmore"
						}
						this.commList = this.commList.concat(data)
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			// 触底加载
			scrolltolower() {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.content {
			padding-bottom: 88rpx;

			.search {
				padding: 24rpx 24rpx;
				background-color: #fff;
			}

			.list {
				width: 100%;
				padding: 12rpx 24rpx 100rpx;

				.thumb-box {
					margin-bottom: 12rpx;
					border-bottom: 1rpx solid #f4f4f4;
					display: flex;
					flex-flow: row nowrap;
					padding: 12rpx 12rpx;
					align-items: center;
					background-color: #fff;
					border-radius: 12rpx;
				}

				.select-img {
					width: 34rpx;
					height: 34rpx;
				}

				.check-content {
					width: 100%;
					display: flex;
					flex-direction: row;
					align-items: center;
					padding-left: 12rpx;
					position: relative;

					.comm-img {
						width: 130rpx;
						height: 130rpx;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: space-around;
						border-radius: 8rpx;
						overflow: hidden;

						image {
							width: 100%;
						}
					}

					.comm-main {
						box-sizing: border-box;
						padding-left: 18rpx;
						color: #999;

						>view {
							padding: 8rpx 0;
							// width: 330rpx;
						}

						>view:nth-child(1) {
							font-size: 28rpx;
							font-weight: bold;
							color: #333;
							padding: 12rpx 0 8rpx 0;
						}

						>view:nth-child(2) {
							width: 450rpx;
							font-size: 26rpx;
						}

						>view:nth-child(3) {
							width: 450rpx;
							font-size: 26rpx;
						}

						>view:nth-child(4) {
							width: 450rpx;
							font-size: 26rpx;
							padding: 0;
							line-height: 40rpx;
						}

						>.c-cx {
							font-size: 26rpx;
						}

						>.c-pri {
							font-size: 26rpx;

							text {
								font-weight: bold;
								color: red;
								font-size: 30rpx;
							}

							>view {
								display: inline-block;
								font-size: 26rpx;
								color: #2C6FF3;
								text-decoration: underline;
								margin-left: 12rpx;
							}
						}
					}

					.status {
						width: 120rpx;
						height: 120rpx;
						box-sizing: border-box;
						border-radius: 120rpx;
						// border: 6rpx solid #2C6FF3;
						text-align: center;
						display: flex;
						flex-flow: column;
						justify-content: space-around;
						align-items: center;
						position: absolute;
						right: 12rpx;
						top: -12rpx;

						.s-name {
							font-size: 24rpx;
							padding-top: 16rpx;
							font-weight: bold;
						}

						.s-num {
							font-size: 28rpx;
							padding-bottom: 20rpx;
						}
					}

					.sale {
						position: absolute;
						right: 12rpx;
						bottom: 12rpx;
						text-align: center;
						color: #999;

						>view:nth-child(1) {
							font-size: 24rpx;
						}

						>view:nth-child(2) {
							font-size: 32rpx;
							color: #2C6FF3;
							font-weight: bold;
						}
					}
				}
			}
		}

		.empty {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.refresh {
			width: 80rpx;
			height: 80rpx;
			border-radius: 80rpx;
			text-align: center;
			line-height: 80rpx;
			position: fixed;
			right: 24rpx;
			bottom: 230rpx;
			background-color: #999;
			color: #fff;
			opacity: .8;
		}

		.btn {
			background-color: #fff;
			padding: 24rpx;
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;

			.all-select {
				padding: 20rpx 0;

				.select-line-img {
					width: 34rpx;
					height: 34rpx;
					margin-right: 28rpx;

					>image {
						width: 34rpx;
						height: 34rpx;
					}
				}
			}
		}

		.pop-content {
			padding: 24rpx;
		}
	}
</style>
