<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="活动列表"></u-navbar>
		<view class="content">
			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view v-if="list.length>0">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="list-content">
							<view class="name">{{item.title}}</view>
							<view class="item-row">
								<text class="item-label">设备：</text><text class="item-value"
									v-if="item.deviceList&&item.deviceList.length>0">{{item.deviceList.map(i=>i.deviceName)}}</text>
								<text class="item-value" v-else>全部设备</text>
							</view>
							<view class="item-row">
								<text class="item-label">开始时间：</text><text class="item-value">{{item.startTime}}</text>
							</view>
							<view class="item-row">
								<text class="item-label">结束时间：</text><text class="item-value">{{item.endTime}}</text>
							</view>
							<view class="item-row">
								<text class="item-label">模式：</text><text
									class="item-value">{{item.lotteryTypeName}}</text>
							</view>
							<view class="set flex justify-end">
								<view style="margin-right: 12rpx;">
									<xbutton round='88rpx' width="124rpx" bgColor="#fff" color="#2C6FF3"
										borderColor="#2C6FF3"
										@click="$tab.navigateTo(`/pages/lottery/winnerRec?id=${item.id}`)">
										中奖名单
									</xbutton>
								</view>
								<!-- <view style="margin-right: 12rpx;">
									<xbutton round='88rpx' width="114rpx" bgColor="#fff" color="#2C6FF3"
										borderColor="#2C6FF3" @click="del(item)">
										删除
									</xbutton>
								</view> -->
								<view style="margin-right: 12rpx;">
									<xbutton round='88rpx' width="124rpx" bgColor="#fff" color="#2C6FF3"
										borderColor="#2C6FF3" @click="$tab.navigateTo(`/pages/lottery/setPrize?id=${item.id}`)">
										奖品编辑
									</xbutton>
								</view>
								<view style="margin-right: 12rpx;" v-if="status!='已结束'">
									<xbutton round='88rpx' width="114rpx" bgColor="#fff" color="#2C6FF3"
										borderColor="#2C6FF3" @click="switchBtn(item)">
										{{item.status?'禁用':'启用'}}
									</xbutton>
								</view>
								<view>
									<xbutton width="114rpx" round="88rpx"
										@click="$tab.navigateTo(`/pages/lottery/add?id=${item.id}`)">修改</xbutton>
								</view>
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

			<view class="btn">
				<xbutton size="large" @click="add">
					添加活动
				</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		page,
		saveOrUpdate
	} from "@/api/lottery.js"
	export default {
		data() {
			return {
				list: [],
				loadmoreStatus: 'loadmore',
				page: 1, //当前分页
				size: 10, //分页数据条数
				fullHeight: 0,

				tabList: [{
						name: '进行中'
					},
					{
						name: '未开始'
					},
					{
						name: '已禁用'
					},
					{
						name: '已结束'
					},
				],
				current: 0,
				status: '进行中',
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)
			this.search()
			
			uni.$on('updateList', (data) => {
				this.search()
			})
		},

		methods: {
			switchBtn(item) {
				let _this = this
				let status = item.status ? '禁用' : '启用'
				this.$modal.oldConfirm(`是否确定${status}活动`).then(res => {
					saveOrUpdate({
						id: item.id,
						status: status == '启用'
					}).then(res => {
						_this.$modal.msg('成功~')
						item.status = !item.status
						_this.$forceUpdate()
					})
				})
			},

			tabClick(e) {
				console.log(e)
				this.current = e.index
				this.status = e.name
				this.reset()
				this.getpage()
			},

			//获取订单列表
			getpage() {
				page({
					page: {
						current: this.page,
						size: 10
					},
					lotteryStatus: this.status
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			add() {
				this.$tab.navigateTo('/pages/lottery/add')
			}
		},
		
		onUnload() {
			uni.$off('updateList')
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;

		.content {
			overflow: hidden;

			.empty {
				padding-top: 40%;
			}

			.martop {
				margin-top: 20rpx;
			}

			.tab-wrap {
				padding: 12rpx 0;

				.tab {}
			}

			.scrollview {
				.content {
					padding-bottom: 90rpx;
					overflow: hidden;
				}
			}

			.list-content {
				position: relative;
				margin: 13rpx 13rpx 0;
				padding: 24rpx 30rpx 40rpx;
				border-radius: 14rpx;
				background-color: #fff;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

				.name {
					font-size: 32rpx;
					font-weight: bold;
				}

				.item-row {
					font-size: 28rpx;
					margin-top: 16rpx;
				}

				.set {
					margin-top: 20rpx;
				}
			}

			.btn {
				width: 724rpx;
				position: fixed;
				left: 13rpx;
				bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
				border-radius: 88rpx;
				overflow: hidden;

				.btn-wrap {
					text-align: center;
					font-size: 36rpx;
					font-weight: 500;
				}
			}
		}
	}
</style>