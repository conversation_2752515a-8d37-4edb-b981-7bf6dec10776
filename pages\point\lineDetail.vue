<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="lineName"></u-navbar>
		<view class="content">
			<view class="point">
				<!-- <view class="area-pix">
					<view class="point-name">
						{{tagNamePicker}}
					</view>
					<xbutton width="140rpx" @click="chooseTag">选择标签</xbutton>
				</view> -->
				<view class="xy-card point-item" v-for="(item,index) in pointList" :key="item.id">
					<view class="area-line">
						{{item.placeName}}
						<view>{{item.lineName}}</view>
					</view>
					<view class="point-content">
						<view class="point-item-a">
							<view>标签：</view>
							<view>{{item.tagNames}}</view>
						</view>
						<view class="point-item-a">
							<view>场景：</view>
							<view>{{item.sceneNames}}</view>
						</view>
					</view>
					<view class="point-btn">
						<xbutton width="140rpx" bgColor="red" color="#fff" @click="delPoint(item)">删除
						</xbutton>
						<xbutton width="140rpx" style="margin-left:12rpx;" @click="add('editPoint',item)">编辑
						</xbutton>
						<!-- <xbutton width="140rpx">上传坐标</xbutton> -->
					</view>
				</view>
				<view class="empty">
					<u-empty v-if="pointList.length==0"></u-empty>
				</view>
				<u-loadmore :status="status" v-if="pointList.length>=1" />
				<view class="btn">
					<xbutton size="large" @click="add('addPoint')">新增点位</xbutton>
				</view>
			</view>

			<!-- 弹框 -->
			<xpopup :show="popShow" @close="close" @confirm="submit" showBtn :title="title">
				<!-- 删除区域/线路/点位 -->
				<view class="del-popup-content" v-if="title=='删除点位'">
					确认删除？
				</view>
				<!-- 点位新增 -->
				<!-- 	<view class="popup-content" v-if="title=='新增点位'">
					<view>点位名称：</view>
					<input type="text" v-model="pointName" />
				</view> -->
				<!-- 编辑点位 -->
				<view class="popup-content edit-point" v-if="title=='新增点位'||title=='编辑点位'">
					<view class="edit-point-item" @click="chooseScene">
						<view>场景：</view>
						<u--input type="text" placeholder="多个场景逗号隔开" border="surround" readonly
							v-model="pointForm.sceneNames"></u--input>
					</view>
					<view class="edit-point-item">
						<view>自定义标签：</view>
						<u--input type="text" placeholder="多个标签逗号隔开" border="surround"
							v-model="pointForm.tagNames"></u--input>
					</view>
					<view class="edit-point-item">
						<view>点位名称：</view>
						<u--input type="text" placeholder="请输入点位名称" border="surround"
							v-model="pointForm.placeName"></u--input>
					</view>
				</view>

			</xpopup>
			
			<!-- 商户选择 -->
			<xtree ref="tkitree" :multiple="true" :range="sceneTree" :selectParent="true" rangeKey="name" confirmColor="#2C6FF3"
				@confirm="sceneSubmit"></xtree>
		</view>
	</view>
</template>

<script>
	import lotusAddress from "./components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";
	import {
		pointPage,
		pointSave,
		pointDel,
		sceneTree
	} from "@/api/point/point"
	export default {
		components: {
			lotusAddress
		},
		data() {
			return {
				tabList: [{
						name: '区域'
					},
					{
						name: '线路'
					},
					{
						name: '点位'
					}
				],
				height: "0",

				flod: false, //折叠/打开
				list: [],

				popShow: false, //新增线路弹框

				pointName: '', //点位名称
				id: null, //点选节点id

				page: 1, //当前分页
				size: 10, //分页数据条数
				pointList: [], //点位列表
				noMore: false, //没有更多数据
				status: 'loadmore', //加载更多

				lineNamePicker: '请选择线路', //线路选择
				tagNamePicker: '请选择标签', //标签选择
				lineOptions: [], //线路options
				tagOptions: [],
				columns: [], //picker选项


				lineIdPicker: undefined, //picker选中线路id
				tagIdPicker: undefined, //picker选中tag id
				pickerType: undefined, //picker选择器类型

				pointForm: { //新增点位
					placeName: undefined,
					sceneNames: undefined,
					tagNames: undefined,
				},
				
				lineId:null,
				lineName:null,
				title:'',
				sceneTree:[]
			}
		},

		onLoad(o) {
			this.lineId=o.id;
			this.lineName=o.lineName;
			this.getSceneTree()
			this.getPointList()
		},

		methods: {
			getSceneTree(){
				sceneTree({}).then(res => {
					this.sceneTree=res.data
				})
			},
			
			chooseScene(){
				this.$refs.tkitree._show();
			},
			
			sceneSubmit(e) {
				if(e.length>0){
					let sceneNames=e.map(i=>i.name)
					this.pointForm.sceneNames=sceneNames.join(',')
				}
			},
			
			//重置点位分页请求参数
			reset() {
				this.page = 1;
				this.pointList = [];
				this.noMore = false;
				this.status = "loadmore";
			},

			//获取点位分页
			getPointList() {
				pointPage({
					page: {
						current: this.page,
						size: this.size,
					},
					lineId: this.lineId
				}).then(res => {
					let data = res.data.records;
					this.pointList = this.pointList.concat(data)
					if (data && data.length < 10) {
						this.status = "nomore";
						this.noMore = true;
					} else {
						this.status = "loading";
					}
				})
			},

			//滚动到底加载更多
			onReachBottom() {
				if (this.noMore) return
				this.page++
				this.getPointList()
			},

			//新增点位
			add(type, item) {
				if (type == 'addPoint') { //新增点位
					this.title = '新增点位'
				} else if (type == 'editPoint') { //新增点位
					this.title = '编辑点位'
					this.pointForm = JSON.parse(JSON.stringify(item))
				}
				this.popShow = true
			},
			
			//删除点位
			delPoint(item) {
				this.title = "删除点位"
				this.id = item.id
				this.popShow = true
			},

			//弹框关闭
			close() {
				this.popShow = false
			},

			//弹框提交
			submit() {
				if (this.title == '删除点位') {
					pointDel({
						id: this.id
					}).then(res => {
						this.$modal.msg('删除成功~')
						this.reset()
						this.getPointList()
					})
				}else{
					this.addOrEditPoint()
				}
				this.close()
				this.id = null
			},

			// 新增编辑点位
			addOrEditPoint() {
				if (!this.pointForm.sceneNames) {
					this.$modal.msg('请填写场景!')
					return
				}
				if (!this.pointForm.tagNames) {
					this.$modal.msg('请填写标签!')
					return
				}
				if (!this.pointForm.placeName) {
					this.$modal.msg('请填入点位名称!')
					return
				}
				let params = {
					...this.pointForm,
					lineId: this.lineId
				}
				pointSave(params).then(res => {
					this.$modal.msg('成功~')
					this.pointForm = {
						placeName: undefined,
						sceneNames: undefined,
						tagNames: undefined,
					}
					this.reset();
					this.getPointList();
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.tab-wrap {
			padding: 0 24rpx;
			background-color: #fff;
		}

		.content {
			// 点位
			.point {
				padding: 24rpx 24rpx 88rpx;
			
				.point-item {
					margin-bottom: 24rpx;
			
					.area-line {
						display: flex;
						flex-flow: row nowrap;
						justify-content: space-between;
					}
			
					.point-content {
						border-radius: 8rpx;
						background-color: rgb(245, 248, 251);
						box-sizing: border-box;
						padding: 24rpx 12rpx;
						font-size: 26rpx;
						margin-top: 18rpx;
						display: flex;
						flex-flow: row wrap;
			
						.point-item-a {
							width: 80%;
							display: flex;
							flex-flow: row nowrap;
							line-height: 50rpx;
						}
					}
			
					.point-btn {
						display: flex;
						flex-direction: row;
						justify-content: flex-end;
						margin-top: 12rpx;
					}
				}
			}
		}
	}

	.empty {
		margin-top: 40%;
	}

	.btn {
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 24rpx;
		padding: 0 24rpx;
	}

	.popup-content {
		padding: 36rpx 24rpx;
		display: flex;
		flex-flow: row nowrap;
		justify-content: flex-start;
		align-items: center; 

		input {
			border: 1rpx solid #999;
			border-radius: 6rpx;
			width: 530rpx;
			padding: 0 24rpx;
		}

		&.edit-point {
			flex-direction: column;
			align-items: flex-start;

			.edit-point-item{
				width: 100%;
				padding-left: 170rpx;
				position: relative;
				&+.edit-point-item{
					margin-top: 12rpx;
				}
				>view:nth-child(1){
					position: absolute;
					left:0;
					top:50%;
					transform: translateY(-50%);
				}
			}
		}
	}

	.del-popup-content {
		padding: 36rpx 24rpx;
		text-align: center;
		font-size: 32rpx;
	}
</style>