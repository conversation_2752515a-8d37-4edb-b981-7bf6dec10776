<template>
  <view class="container">
    <u-navbar
      leftIconColor="#fff"
      titleStyle="color:#fff;fontSize:36rpx;"
      :autoBack="true"
      bgColor="#2C6FF3"
      :placeholder="true"
      title="库存管理"></u-navbar>

    <view class="tab-wrap">
      <view class="tab">
        <u-tabs
          :list="tabList"
          :activeStyle="{
            color: '#333',
            fontWeight: 'bold',
            fontSize: '36rpx',
          }"
          :inactiveStyle="{ fontSize: '32rpx' }"
          :scrollable="false"
          :current="current"
          @click="tabClick"
          lineColor="#2C6FF3">
        </u-tabs>
      </view>
    </view>

    <view class="flex align-center justify-between screen-container">
      <view v-if="checkPermi(['inv:exp'])">
        <xbutton @click="expor">导出</xbutton>
      </view>
      <view class="flex align-center justify-end">
        <view
          class="flex align-center"
          style="margin-right: 36rpx"
          @click="sortClick">
          <view>{{ sortName }}</view>
          <view style="margin-left: 8rpx">
            <u-icon name="arrow-down" color="#999" size="14"></u-icon>
          </view>
        </view>
        <view class="flex align-center justify-center" @tap="screen">
          <view
            class=""
            style="font-size: 28rpx; font-weight: 500; color: #333333"
            >筛选</view
          >
          <image
            src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png"
            style="width: 32rpx; height: 32rpx; margin-left: 12rpx"
            mode="widthFix"></image>
        </view>
      </view>
    </view>

    <scroll-view
      class="scrollview"
      :scroll-with-animation="true"
      scroll-y
      lower-threshold="100"
      :style="{ height: fullHeight }">
      <view v-if="list.length > 0">
        <block v-for="(item, index) in list" :key="item.id">
          <view class="equipment-container" @click="details(item)">
            <view class="flex align-center justify-between">
              <view class="title" v-if="current == 1">
                <view v-if="item.deviceName"
                  >{{ item.deviceName }}<text>({{ item.deviceId }})</text></view
                >
                <view v-else>{{ item.deviceId }}</view>
              </view>
              <view class="title" v-else>{{ item.goodsName }}</view>
              <view>
                <u-icon name="arrow-right" size="14"></u-icon>
              </view>
            </view>

            <view class="flex">
              <view class="order-detail-item" style="color: red">
                <view>缺货：</view>{{ item.lackSum }}
              </view>

              <view class="order-detail-item">
                <view>库存：</view>{{ item.stockSum }}
              </view>

              <view class="order-detail-item">
                <view>容量：</view>{{ item.capacitySum }}
              </view>
            </view>

            <view class="percent flex justify-end align-center">
              <view>缺货占比：</view>
              <view class="progress">
                <u-line-progress
                  activeColor="red"
                  :percentage="item.percent"></u-line-progress>
              </view>
            </view>
          </view>
        </block>
      </view>
      <view v-else class="empty">
        <u-empty mode="data" text="数据为空"></u-empty>
      </view>
    </scroll-view>

    <xpopup
      :show="screenShow"
      @close="close"
      @confirm="sure"
      :showBtn="true"
      title="筛选">
      <view class="popup-container">
        <view class="pop-item-name"> 商品名称: </view>
        <view class="martop">
          <u--input
            placeholder="商品名称"
            v-model="searchQuery.goodsName"
            border="surround"></u--input>
        </view>
        <view class="pop-item-name" style="margin-top: 30rpx">
          机器名称/机器编号:
        </view>
        <scroll-view style="height: 600rpx" scroll-y scroll-with-animation>
          <view class="martop">
            <u-checkbox-group
              v-model="searchQuery.deviceIdList"
              @change="checkboxChange"
              activeColor="#2C6FF3">
              <u-checkbox
                :customStyle="{
                  marginBottom: '14px',
                  marginRight: '24px',
                  width: '300rpx',
                }"
                v-for="(item, index) in checkboxList"
                :key="item.deviceId"
                :label="item.deviceName || item.deviceId"
                :name="item.deviceId">
              </u-checkbox>
            </u-checkbox-group>
          </view>
        </scroll-view>
      </view>
    </xpopup>
    <u-action-sheet
      :show="actionSheetShow"
      :actions="actions"
      :title="title"
      @close="actionSheetShow = false"
      @select="actionsheetSelect($event)"></u-action-sheet>
  </view>
</template>

<script>
import {
  stockByDevice, //按设备分组
  stockByGoods, // 按商品分组
  exportStockByDevice,
  exportStockByGoods,
  simpleDeviceSearchPage,
} from "@/api/replenishment/replenishment.js";

export default {
  data() {
    return {
      sortName: "缺货率从多到少",
      searchQuery: {
        deviceIdList: [],
        goodsName: null,
      },
      screenShow: false,
      list: [],
      loadmoreStatus: "loadmore",
      fullHeight: 0,
      tabList: [
        {
          name: "商品",
        },
        {
          name: "机器",
        },
      ],
      current: 0,

      actionSheetShow: false,
      actions: [
        {
          type: "1",
          name: "缺货率从多到少",
        },
        {
          type: "2",
          name: "缺货率从少到多",
        },
        {
          type: "3",
          name: "缺货数从多到少",
        },
        {
          type: "4",
          name: "缺货数从少到多",
        },
      ],

      checkboxList: [],
    };
  },
  async onLoad() {
    let _this = this;
    _this.fullHeight = await _this.$xy.scrollHeight(_this, ".scrollview");

    this.search();
    this.getDeviceList();
  },
  methods: {
    search() {
      let params = this.delParams();

      if (this.current == 0) {
        //商品
        this.getListA(params);
      } else {
        //机器
        this.getListB(params);
      }
    },

    delParams() {
      let params = {
        deviceIdList: this.searchQuery.deviceIdList,
        goodsName: this.searchQuery.goodsName,
        orderBy: "",
        orderByKey: "",
      };
      switch (this.sortName) {
        case "缺货率从多到少":
          params.orderBy = "desc";
          params.orderByKey = "percent";
          break;
        case "缺货率从少到多":
          params.orderBy = "asc";
          params.orderByKey = "percent";
          break;
        case "缺货数从多到少":
          params.orderBy = "desc";
          params.orderByKey = "lackSum";
          break;
        case "缺货数从少到多":
          params.orderBy = "asc";
          params.orderByKey = "lackSum";
          break;
        default:
          break;
      }
      return params;
    },

    getDeviceList() {
      simpleDeviceSearchPage({
        page: {
          size: -1,
        },
        // searchKey: this.searchKey
      }).then((res) => {
        this.checkboxList = res.data.records;
      });
    },

    //按商品分组
    getListA(params) {
      stockByGoods(params).then((res) => {
        if (res.code == 200) {
          if (res.data && res.data.length > 0) {
            for (var i = 0; i < res.data.length; i++) {
              let item = res.data[i];
              item.id = item.goodsId;
            }
            this.list = res.data;
          } else {
            this.list = [];
          }
        } else {
          this.list = [];
        }
      });
    },

    //按设备分组
    getListB(params) {
      stockByDevice(params).then((res) => {
        if (res.code == 200) {
          if (res.data && res.data.length > 0) {
            for (var i = 0; i < res.data.length; i++) {
              let item = res.data[i];
              item.id = item.deviceId;
            }
            this.list = res.data;
          } else {
            this.list = [];
          }
        } else {
          this.list = [];
        }
      });
    },

    //点击筛选
    screen() {
      this.screenShow = true;
    },

    close() {
      this.screenShow = false;
    },

    confirm(e) {
      this.show = false;
      this.search();
    },

    sortClick() {
      this.actionSheetShow = true;
    },

    actionsheetSelect(e) {
      this.sortName = e.name;
      this.search();
    },

    checkboxChange(e) {
      this.searchQuery.deviceIdList = e;
    },

    sure() {
      this.close();
      this.search();
    },

    // 时间选择
    timeSubmit(e) {
      this.searchQuery.orderDate = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
      this.timeShow = false;
      this.reset();
      this.getpage();
      //改为分页接口获取 this.getCountData()
    },

    tabClick(e) {
      this.current = e.index;
      this.search();
    },

    details(item) {
      let id = this.current == 0 ? item.goodsId : item.deviceId;
      let title =
        this.current == 0
          ? item.goodsName
          : item.deviceName
          ? item.deviceName
          : item.deviceId;
      this.$tab.navigateTo(
        `/pages/replenish/invSearchDetail?type=${this.current}&id=${id}&title=${title}`
      );
    },

    expor() {
      let params = this.delParams();
      if (this.current == 0) {
        exportStockByGoods(params, "库存记录-商品.xlsx");
      } else {
        exportStockByDevice(params, "库存记录-设备.xlsx");
      }
    },
  },
};
</script>
<style scoped lang="scss">
.container {
  ::v-deep .u-checkbox-group {
    flex-flow: row wrap !important;
    justify-content: space-between !important;
  }

  ::v-deep .u-checkbox {
    min-width: 260rpx !important;
  }

  ::v-deep .u-checkbox__icon-wrap {
    margin-left: 20rpx !important;
  }

  .empty {
    margin-top: 40%;
  }

  .martop {
    margin-top: 20rpx;
  }

  .search {
    padding: 24rpx 13rpx;
    background-color: #fff;
  }

  .tab-wrap {
    background-color: #fff;

    .tab {
      // width: 40%;
    }
  }

  .marleft {
    margin-left: 10rpx;
  }

  .scrollview {
    overflow: hidden;
  }

  .screen-container {
    background-color: #fff;
    padding: 30rpx 13rpx;

    > view:nth-child(1) {
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  .equipment-container {
    margin: 13rpx 13rpx 0;
    padding: 12rpx 20rpx 24rpx;
    border-radius: 14rpx;
    background-color: #fff;
    box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
    position: relative;

    .sb-box {
      padding: 24rpx 18rpx;
      background-color: #f5f8fb;
      border-radius: 8rpx;
      margin-top: 12rpx;
    }

    .title {
      line-height: 60rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;

      > text {
        font-size: 24rpx;
        color: #333;
      }
    }

    .order-detail-item {
      font-size: 28rpx;
      margin-top: 12rpx;
      color: #777;
      width: 160rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      > view {
        display: inline-block;
      }
    }

    .percent {
      padding-top: 20rpx;
      color: #777;

      .progress {
        width: 160rpx;
      }
    }
  }

  .popup-container {
    padding: 20rpx;
  }
}
</style>
