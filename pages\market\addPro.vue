<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="促销活动创建"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" :rules="rules" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap">
					<u-form-item label="活动名称">
						<u-input inputAlign="right" v-model="form.name" placeholder="请输入活动名称" border="none"></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<view class="tab-wrap">
						<view class="tab">
							<u-tabs :list="tabList"
								:activeStyle="{color: '#2C6FF3',fontWeight: 'bold',fontSize:'28rpx'}"
								:inactiveStyle="{fontSize:'28rpx'}" :scrollable="false" :current="current"
								@click="tabClick" lineColor="#2C6FF3">
							</u-tabs>
						</view>
					</view>

					<u-form-item label="选择设备" borderBottom>
						<u-radio-group v-model="form.is_all_device" placement="row" @change="actionAllDeviceRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
							</u-radio>
							<u-radio label="指定设备" name="0">
							</u-radio>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="指定设备" borderBottom @click="chooseDev" v-if="form.is_all_device==0">
						<view class="choose-list" v-if="form.device_ids&&form.device_ids.length>0">
							<text v-for="(item,index) in form.device_list" :key="index">
								{{item.device_name}}
								<text v-if="index!=form.device_list.length-1">，</text>
							</text>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择设备</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<u-form-item label="选择商品" borderBottom>
						<u-radio-group v-model="form.is_all_goods" placement="row" @change="actionAllGoodsRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
							</u-radio>
							<u-radio label="指定商品" name="0">
							</u-radio>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="指定商品" borderBottom @click="chooseCom" v-if="form.is_all_goods!=1">
						<view class="choose-list" v-if="form.goods_ids&&form.goods_ids.length>0">
							<text v-for="(item,index) in form.goods_list" :key="index">
								{{item.goods_name}}
								<text v-if="index!=form.goods_list.length-1">，</text>
							</text>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择商品</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<view class="mj-content" v-if="current==0">
						<view class="mj-head flex justify-between align-center">
							<view class="mj-title">
								满减金额(元)
							</view>
							<view class="edit" @click="addMj">
								增加
							</view>
						</view>
						<view class="mj-body">
							<block v-for="(item,index) in form.price" :key="item.id">
								<view class="mj-item flex align-center justify-between">
									<view class="left-input flex align-center">
										<text>满</text><u-input v-model="item.limit" type="digit" placeholder="请输入"
											border="none"></u-input>
									</view>
									<view class="left-input flex align-center">
										<text>减</text><u-input v-model="item.price" type="digit" placeholder="请输入"
											border="none"></u-input>
									</view>
									<view class="right-switch flex align-center">
										<text>循环</text>
										<view><u-switch :disabled="form.price.length>1" size="16"
												v-model="form.price[index].is_loop"></u-switch>
										</view>
									</view>
									<view class="edit del" @click="delMj(index)">
										删除
									</view>
								</view>
							</block>
						</view>
					</view>

					<u-form-item label="折扣" v-else>
						<u-input inputAlign="right" type="digit" v-model="form.price.scalar" placeholder="请输入不大于1的小数"
							border="none">
							<!-- <template slot="suffix">
								<text>折</text>
							</template> -->
						</u-input>
					</u-form-item>

					<!-- <view class="mj-content" v-else>
						<view class="mj-head flex justify-between align-center">
							<view class="mj-title">
								折扣金额(元)
							</view>
							<view class="edit" @click="addZk">
								增加
							</view>
						</view>
						<view class="mj-body">
							<block v-for="(item,index) in form.price" :key="item.id">
								<view class="mj-item flex align-center justify-between">
									<view class="left-input zk-left-input flex align-center">
										<text>满</text><u-input v-model="item.limit" placeholder="请输入"
											border="none"></u-input>
									</view>
									<view class="left-input zk-left-input flex align-center">
										<text>折扣</text><u-input v-model="item.price" placeholder="请输入"
											border="none"></u-input>
									</view>
									<view class="edit del" @click="delZk(index)">
										删除
									</view>
								</view>
							</block>
						</view>
					</view> -->
				</view>

				<view class="section-wrap">
					<u-form-item label="活动日期" borderBottom @click="chooseDate">
						<view style="text-align: right;" v-if="form.start_date">{{form.start_date}}~{{form.end_date}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择活动日期</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<view class="action-time">
						<u-form-item label="活动时间">
							<u-radio-group v-model="form.is_all_day" placement="row" @change="actionTimeRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="全天" name="全天">
								</u-radio>
								<u-radio label="指定时间" name="指定时间">
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<view class="edit action-time-edit" @click="addTime" v-if="form.is_all_day=='指定时间'">
							增加
						</view>
					</view>
					<view class="time-wrap" v-if="form.is_all_day=='指定时间'">
						<view class="time-item flex justify-between align-center" v-for="(item,index) in form.day_time"
							:key="item.id">
							<view class="time-tip">
								指定时间段{{index+1}}
							</view>
							<view class="time-select" @click="timeChange(index)">
								{{item.start_time}}~{{item.end_time}}
							</view>
							<view class="edit action-time-del" @click="delTime(index)">
								删除
							</view>
						</view>
					</view>
				</view>

				<view class="section-wrap">
					<view class="priority">
						<u-form-item label="优先级">
							<u-input inputAlign="right" type="number" v-model="form.weight" placeholder="请输入活动优先级"
								border="none" maxlength="8"></u-input>
						</u-form-item>
						<view class="tips">(数字越大活动优先级越高)</view>
					</view>
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<u-form-item label="会员优先" @click="plusCanChoose">
						<view style="text-align: right;">{{form.plus_can_type}}
						</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<view class="action-time">
						<u-form-item label="能否使用优惠券">
							<u-radio-group v-model="form.can_coupon" placement="row" @change="actionDisRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="可用" name="可用">
								</u-radio>
								<u-radio label="禁用" name="禁用">
								</u-radio>
							</u-radio-group>
						</u-form-item>
					</view>
				</view>

				<view class="section-wrap">
					<view class="action-time">
						<u-form-item label="状态">
							<u-radio-group v-model="form.enable_status" placement="row" @change="actionStatusRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="启用" name="启用">
								</u-radio>
								<u-radio label="禁用" name="禁用">
								</u-radio>
							</u-radio-group>
						</u-form-item>
					</view>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>

		<u-calendar :show="calendarShow" allowSameDay closeOnClickOverlay @close="calendarShow=false"
			:monthNum="calendarMonthNum" mode="range" color="#2C6FF3" @confirm="calendarConfirm"></u-calendar>

		<xtimeslot ref="timeslot" :title="'选择时间段'" @confirm="confirmTime">
		</xtimeslot>

		<u-action-sheet :show="actionsShow" :actions="actions" title="会员优先选择" @close="actionsShow = false"
			@select="actionsSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		addPro,
		proDetail,
		proEdit
	} from "@/api/market/promotion.js"
	//验证规则
	let rulesObj = {
		type: 'string',
		required: true,
		message: '必填项',
		trigger: ['change']
	}

	export default {
		data() {
			return {
				form: {
					name: null,
					device_ids: [],
					goods_ids: [],
					goods_list: [],
					goodsNames: [],
					price: [{
						id: Math.random(),
						limit: null,
						price: null,
						is_loop: false
					}],
					start_date: null,
					end_date: null,
					is_all_day: '全天',
					day_time: [{
						id: Math.random(),
						start_time: "8:00",
						end_time: "20:00"
					}],
					can_coupon: '可用',
					enable_status: '启用',
					weight: 1,
					device_list: [],
					is_all_device: '1',
					is_all_goods: '0',
					plus_can_type: '活动优惠优先',
				},

				rules: {
					'unitName': {
						type: 'string',
						required: true,
						message: '请选择商品规格~',
						trigger: ['change']
					},
				},

				calendarShow: false,
				calendarMonthNum: '3',
				tabList: [{
						name: '满减'
					},
					{
						name: '折扣'
					},
				],
				current: 0,
				timeIndex: 0,

				actions: [{
						name: '会员折扣优先'
					},
					{
						name: '活动优惠优先'
					},
					{
						name: '会员折扣叠加'
					}
				],
				actionsShow: false,
			}
		},

		onLoad(o) {
			this.getMonthNum()
			if (o.id) {
				this.getDetail(o.id)
			}

			uni.$on('updateGoods', (data) => {
				let goods_ids = []
				let goods_list = []
				if (data && data.length > 0) {
					goods_ids = data.map(item => item.goodsId)
					goods_list = data.map(item => ({
						goods_id: item.goodsId,
						goods_name: item.goodsName,
					}))
				}
				this.form.goods_ids = goods_ids
				this.form.goods_list = goods_list
			})

			uni.$on('updateDevices', (data) => {
				let device_ids = []
				let device_list = []
				if (data && data.length > 0) {
					device_ids = data.map(item => item.device_id)
					device_list = data.map(item => ({
						device_id: item.device_id,
						device_name: item.device_name,
					}))
				}
				this.form.device_ids = device_ids
				this.form.device_list = device_list
			})
		},

		methods: {
			getDetail(id) {
				proDetail({
					id: id
				}).then(res => {
					let data = res.data
					let price = null

					if (data.type == '满减') {
						price = []
						for (let key in data.price) {
							let item = data.price[key]
							let obj = {}
							obj = {
								id: Math.random(),
								limit: this.$xy.delMoney(item.limit),
								price: this.$xy.delMoney(item.price),
								is_loop: item.is_loop && item.is_loop == 1
							}
							price.push(obj)
						}
					} else {
						price = {
							scalar: data.price.scalar
						}
					}

					// if (data.price && data.price != null) {
					// 	for (let key in data.price) {
					// 		let item = data.price[key]
					// 		let obj = {}
					// 		if (data.type == '满减') {
					// 			console.log(item.price)
					// 			obj = {
					// 				id: Math.random(),
					// 				limit: this.$xy.delMoney(item.limit),
					// 				price: this.$xy.delMoney(item.price),
					// 				is_loop: item.is_loop && item.is_loop == 1
					// 			}
					// 		} else {
					// 			obj = {
					// 				id: Math.random(),
					// 				limit: this.$xy.delMoney(item.limit),
					// 				price: Number(item.price) * 10
					// 			}
					// 		}
					// 		price.push(obj)
					// 	}
					// } else {
					// 	price = [{
					// 		id: Math.random(),
					// 		limit: null,
					// 		price: null,
					// 		is_loop: false
					// 	}]
					// }

					let goodsNames = []
					let goods_ids = []
					if (!data.is_all_goods && data.goods_list && data.goods_list.length > 0) {
						for (let key in data.goods_list) {
							let item = data.goods_list[key]
							goods_ids.push(item.goods_id)
						}
					}
					let dayTime = [];
					if (!data.is_all_day && data.day_time) {
						for (let key in data.day_time) {
							let item = data.day_time[key]
							let obj = {
								id: Math.random(),
								end_time: item.end_time,
								start_time: item.start_time
							}
							dayTime.push(obj)
						}
					} else {
						dayTime = [{
							id: Math.random(),
							start_time: "8:00",
							end_time: "20:00"
						}]
					}

					let device_ids = []
					if (!data.is_all_device && data.device_list && data.device_list.length > 0) {
						for (let key in data.device_list) {
							let item = data.device_list[key]
							device_ids.push(item.device_id)
						}
					}

					let params = {
						id: data.id,
						name: data.name,
						goods_ids: goods_ids,
						goodsNames: goodsNames,
						device_ids: device_ids,
						price: price,
						start_date: data.start_date,
						end_date: data.end_date,
						is_all_day: data.is_all_day ? '全天' : '指定时间',
						day_time: dayTime,
						can_coupon: data.can_coupon ? '可用' : '禁用',
						enable_status: data.enable_status,
						type: data.type,
						weight: data.weight,
						device_list: data.device_list,
						goods_list: data.goods_list,
						is_all_device: data.is_all_device ? '1' : '0',
						is_all_goods: data.is_all_goods ? '1' : '0',
						plus_can_type: data.plus_can_type
					}
					this.current = data.type == '满减' ? '0' : '1'
					this.form = JSON.parse(JSON.stringify(params))
					console.log('反显数据=======' + JSON.stringify(this.form))
				})
			},
			chooseDev() {
				this.$tab.navigateTo(`/pages/market/chooseDevice?type=1&deviceIds=${JSON.stringify(this.form.device_ids)}`)
			},

			chooseCom() {
				let goodsId = []
				if (this.form.goods_list && this.form.goods_list.length > 0) {
					goodsId = this.form.goods_list.map(item => item.goods_id)
				}
				this.$tab.navigateTo(`/pages/market/chooseGoods?type=1&goodsId=${JSON.stringify(goodsId)}`)
			},

			chooseDate() {
				this.calendarShow = true
			},

			calendarConfirm(e) {
				console.log(e)
				this.form.start_date = e[0];
				this.form.end_date = e.at(-1)
				this.calendarShow = false
			},

			getMonthNum() {
				getDict('calendar_range').then(res => {
					this.calendarMonthNum = Number(res[0].value)
					console.log(this.calendarMonthNum)
				})
			},

			tabClick(e) {
				this.current = e.index
				if (this.current == 0) {
					this.form.price = [{
						id: Math.random(),
						limit: null,
						price: null,
						is_loop: false
					}]
				} else {
					this.form.price = {
						scalar: null
					}
				}
				this.$forceUpdate()
			},

			addMj() {
				if (this.form.price[0].is_loop) {
					this.$modal.msg('满减循环不能添加 ！')
					return
				}
				let list = JSON.parse(JSON.stringify(this.form.price))
				list.push({
					id: Math.random(),
					limit: null,
					price: null,
					is_loop: false
				})
				this.form.price = list
				this.$forceUpdate()
			},

			delMj(index) {
				if (this.form.price.length == 1) {
					this.$modal.msg('最少保留一项！')
					return
				}
				this.form.price.splice(index, 1)
			},

			addZk() {
				let list = JSON.parse(JSON.stringify(this.form.price))
				list.push({
					id: Math.random(),
					limit: null,
					price: null
				})
				this.form.price = list
				this.$forceUpdate()
			},

			delZk(index) {
				if (this.form.price.length == 1) {
					this.$modal.msg('最少保留一项！')
					return
				}
				this.form.price.splice(index, 1)
			},

			addTime() {
				if (this.form.day_time.length == 3) {
					this.$modal.msg('最多设置三个时段！')
					return
				}
				let list = JSON.parse(JSON.stringify(this.form.day_time))
				list.push({
					id: Math.random(),
					start_time: "8:00",
					end_time: "20:00"
				})
				this.form.day_time = list
				this.$forceUpdate()
			},

			delTime(index) {
				if (this.form.day_time.length == 1) {
					this.$modal.msg('最少保留一项！')
					return
				}
				this.form.day_time.splice(index, 1)
			},

			actionAllDeviceRadio(e) {
				this.form.is_all_device = e
			},

			actionAllGoodsRadio(e) {
				if (e == 1) {
					this.$modal.oldConfirm('警告：后续新增的商品，也将全部适用当前活动！！！').then(res => {
						this.form.is_all_goods = e
					}).catch(err => {
						this.form.is_all_goods = '0'
					})
				} else {
					this.form.is_all_goods = e
				}
			},

			actionTimeRadio(e) {
				this.form.is_all_day = e
			},


			actionDisRadio(e) {
				this.form.can_coupon = e
			},

			actionStatusRadio(e) {
				console.log(e)
				this.form.enable_status = e
			},

			plusCanChoose() {
				this.actionsShow = true
			},

			actionsSelect(e) {
				this.form.plus_can_type = e.name
			},

			timeChange(index) {
				this.timeIndex = index
				this.$refs.timeslot.open()
			},

			confirmTime(e) {
				this.form.day_time[this.timeIndex].start_time = e.start.hour + ':' + e.start.min
				this.form.day_time[this.timeIndex].end_time = e.end.hour + ':' + e.end.min
			},

			//表单提交
			submit() {
				console.log(this.form)
				if (this.checkParams(this.form)) return
				this.$refs.form.validate().then(res => {
					let price = []
					if (this.current == 0) {
						price = this.form.price.map(i => ({
							limit: this.$xy.delMoneyL(i.limit),
							price: this.$xy.delMoneyL(i.price),
							is_loop: i.is_loop ? 1 : 0
						}))
					} else {
						price = {
							scalar: this.form.price.scalar
						}
					}
					let params = {
						name: this.form.name,
						goods_ids: this.form.is_all_goods == 0 ? this.form.goods_ids : null,
						device_ids: this.form.is_all_device == 0 ? this.form.device_ids : null,
						price: price,
						start_date: this.form.start_date,
						end_date: this.form.end_date,
						is_all_day: this.form.is_all_day == '全天' ? 1 : 0,
						day_time: this.form.day_time,
						can_coupon: this.form.can_coupon == '可用' ? 1 : 0,
						enable_status: this.form.enable_status,
						type: this.current == 0 ? '满减' : '折扣',
						weight: this.form.weight,
						is_all_device: this.form.is_all_device,
						is_all_goods: this.form.is_all_goods,
						plus_can_type: this.form.plus_can_type
					}

					if (this.form.id) {
						params.id = this.form.id
						proEdit(params).then(res => {
							uni.setStorageSync('market', '') //清空临时存储
							this.$modal.msg('修改成功~')
							setTimeout(() => {
								this.$tab.navigateBack()
							}, 1000)
						}).catch(err => {

						})
					} else {
						addPro(params).then(res => {
							uni.setStorageSync('market', '') //清空临时存储
							this.$modal.msg('新建成功~')
							setTimeout(() => {
								this.$tab.navigateBack()
							}, 1000)
						}).catch(err => {

						})
					}
				}).catch(errors => {
					console.log(errors)
				})
			},

			checkParams(params) {
				if (this.current == 1) {
					if (!this.$xy.regScalar(params.price.scalar)) {
						this.$modal.msg('折扣必须是0-1之间最多两位小数')
						return true
					}
				}
				if (this.current == 0) {
					let bflag = false
					for (var i = 0; i < params.price.length; i++) {
						let item = params.price[i];
						if (!this.$xy.regdoitTwo(item.limit) || !this.$xy.regdoitTwo(item.price)) {
							this.$modal.msg('满减金额最多保留两位小数')
							bflag = true
							break
						}
					}
					if (bflag) {
						return true
					}
				}

				return false
			},
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},

		onUnload() {
			uni.$off(['updateGoods', 'updateDevices'])
		}
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
			}

			.input-text {
				text-align: right !important;
			}

			.tab-wrap {
				padding: 12rpx 0;
			}

			.choose-list {
				width: 418rpx;

				>text {
					word-break: break-all;
				}
			}

			.mj-content {
				padding-bottom: 30rpx;

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 230rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.fail-msg {
				color: red;
				font-size: 28rpx;
				line-height: 32rpx;
				margin-top: 12rpx;
				margin-bottom: 24rpx;
				width: 726rpx;
				margin-left: -24rpx;
				background-color: skyblue;
				padding: 24rpx 12rpx;
				box-sizing: border-box;
				border-radius: 12rpx;
			}

			.example-img {
				height: 212rpx;
				padding: 24rpx 0 12rpx;
				display: flex;
				flex-flow: row nowrap;
				justify-content: flex-start;

				>image {
					width: 160rpx;
					margin-right: 66rpx;
					border-radius: 4rpx;
				}
			}

			.img-tips {
				color: red;
				margin-top: 30rpx;
				line-height: 44rpx;
			}

			.img-upload {
				height: 212rpx;
				padding: 24rpx 0 12rpx;
			}

			.bar-code {
				position: relative;

				.scan-icon {
					position: absolute;
					right: 0;
					top: 50%;
					transform: translateY(-50%);
					z-index: 9999;
				}
			}

			.form-item {
				position: relative;

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.priority {
			position: relative;

			.tips {
				position: absolute;
				color: red;
				font-size: 24rpx;
				left: 100rpx;
				top: 50%;
				transform: translateY(-50%);
			}
		}

		.action-time {
			position: relative;

			.edit {
				color: #2C6FF3;
				text-decoration: underline;
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);

				&.del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.time-wrap {
			.time-item {
				line-height: 84rpx;

				.time-select {}

				.action-time-del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 9;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>