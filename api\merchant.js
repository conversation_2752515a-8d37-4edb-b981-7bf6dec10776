import request from '@/utils/request'
//商户列表
export function subMerc(data) {
	return request({
		url: '/merc/mini/merc/subMerc',
		method: 'post',
		data: data
	})
}

//登录子商户
export function changeSubAuth(data) {
	return request({
		url: '/merc/mini/changeSubAuth',
		method: 'post',
		data: data
	})
}

//返回主商户
export function subAuthBack(data) {
	return request({
		url: '/merc/mini/subAuthBack',
		method: 'post',
		data: data
	})
}

//黑名单列表
export function blackList(data) {
	return request({
		url: '/merc/mini/member/blackList',
		method: 'post',
		data: data
	})
}



