import request from '@/utils/request'
/**
 * 渠道类型
 * @param {Object} data
 */
export function channelPage(data) {
	return request({
		url: '/merc/mini-merc-channel-type/page',
		method: 'post',
		data: data
	})
}

/**
 * 保存/更新
 * @param {Object} data
 */
export function saveOrUpdate(data) {
	return request({
		url: '/merc/mini-merc-channel/saveOrUpdate',
		method: 'post',
		data: data
	})
}

/**
 * 活动列表
 * @param {Object} data
 */
export function searchAct(data) {
	return request({
		url: '/merc/mini-merc-channel/searchAct',
		method: 'post',
		data: data
	})
}

/**
 * 活动列表
 * @param {Object} data
 */
export function actList(data) {
	return request({
		url: '/merc/mini-merc-channel/list',
		method: 'post',
		data: data
	})
}

/**
 * 活动详情
 * @param {Object} data
 */
export function detail(data) {
	return request({
		url: '/merc/mini-merc-channel/obj',
		method: 'post',
		data: data
	})
}

/**
 * 渠道分页
 * @param {Object} data
 */
export function channelType(data) {
	return request({
		url: '/merc/merc-channel-type/page',
		method: 'post',
		data: data
	})
}

/**
 * 渠道新增更新
 * @param {Object} data
 */
export function saveOrUpdateType(data) {
	return request({
		url: '/merc/merc-channel-type/saveOrUpdate',
		method: 'post',
		data: data
	})
}

/**
 * 删除渠道类型
 * @param {Object} data
 */
export function delChannelType(data) {
	return request({
		url: '/merc/merc-channel-type/del',
		method: 'post',
		data: data
	})
}
 

