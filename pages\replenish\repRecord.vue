<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="补货记录"></u-navbar>
		<view class="content">
			<view class="section" style="border-radius: 0">
				<view class="item flex justify-between align-center">
					<view class="item-left"> 选择设备 </view>
					<!-- <view class="item-right flex align-center" @click="chooseDevice">
						<text :class="[params.deviceId?'item-val':'item-val-show']">{{params.deviceId||'请选择设备'}}</text>
						<u-icon name="arrow-right" color="#777" size="16px"></u-icon>
					</view> -->
					<view class="item-right">
						<xdeviceSelect @inputSelect="inputSelect" @inputClear="inputClear" />
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left"> 商品名称 </view>
					<view class="item-right flex align-center">
						<u--input v-model="params.goodsName" inputAlign="right" placeholder="请输入商品名称"
							type="text"></u--input>
					</view>
				</view>
				<view class="item flex justify-between align-center">
					<view class="item-left"> 起始时间 </view>
					<view class="item-right flex align-center">
						<view :class="[params.startTime ? 'start-time' : 'start-time-show']"
							@click="selectTime('startTime')">
							{{ params.startTime || "开始日期" }}
						</view>
						<view style="padding: 0 12rpx"> 一 </view>
						<view :class="[params.endTime ? 'end-time' : 'end-time-show']" @click="selectTime('endTime')">
							{{ params.endTime || "结束日期" }}
						</view>
						<u-icon name="arrow-right" color="#777" size="16px"></u-icon>
					</view>
				</view>
			</view>

			<view class="section" style="margin-top: 20rpx; padding: 6 13rpx 0" v-if="list && list.dataList">
				<view class="device-msg flex justify-between align-center">
					<view class="device-name" v-if="list.deviceName">
						{{ list.deviceName }}({{ list.deviceId }})
					</view>
					<view class="device-name" v-else>
						{{ list.deviceId }}
					</view>
					<view class="export" v-if="checkPermi(['repRec:exp'])">
						<xbutton width="140" @click="exportRec">导出记录</xbutton>
					</view>
				</view>
				<view class="rep-msg">
					<view class="rep-msg-item flex align-center">
						<view> 补货时间： </view>
						<view class="flex align-center">
							<view>
								{{ params.startTime }}
							</view>
							<view style="padding: 0 12rpx"> 至 </view>
							<view>
								{{ params.endTime }}
							</view>
						</view>
					</view>
					<view class="rep-msg-item flex align-center">
						<view> 补货种类： </view>
						<view> {{ list.kindNum }}种 </view>
					</view>
					<view class="rep-msg-item flex align-center">
						<view> 补货数量： </view>
						<view> {{ list.allSupplyNum }}个 </view>
					</view>
					<view class="rep-msg-item flex align-center">
						<view> 补货员： </view>
						<view>
							{{ list.supplyNames }}
						</view>
					</view>
				</view>
				<view class="rep-content">
					<block v-for="(item, key, index) in list.dataList" :key="item.id">
						<view class="xy-card time-item">
							<view class="time-top flex justify-between">
								<view class="time flex align-center">
									<image
										src="https://ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/time.png"
										mode="widthFix"></image>
									<view style="padding: 0 28rpx 0 8rpx"> 补货日期： </view>
									<view>
										{{ key }}
									</view>
								</view>
								<u-icon name="arrow-right" color="#777" size="16px"></u-icon>
							</view>
							<view class="time-content">
								<block v-for="item1 in item" :key="item1.goodsId">
									<view class="goods-item">
										<view class="goods-detail flex align-center">
											<view class="goods-image">
												<u--image radius="8" width="124rpx" height="130rpx" :src="item1.cover"
													mode="aspectFit" :lazy-lord="true"></u--image>
											</view>
											<view class="goods-msg">
												<view class="goods-name">
													{{ item1.goodsName }}
												</view>
												<view class="flex align-center">
													<view class="goods-msg-item" style="margin-right: 50rpx">
														原库存：{{ item1.oldStock }}
													</view>
													<view class="goods-msg-item">
														新库存：{{ item1.newStock }}
													</view>
												</view>
												<view class="goods-msg-item">
													补货员：{{ item1.createUserName }}
												</view>
												<view class="stock-time">
													{{ item1.createTime }}
												</view>
												<view class="stock-num">
													<view> 补货数量 </view>
													<view>
														<text v-if="item1.supplyNum > 0">+</text>{{ item1.supplyNum }}
													</view>
												</view>
											</view>
										</view>
										<view class="stock-rec-btn flex justify-end">
											<xbutton width="180" height="57" bgColor="#DBE8FF" color="#2C6FF3"
												@click="stockRecord(JSON.stringify(item1))">库存变化记录
											</xbutton>
										</view>
									</view>
								</block>
							</view>
						</view>
					</block>
				</view>
			</view>

			<view v-else class="empty">
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</view>

		<u-picker :show="typeShow" :columns="actions" confirmColor="#2C6FF3" :closeOnClickOverlay="true"
			keyName="deviceName" @close="typeShow = false" @confirm="actionSelect" @cancel="typeShow = false">
		</u-picker>

		<u-picker :show="employeeShow" :columns="employeeActions" confirmColor="#2C6FF3" :closeOnClickOverlay="true"
			keyName="name" @close="employeeShow = false" @confirm="employeeSelect" @cancel="employeeShow = false">
		</u-picker>

		<u-datetime-picker :show="show" v-model="time" confirmColor="#2C6FF3" mode="date" :closeOnClickOverlay="true"
			@cancel="show = false" @confirm="confirm" @close="show = false"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		listOfIdName
	} from "@/api/device/device.js";

	import {
		totalRep,
		repExport
	} from "@/api/replenishment/replenishment.js";

	export default {
		data() {
			return {
				show: false,
				time: new Date(),
				timeType: "startTime",
				list: null,
				actions: [],
				typeShow: false,
				params: {
					deviceId: null,
					startTime: null,
					endTime: null,
					goodsName: null,
				},

				employeeShow: false,
				employeeActions: [],

				mercTree: [],
			};
		},

		onLoad(o) {
			if (o.deviceId) {
				this.params.deviceId = o.deviceId;
			}
			//默认一周
			this.params.startTime = uni.$u.timeFormat(
				new Date() - 7 * 24 * 60 * 60 * 1000,
				"yyyy-mm-dd"
			);
			this.params.endTime = uni.$u.timeFormat(new Date(), "yyyy-mm-dd");
		},

		watch: {
			params: {
				handler(newVal, oldVal) {
					console.log(123);
					this.getData();
				},
				deep: true,
				immediate: true,
			},
		},

		methods: {
			inputSelect(item) {
				this.params.deviceId = item.deviceId;
			},

			inputClear() {
				this.params.deviceId = "";
			},

			selectTime(type) {
				this.timeType = type;
				this.show = true;
			},

			confirm(e) {
				this.params[this.timeType] = uni.$u.timeFormat(e.value, "yyyy-mm-dd");
				this.show = false;
			},

			chooseDevice() {
				listOfIdName({}).then((res) => {
					this.actions = [res.data];
					this.typeShow = true;
				});
			},

			// 下拉选择
			actionSelect(e) {
				console.log(e);
				this.params.deviceId = e.value[0].deviceId;
				this.typeShow = false;
			},

			close() {
				this.show = false;
			},

			getData() {
				if (this.checkParams()) return;
				let params = JSON.parse(JSON.stringify(this.params));
				params.startTime = params.startTime + " 00:00:00";
				params.endTime = params.endTime + " 23:59:59";
				totalRep(params).then((res) => {
					this.list = res.data;
				});
			},

			checkParams() {
				if (!this.params.startTime) {
					return true;
				}
				if (!this.params.endTime) {
					return true;
				}
				// if (!this.params.deviceId) {
				// 	return true
				// }
				return false;
			},

			stockRecord(item1) {
				let item = JSON.parse(item1);
				this.$tab.navigateTo(
					`/pages/replenish/stockRecord?deviceId=${this.params.deviceId}&goodsId=${item.goodsId}&goodsName=${item.goodsName}&startTime=${this.params.startTime}&endTime=${this.params.endTime}`
				);
			},

			exportRec() {
				if (this.checkParams()) return;
				let params = JSON.parse(JSON.stringify(this.params));
				params.startTime = params.startTime + " 00:00:00";
				params.endTime = params.endTime + " 23:59:59";
				repExport(
					params,
					`机器${this.params.deviceId}补货记录(${this.params.startTime}~${this.params.endTime}).xlsx`
				)
			},
		},
	};
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		::v-deep .u-border {
			border-color: transparent !important;
			text-align: right;
		}

		.content {
			.section {
				padding: 6rpx 20rpx 0;
				border-radius: 14rpx;
				background-color: #fff;

				.item {
					line-height: 28rpx;
					padding: 10rpx 17rpx 10rpx 23rpx;
					height: 88rpx;

					&:not(:last-child) {
						border-bottom: 1rpx solid #eaeaea;
					}

					.item-left {
						line-height: 50rpx;
					}

					.item-right {
						line-height: 50rpx;

						.item-val {
							color: #333;
							display: inline-block;
							max-width: 362rpx;
							text-align: right;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}

						.item-val-show {
							color: #c6c6c6;
						}

						.report-type {
							text-decoration: underline;
							color: #2c6ff3;
							font-style: oblique;
						}

						.device {
							margin-right: 24rpx;
						}

						.start-time,
						.end-time {
							padding: 0 12rpx;
						}

						.start-time-show,
						.end-time-show {
							color: #c6c6c6;
						}
					}
				}

				.device-msg {
					font-size: 32rpx;
					padding: 26rpx 30rpx 14rpx;

					.device-name {
						font-weight: 800;
						color: #333333;
					}
				}

				.rep-msg {
					padding: 12rpx 30rpx;

					.rep-msg-item {
						line-height: 56rpx;

						>view:nth-child(1) {
							width: 160rpx;
						}
					}
				}

				.rep-content {
					.time-item {
						box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.4);

						+.time-item {
							margin-top: 20rpx;
						}

						.time-top {
							.time {
								font-weight: 800;
								color: #333333;

								>image {
									width: 32rpx;
									height: 32rpx;
								}
							}
						}

						.time-content {
							.goods-item {
								background: #f6f7fa;
								border-radius: 14rpx;
								padding: 37rpx 25rpx 28rpx 8rpx;
								margin-top: 20rpx;

								.goods-detail {
									.goods-image {
										width: 124rpx;
										height: 130rpx;
									}

									.goods-msg {
										position: relative;
										margin-left: 27rpx;
										width: 600rpx;

										.goods-name {
											font-size: 26rpx;
											font-weight: 800;
											padding-bottom: 12rpx;
											max-width: 382rpx;
										}

										.goods-msg-item {
											font-size: 24rpx;
											color: #555555;
											line-height: 40rpx;
										}

										.stock-time {
											font-size: 26rpx;
											font-weight: 500;
											line-height: 46rpx;
											color: #999999;
										}

										.stock-num {
											font-size: 28rpx;
											color: #ff0000;
											text-align: right;
											line-height: 44rpx;
											position: absolute;
											right: 0;
											top: 0;

											>view:nth-child(2) {
												font-weight: bold;
											}
										}
									}
								}

								.stock-rec-btn {
									margin-top: 18rpx;
								}
							}
						}
					}
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}

		.popup-content {
			padding: 0 36rpx;
		}

		.empty {
			margin-top: 40%;
		}
	}
</style>