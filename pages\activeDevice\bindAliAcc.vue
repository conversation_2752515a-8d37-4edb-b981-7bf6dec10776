<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="账号绑定">
		</u-navbar>

		<view class="content flex flex-direction justify-between" :style="{height:fullHeight}">
			<view class="top flex flex-direction align-center">
				<view class="img">
					<u-image width="400rpx" height="400rpx"
						src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/alipayUid.png"
						mode="widthFix" :lazy-load="true">
					</u-image>
				</view>
				<view class="btn flex justify-center">
					<view @click="save">保存二维码到手机相册</view>
					<!-- <view>帮助</view> -->
				</view>
				
				<view style="color:red;font-size: 24rpx;margin-top: 30rpx;">
					请使用支付宝扫描二维码获取支付宝UID进行绑定
				</view>

				<view class="input flex">
					<input type="text" placeholder="请输入" placeholder-class="place-class" v-model="aliAcc" />
					<view class="bind" @click="bind">
						绑定
					</view>
				</view>
			</view>

			<view class="user-info">
				<view class="">
					用户名称：{{info.name}}
				</view>
				<view class="">
					手机号码：{{info.tel}}
				</view>
				<view class="flex align-center">
					是否绑定：<view v-if="info.aliUserId">{{info.aliUserId}}<text style="color:green;">(已绑定)</text></view>
					<view style="color:red;" v-else>
						未绑定
					</view>
				</view>
			</view>

			<view class="tips">
				支付宝刷脸AI柜，需要绑定操作员的支付宝账号，操作员刷脸操作货柜时，系统将对操作员的支付宝账号进行授权。
			</view>
		</view>
	</view>
</template>

<script>
	import {
		updateByAli
	} from "@/api/device/device.js"
	import {
		update,
		objByUserId
	} from "@/api/system/employee.js"
	import {
		userInfo,
		updateUserInfo
	} from "@/api/system/user.js"

	import {
		imgDownLoad
	} from '@/utils/download.js'

	export default {
		data() {
			return {
				fullHeight: 0,
				aliAcc: null,
				userId: null,
				info: {
					aliUserId:null,
					name: null,
					tel: null
				}
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.content',20)

			if (o.userId) {
				this.userId = o.userId
				this.getEmployeInfo()
			} else {
				this.getUserInfo()
			}
		},
		methods: {
			getUserInfo() {
				userInfo().then(res => {
					if (res.code == 200) {
						this.info = res.data
					} else {
						this.info = {
							name: null,
							tel: null,
							aliUserId:null
						}
					}
				})
			},
			getEmployeInfo() {
				objByUserId({
					userId: this.userId
				}).then(res => {
					this.info = {
						name: res.data.name,
						tel: res.data.tel,
						aliUserId:res.data.aliUserId,
					};
				})
			},
			async bind() {
				if (!this.aliAcc) {
					this.$modal.msg('请输入支付宝用户id')
					return
				}
				if (!this.userId) {
					//商户绑定
					// await this.mercUpdate()
					this.$tab.navigateTo(`/pages/globalPages/setting?aliId=${this.aliAcc}`)
				} else {
					//员工绑定
					console.log(this.aliAcc)
					if(this.aliAcc.indexOf('我的支付宝UID')==-1&&!/^\d+$/.test(this.aliAcc)){
						this.$modal.msg('您输入的支付宝UID不正确！')
						return
					}
					await this.partnerUpdate()
					this.$modal.msg('绑定成功~')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000)
				}
				
			},

			mercUpdate() {
				return new Promise((resolve, reject) => {
					updateByAli({
						aliUserId: this.delParams(),
					}).then(res => {
						resolve(res)
					}).catch(err => {
						reject(res)
					})
				})
			},

			partnerUpdate() {
				return new Promise((resolve, reject) => {
					update({
						aliUserId: this.delParams(),
						userId: this.userId
					}).then(res => {
						resolve(res)
					}).catch(err => {
						reject(res)
					})
				})
			},
			
			delParams(){
				let uid=''
				if(this.aliAcc.indexOf('我的支付宝UID')!=-1){
					uid=this.aliAcc.split('：')[1]
					console.log(this.aliAcc.split('：')[1])
				}else{
					uid=this.aliAcc
				}
				return uid
			},

			save() {
				let imgUrl = 'https://ossfile.mxrvending.com/assets/xy_merc_mini/images/alipayUid.png'
				imgDownLoad(imgUrl).then(res => {
					uni.showToast({
						title: '保存成功~',
					})
				}).catch(err => {
					uni.showToast({
						title: '保存失败，请重试！',
					})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		position: relative;

		.content {
			background-color: #fff;
			width: 724rpx;
			margin: 20rpx 13rpx 0;
			overflow: hidden;
			border-radius: 12rpx;

			.top {
				.btn {
					width: 400rpx;
					color: #3876f3;
					text-decoration: underline;
					margin-top: 30rpx;
					text-align: center;
				}

				.input {
					width: 460rpx;
					line-height: 80rpx;
					height: 80rpx;
					margin-top: 70rpx;

					>input {
						line-height: 80rpx;
						height: 80rpx;
						width: 76%;
						background-color: #f6f8fb;
						padding: 0 30rpx;
						border-top-left-radius: 18rpx;
						border-bottom-left-radius: 18rpx;
					}

					>view {
						width: 24%;
						text-align: center;
						border-top-right-radius: 18rpx;
						border-bottom-right-radius: 18rpx;
						background-color: #3876f3;
						color: #fff;
					}
				}

				.place-class {
					color: #dcdcdc;
				}
			}

			.tips {
				background-color: #ebf2ff;
				padding: 16rpx 20rpx;
				color: #3876f3;
				font-size: 26rpx;
				line-height: 40rpx;
			}
		}

		.user-info {
			padding-left: 150rpx;
			font-size: 28rpx;
			line-height: 50rpx;
		}

		.img {
			width: 400rpx;
			height: 400rpx;
			margin-top: 70rpx;
		}

		.canvas-box {
			position: absolute;
			left: -1000rpx;
		}
	}
</style>