import request from '@/utils/request'

// 获取用户菜单
export function workMenuList() {
  return request({
    url: '/authorize/sys-menu/workMenuList',
    method: 'post'
  })
}

// 获取用户信息
export function userInfo() {
  return request({
    url: '/merc/mini/orders/my',
    method: 'post'
  })
}

// 协议
export function agreement(data) {
  return request({
    url: '/sys/sys-agreement/obj',
    method: 'post',
	data:data
  })
}

// 协议列表
export function agreementPage(data) {
  return request({
    url: '/sys/sys-agreement/page',
    method: 'post',
	data:data
  })
}

// 修改用户密码等信息
export function updateUserInfo(data) {
  return request({
    url: '/merc/mini/orders/updateUserInfo',
    method: 'post',
	data:data
  })
}

// 短信验证码
export function sendCode(data) {
  return request({
    url: '/merc/mercSms/sendCode',
    method: 'post',
	data:data
  })
}

/**
 * 费用管理角标
 * @param {Object} data
 */
export function arrearageCount(data) {
  return request({
    url: '/merc/be/merc/arrearageCount',
    method: 'post',
	data:data
  })
}

/**
 * 更新短信通知
 * @param {Object} data
 */
export function updateMerConfig(data) {
  return request({
    url: '/merc/merc-config/updateMerConfig ',
    method: 'post',
	data:data
  })
}











