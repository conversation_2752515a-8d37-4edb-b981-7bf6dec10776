import store from '@/store'
import config from '@/config'
import {
	getToken
} from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import {
	toast,
	tansParams
} from '@/utils/common'

let timeout = 10000
const baseUrl = config.baseUrl
let loadingCount = 0 // 请求计数器

// 显示加载提示
const showLoading = () => {
	if (loadingCount === 0) {
		uni.showLoading({
			title: '加载中'
		})
	}
	loadingCount++
}

// 隐藏加载提示
const hideLoading = () => {
	loadingCount--
	if (loadingCount === 0) {
		uni.hideLoading({
			noConflict: true
		})
	}
}

// 处理请求配置
const handleConfig = (config) => {
	// 是否需要设置 token
	const isToken = (config.headers || {}).isToken === false
	config.header = config.header || {}
	if (getToken() && !isToken) {
		config.header['satoken'] = getToken()
		config.header['sysId'] = uni.getStorageSync('sysId')
	}

	// get请求映射params参数
	if (config.params) {
		let url = config.url + '?' + tansParams(config.params)
		url = url.slice(0, -1)
		config.url = url
	}

	return config
}

// 处理错误消息
const handleErrorMessage = (error) => {
	let message = error.message || '未知错误'
	if (message === 'Network Error') {
		message = '网络连接超时'
	} else if (message.includes('timeout')) {
		message = '网络连接超时'
	} else if (message.includes('Request failed with status code')) {
		message = '系统接口' + message.substr(message.length - 3) + '异常'
	}
	return message
}

// 处理响应状态码
const handleResponseCode = (code, msg, reject) => {
	if (code === 501) {
		let pages = getCurrentPages()
		let currentPage = pages[pages.length - 1].route
		if (currentPage != '/pages/login') {
			toast('登录状态已过期，请重新登陆！')
			reject('无效的会话，或者会话已过期，请重新登录。')
			store.dispatch('LogOut').then(() => {
				uni.reLaunch({
					url: '/pages/login'
				})
			})
		}
	} else if (code === 500) {
		toast(msg.substr(0, 50))
		reject(msg)
	} else if ([202, 203, 204].includes(code)) { // 管理费欠费
		reject(code)
	} else if (code !== 200) {
		toast(msg)
		reject(code)
	} else if (code === 404) {
		toast('请求超时')
		reject(code)
	}
}

// 保存日志
const saveLogs = (url, params, res, startTime) => {
	try {
		const pages = getCurrentPages()
		const page = pages[pages.length - 1]
		const route = page ? page.route : ''

		const logObj = {
			route,
			url: baseUrl + url,
			token: getToken(),
			startTime: uni.$u.timeFormat(startTime, 'yyyy-mm-dd hh:MM:ss'),
			endTime: new Date() - startTime,
			params: JSON.stringify(params) || '无',
			res: JSON.stringify(res).substr(0, 500)
		}

		let logs = []
		try {
			const storedLogs = uni.getStorageSync('logs')
			if (storedLogs) {
				logs = JSON.parse(storedLogs)
			}
		} catch (e) {
			logs = []
		}

		logs.unshift(logObj)
		if (logs.length > 30) {
			logs.pop()
		}
		uni.setStorageSync('logs', JSON.stringify(logs))
	} catch (e) {
		console.error('保存日志失败', e)
	}
}

// 主请求函数
const request = config => {
	const startTime = new Date()
	if (!config.hiddenLoading) showLoading()
	config = handleConfig(config)

	return new Promise((resolve, reject) => {
		uni.request({
			method: config.method || 'get',
			timeout: config.timeout || timeout,
			url: config.baseUrl || baseUrl + config.url,
			data: config.data,
			header: config.header,
			dataType: 'json'
		}).then(response => {
			hideLoading()

			let [error, res] = response
			if (error) {
				toast(error.errMsg)
				reject(error.errMsg)
				return
			}

			// if (process.env.NODE_ENV === 'production') {
			saveLogs(config.url, config.data, res.data || '', startTime)
			// }

			const code = res.data.code || 404
			const msg = errorCode[code] || res.data.msg || errorCode['default']

			handleResponseCode(code, msg, reject)

			if (code === 200) {
				resolve(res.data)
			}
		}).catch(error => {
			hideLoading()

			// if (process.env.NODE_ENV === 'production') {
			saveLogs(config.url, config.data, error, startTime)
			// }

			const message = handleErrorMessage(error)
			toast(message)
			reject(error)
		})
	})
}

// 验证是否为blob格式
async function blobValidate(data) {
	try {
		const text = await data.text()
		JSON.parse(text)
		return false
	} catch (error) {
		return true
	}
}

// 下载请求
export function downLoadReq(config) {
	uni.showLoading({
		title: '加载中'
	})
	config = handleConfig(config)

	return new Promise((resolve, reject) => {
		uni.request({
			method: config.method || 'get',
			timeout: config.timeout || timeout,
			url: config.baseUrl || baseUrl + config.url,
			data: config.data,
			header: config.header,
			responseType: "arraybuffer",
		}).then(response => {
			uni.hideLoading()
			let [error, res] = response

			if (res && res.statusCode === 200) {
				const fs = uni.getFileSystemManager()
				fs.writeFile({
					filePath: uni.env.USER_DATA_PATH + '/' + config.name,
					data: res.data,
					encoding: "binary",
					success(e) {
						uni.openDocument({
							filePath: uni.env.USER_DATA_PATH + '/' + config.name,
							showMenu: true,
							success: function(x) {
								console.log("successfun", x)
							}
						})
					}
				})
				toast('导出成功！')
				resolve(res)
			} else {
				toast('导出失败！')
				reject(error || res)
			}
		}).catch(error => {
			uni.hideLoading()
			toast('导出失败！')
			reject(error)
		})
	})
}

export default request