<template>
	<view id="wrap">
		<web-view :src="src"></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				src: ''
			}
		},
		onLoad(o) {
			this.src='https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx95d729fe985ef9de&redirect_uri=https://ai.mxrvending.com/html/wxMp.html&response_type=code&scope=snsapi_userinfo&state=type%3Dquan%2Curl%3Dhttp%3A%2F%2Fmm.dianping.com%2Fweixin%2Faccount%2Fhome'
		},
		methods: {

		}
	}
</script>

<style scoped lang="scss">
	web-view {
		width: 100%;
		height: 100vh;
		padding: 0;
		margin: 0;
		overflow: hidden;
	}

	#wrap {
		width: 100%;
		height: 100%;
		overflow-x: hidden;
	}

	web-view {
		border: none;
	}
</style>
