<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="算法扣费明细"></u-navbar>
		<view class="content">
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view class="scroll-content" v-if="list&&list.length>0">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="xy-card section">
							<view class="flex justify-between align-center"
								style="font-size: 32rpx; line-height: 68rpx;margin-bottom: 12rpx;">
								<view class="row-item flex align-center"
									style="font-size: 32rpx; line-height: 68rpx;font-weight: bold;">
									<view>
										算法服务费（{{item.id}}）
									</view>
									<!-- <view>
									123123213
								</view> -->
								</view>
								<view>
									<text v-if="item.status&&!item.arrearageBalance" style="color: green;">已缴费</text>
									<text v-else-if="item.status&&item.arrearageBalance" style="color: #bdbdbd;">余额不足</text>
									<text v-else-if="!item.status" style="color: #FF1E1E;">待充值</text>
								</view>
							</view>
						<!-- 	<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									{{item.algorithmName}}
								</view>
							</view> -->
							<view class="row-item flex align-center">
								<view class="row-name">
									设备id：
								</view>
								<view class="row-val">
									{{item.deviceId}}
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									算法类型：
								</view>
								<view class="row-val">
									{{item.algorithmName}}
								</view>
							</view>
							<view class="row-item flex align-center" v-if="item.status&&item.arrearageBalance" style="color: red;">
								<view class="row-name">
									欠费金额：
								</view>
								<view class="row-val">
									￥{{$xy.delMoney(item.arrearageBalance)}}
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									扣除笔数：
								</view>
								<view class="row-val">
									{{item.feeAlgorithm}}笔
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									应扣笔数：
								</view>
								<view class="row-val">
									{{item.algorithmCount}}笔
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									扣除金额：
								</view>
								<view class="row-val">
									￥{{$xy.delMoney(item.feeBalance)}}
								</view>
							</view>
							<view class="row-item flex align-center">
								<view class="row-name">
									计费标准：
								</view>
								<view class="row-val">
									￥{{$xy.delMoney(item.standard)}}元/笔
								</view>
							</view>
							
							<view class="row-item flex align-center">
								<view class="row-name">
									缴费时间：
								</view>
								<view class="row-val">
									{{item.updateTime}}
								</view>
							</view>
						</view>
					</block>
				</view>

				<view v-else style="padding-top: 40%;">
					<u-empty text="没有数据!"></u-empty>
				</view>
			</scroll-view>

		</view>
	</view>
</template>

<script>
	import {
		costAlgoDetailList,
	} from "@/api/recharge.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
				loadmoreStatus: 'loadmore',
				fullHeight: 0,
				page: 1,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')

			this.deviceId = o.deviceId
			this.search()
		},
		methods: {
			search() {
				this.reset()
				this.getList()
			},
			reset() {
				this.page = 1;
				this.list = [];
				this.loadmoreStatus = "loadmore"
			},

			getList(id) {
				costAlgoDetailList({
					deviceIds: [this.deviceId],
					page: {
						current: this.page,
						size: 10
					}
				}).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data);
				})
			},

			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getList()
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			padding: 0 13rpx;
			overflow: hidden;

			.search {
				margin-top: 30rpx;
			}

			.scroll-content {
				overflow: hidden;
			}

			.section {
				margin-top: 20rpx;
				padding: 24rpx 30rpx 24rpx 48rpx;

				.row-item {
					font-size: 28rpx;
					color: #555555;
					line-height: 56rpx;

					.row-name {
						min-width: 140rpx;
					}

					.row-val {}
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}
	}
</style>