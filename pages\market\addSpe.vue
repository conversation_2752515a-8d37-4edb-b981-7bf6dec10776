<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="特价活动创建"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" :rules="rules" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap" style="padding:0 30rpx;">
					<u-form-item label="活动名称" prop="goodsName">
						<u--input inputAlign="right" v-model="form.name" placeholder="请输入活动名称" border="none"></u--input>
					</u-form-item>

				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<u-form-item label="选择设备" borderBottom>
						<u-radio-group v-model="form.is_all_device" placement="row" @change="actionAllDeviceRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
							</u-radio>
							<u-radio label="指定设备" name="0">
							</u-radio>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="指定设备" borderBottom @click="chooseDev" v-if="form.is_all_device==0">
						<view class="choose-list" v-if="form.device_ids&&form.device_ids.length>0">
							<text v-for="(item,index) in form.device_list" :key="index">
								{{item.device_name}}
								<text v-if="index!=form.device_list.length-1">，</text>
							</text>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择设备</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<u-form-item label="选择商品" borderBottom @click="chooseCom">
						<view v-if="form.is_all_goods==1" style="text-align: right;">全部</view>
						<view class="choose-list" v-else-if="form.goods_list&&form.goods_list.length>0">
							<text v-for="(item,index) in form.goods_list" :key="index">
								{{item.goods_name}}
								<text v-if="index!=form.goods_list.length-1">，</text>
							</text>
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择商品</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<!-- <u-form-item label="每人限购数量" prop="goodsName">
						<u--input inputAlign="right" type="number" v-model="form.user_limit_num" placeholder="请输入限购数量"
							border="none"></u--input>
					</u-form-item> -->
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<u-form-item label="活动日期" borderBottom @click="chooseDate">
						<view style="text-align: right;" v-if="form.start_date">{{form.start_date}}~{{form.end_date}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择活动日期</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<view class="action-time">
						<u-form-item label="活动时间">
							<u-radio-group v-model="form.is_all_day" placement="row" @change="actionTimeRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="全天" name="全天">
								</u-radio>
								<u-radio label="指定时间" name="指定时间">
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<view class="edit action-time-edit" @click="addTime" v-if="form.is_all_day=='指定时间'">
							增加
						</view>
					</view>
					<view class="time-wrap" v-if="form.is_all_day=='指定时间'">
						<view class="time-item flex justify-between align-center" v-for="(item,index) in form.day_time"
							:key="item.id">
							<view class="time-tip">
								指定时间段{{index+1}}
							</view>
							<view class="time-select" @click="timeChange(index)">
								{{item.start_time}}~{{item.end_time}}
							</view>
							<view class="edit action-time-del" @click="delTime(index)">
								删除
							</view>
						</view>
					</view>
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<view class="priority">
						<u-form-item label="优先级">
							<u-input inputAlign="right" type="number" v-model="form.weight" placeholder="请输入活动优先级"
								border="none" maxlength="8"></u-input>
						</u-form-item>
						<view class="tips">(数字越大活动优先级越高)</view>
					</view>
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<u-form-item label="会员优先" borderBottom @click="plusCanChoose">
						<view style="text-align: right;">{{form.plus_can_type}}
						</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>

				<view class="section-wrap" style="padding:0 30rpx;">
					<view class="action-time">
						<u-form-item label="状态">
							<u-radio-group v-model="form.enable_status" placement="row" @change="actionStatusRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="启用" name="启用">
								</u-radio>
								<u-radio label="禁用" name="禁用">
								</u-radio>
							</u-radio-group>
						</u-form-item>
					</view>
				</view>

				<view class="section-wrap" style="padding:0 13rpx 24rpx;">
					<view class="spe-list">
						<view class="spe-head flex justify-between align-center" style="line-height: 104rpx;">
							<view>特价设置</view>
							<view>
								<xbutton width="134rpx" round="14rpx" @click="chooseCom">增加商品</xbutton>
							</view>
						</view>
						<view class="spe-body" v-if="form.goods_list&&form.goods_list.length>0">
							<block v-for="(item,index) in form.goods_list" :key="item.goods_id">
								<view class="com-item flex align-center">
									<view class="com-img">
										<u--image radius="4" width="124rpx" height="150rpx" :src="item.cover"
											mode="aspectFit" :lazy-lord="true"></u--image>
									</view>
									<view class="com-content">
										<view class="com-name">
											{{item.goods_name}}
										</view>
										<view class="com-old-price">
											原价：¥{{$xy.delMoney(item.price_old)}}
										</view>
										<view class="com-num-spe flex justify-between">
											<view class="com-num flex align-center">
												<view>总数量：</view>
												<view class="com-input flex align-center justify-center">
													<u--input v-model="item.total_num" placeholder="请输入"
														border="none"></u--input>
												</view>
											</view>
											<view class="com-spe flex align-center">
												<view>特价：</view>
												<view class="com-input flex align-center justify-center">
													<u--input v-model="item.price_onsale"
														@blur="priceSet($event,item.price_old)" type="digit"
														placeholder="请输入" border="none"></u--input>
												</view>
											</view>
										</view>
									</view>

									<view class="close-btn" @click="delCom(index)">
										<u-icon name="close" color="#333" size="16"></u-icon>
									</view>
								</view>
							</block>
						</view>
						<view class="spe-body" v-else style="text-align: center;line-height: 100rpx;">
							请选择商品！
						</view>
					</view>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>

		<u-calendar :show="calendarShow" allowSameDay closeOnClickOverlay @close="calendarShow=false"
			:monthNum="calendarMonthNum" mode="range" color="#2C6FF3" @confirm="calendarConfirm"></u-calendar>

		<xtimeslot ref="timeslot" :title="'选择时间段'" @confirm="confirmTime">
		</xtimeslot>

		<u-action-sheet :show="actionsShow" :actions="actions" title="会员优先选择" @close="actionsShow = false"
			@select="actionsSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		addSpe,
		speDetail,
		speEdit
	} from "@/api/market/promotion.js"
	//验证规则
	let rulesObj = {
		type: 'string',
		required: true,
		message: '必填项',
		trigger: ['change']
	}

	export default {
		data() {
			return {
				form: {
					name: null,
					device_ids: [],
					goods_list: [],
					device_list: [],
					start_date: null,
					end_date: null,
					is_all_day: '全天',
					day_time: [{
						id: Math.random(),
						start_time: "8:00",
						end_time: "20:00"
					}],
					enable_status: '启用',
					weight: null,
					is_all_device: '1',
					is_all_goods: '0',
					// user_limit_num: null
					plus_can_type: '活动优惠优先',
				},

				rules: {
					'unitName': {
						type: 'string',
						required: true,
						message: '请选择商品规格~',
						trigger: ['change']
					},
				},

				calendarShow: false,
				calendarMonthNum: '3',
				tabList: [{
						name: '满减红包'
					},
					{
						name: '折扣红包'
					},
				],
				current: 0,
				timeIndex: 0,

				comList: [{
					name: '奥利奥',
					oldPrice: 6,
					price: 5,
					num: 10
				}],

				actions: [{
						name: '会员折扣优先'
					},
					{
						name: '活动优惠优先'
					},
					{
						name: '会员折扣叠加'
					}
				],
				actionsShow: false,
			}
		},

		onLoad(o) {
			this.getMonthNum()
			if (o.id) {
				this.getDetail(o.id)
			}

			uni.$on('updateGoods', (data) => {
				let goods_list = []
				let newGoodsList = []
				if (data && data.length > 0) {
					for (var i = 0; i < data.length; i++) {
						let newGoods = {
							cover: data[i].cover,
							goods_id: data[i].goodsId,
							goods_name: data[i].goodsName,
							price_old: data[i].price,
							price_onsale: this.$xy.delMoney(data[i].price),
							total_num: 1
						}
						for (var j = 0; j < this.form.goods_list.length; j++) {
							let goods = this.form.goods_list[j];
							if (goods.goods_id == newGoods.goods_id) {
								newGoods.price_onsale = goods.price_onsale
								newGoods.total_num = goods.total_numc
							}
						}
						newGoodsList.push(newGoods)
					}
				}
				this.form.goods_list = newGoodsList
			})
			
			uni.$on('updateDevices', (data) => {
				let device_ids = []
				let device_list = []
				if (data && data.length > 0) {
					device_ids = data.map(item => item.device_id)
					device_list = data.map(item => ({
						device_id: item.device_id,
						device_name: item.device_name,
					}))
				}
				this.form.device_ids = device_ids
				this.form.device_list = device_list
			})
		},

		methods: {
			getDetail(id) {
				speDetail({
					id: id
				}).then(res => {
					let data = res.data
					let goods_list = []
					if (!data.is_all_goods && data.goods_list && data.goods_list.length > 0) {
						for (let key in data.goods_list) {
							let item = data.goods_list[key]
							goods_list.push({
								cover: item.cover,
								goods_id: item.goods_id,
								goods_name: item.goods_name,
								price_old: item.price_old,
								price_onsale: this.$xy.delMoney(item.price_onsale),
								total_num: item.total_num
							})
						}
					}
					let dayTime = [];
					if (!data.is_all_day && data.day_time) {
						for (let key in data.day_time) {
							let item = data.day_time[key]
							let obj = {
								id: Math.random(),
								end_time: item.end_time,
								start_time: item.start_time
							}
							dayTime.push(obj)
						}
					} else {
						dayTime = [{
							id: Math.random(),
							start_time: "8:00",
							end_time: "20:00"
						}]
					}

					let device_ids = []
					if (!data.is_all_device && data.device_list && data.device_list.length > 0) {
						for (let key in data.device_list) {
							let item = data.device_list[key]
							device_ids.push(item.device_id)
						}
					}

					let params = {
						id: data.id,
						name: data.name,
						goods_list: goods_list,
						device_ids: device_ids,
						start_date: data.start_date,
						end_date: data.end_date,
						is_all_day: data.is_all_day ? '全天' : '指定时间',
						day_time: dayTime,
						enable_status: data.enable_status,
						type: data.type,
						weight: data.weight,
						device_list: data.device_list,
						is_all_device: data.is_all_device ? '1' : '0',
						is_all_goods: data.is_all_goods ? '1' : '0',
						plus_can_type: data.plus_can_type
					}
					this.current = data.type == '满减' ? '0' : '1'
					this.form = JSON.parse(JSON.stringify(params))
					console.log('反显数据=======' + JSON.stringify(this.form))
				})
			},

			chooseDev() {
				this.$tab.navigateTo(`/pages/market/chooseDevice?type=1&deviceIds=${JSON.stringify(this.form.device_ids)}`)
			},


			chooseCom() {
				let goodsId = []
				if (this.form.goods_list && this.form.goods_list.length > 0) {
					goodsId = this.form.goods_list.map(item => item.goods_id)
				}
				this.$tab.navigateTo(`/pages/market/chooseGoods?type=1&goodsId=${JSON.stringify(goodsId)}`)
			},

			chooseDate() {
				this.calendarShow = true
			},

			calendarConfirm(e) {
				console.log(e)
				this.form.start_date = e[0];
				this.form.end_date = e.at(-1)
				this.calendarShow = false
			},

			getMonthNum() {
				getDict('calendar_range').then(res => {
					this.calendarMonthNum = Number(res[0].value)
					console.log(this.calendarMonthNum)
				})
			},

			addTime() {
				if (this.form.day_time.length == 3) {
					this.$modal.msg('最多设置三个时段！')
					return
				}
				let list = JSON.parse(JSON.stringify(this.form.day_time))
				list.push({
					id: Math.random(),
					start_time: "8:00",
					end_time: "20:00"
				})
				this.form.day_time = list
				this.$forceUpdate()
			},

			delTime(index) {
				if (this.form.day_time.length == 1) {
					this.$modal.msg('最少保留一项！')
					return
				}
				this.form.day_time.splice(index, 1)
			},

			delCom(index) {
				this.form.goods_list.splice(index, 1)
				this.form.is_all_goods = '0'
				this.$forceUpdate()
			},

			actionAllDeviceRadio(e) {
				this.form.is_all_device = e
			},

			actionTimeRadio(e) {
				this.form.is_all_day = e
			},

			actionStatusRadio(e) {
				this.form.enable_status = e
			},

			timeChange(index) {
				this.timeIndex = index
				this.$refs.timeslot.open()
			},

			confirmTime(e) {
				this.form.day_time[this.timeIndex].start_time = e.start.hour + ':' + e.start.min
				this.form.day_time[this.timeIndex].end_time = e.end.hour + ':' + e.end.min
			},

			priceSet(value, oldPrice) {
				if (value != null) {
					if (value <= 0 || value * 100 >= oldPrice) {
						this.$modal.msg('特价价格需低于原价，特价不能为0')
					}
				}
			},

			plusCanChoose() {
				this.actionsShow = true
			},

			actionsSelect(e) {
				this.form.plus_can_type = e.name
			},

			//表单提交
			submit() {
				console.log(this.form)
				if (this.checkParams(this.form)) return
				this.$refs.form.validate().then(res => {
					let goods_list = this.form.goods_list.map(item => ({
						cover: item.cover,
						goods_id: item.goods_id,
						goods_name: item.goods_name,
						price_old: item.price_old,
						price_onsale: this.$xy.delMoneyL(item.price_onsale),
						total_num: item.total_num
					}))
					let params = {
						name: this.form.name,
						device_ids: this.form.is_all_device == 0 ? this.form.device_ids : null,
						goods_list: goods_list,
						start_date: this.form.start_date,
						end_date: this.form.end_date,
						is_all_day: this.form.is_all_day == '全天' ? 1 : 0,
						day_time: this.form.day_time,
						enable_status: this.form.enable_status,
						weight: this.form.weight,
						is_all_device: this.form.is_all_device,
						is_all_goods: this.form.is_all_goods,
						type: '特价',
						plus_can_type: this.form.plus_can_type
						// user_limit_num: this.form.user_limit_num
					}
					if (this.form.id) {
						params.id = this.form.id
						speEdit(params).then(res => {
							this.$modal.msg('修改成功~')
							setTimeout(() => {
								this.$tab.navigateBack()
							}, 1000)
						}).catch(err => {

						})
					} else {
						addSpe(params).then(res => {
							this.$modal.msg('新建成功~')
							setTimeout(() => {
								this.$tab.navigateBack()
							}, 1000)
						}).catch(err => {

						})
					}
				}).catch(errors => {
					console.log(errors)
				})
			},

			checkParams(params) {
				if (!params.goods_list || params.goods_list.length == 0) {
					this.$modal.msg('请至少选择一件商品')
					return true
				}

				let bflag = false
				for (let item of params.goods_list) {
					if (item.price_onsale <= 0 || item.price_onsale * 100 >= item.price_old) {
						this.$modal.msg('特价价格需低于原价，特价不能为0')
						bflag = true
						break
					}

					if (!this.$xy.regdoitTwo(item.price_onsale)) {
						this.$modal.msg('特价价格格式不正确，最多两位小数。')
						bflag = true
						break
					}
				}
				
				if(bflag){
					return true
				}
				
				return false
			},
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},

		onUnload() {
			uni.$off(['updateGoods', 'updateDevices'])
		}
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
			}

			.input-text {
				text-align: right !important;
			}

			.tab-wrap {
				padding: 12rpx 0;
			}

			.choose-list {
				width: 418rpx;

				>text {
					word-break: break-all;
				}
			}

			.mj-content {

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 230rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.form-item {
				position: relative;

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.action-time {
			position: relative;

			.edit {
				color: #2C6FF3;
				text-decoration: underline;
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);

				&.del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.priority {
			position: relative;

			.tips {
				position: absolute;
				color: red;
				font-size: 24rpx;
				left: 100rpx;
				top: 50%;
				transform: translateY(-50%);
			}
		}


		.time-wrap {
			.time-item {
				line-height: 84rpx;

				.time-select {}

				.action-time-del {
					color: red;
					text-decoration: underline;
				}
			}
		}

		.spe-list {
			.spe-head {
				padding: 0 17rpx;
			}

			.spe-body {
				.com-item {
					padding: 25rpx 25rpx 30rpx;
					box-shadow: 0 6px 18px #1d293924;
					position: relative;

					+.com-item {
						margin-top: 20rpx;
					}

					background: #FFFFFF;
					border-radius: 14rpx;
					box-shadow: #333333;

					.com-img {
						margin-right: 26rpx;
					}

					.com-content {
						.com-name {
							font-size: 28rpx;
							font-weight: 800;
							color: #333333;
							width: 460rpx;
						}

						.com-old-price {
							font-size: 26rpx;
							color: #555555;
							margin-top: 20rpx;
						}

						.com-num-spe {
							font-size: 26rpx;
							margin-top: 20rpx;

							.com-num,
							.com-spe {
								.com-input {
									width: 139rpx;
									height: 68rpx;
									border: 1px solid #D8D8D8;
									border-radius: 8rpx;
									padding: 0 12rpx;
								}
							}

							.com-num {
								margin-right: 58rpx;
							}
						}
					}

					.close-btn {
						position: absolute;
						right: 0;
						top: 0;
						padding: 24rpx;
					}
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>