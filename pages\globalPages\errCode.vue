<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="常见错误码"></u-navbar>
		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
					:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
					lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y :style="{height:fullHeight}">
			<view class="content">
				<view class="log-item" v-for="(item,index) in logs" :key="index">
					<view class="logs">
						<view class="name">错误码</view>
						<view class="val">{{item.code}}</view>
					</view>
					<view class="logs">
						<view class="name">错误描述</view>
						<view class="val">{{item.describe}}</view>
					</view>
					<view class="logs">
						<view class="name">建议处理方案</view>
						<view class="val">{{item.deal}}</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		byId,
		orderLogs,
		refundDetail
	} from "@/api/order/order.js"
	let normalLogs = [{
			code: 'IPC1001',
			describe: 'IPC子板无法连接',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'IPC1002',
			describe: 'IPC子板网络连接正常，但无法通讯',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'IPC1101',
			describe: 'IPC无法识别摄像头',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'CTRL1001',
			describe: '控制板无法连接',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'CTRL1002',
			describe: '控制板网络连接正常，但无法通讯',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'CTRL1102',
			describe: '电插锁发送关门命令后锁芯无法落下',
			deal: '请检查柜门是否关到位，如无法顺畅关门，请报修；'
		},
		{
			code: 'CTRL1104',
			describe: '非交易状态下发现锁开',
			deal: '请撕掉门锁上的胶带；'
		},
		{
			code: 'TRA1001',
			describe: '在非交易状态下检测到门处于开启状态',
			deal: '请检查柜门是否关到位，如无法顺畅关门，请报修；'
		},
		{
			code: 'Z1002',
			describe: '刷脸摄像头异常，请重启设备后再试（Z1002）',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'Z1020',
			describe: '刷脸摄像头异常，请重启设备后再试（Z1020）',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'Z1021',
			describe: '刷脸摄像头异常，请重启设备后再试（Z1021）',
			deal: '请重启设备，若故障未恢复，请报修；'
		},
		{
			code: 'LCN1001',
			describe: '网络问题/后台服务器无法连接',
			deal: '请检查网络连接，切换可用网络；'
		},
		{
			code: 'HRD1002',
			describe: '设备断电',
			deal: '设备断电，请到现场恢复供电'
		},
		{
			code: '33019',
			describe: '用户授权异常（上一笔交易未结束，或存在未解约的协议）',
			deal: '账号存在未解约协议，请联系商户处理'
		},
		{
			code: '33004',
			describe: '找不到商品列表',
			deal: '设备未配置商品'
		},
		{
			code: '33005',
			describe: '商品列表下没有商品',
			deal: '设备未配置商品'
		},
		{
			code: '33015',
			describe: '授权处理超时',
			deal: '请重新刷脸或扫码授权'
		}
	]
	let aliLogs = [{
		code: 'E---',
		describe: '软件未正常启动',
		deal: '请断完电（等到门锁灯灭了）后再开机。'
	}, {
		code: 'E200',
		describe: '左（主）柜主摄像头打开错误',
		deal: '软件会自动重启修复此错误，如反复出现，很可能是出现了电池干扰，这可能是电源引起的，请联系售后进行处理。'
	}, {
		code: 'E201',
		describe: '左（主）柜辅摄像头打开错误',
		deal: '为确保交易的稳定完成，软件会暂时停用辅助摄像头，待下一次重启运行时重新检查恢复。'
	}, {
		code: 'E202',
		describe: '右柜主摄摄像头打开错误',
		deal: '软件会自动重启修复此错误，如反复出现，很可能是出现了电池干扰，这可能是电源引起的，请联系售后进行处理。'
	},  {
		code: 'E203',
		describe: '右柜辅摄像头打开错误',
		deal: '为确保交易的稳定完成，软件会暂时停用辅助摄像头，待下一次重启运行时重新检查恢复。'
	},  {
		code: 'F001',
		describe: '磁盘空间不足',
		deal: '请尝试断电（等到门锁灯灭了）重启机器，或联系后台运维人员处理。'
	},  {
		code: 'E002',
		describe: '连接密码错误',
		deal: '设备SN号未注册，请联系后台运维人员处理。'
	},  {
		code: 'E003',
		describe: '后台断开',
		deal: '网络不稳定，可以尝试断电（等到门锁灯灭了）重启机器。'
	},  {
		code: 'E004',
		describe: '后台连接失败',
		deal: '机器有网，但连接后台失败，可能是网络不稳定，可以尝试断电（等到门锁灯灭了）重启机器。'
	},  {
		code: 'E02',
		describe: '未识别SIM卡',
		deal: '请在确保机器已断电再取出SIM卡后，重新插入，也可以尝试在SIM卡背面垫纸片后再插入。'
	},  {
		code: 'E009',
		describe: '网络不可用',
		deal: '机器没有网络，请依次检查天线是否接好，流量卡是否欠费。'
	},  {
		code: 'E10',
		describe: '温控探头错误',
		deal: '请联系售后维修处理。'
	},  {
		code: 'LL',
		describe: '温度故障',
		deal: '未能读取到机器温度（部分又门柜是有一个柜没有温度检测的，可能会显示这个错误码，是正常的）。'
	},  {
		code: 'P01',
		describe: '已锁机',
		deal: '机器已锁机，不能交易'
	},   {
		code: 'P51',
		describe: '开锁成功',
		deal: '交易或补货开锁成功'
	},   {
		code: 'P52',
		describe: '开锁失败',
		deal: '交易或补货开锁失败'
	},   {
		code: 'P53',
		describe: '已锁好',
		deal: '已关门并锁好'
	},   {
		code: 'P54',
		describe: '未锁好',
		deal: '门已拉开，未锁好'
	},   {
		code: 'P55',
		describe: '门未拉开',
		deal: '门锁已打开，但未拉开门'
	},   {
		code: 'P60',
		describe: '上传录像中',
		deal: '交易视频上传中'
	},   {
		code: 'P61',
		describe: '上传结束',
		deal: '交易视频上传完成'
	},   {
		code: 'P62',
		describe: '上传失败',
		deal: '交易视频上传失败'
	},  ]
	export default {
		data() {
			return {
				tabList: [{
						code: 0,
						name: '支付宝动态视觉柜',
					},
					{
						code: 1,
						name: '动态视觉柜',
					}
				],
				current: 0,
				fullHeight: 0
			}
		},
		computed: {
			logs() {
				return this.current == 0 ? normalLogs : aliLogs
			}
		},
		async onLoad(o) {
			this.fullHeight = await this.$xy.scrollHeight(this, '.scrollview', 0)
		},
		methods: {
			tabClick(e) {
				this.current = e.index
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		line-height: 50rpx;

		.tab-wrap {
			padding: 12rpx 0;
			background-color: #fff;

			.tab {
				width: 100%;
			}
		}

		.text {
			color: #2C6FF3;
		}

		.content {
			padding-top: 24rpx;
		}

		.log-item {
			width: 702rpx;
			margin-bottom: 50rpx;
			padding: 0 24rpx;

			.logs {
				overflow: hidden;
				width: 100%;
			}

			.name {
				float: left;
				margin-right: 24rpx;
				width: 26%;
				color: gray;
			}

			.val {
				float: left;
				word-wrap: break-word;
				word-break: break-all;
				width: 70%;
			}
		}
	}
</style>