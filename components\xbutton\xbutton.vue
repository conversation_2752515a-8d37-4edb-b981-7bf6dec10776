<template>
	<button @click="btnClick" :class="['cu-btn',size,icon?'has-icon':'']"
		:style="{backgroundColor:bgColor,padding:padding,color:color,borderRadius:round,width:delWidth,border:delBorder,fontSize:fontSize}">
		<slot></slot>
		<view class="image" v-if="icon">
			<u--image width="32rpx" height="32rpx" :src="icon" mode="widthFix" :lazy-load="true">
			</u--image>
		</view>
	</button>
</template>

<script>
	import {
		debounce
	} from '@/utils/common.js'
	export default {
		data() {
			return {
				bflag: false,
				timer: null,
			}
		},
		props: {
			text: {
				type: String,
				require: true,
				default: ''
			},
			color: {
				type: String,
				require: true,
				default: '#fff'
			},
			bgColor: {
				type: String,
				require: true,
				default: '#2C6FF3'
			},
			padding: {
				type: String
			},
			round: {
				type: [Number, String],
				default: 'none'
			},
			size: {
				type: String,
				require: true,
				default: 'normal'
			},
			width: {
				type: String
			},
			borderColor: {
				type: String
			},

			//防重击锁止按钮
			delay: {
				type: [Number, String],
				require: false,
				default: 1000
			},
			icon: {
				type: String,
				require: false
			},
			fontSize: {
				type: String,
				require: false
			}
		},

		computed: {
			delWidth() {
				let width = this.width;
				if (this.size == 'mini' && !this.width) {
					width = "auto"
				}
				if (this.size != 'mini' && !this.width) {
					width = "100%"
				}
				return width
			},
			delBorder() {
				let border = this.borderColor;
				if (!this.borderColor) {
					border = `1rpx solid ${this.bgColor}`
				} else {
					border = `1rpx solid ${border}`
				}
				return border
			}
		},

		methods: {
			btnClick() {
				if (this.bflag) {
					this.$modal.msg('操作过快~')
					return
				}
				console.log('确定提交')
				this.$emit('click')
				this.bflag = true;
				this.timer = setTimeout(() => {
					this.bflag = false;
				}, this.delay)
			}
		},

		destroyed() {
			if (this.timer) {
				clearTimeout(this.timer)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mini {
		font-size: 24rpx;
		border-radius: 6rpx;
		padding: 0 12rpx;
		height: 40rpx;
		line-height: 38rpx;
		display: inline-block;
		width: 100rpx;
	}

	.medium {
		font-size: 24rpx;
		border-radius: 8rpx;
		padding: 0 12rpx;
		height: 50rpx;
		line-height: 48rpx;
		display: inline-block;
		width: 100rpx;
	}

	.normal {
		padding: 0 14rpx;
		font-size: 26rpx;
		height: 60rpx;
		line-height: 58rpx;
		border-radius: 8rpx;
	}

	.large {
		border-radius: 12rpx;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
	}

	.cu-btn {
		white-space: nowrap;

		&.has-icon {
			padding-left: 54rpx;
			padding-right: 22rpx;
			position: relative;

			.image {
				width: 32rpx;
				height: 32rpx;
				position: absolute;
				left: 13rpx;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}
</style>