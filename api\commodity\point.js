import request from '@/utils/request'

// 分页
export function pointPage(data) {
  return request({
    url: '/mini/mercPlace/page',
    method: 'post',
	data:data
  })
}

// 新增修改点位
export function pointSave(data) {
  return request({
    url: '/mini/mercPlace/save',
    method: 'post',
	data:data
  })
}



// 绑定/解绑设备 
export function bindDevice(data) {
  return request({
    url: '/mini/mercPlace/bindDevice',
    method: 'post',
    data:data
  })
}

// 列表(内部服务调用) 
export function listById(data) {
  return request({
    url: '/mini/mercPlace/listById',
    method: 'post',
    data:data
  })
}

// 列表
export function pointListByMerc(data) {
  return request({
    url: '/mini/mercPlace/listByMerc',
    method: 'post',
    data:data
  })
}

// 删除
export function pointDel(data) {
  return request({
    url: '/mini/mercPlace/del',
    method: 'post',
    data:data
  })
}
