<template>
	<view class="notice-wrap">
		<!-- 每日一图 -->
		<u-popup :show="tipsShow" @close="tipsClose" :overlayStyle="{zIndex:zIndex-5}" :zIndex="zIndex" @open="open"
			mode="center" :safeAreaInsetBottom="false" bgColor="transparent">
			<view class="pop-content">
				<rich-text :nodes="tipsDetail.noticeContent" @click="detail"></rich-text>
				<image @click="tipsClose" class="close-btn" src="../../../static/images/global/close-btn.png" mode="widthFix"></image>
				<view class="next" @click="refresh">下一图</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		props: {
			tipsList: {
				type: Array,
				default: function() {
					return []
				}
			},
			zIndex: {
				type: Number,
				require: false,
				default: 10075
			},
		},

		data() {
			return {
				tipsShow: false,
				tipsIndex: 0
			}
		},

		computed: {
			tipsDetail() {
				let obj = this.tipsList[this.tipsIndex]
				return obj
			}
		},

		methods: {
			btnClick() {
				if (this.tipsIndex == this.tipsList.length - 1) {
					this.tipsClose()
				} else {
					this.tipsIndex++
				}
			},
			tipsClose() {
				this.tipsShow = false
				this.$emit('close', this.tipsIndex)
			},
			detail() {
				this.$emit('conClick')
			},
			/**
			 * 下一张图片
			 */
			refresh() {
				this.$emit('refresh')
			},
			
			open() {
				this.$emit('open', true)
			},
		}
	}
</script>

<style scoped lang="scss">
	.pop-content {
		width: 602rpx;
		position: relative;
		img{
			display: block;
		}

		.close-btn {
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translate(-50%,96%);
			width: 60rpx;
		}
		
		.next{
			position: absolute;
			right:10rpx;
			bottom:10rpx;
			text-align: center;
			padding:0 20rpx;
			border-radius: 50rpx;
			line-height: 50rpx;
			color: #fff;
			background-color: rgba(0, 0, 0, .3);
		}
	}
</style>