<template>
	<Imgresiz @upload="doUpload" :avatarSrc="value" quality="1" ref="avatar" />
</template>

<script>
	import {
		ossInfo
	} from '@/api/oss.js'
	import {
		getRandomNum
	} from '@/utils/common.js'
	import imgUpload from '@/utils/upload.js'
	import Imgresiz from "../imgresiz"
	export default {
		components:{
			Imgresiz
		},
		data() {
			return {
				uploadData: { //oss返回数据
					policy: undefined,
					Signature: undefined,
					OSSAccessKeyId: undefined,
					uploadImgUrl: undefined,
					dir: undefined,
					key: undefined
				},
			}
		},
		props: {
			value: {
				type: String,
				default () {
					return ''
				}
			},
		},


		methods: {
			fChooseImg(obj) {
				this.$refs.avatar.fChooseImg(obj);
			},

			oss() {
				return new Promise((resolve, reject) => {
					ossInfo().then(response => {
						this.uploadData.policy = response.data.policy;
						this.uploadData.Signature = response.data.signature;
						this.uploadData.OSSAccessKeyId = response.data.accessid;
						this.uploadData.uploadImgUrl = response.data.domain;
						this.uploadData.dir = response.data.dir;
						resolve(response)
					}).catch(err => {
						reject(err)
					})
				})
			},

			async doUpload(rsp) {
				await this.oss() //获取oss数据
				await this.uploadFilePromise(rsp.path).then(res => {
					this.$emit('success', res)
				})
			},

			uploadFilePromise(url) {
				//组装上传数据
				let timestamp = new Date().getTime()
				let randomNum = getRandomNum(10000, 99999)
				let imgType = url.substr(url.indexOf('.'))
				this.uploadData.key = `${this.uploadData.dir}${timestamp}${randomNum}${imgType}`
				let params = {
					policy: this.uploadData.policy,
					Signature: this.uploadData.Signature,
					OSSAccessKeyId: this.uploadData.OSSAccessKeyId,
					key: this.uploadData.key
				}

				return new Promise((resolve, reject) => {
					imgUpload({
						url: this.uploadData.uploadImgUrl,
						header: {
							isToken: true
						},
						formData: params,
						filePath: url
					}).then((res) => {
						console.log('res', res)
						let url = this.uploadData.uploadImgUrl + '/' + this.uploadData
							.key //图片路径需要前端组装
						resolve(url)
					}).catch(err => {
						reject(err)
					})
				})
			},
		}
	}
</script>