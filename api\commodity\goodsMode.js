import request from '@/utils/request'

// 新增
export function save(data) {
  return request({
    url: '/goods/merc-mini/goodsMode/save',
    method: 'post',
	data:data
  })
}

// 修改
export function update(data) {
  return request({
    url: '/goods/merc-mini/goodsMode/update',
    method: 'post',
	data:data
  })
}

// 对象查询
export function searchObj(data) {
  return request({
    url: '/goods/merc-mini/goodsMode/obj',
    method: 'post',
	data:data
  })
}

// 分页
export function goodsPage(data) {
  return request({
    url: '/goods/merc-mini/goodsMode/page',
    method: 'post',
	data:data
  })
}

// 商品类目
export function categoryTree(data) {
  return request({
    url: '/goods/merc-mini/goods/category/tree',
    method: 'post',
	data:data
  })
}

// 商品规格
export function listIdName(data) {
  return request({
    url: '/goods/merc-mini/goods/unit/listIdName',
    method: 'post',
	data:data
  })
}

// 设备商品价格修改
export function changePrice(data) {
  return request({
    url: '/goods/merc-mini/goodsDevice/update',
    method: 'post',
	data:data
  })
}


