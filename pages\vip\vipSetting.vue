<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="active?'vip会员':'会员激活'"></u-navbar>
		<view class="content safe-bottom">
			<view class="" v-if="active">
				<view class="flex justify-between" style="padding: 20rpx 20rpx 0 0;">
					<xhelpPopup guideId="HY0001" />
					<xbutton padding="15rpx 40rpx" @click="$tab.navigateTo('/pages/rechargeAct/oldVsNewRec')">老带新记录
					</xbutton>
				</view>
				<view class="img-upload flex align-center">
					<view>
						<view>弹窗活动图片</view>
						<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：690*800</view>
					</view>
					<ximgUpload :maxCount="1" v-model="popImg" />
				</view>
				<view class="img-upload flex align-center">
					<view>
						<view>横幅推广图片</view>
						<view style="font-size: 22rpx;line-height: 40rpx;color: red;">尺寸：750*280</view>
					</view>
					<ximgUpload :maxCount="1" v-model="bannerImgList" />
				</view>

				<view class="choose-dev">
					<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
						<u-form-item label="选择设备" :borderBottom="form.isAllDevice==0">
							<u-radio-group v-model="form.isAllDevice" placement="row" @change="actionAllDeviceRadio">
								<u-radio :customStyle="{marginRight: '40rpx'}" label="全部" name="1">
								</u-radio>
								<u-radio label="指定设备" name="0">
								</u-radio>
							</u-radio-group>
						</u-form-item>

						<u-form-item label="指定设备" @click="chooseDev" v-if="form.isAllDevice==0">
							<view class="choose-list" v-if="form.deviceList&&form.deviceList.length>0">
								<text v-for="(item,index) in form.deviceList" :key="item.deviceId">
									{{item.deviceName||item.deviceId}}
									<text v-if="index!=form.deviceList.length-1">，</text>
								</text>
							</view>
							<view v-else style="color: #c0c4cc;text-align: right;">请选择设备</view>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
					</u--form>
				</view>

				<view class="title flex justify-between align-center">
					<view>
						会员套餐
					</view>
					<view class="flex align-center add" @click="addVip">
						<u-icon name="plus-circle" size="20" color="#2C6FF3"></u-icon>
						<view style="padding-left: 8rpx;">
							新增套餐
						</view>
					</view>
				</view>
				<view class="section section1" v-if="comList&&comList.length>0">
					<view class="vip-scroll">
						<u-scroll-list :indicator="false">
							<view class="flex vip-scroll-box">
								<block v-for="(item, index) in comList" :key="item.id">
									<view class="vip-item-wrap">
										<view :class="[index==current?'vip-item vip-item-selected':'vip-item']"
											@click="vipSelect(index)">
											<view class="dis-tips">
												<image v-if="index==current"
													src="../../static/images/vip/vipselected.png">
												</image>
												<image v-else src="../../static/images/vip/vipselect.png"></image>
												<view class="dis-con">限时特惠{{Number(item.discount)*10}}折</view>
											</view>
											<view class="types">
												{{item.mealName}}
											</view>
											<view class="price">
												￥<text>{{$xy.delMoney(item.price)}}</text>
											</view>
											<view class="exp-time">
												有效时间：{{item.days}}天
											</view>
										</view>
										<view class="edit flex justify-around">
											<xbutton width="130rpx" height="58rpx" round="58rpx" @click="editVip(item)">
												编辑
											</xbutton>
										</view>
									</view>
								</block>
							</view>
						</u-scroll-list>
					</view>
					<view class="tips">
						{{remark}}
					</view>
				</view>

				<view class="empty" v-else>
					请前往新增套餐~
				</view>
			</view>
			<view class="active-content" v-else>
				<view class="active-bg">
					<u--image width="724rpx" height="370rpx"
						src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/vip/active-bg.png"
						mode="widthFix" :lazy-load="true"></u--image>
				</view>

				<view class="btn flex justify-around">
					<xbutton size="large" width="720rpx" height="90rpx" round="90rpx" fontSize="32rpx"
						@click="goActive">
						立即激活（{{$xy.delMoney(activeMoney)}}元）</xbutton>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import getDict from '@/utils/getDict.js'
	import {
		page,
		objs,
		updateImgs,
		updateDevice
	} from '@/api/vip.js'
	export default {
		data() {
			return {
				agree: false,
				comList: [],
				current: 0,
				popImg: [],
				bannerImgList: [],
				active: true,
				activeMoney: 0,
				form: {
					isAllDevice: '1',
					deviceList: [],
				}
			}
		},

		onLoad() {
			this.getData()

			uni.$on('refresh', res => {
				this.getData()
			})

			uni.$on('updateList', res => {
				this.getList()
			})

			uni.$on('updateDev', res => {
				if (res && res.length > 0) {
					this.form.deviceList = res
				} else {
					this.form = {
						isAllDevice: '1',
						deviceList: [],
					}
				}
				this.updateDevice()
			})
		},

		computed: {
			remark() {
				return this.comList.length > 0 ? this.comList[this.current].remark : '暂无说明'
			}
		},

		watch: {
			popImg: {
				handler(newVal, oldVal) {
					this.updateImgs()
				},
				immediate: false,
				deep: true
			},
			bannerImgList: {
				handler(newVal, oldVal) {
					this.updateImgs()
				},
				immediate: false,
				deep: true
			}
		},

		methods: {
			/**
			 * 获取数据
			 */
			getData() {
				this.getObj().then(res => {
					this.getList()
				}).catch(err => {
					this.getActiveMoney()
				})
			},

			/**
			 * 首页信息
			 */
			getObj() {
				return new Promise((resolve, reject) => {
					objs({}).then(res => {
						if (res.data && res.data.activeState) {
							let data = res.data
							this.popImg = data.popImg ? [data.popImg] : []
							this.bannerImgList = data.bannerImgList || []
							this.active = data.activeState
							this.form.isAllDevice = data.isAllDevice ? '1' : '0'
							this.form.deviceList = data.deviceList || []
							resolve(res)
						} else {
							this.active = false
							reject(res)
						}
					})
				})
			},

			/**
			 * @param {Object} img
			 * 更新图片
			 */
			updateImgs(img) {
				updateImgs({
					popImg: this.popImg[0],
					bannerImgList: this.bannerImgList
				})
			},

			/**
			 * @param {Object} e
			 * 选择设备切换
			 */
			actionAllDeviceRadio(e) {
				this.form.isAllDevice = e
				if (e == '0') { //指定设备
					if (this.form.deviceList && this.form.deviceList.length > 0) {
						this.updateDevice()
					} else {
						this.chooseDev()
					}
				} else { //全部设备
					this.updateDevice()
				}
			},
			/**
			 * 更新设备
			 */
			updateDevice() {
				updateDevice({
					isAllDevice: this.form.isAllDevice == '1',
					deviceIdList: this.form.isAllDevice == '0' ? this.form.deviceList.map(i => i.deviceId) : []
				})
			},

			/**
			 * 选择设备
			 */
			chooseDev() {
				let ids = []
				if (this.form.deviceList && this.form.deviceList.length > 0) {
					ids = this.form.deviceList.map(i => i.deviceId.toString())
				}
				this.$tab.navigateTo(`/pages/vip/chooseDevice?ids=${JSON.stringify(ids)}`)
			},

			/**
			 * 套餐列表
			 */
			getList() {
				page({
					page: {
						current: 1,
						size: 100
					}
				}).then(res => {
					this.comList = res.data.records
				})
			},

			editVip(item) {
				this.$tab.navigateTo(`/pages/vip/editVip?id=${item.id}`)
			},

			addVip() {
				console.log(123)
				this.$tab.navigateTo('/pages/vip/editVip')
			},

			activeStatus() {

			},

			getActiveMoney() {
				getDict('merc_plus_config').then(res => {
					this.activeMoney = res[0].value
				})
			},

			vipSelect(index) {
				this.current = index
			},

			agreeSelect() {
				this.agree = !this.agree
			},

			goActive() {
				let type = [6]
				let url =
					`/pages/recharge/rechargeIng?type=${JSON.stringify(type)}&money=${this.activeMoney}`
				this.$tab.navigateTo(url)
			}
		},
		onUnload() {
			uni.$off(['updateDev', 'refresh', 'updateList'])
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;
		background-color: #EEEEEE;

		.content {
			overflow: hidden;

			.img-upload {
				height: 200rpx;
				font-size: 26rpx;
				color: #333;
				background-color: #fff;
				padding: 0 14rpx;
				margin-top: 20rpx;

				>view {
					margin-right: 41rpx;
				}
			}

			.choose-dev {
				background-color: #fff;
				padding: 14rpx;
				margin-top: 20rpx;
				line-height: 40rpx;
			}

			.choose-list {
				>text {
					word-break: break-all;
				}
			}

			.title {
				line-height: 96rpx;
				padding: 0 14rpx;
				background-color: #fff;
				margin-top: 20rpx;

				>view:nth-child(1) {
					font-size: 28rpx;
					color: #000033;
					font-weight: bold;
				}

				.add {
					font-size: 22rpx;
					color: #2C6FF3;
				}
			}

			.section {
				width: 100%;
				padding: 12rpx 0 20rpx;
				background: #FFFFFF;
				border-radius: 0rpx 0rpx 14rpx 14rpx;
				margin-top: -20rpx;

				&.section1 {
					padding-bottom: 90rpx;
					margin-bottom: 20rpx;
				}

				&.section2 {
					padding-bottom: 40rpx;
				}

				.vip-scroll {
					// height: 268rpx;
					width: 750rpx;
					position: relative;

					.vip-scroll-box {
						padding: 15rpx 0;
					}

					.vip-item-wrap {
						&:nth-last-child(1) {
							margin-right: 20rpx;
						}
					}

					.vip-item {
						width: 196rpx;
						height: 238rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 1rpx 7rpx 0rpx rgba(29, 76, 63, 0.17);
						border-radius: 10rpx;
						margin-left: 12rpx;
						text-align: center;

						&:nth-last-child(1) {
							margin-right: 20rpx;
						}

						.dis-tips {
							width: 118rpx;
							height: 30rpx;
							position: relative;

							image {
								width: 118rpx;
								height: 30rpx;
							}

							.dis-con {
								width: 100%;
								height: 100%;
								font-size: 16rpx;
								color: #734C01;
								text-align: center;
								line-height: 30rpx;
								position: absolute;
								left: 0;
								top: 0;
							}
						}

						.types {
							width: 100%;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
							font-size: 24rpx;
							color: #333333;
							margin-top: 38rpx;
						}

						.price {
							font-size: 24rpx;
							color: #333333;
							margin-top: 18rpx;

							>text {
								font-size: 36rpx;
								font-weight: bold;
							}
						}

						.exp-time {
							font-size: 18rpx;
							color: #777777;
							margin-top: 38rpx;
						}

						&.vip-item-selected {
							background-color: #FFF3DC;

							.dis-tips {
								.dis-con {
									color: #fff;
								}
							}

							.types {
								color: #734C01;
							}

							.price {
								color: #FF2F2F;
							}

							.exp-time {
								color: #333333;
							}
						}
					}

					.edit {
						margin-top: 25rpx;
					}
				}

				.tips {
					color: #777777;
					margin-top: 20rpx;
					padding: 0 46rpx;
					font-size: 24rpx;
					color: #777777;
					line-height: 40rpx;
					white-space: pre-line;
				}

			}

			.empty {
				text-align: center;
				line-height: 200rpx;
				background-color: #fff;
				color: #cbcbcb;
			}
		}

		.active-content {
			height: 100vh;
			width: 100%;
			background-color: #fff;
			padding: 20rpx 13rpx;
			text-align: center;

			.active-bg {
				height: 370rpx;
				position: relative;
			}

			.btn {
				margin-top: 87rpx;
			}
		}
	}
</style>