<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="渠道类型管理"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap" style="padding:0 30rpx;">

					<u-form-item label="推广标题" borderBottom prop="title">
						<u--input inputAlign="right" v-model="form.title" placeholder="请输入推广标题"
							border="none"></u--input>
					</u-form-item>

					<u-form-item label="渠道类型" borderBottom @click="actionChoose('channelType')">
						<view style="text-align: right;" v-if="form.typeName">{{form.typeName}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择渠道类型</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<u-form-item label="活动类型" borderBottom @click="actionChoose('actType')">
						<view style="text-align: right;" v-if="actTypeName">{{actTypeName}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择活动类型</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<u-form-item label="活动" borderBottom @click="actionChoose('actName')">
						<view style="text-align: right;" v-if="actName">{{actName}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择活动</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<u-form-item label="开始日期" borderBottom @click="chooseDate('start')">
						<view style="text-align: right;" v-if="form.startDate">{{form.startDate}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择开始日期</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>

					<u-form-item label="结束日期" borderBottom @click="chooseDate('end')">
						<view style="text-align: right;" v-if="form.endDate">{{form.endDate}}
						</view>
						<view v-else style="color: #c0c4cc;text-align: right;">请选择结束日期</view>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>

		<u-action-sheet :show="actionSheetShow" :actions="actions" :title="actionTitle" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>

		<u-datetime-picker :show="dateShow" v-model="date" confirmColor="#2C6FF3" mode="date"
			:closeOnClickOverlay="true" @close="dateShow=false" @confirm="dateConfirm"></u-datetime-picker>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		channelPage,
		searchAct,
		saveOrUpdate,
		detail
	} from "@/api/channel.js"

	export default {
		data() {
			return {
				form: {
					endDate: null,
					startDate: null,
					title: null,
					typeName: null,
					actType: null,
					actId: null
				},
				actTypeName: null,
				actName: null,

				dateShow: false,
				typeOptions: [],
				calendarMonthNum: 10,
				popImg: [],
				bannerImg: [],
				actionSheetShow: false,
				actions: [],
				date: new Date(),
				dateType: '',
				actionTitle: '',
				channelActions: [],
				actTypeActions: [],
				actActions: []
			}
		},

		async onLoad(o) {
			if (o.id) {
				await this.getDetail(o.id)
				this.getActList(this.form.actType)
			}

			await getDict('channel-act-type').then(res => {
				this.actTypeActions = res.map(i => ({
					name: i.msg,
					value: i.code
				}))
			})

			this.getChannelType()
		},

		methods: {
			getDetail(id) {
				return new Promise((resolve, reject) => {
					detail({
						id: id
					}).then(res => {
						let data = res.data
						this.form = data
						this.actName = data.actName
						this.actTypeName = data.actTypeName
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			getChannelType() {
				channelPage({
					page: {
						current: 1,
						size: 1000
					}
				}).then(res => {
					let data = res.data.records
					if (data.length > 0) {
						let actions = data.map(i => {
							return {
								name: i.name,
								value: i.id
							}
						})
						this.channelActions = actions
						console.log(this.channelActions)
					} else {
						this.noChannelType()
					}
				})
			},

			noChannelType() {
				this.$modal.confirm('新建推广至少需要配置一个渠道类型，是否前往添加？').then(res => {
					this.$tab.navigateTo('/pages/channel/type')
				}).catch(err => {

				})
			},

			actionChoose(type) {
				switch (type) {
					case 'channelType':
						this.actionTitle = '渠道类型';
						this.actions = this.channelActions
						if (this.actions.length == 0) {
							this.noChannelType()
						} else {
							this.actionSheetShow = true
						}
						break;
					case 'actType':
						this.actionSheetShow = true
						this.actionTitle = '活动类型';
						this.actions = this.actTypeActions
						break;
					case 'actName':
						this.actionSheetShow = true
						this.actionTitle = '活动';
						this.actions = this.actActions
						break;
					default:
						break;
				}
			},

			/**
			 * @param {Object} e
			 * 筛选
			 */
			actionsheetSelect(e) {
				switch (this.actionTitle) {
					case '渠道类型':
						this.form.typeName = e.name
						break;
					case '活动类型':
						this.form.actType = e.value
						this.actTypeName = e.name
						this.form.actId = null
						this.actName = null
						this.getActList(e.value)
						break;
					case '活动':
						this.form.actId = e.value
						this.actName = e.name
						break;
					default:
						break;
				}
			},

			getActList(value) {
				searchAct({
					actType: value
				}).then(res => {
					let data = res.data
					if (data.length > 0) {
						let actions = data.map(i => {
							return {
								name: i.name,
								value: i.id
							}
						})
						this.actActions = actions
					}
				})
			},

			chooseDate(type) {
				this.dateType = type
				this.dateShow = true
			},

			dateConfirm(e) {
				let date = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				if (this.dateType == 'start') {
					console.log('start')
					if (this.form.endDate && new Date(date).getTime() > new Date(this.form.endDate).getTime()) {
						this.$modal.msg('开始日期不能在结束日期之后')
						return
					}
					this.form.startDate = date
				} else {
					console.log('end')
					if (this.form.startDate && new Date(this.form.startDate).getTime() > new Date(date).getTime()) {
						this.$modal.msg('结束日期不能在开始日期之前')
						return
					}
					this.form.endDate = date
				}
				this.dateShow = false
			},

			submit() {
				saveOrUpdate(this.form).then(res => {
					let msg = this.form.id ? '修改成功！！！' : '新建成功！！！'
					this.$modal.msg(msg)
					setTimeout(() => {
						this.$tab.navigateBack()
						uni.$emit('refresh')
					}, 1500)
				})
			},
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding: 20rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
				border-radius: 12rpx;

				::v-deep .u-radio-group {
					justify-content: flex-end !important;
				}

				.radio-group {
					position: relative;

					.tips {
						font-size: 24rpx;
						color: red;
						line-height: 50rpx;
						padding-bottom: 10rpx;
					}
				}

				.choose-list {
					>text {
						word-break: break-all;
					}
				}
			}

			.mj-content {
				padding-bottom: 30rpx;

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 250rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.img-upload {
				height: 200rpx;
				font-size: 26rpx;
				color: #333;
				background-color: #fff;
				padding: 0 14rpx 0 30rpx;
				margin-top: 20rpx;
				border-radius: 12rpx;

				>view {
					margin-right: 41rpx;
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>