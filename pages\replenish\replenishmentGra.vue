<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="补货首页"></u-navbar>

		<view class="machine-box"> 设备号：{{ deviceId }} </view>

		<view style="padding: 0 150rpx" v-if="deviceType == 7">
			<u-tabs :list="tabList" :activeStyle="{ color: '#333', fontWeight: 'bold', fontSize: '36rpx' }"
				:inactiveStyle="{ fontSize: '32rpx' }" lineColor="#2C6FF3" :scrollable="false" :current="door"
				@click="tabClick"></u-tabs>
		</view>

		<view class="flex justify-between align-center" style="padding: 0 20rpx; margin-top: 30rpx">
			<xbutton width="158rpx" round="14rpx" @click="addGoodsToChannel('floor')">整层商品</xbutton>
			<xbutton width="158rpx" round="14rpx" @click="addStockToChannel">整层库存</xbutton>
			<xbutton width="158rpx" round="14rpx" @click="addgoods">增加商品</xbutton>
			<xbutton width="158rpx" round="14rpx" @click="batchSet">批量设置</xbutton>
		</view>

		<view class="floor">
			<block v-for="(item, index) in floorList" :key="index">
				<view :class="[index == floorIndex ? 'floor-item show' : 'floor-item']"
					@click="floorClick(item, index)">
					{{ item }}
				</view>
			</block>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y :style="{ height: fullHeight }"
			lower-threshold="100" @scrolltolower="scrolltolower">
			<view v-if="goodsList && goodsList.length > 0" class="scroll-content">
				<block v-for="(item, index) in goodsList" :key="item.id">
					<view class="goods-box" v-if="item.goodsId">
						<view class="edit-mark" v-if="isItemModified(item)">改</view>
						<view class="flex flex-sub align-center">
							<view class="image" @click="addGoodsToChannel('single', item)">
								<u--image radius="14rpx" width="120rpx" height="120rpx" :src="
                    item.cover
                      ? item.cover
                      : 'https://static.xynetweb.com/sysFile/defaultgoods.png'
                  " mode="aspectFit" :lazy-load="true">
								</u--image>
							</view>
							<view class="flex flex-sub flex-direction justify-around goods-content">
								<view class="goods-name-price flex justify-between align-start">
									<view class="goods-name" @click="addGoodsToChannel('single', item)">
										{{ item.name }}
									</view>
									<view class="aisle-no"> No.{{ item.aisleNo }} </view>
								</view>

								<view class="flex align-center" style="margin-top: 30rpx">
									<view style="margin-right: 50rpx">
										<view class="goods-stock flex">
											<view>价格：<text>{{ $xy.delMoney(item.price) }}</text>
											</view>
										</view>
									</view>

									<view>
										<view class="goods-stock flex" v-if="item.newStock == 0">
											<view>库存：</view><text>{{ item.stock }}</text>
										</view>
										<view class="goods-stock flex" v-else>
											<view>库存：<text style="text-decoration: line-through">{{
                          item.stock
                        }}</text>
											</view>
											<view style="margin-left: 12rpx; color: red" v-if="item.stock > 0">
												新：{{ Number(item.stock) + Number(item.newStock) }}
											</view>
											<view style="margin-left: 12rpx; color: red" v-else>
												新：{{ Number(item.newStock) }}
											</view>
										</view>
									</view>

									<!-- <view class="flex align-center">
										<view style="margin-left: 20rpx">
											<xbutton round="14rpx" @click="fullItem(item)">满货</xbutton>
										</view>
									</view> -->
								</view>

								<view style="margin-top: 30rpx">
									<view class="goods-stock flex">
										<view>总重量：<text>{{ item.weight / 1000 }}g</text>
										</view>
									</view>
								</view>

								<view class="btn-input flex align-center flex-start" a>
									<view class="btn-input-left">补货：</view>
									<view class="btn-input-right flex align-center">
										<image
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_sub.png"
											mode="widthFix" @click="sub(item)"></image>
										<input type="doit" v-model="item.newStock" />
										<image
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_sup.png"
											mode="widthFix" @click="sup(item)"></image>
									</view>
								</view>
								<image class="edit-goods" @click.stop="editGoods(item, index)"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_edit.png"
									mode="widthFix"></image>
							</view>
						</view>

						<view class="edit-box" v-if="item.show">
							<view @click.stop="changePrice(item)">编辑价格</view>
							<view @click.stop="calibration(item.aisleNo)">秤校准</view>
						</view>
						<view class="bg" v-if="editIndex != null" @click.stop="editHidden"></view>
					</view>

					<view class="goods-box" v-else @click="addGoodsToChannel('single', item)">
						<view class="flex align-start">
							<view class="image">
								<u--image radius="14rpx" width="120rpx" height="120rpx"
									src="https://static.xynetweb.com/sysFile/defaultgoods.png" mode="aspectFit"
									:lazy-load="true">
								</u--image>
							</view>
							<view class="flex flex-sub flex-direction justify-around goods-content">
								<view class="goods-name-price flex justify-end align-start">
									<view class="aisle-no"> No.{{ item.aisleNo }} </view>
								</view>
								<view style="text-decoration: underline; color: red">暂无商品，前往添加</view>
							</view>
						</view>
					</view>
				</block>
				<u-loadmore :status="loadmoreStatus" />
			</view>
			<view v-else class="empty">
				<u-empty mode="data" text="无数据"></u-empty>
			</view>
		</scroll-view>

		<view class="btn safe-bottom">
			<xbutton width="340rpx" round="88rpx" size="large" @click="clearLocalChange" v-if="startRep">清除修改
			</xbutton>
			<xbutton width="340rpx" round="88rpx" size="large" @click="tipsShow = true" v-else>开始补货
			</xbutton>
			<xbutton width="340rpx" round="88rpx" size="large" @click="keep">提交
			</xbutton>
		</view>

		<xpopup :show="tipsShow" @close="tipsClose" :showBtn="false" mode="center">
			<view class="sub-popup-content">
				<view class="sub-title"> 称重视觉柜补货说明 </view>
				<view class="sub-tips flex"><text>1、</text>商品归位重要将被客户拿乱的商品摆回正确的货栏</view>
				<view class="sub-tips flex"><text>2、</text>将新增商品放入商品货栏</view>
				<view class="sub-tips flex"><text>3、</text>补货完成核对商品数量后提交</view>
				<view class="sub-btn" @click="subSubmit"> 商品已归位，开始补货 </view>
			</view>
		</xpopup>

		<!-- 选择左右门 -->
		<u-modal :show="doorShow" @confirm="rightDoor" :showCancelButton="true" confirmText="右门" cancelText="左门"
			confirmColor="#2C6FF3" cancelColor="#2C6FF3" @cancel="leftDoor" title="提示" content="请选择打开左门还是右门~">
		</u-modal>

		<!-- 改价 -->
		<xpopup :show="editPriceShow" @close="editPriceClose" @confirm="priceSet" :showBtn="true" title="修改价格">
			<view class="pop-content">
				<u--input :focus="inputFocus" placeholder="请输入新的价格" border="surround" v-model="editPrice"></u--input>
			</view>
		</xpopup>

		<!-- 修改库存 -->
		<xpopup :show="stockShow" @close="stockClose" @confirm="stockSet" :showBtn="true" title="整层库存修改">
			<view class="pop-content">
				<u--input :focus="inputFocus" placeholder="请输入新的库存" type="digit" border="surround"
					v-model="editStock"></u--input>
			</view>
		</xpopup>

		<!-- 货道商品设置弹框 -->
		<ProductSelector ref="productSelector" :products="productList" @confirm="onProductSelected"
			:placeholder="formData.name || '请选择商品'" />
	</view>
</template>

<script>
	import {
		supplyH5List,
		deviceGoodsList,
		modifyGoodsPrice,
		batchSave,
	} from "@/api/replenishment/replenishment.js";

	import ProductSelector from "./components/product-selector.vue"; // 商品选择器组件

	export default {
		components: {
			ProductSelector,
		},
		data() {
			return {
				goodsList: [], // 商品列表数据
				page: 1, // 当前分页
				size: 10, // 分页数据条数
				tabList: [{
						name: "左门",
					},
					{
						name: "右门",
					},
				], // 门类型
				tabsIndex: 0, // 未使用
				deviceId: null, // 设备ID
				door: 0, // 门类型 0:左门 1:右门
				name: "", // 未使用
				doorShow: false, // 控制门选择显示

				workNo: "", // 未使用
				fullHeight: 0, // 滚动区域高度
				keyword: "", // 未使用
				editIndex: null, // 编辑项索引
				editPriceShow: false, // 控制价格编辑弹窗显示
				editPrice: null, // 编辑价格值
				editGoodsIndex: null, // 编辑商品索引
				changeList: [], // 变更列表（用于结果反显）

				backShow: false, // 控制回显弹窗显示
				deviceInfo: {}, // 未使用
				editTitle: "修改价格", // 编辑弹窗标题
				editCapacity: null, // 未使用

				tempData: [], // 临时数据（所有商品）
				loadmoreStatus: "loadmore", // 加载状态

				tipsShow: false, // 控制提示弹窗显示

				floorIndex: 0, // 当前楼层索引
				floorList: [], // 楼层列表

				deviceType: null, // 设备类型

				goodsId: null, // 当前操作的商品ID
				productList: [], // 商品列表（用于选择器）
				formData: {
					// 表单数据
					goodsId: "",
					name: "",
					cover: "",
				},
				aisleId: null, // 货道ID
				addGoodsToChannelType: null, // 添加商品的类型（整层/单个）
				deviceAisleId: null, // 设备货道ID
				stockShow: false, // 控制库存修改弹窗显示
				editStock: null, // 编辑库存值

				// 新增字段，用于统一本地存储修改记录
				localChanges: {}, // 本地存储所有修改，格式：{layerIndex: [{aisleId, goodsId, stock, price}]}
				inputFocus: false, // 是否聚焦输入框

				startRep: false, //是否已开始补货
			};
		},

		// 页面加载时初始化
		async onLoad(o) {
			// 计算滚动区域高度
			this.fullHeight = await this.$xy.scrollHeight(this, ".scrollview", 50);

			this.deviceId = o.id;
			this.deviceType = o.deviceType;
			this.search(); // 加载商品数据
			this.getDeviceGoodsList(); // 获取设备商品列表

			// 初始化本地更改对象
			this.initLocalChanges();

			// 监听刷新事件
			uni.$on("refresh", (res) => {
				this.search();
				this.getDeviceGoodsList();
			});

			setTimeout(() => {
				this.$forceUpdate();
				console.log("强制更新UI");
			}, 500);
		},

		methods: {
			// 初始化本地修改记录
			initLocalChanges() {
				const storageKey = `replenish_changes_${this.deviceId}_${this.door}`;
				const storedChanges = uni.getStorageSync(storageKey);
				this.localChanges = storedChanges ? JSON.parse(storedChanges) : {};
			},

			// 保存本地修改
			saveLocalChanges() {
				// 保存到本地存储，使用设备ID和门号作为key
				const storageKey = `replenish_changes_${this.deviceId}_${this.door}`;
				try {
					uni.setStorageSync(storageKey, JSON.stringify(this.localChanges));
					console.log("本地修改已保存", Object.keys(this.localChanges).length);
				} catch (e) {
					console.error("保存本地修改失败", e);
				}
			},

			async clearLocalChange() {
				let confirmRes = await this.$modal.confirm('是否确认清空您的所有改动？')
				if (confirmRes.cancel) return
				const storageKey = `replenish_changes_${this.deviceId}_${this.door}`;
				uni.removeStorageSync(storageKey)
				this.localChanges = {}
				this.search()
			},

			// 获取设备商品列表（用于选择器）
			getDeviceGoodsList() {
				deviceGoodsList({
						deviceId: this.deviceId,
					})
					.then((res) => {
						if (res.code === 200 && res.data) {
							this.productList = res.data;
						} else {
							uni.showToast({
								title: res.msg || "获取商品列表失败",
								icon: "none",
							});
						}
					})
					.catch((err) => {
						console.error("获取商品列表失败", err);
						uni.showToast({
							title: "网络异常，请重试",
							icon: "none",
						});
					});
			},

			// 搜索并加载数据
			search() {
				this.reset();
				this.gethomePage();
			},

			// 切换左右门
			tabClick(e) {
				this.door = e.index;
				this.floorList = [];
				this.floorIndex = 0;
				this.search();
			},

			// 切换楼层
			floorClick(item, index) {
				this.floorIndex = index;
				this.search();
			},

			// 跳转到批量设置页面
			batchSet() {
				let url = "";
				if (this.deviceType == 7) {
					url =
						"/pages/replenish/channelSet?deviceId=" +
						this.deviceId +
						"&doorType=" +
						this.door;
				} else {
					url = "/pages/replenish/channelSet?deviceId=" + this.deviceId;
				}
				this.$tab.navigateTo(url);
			},

			// 设置单个商品为满货状态
			fullItem(item) {
				// ...
			},

			// 更新本地修改记录
			updateLocalChange(item) {
				const floorKey = `layer_${this.floorIndex}`;
				if (!this.localChanges[floorKey]) {
					this.localChanges[floorKey] = [];
				}

				const existingIndex = this.localChanges[floorKey].findIndex(
					(change) => change.aisleId === item.id
				);

				const newChange = {
					aisleId: item.id,
					goodsId: item.goodsId,
					stock: item.newStock !== 0 ?
						item.stock >= 0 ?
						Number(item.stock) + Number(item.newStock) :
						Number(item.newStock) : item.stock,
				};

				if (existingIndex >= 0) {
					this.localChanges[floorKey][existingIndex] = newChange;
				} else {
					this.localChanges[floorKey].push(newChange);
				}

				this.saveLocalChanges();
			},

			// 添加查找商品的方法
			findItemInAllLayers(aisleId) {
				// 先在当前显示的商品列表中查找
				const foundInCurrent = this.goodsList.find((item) => item.id === aisleId);
				if (foundInCurrent) return foundInCurrent;

				// 如果当前页面没有，在tempData中查找
				const foundInTemp = this.tempData.find((item) => item.id === aisleId);
				if (foundInTemp) return foundInTemp;

				return null;
			},

			// 保存商品变更
			keep() {
				const allChanges = [];
				let changeList = [];

				Object.keys(this.localChanges).forEach((layerKey) => {
					const layerChanges = this.localChanges[layerKey];
					layerChanges.forEach((change) => {
						if (change.aisleId && change.goodsId) {
							allChanges.push({
								aisleId: change.aisleId,
								goodsId: change.goodsId,
								stock: change.stock,
							});

							// 只添加库存变更记录
							const foundItem = this.findItemInAllLayers(change.aisleId);
							if (foundItem && foundItem.stock !== change.stock) {
								changeList.push({
									goodsName: foundItem.name || "未知商品",
									type: "stock",
									oldStock: foundItem.stock,
									newStock: change.stock,
								});
							}
						}
					});
				});

				this.changeList = changeList;

				if (allChanges.length == 0) {
					this.$modal.msg("您未作任何改变");
					return;
				}

				this.$modal.oldConfirm("确定保存?").then((res) => {
					this.saveBatchChanges(allChanges);
				});
			},

			// 修改批量保存方法
			saveBatchChanges(changes) {
				const params = {
					deviceId: this.deviceId,
					list: changes,
				};

				if (this.deviceType == 7) {
					params.doorType = Number(this.door) + 1;
				}

				batchSave(params).then((res) => {
					if (res.code === 200) {
						this.clearAllLocalChanges();
						this.reset();
						this.gethomePage();
					} else {
						uni.showModal({
							title: "提示",
							content: res.msg || "保存失败",
							showCancel: false,
						});
					}
				});
			},

			// 清除所有本地修改记录
			clearAllLocalChanges() {
				this.localChanges = {};
				uni.removeStorageSync(`replenish_changes_${this.deviceId}_${this.door}`);
			},

			// 获取首页数据
			gethomePage() {
				supplyH5List({
					deviceId: this.deviceId,
					layerNo: this.floorIndex + 1,
					doorType: Number(this.door) + 1,
				}).then((res) => {
					let data = res.data || [];

					// 构建楼层列表
					let floorList = [];
					for (var i = 0; i < data.layerNum; i++) {
						floorList.push(`${i + 1} 层`);
					}
					this.floorList = floorList;

					this.deviceAisleId = data.deviceAisleId;

					// 处理货道数据
					let list = data.list;

					// 初始化商品列表并恢复本地修改
					for (var i = 0; i < list.length; i++) {
						list[i].show = false;
						list[i].newStock = 0;

						// 从本地修改记录中恢复数据
						this.restoreLocalChanges(list[i]);
					}

					this.tempData = list;
					this.setgGoodsData();
				});
			},

			// 从本地修改恢复数据
			restoreLocalChanges(item) {
				const floorKey = `layer_${this.floorIndex}`;
				if (!this.localChanges[floorKey]) return;

				const savedChange = this.localChanges[floorKey].find(
					(change) => change.aisleId === item.id
				);

				if (savedChange) {
					// 只恢复库存修改
					if (savedChange.stock !== item.stock) {
						item.newStock = savedChange.stock - item.stock;
					}

					// 恢复商品修改
					if (savedChange.goodsId !== item.goodsId) {
						item.goodsId = savedChange.goodsId;
						const selectedProduct = this.productList.find(
							(p) => p.goodsId === savedChange.goodsId
						);
						if (selectedProduct) {
							item.name = selectedProduct.name;
							item.cover = selectedProduct.cover;
						}
					}
				}
			},

			// 滚动到底部加载更多
			scrolltolower() {
				if (this.loadmoreStatus == "nomore") return;
				this.page++;
				this.setgGoodsData();
			},

			// 分页加载数据
			setgGoodsData() {
				// 判断是否还有更多数据
				if (
					this.tempData.slice((this.page - 1) * 10, this.page * 10).length < 10
				) {
					this.loadmoreStatus = "nomore";
				}
				// 追加数据到列表
				this.goodsList = this.goodsList.concat(
					this.tempData.slice((this.page - 1) * 10, this.page * 10)
				);
			},

			// 重置数据
			reset() {
				this.loadmoreStatus = "loadmore";
				this.page = 1;
				this.goodsList = [];
			},

			// 编辑商品
			editGoods(item, index) {
				item.show = true;
				this.editIndex = index;
				this.editGoodsIndex = index;
			},

			// 隐藏编辑菜单
			editHidden() {
				if (this.editIndex != null) {
					this.goodsList[this.editIndex].show = false;
					this.editIndex = null;
				}
			},

			// 减少商品数量
			sub(item) {
				// 检查库存是否已为0
				if (item.stock >= 0) {
					if (Number(item.stock) + Number(item.newStock) == 0) {
						this.$modal.msg("这个商品没库存啦~");
						return;
					}
				} else {
					if (Number(item.newStock) <= 0) {
						this.$modal.msg("这个商品没库存啦~");
						return;
					}
				}

				item.newStock--;
				this.updateLocalChange(item);
			},

			// 增加商品数量
			sup(item) {
				item.newStock++;
				this.updateLocalChange(item);
			},

			// 修改商品价格
			changePrice(item) {
				this.editTitle = "修改价格";
				this.editPriceShow = true;
				this.goodsId = item.goodsId;
				this.currentEditItem = item; // 保存当前编辑项
				setTimeout(() => {
					this.inputFocus = true;
				}, 300)
				this.editHidden();
			},

			// 保存价格设置
			priceSet() {
				if (this.editTitle == "修改价格") {
					// 验证价格
					if (this.editPrice == null) {
						this.$modal.msg("商品价格不能为空！");
						return;
					}
					let reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
					if (!reg.test(this.editPrice)) {
						uni.showToast({
							icon: "none",
							title: "商品价格最多保留两位小数！",
						});
						return;
					}

					// 直接调用修改价格接口
					modifyGoodsPrice({
						goodsId: this.goodsId,
						deviceId: this.deviceId,
						price: this.$xy.delMoneyL(this.editPrice),
					}).then((res) => {
						if (res.code === 200) {
							this.$modal.showToast("价格修改成功");
							this.editPriceClose();
							this.search(); // 刷新数据
						} else {
							this.$modal.msg(res.msg || "价格修改失败");
						}
					});

					this.editPrice = null;
					this.editGoodsIndex = null;
				}
			},

			// 关闭价格编辑弹窗
			editPriceClose() {
				this.editPriceShow = false;
				this.inputFocus = false
			},

			// 跳转到添加商品页面
			addgoods() {
				this.$tab.navigateTo("/pages/equipment/addCom?id=" + this.deviceId);
			},

			// 添加商品到货道（整层或单个）
			addGoodsToChannel(type, item) {
				this.$refs.productSelector.showPopup = true;
				if (type == "floor") {
					this.addGoodsToChannelType = "floor";
				} else if (type == "single") {
					this.addGoodsToChannelType = "single";
					this.aisleId = item.id;
				}
			},

			// 商品选择后的回调
			onProductSelected(selectedProduct) {
				if (selectedProduct) {
					// 设置表单数据
					this.formData.goodsId = selectedProduct.goodsId;
					this.formData.cover = selectedProduct.cover;
					this.formData.name = selectedProduct.name;

					// 保存到本地修改记录而不是立即提交
					if (this.addGoodsToChannelType == "floor") {
						this.saveFloorGoodsLocally(selectedProduct);
					} else if (this.addGoodsToChannelType == "single") {
						this.saveSingleGoodsLocally(selectedProduct);
					}
				}
			},

			// 本地保存单个货道商品修改
			saveSingleGoodsLocally(selectedProduct) {
				const floorKey = `layer_${this.floorIndex}`;
				if (!this.localChanges[floorKey]) {
					this.localChanges[floorKey] = [];
				}

				// 查找当前货道信息
				const currentItem = this.goodsList.find(
					(item) => item.id === this.aisleId
				);

				if (!currentItem) {
					this.$modal.showToast("找不到对应货道");
					return;
				}

				// 当商品ID不一致时，记录变更
				const originalGoodsId = currentItem.goodsId || "";
				const newGoodsId = selectedProduct.goodsId || "";

				if (originalGoodsId !== newGoodsId) {
					// 创建变更记录
					const newChange = {
						aisleId: this.aisleId,
						originalGoodsId: originalGoodsId, // 记录原始商品ID
						goodsId: newGoodsId,
						stock: 0, // 新商品默认库存为0
						price: selectedProduct.price || 0,
					};

					// 更新或添加变更记录
					const existingIndex = this.localChanges[floorKey].findIndex(
						(change) => change.aisleId === this.aisleId
					);

					if (existingIndex >= 0) {
						this.localChanges[floorKey][existingIndex] = newChange;
					} else {
						this.localChanges[floorKey].push(newChange);
					}

					// 更新UI显示
					currentItem.goodsId = newGoodsId;
					currentItem.name = selectedProduct.name;
					currentItem.cover = selectedProduct.cover;
					currentItem.stock = 0;
					currentItem.newStock = 0;

					// 保存变更
					this.saveLocalChanges();

					// 强制更新UI
					this.$nextTick(() => {
						this.$forceUpdate();
					});

					this.$modal.showToast("商品已更换");
				} else {
					this.$modal.showToast("选择的商品与当前商品相同");
				}
			},

			// 本地保存整层商品修改
			saveFloorGoodsLocally(selectedProduct) {
				const floorKey = `layer_${this.floorIndex}`;
				if (!this.localChanges[floorKey]) {
					this.localChanges[floorKey] = [];
				}

				let changedCount = 0;

				// 只处理真正变更的货道
				this.tempData.forEach((item) => {
					if (item.layerNo === this.floorIndex + 1) {
						const originalGoodsId = item.goodsId || "";
						const newGoodsId = selectedProduct.goodsId || "";

						// 只有当商品ID不同时才记录变更
						if (originalGoodsId !== newGoodsId) {
							changedCount++;

							const newChange = {
								aisleId: item.id,
								originalGoodsId: originalGoodsId,
								goodsId: newGoodsId,
								stock: 0,
								price: selectedProduct.price || 0,
							};

							const existingIndex = this.localChanges[floorKey].findIndex(
								(change) => change.aisleId === item.id
							);

							if (existingIndex >= 0) {
								this.localChanges[floorKey][existingIndex] = newChange;
							} else {
								this.localChanges[floorKey].push(newChange);
							}

							// 更新UI
							if (this.goodsList.includes(item)) {
								item.goodsId = newGoodsId;
								item.name = selectedProduct.name;
								item.cover = selectedProduct.cover;
								item.stock = 0;
								item.newStock = 0;
							}
						}
					}
				});

				// 保存变更
				this.saveLocalChanges();

				// 强制更新UI
				this.$nextTick(() => {
					this.$forceUpdate();
				});

				if (changedCount > 0) {
					this.$modal.showToast(
						`已更换${changedCount}个货道商品`
					);
				} else {
					this.$modal.showToast("所有货道已设置为相同商品，无需更改");
				}
			},

			// 显示整层库存设置弹窗
			addStockToChannel() {
				this.stockShow = true;
				setTimeout(() => {
					this.inputFocus = true
				}, 200)
			},

			// 关闭库存设置弹窗
			stockClose() {
				this.stockShow = false;
				this.inputFocus = false
			},

			// 本地保存整层库存设置
			stockSet() {
				if (!this.editStock || isNaN(this.editStock)) {
					this.$modal.msg("请输入有效的库存数量");
					return;
				}

				const floorKey = `layer_${this.floorIndex}`;
				if (!this.localChanges[floorKey]) {
					this.localChanges[floorKey] = [];
				}

				// 为当前层的所有货道设置相同库存
				this.tempData.forEach((item) => {
					if (item.layerNo === this.floorIndex + 1 && item.goodsId) {
						// 确保是当前层有商品的货道
						const existingIndex = this.localChanges[floorKey].findIndex(
							(change) => change.aisleId === item.id
						);

						// 计算新的库存差值
						const newStock = Number(this.editStock);
						const stockDiff = newStock - item.stock;

						if (existingIndex >= 0) {
							this.localChanges[floorKey][existingIndex].stock = newStock;
						} else {
							this.localChanges[floorKey].push({
								aisleId: item.id,
								goodsId: item.goodsId,
								stock: newStock,
								price: item.price,
							});
						}

						// 更新UI
						if (this.goodsList.includes(item)) {
							item.newStock = stockDiff;
						}
					}
				});

				// 保存到本地存储
				this.saveLocalChanges();

				this.stockClose();
				this.editStock = null;
				this.$modal.showToast("整层库存已设置，点击提交按钮保存");
			},

			// 关闭提示弹窗
			tipsClose() {
				this.tipsShow = false;
			},

			// 提示弹窗确认
			subSubmit() {
				this.startRep = true
				this.tipsShow = false;
			},

			// 跳转到秤校准页面
			calibration(aisleNo) {
				this.$tab.navigateTo(`/pages/replenish/calibration?id=${this.deviceId}&aisleNo=${aisleNo}`)
			},

			// 判断商品是否被修改
			isItemModified(item) {
				if (!item || !item.id) return false;

				const floorKey = `layer_${this.floorIndex}`;
				if (!this.localChanges[floorKey]) return false;

				const savedChange = this.localChanges[floorKey].find(
					(change) => change.aisleId === item.id
				);

				if (!savedChange) return false;

				// 判断商品是否被修改（通过originalGoodsId）
				if (
					savedChange.originalGoodsId !== undefined &&
					savedChange.originalGoodsId !== savedChange.goodsId
				) {
					return true;
				}

				// 判断库存是否被修改
				if (savedChange.stock !== item.stock || item.newStock !== 0) {
					return true;
				}

				return false;
			},
		},

		// 页面卸载时清理事件监听
		onUnload() {
			uni.$off("refresh");
		},
	};
</script>
<style scoped lang="scss">
	.container {
		height: 100vh;
		background-color: #f4f4f4;
		position: static;

		.machine-box {
			width: 750rpx;
			height: 74rpx;
			font-size: 32rpx;
			line-height: 58rpx;
			text-align: center;
			background: #2c6ff3;
			color: #fff;
			border-radius: 0px 0px 50rpx 50rpx;
		}

		.floor {
			padding: 20rpx 20rpx 8rpx;

			.floor-item {
				display: inline-block;
				width: 130rpx;
				line-height: 50rpx;
				border: 1rpx solid #2c6ff3;
				border-radius: 50rpx;
				text-align: center;
				color: #2c6ff3;
				margin-right: 12rpx;
				margin-bottom: 12rpx;

				&:nth-child(5n) {
					margin-right: 0;
				}

				&.show {
					color: #fff;
					background-color: #2c6ff3;
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			z-index: 3;
			bottom: 24rpx;
			left: 0;
			display: flex;
			flex-flow: row nowrap;
			justify-content: space-between;
			padding: 0 24rpx;
		}

		.empty {
			padding-top: 40%;
		}

		.scrollview {
			position: relative;
		}

		.goods-box {
			width: 724rpx;
			margin-left: 13rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10px 0px rgba(174, 201, 255, 0.2);
			border-radius: 14rpx;
			padding: 28rpx 12rpx 22rpx;
			position: relative;

			.edit-mark {
				position: absolute;
				top: 0;
				left: 0;
				background-color: #ff6b6b;
				color: #fff;
				font-size: 20rpx;
				padding: 4rpx 12rpx;
				border-radius: 14rpx 0 14rpx 0;
				z-index: 2;
			}

			.edit-box {
				width: 150rpx;
				background-color: #fff;
				font-size: 30rpx;
				text-align: center;
				line-height: 80rpx;
				padding: 10rpx 0;
				box-shadow: 0px 0px 10px 0px rgba(174, 201, 255, 0.5);
				position: absolute;
				right: 20rpx;
				bottom: -170rpx;
				z-index: 777;
			}

			&+.goods-box {
				margin-top: 10rpx;
			}

			.image {
				margin-right: 18rpx;
				height: 120rpx;
				width: 120rpx;
			}

			.goods-content {
				position: relative;

				.goods-name-price {
					.goods-name {
						font-size: 28rpx;
						font-weight: 800;
						color: #333333;
						line-height: 34rpx;
					}

					.aisle-no {
						font-size: 28rpx;
						line-height: 32rpx;
						font-weight: 800;
						color: #2c6ff3;
						margin-right: 10rpx;
					}
				}

				.goods-stock {
					font-size: 26rpx;
					font-weight: 500;
					color: #555555;
					line-height: 26rpx;
				}

				.btn-input {
					margin-top: 30rpx;

					.btn-input-left {
						font-size: 26rpx;
					}

					.btn-input-right {
						justify-content: flex-end;
						margin-left: 10rpx;

						>image {
							width: 56rpx;
							height: 56rpx;
						}

						>input {
							width: 100rpx;
							height: 56rpx;
							line-height: 56rpx;
							border: 2rpx solid #2c6ff3;
							border-radius: 10rpx;
							text-align: center;
							margin: 0 10rpx;
							padding: 0 12rpx;
						}
					}
				}

				.edit-goods {
					width: 56rpx;
					height: 56rpx;
					position: absolute;
					right: 10rpx;
					bottom: 0;
				}
			}
		}

		.sub-popup-content {
			width: 702rpx;
			padding: 40rpx;

			.sub-title {
				font-size: 32rpx;
				font-weight: bold;
				line-height: 44rpx;
				text-align: center;
				padding-bottom: 40rpx;
			}

			.sub-tips {
				line-height: 50rpx;
				font-size: 28rpx;
			}

			.sub-btn {
				width: 622rpx;
				border: 1rpx solid #eee;
				border-radius: 8rpx;
				text-align: center;
				line-height: 70rpx;
				margin-top: 150rpx;
			}
		}

		.pop-content {
			padding: 24rpx;
		}

		.scroll-content {
			padding-bottom: calc(160rpx + env(safe-area-inset-bottom) / 2);
		}

		.bg {
			position: fixed;
			width: 100%;
			height: 100vh;
			z-index: 776;
			left: 0;
			top: 0;
		}
	}
</style>