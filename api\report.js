import request from '@/utils/request'
//类型
export function typs(data) {
	return request({
		url: '/order/report-info/typs',
		method: 'post',
		data: data
	})
}

//添加报表
export function save(data) {
	return request({
		url: '/order/report-info/save',
		method: 'post',
		data: data
	})
}

//报表分页
export function page(data) {
	return request({
		url: '/order/report-info/page',
		method: 'post',
		data: data
	})
}

//商户树
export function mercTree(data) {
	return request({
		url: '/merc/mini/merc/tree',
		method: 'post',
		data: data
	})
}

//报表类型列表
export function reportList(data) {
	return request({
		url: '/order/report-merc-type/list',
		method: 'post',
		data: data
	})
}

//报表最新一条数据
export function reportNews(data) {
	return request({
		url: '/order/report-info/news',
		method: 'post',
		data: data
	})
}


