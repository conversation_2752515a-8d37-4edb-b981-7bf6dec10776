<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff"
			:placeholder="true" title="准备采集">
		</u-navbar>

		<!-- 进度显示区域 -->
		<view class="progress-container">
			<!-- 进度圆环 -->
			<view class="progress-ring"
				:style="{ background: `conic-gradient(from 180deg, #4177fb 0deg, #4177fb ${progress * 3.6}deg, #e5e5e5 ${progress * 3.6}deg)` }">
				<view class="mask"></view>
				<text class="progress-text">{{ progress }}%</text>
			</view>

			<!-- 状态提示 -->
			<view class="status-text">
				<text>正在准备商品采集中...</text>
			</view>

			<!-- 状态列表 -->
			<view class="status-list">
				<view class="status-item" v-for="(item, index) in statusList" :key="index">
					<block v-if="item.status == 0">
						<u-loading-icon size="16"></u-loading-icon>
						<text class="status-item-text">正在{{ item.text }}...</text>
					</block>
					<block v-if="item.status == 1">
						<u-icon name="checkmark" size="16"></u-icon>
						<text class="status-item-text">{{ item.text }}成功</text>
					</block>
					<block v-if="item.status == 2">
						<u-icon name="close" size="16"></u-icon>
						<text class="status-item-text">{{ item.text }}失败</text>
					</block>
				</view>
			</view>
		</view>

		<!-- 固定在底部的操作按钮 -->
		<view class="fixed-bottom-bar">
			<button class="save-btn" @click="submitForm" :disabled="!isReady">
				开始采集
			</button>
		</view>
	</view>
</template>

<script>
	import {
		gatherProduct,
		checkConfig
	} from "@/api/collection.js";
	export default {
		data() {
			return {
				goodsInfo: {
					"productId": null,
					"name": null,
					"brandName": null,
					"specS": null,
					"category": null
				}, // 商品信息
				progress: 0, // 进度百分比
				isReady: false, // 是否准备完成
				timer: null, // 定时器
				statusList: [{
						text: "打开摄像头",
						status: 0
					},
					{
						text: "初始化设备",
						status: 0
					},
				],
				deviceId: '',
				checkTimer: null, // 检查配置的定时器
				goodsGatherId:null
			};
		},

		onLoad(o) {
			this.goodsInfo = JSON.parse(o.goodsInfo);

			this.deviceId = o.deviceId;
			// 页面加载后立即开始准备采集
			this.startGatherPreparation();
		},

		onUnload() {
			// 页面卸载时清除定时器
			if (this.checkTimer) {
				clearInterval(this.checkTimer);
				this.checkTimer = null;
			}
		},

		methods: {
			// 开始准备采集
			async startGatherPreparation() {
				try {
					//准备采集
					let gatherProductRes = await this.gatherProductFun()
					if (!gatherProductRes.data) {
						this.$modal.msg('准备采集失败')
						setTimeout(() => {
							this.$tab.navigateBack()
						}, 1000);
						return
					}
					// 保存采集ID
					this.goodsGatherId = gatherProductRes.data

					// 检查配置是否OK
					this.checkConfigFun()
				} catch (error) {
					this.$modal.msg('准备采集失败')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000);
				}
			},

			gatherProductFun() {
				let params = Object.assign({
					identifier: this.deviceId
				}, this.goodsInfo)
				params.brand = params.brandName
				return new Promise((resolve, reject) => {
					gatherProduct(params).then((res) => {
						if (res.code == 200) {
							resolve(res)
						} else {
							reject(res)
						}
					}).catch((err) => {
						reject(err)
					})
				})
			},

			checkConfigFun() {
				// 初始化进度
				this.progress = 0;

				this.checkTimer = setInterval(async () => {
					try {
						let res = await checkConfig({
							id: this.goodsGatherId,
							identifier: this.deviceId, // 设备ID
						})

						if (res.code == 200) {
							let data = res.data;
							let cameraReady = data.cameraStatus === 'yes';
							let configReady = data.configParamStatus === 'yes';
							let startReady = data.start === 'yes';

							// 更新状态列表
							if (data?.cameraStatus) {
								this.statusList[0].status = cameraReady ? 1 : 0;
								this.statusList[1].status = configReady ? 1 : 0;
							}

							// 根据状态更新进度
							this.updateProgress(cameraReady, configReady, startReady);

							// 如果所有条件都满足，完成准备
							if (cameraReady && configReady && startReady) {
								clearInterval(this.checkTimer);
								this.checkTimer = null;
								this.isReady = true;
								this.progress = 100;
							}
						}
					} catch (error) {
						console.error('检查配置出错:', error);
					}
				}, 2000);
			},

			// 根据状态更新进度
			updateProgress(cameraReady, configReady, startReady) {
				// 计算进度：摄像头30%，配置参数40%，启动状态30%
				let newProgress = 0;

				if (cameraReady) newProgress += 30;
				if (configReady) newProgress += 40;
				if (startReady) newProgress += 30;

				// 平滑过渡到新进度
				this.smoothProgress(newProgress);
			},

			// 平滑过渡到目标进度
			smoothProgress(targetProgress) {
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}

				const step = 1;
				const interval = 30;

				this.timer = setInterval(() => {
					if (this.progress < targetProgress) {
						this.progress = Math.min(this.progress + step, targetProgress);
					} else {
						clearInterval(this.timer);
						this.timer = null;
					}
				}, interval);
			},

			// 提交表单，开始采集
			submitForm() {
				if (!this.isReady) {
					uni.showToast({
						title: "请等待准备完成",
						icon: "none",
					});
					return;
				}

				// 跳转到采集页面
				this.$tab.navigateTo(`/pages/collection/collecting?deviceId=${this.deviceId}&goodsId=${this.goodsGatherId}`);
			},
		},
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #fff;
		position: relative;
		padding: 0 20rpx;

		// 进度显示区域
		.progress-container {
			width: 61.8%;
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;

			position: absolute;
			top: 35%;
			left: 50%;
			transform: translate(-50%, -35%);

			// 进度圆环
			.progress-circle {
				width: 200rpx;
				height: 200rpx;
				border-radius: 50%;
				border: 8rpx solid #4177fb;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 40rpx;

				.progress-text {
					font-size: 48rpx;
					font-weight: bold;
					color: #333;
				}
			}

			// 状态提示文字
			.status-text {
				font-size: 32rpx;
				color: #333;
				margin-bottom: 40rpx;
			}

			// 状态列表
			.status-list {
				width: 56%;

				.status-item {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					.status-item-text {
						font-size: 28rpx;
						color: #666;
						margin-left: 10rpx;
					}
				}
			}
		}

		.fixed-bottom-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			background-color: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.save-btn {
				height: 88rpx;
				background-color: #4177fb;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0;

				&[disabled] {
					background-color: #cccccc;
					color: #ffffff;
				}
			}
		}
	}

	.progress-ring {
		width: 200rpx;
		height: 200rpx;
		border-radius: 50%;
		background: conic-gradient(from 180deg, #4177fb 0deg, #e5e5e5 0deg);
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 40rpx;
		transition: background 0.3s ease;
	}

	.mask {
		position: absolute;
		width: 80%;
		height: 80%;
		background: white;
		border-radius: 50%;
		top: 10%;
		left: 10%;
	}

	.progress-text {
		position: relative;
		z-index: 1;
		font-size: 48rpx;
		font-weight: bold;
		color: #333;
	}
</style>