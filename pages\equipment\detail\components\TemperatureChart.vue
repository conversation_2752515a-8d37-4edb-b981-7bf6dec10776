<template>
  <view class="chart" style="height: 650rpx">
    <qiun-data-charts
      canvasId="temp"
      :canvas2d="true"
      :ontouch="true"
      type="line"
      :opts="tempOpts"
      :chartData="chartData"
      :errorMessage="errorMessage"
      :onzoom="true" />
  </view>
</template>

<script>
export default {
  name: "TemperatureChart",
  props: {
    deviceDetail: {
      type: Object,
      default: () => ({}),
    },
    chartData: {
      type: Object,
      default: () => ({
        series: [],
        categories: [],
      }),
    },
  },
  data() {
    return {
      errorMessage: "无数据",
      tempOpts: {
        padding: [15, 0, 0, 15],
        dataLabel: false,
        color: ["#2D54EC", "#F49B37", "#ff0000"],
        // 开启图表可拖拽滚动
        enableScroll: true,
        dataPointShape: false, //是否显示折线上的点
        enableMarkLine: true,
        xAxis: {
          // 开启图表可拖拽滚动后 必配置（显示多少个）
          itemCount: 24,
          rotateLabel: true,
          labelCount: 6,
          marginTop: 10,
          scrollShow: true,
          scrollAlign: "right",
          scrollColor: "#c5c5c5",
        },
        yAxis: {
          splitNumber: 5,
          gridType: "dash",
          dashLength: 8,
          gridColor: "rgba(0, 0, 0, 0.15)",
          data: [
            {
              axisLine: false, //坐标轴轴线是否显示
              min: -30,
              max: 30,
            },
          ],
        },
        extra: {
          column: {
            width: 15,
            seriesGap: 2,
            barBorderRadius: [10, 10, 10, 10],
          },
          line: {
            type: "curve",
            width: 2,
            activeType: "hollow",
            linearType: "custom",
            onShadow: true,
            animation: "horizontal",
          },
          area: {
            type: "curve",
            opacity: 0.8,
            addLine: true, //是否显示曲线
            width: 2, //曲线宽度
            gradient: true, //渐变开关
          },
          markLine: {
            type: "dash",
            dashLength: 5,
            data: [
              {
                value: 15,
                lineColor: "blue",
                showLabel: true,
                labelText: "告警上限",
                labelOffsetX: -260,
                labelAlign: "right",
                labelFontSize: 12,
                labelBgOpacity: 0.2,
              },
              {
                value: 0,
                lineColor: "blue",
                showLabel: true,
                labelText: "告警下限",
                labelOffsetX: -260,
                labelAlign: "right",
                labelFontSize: 12,
                labelBgOpacity: 0.2,
              },
              {
                value: 0,
                lineColor: "green",
                showLabel: true,
                labelText: "设定温度",
                labelOffsetX: -260,
                labelAlign: "right",
                labelFontSize: 12,
                labelBgOpacity: 0.2,
              },
            ],
          },
        },
      },
    };
  },
  watch: {
    deviceDetail: {
      handler(newVal) {
        if (newVal) {
          this.updateMarkLineData();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    updateMarkLineData() {
      let tempMin = "";
      let tempMax = "";
      let targetTemp = this.deviceDetail.jobTempSetValue || 0;
      let tempArr = [];

      if (this.deviceDetail.tempSetInfo) {
        tempMin = this.deviceDetail.tempSetInfo.tempMin;
        tempMax = this.deviceDetail.tempSetInfo.tempMax;
        tempArr = [
          {
            value: tempMin,
            lineColor: "#ff5500",
            showLabel: true,
            labelText: `告警下限${tempMin}℃`,
            labelOffsetX: -260,
            labelAlign: "right",
            labelFontSize: 12,
            labelBgOpacity: 0.2,
          },
          {
            value: tempMax,
            lineColor: "#ff5500",
            showLabel: true,
            labelText: `告警上限${tempMax}℃`,
            labelOffsetX: -260,
            labelAlign: "right",
            labelFontSize: 12,
            labelBgOpacity: 0.2,
          },
          {
            value: targetTemp,
            lineColor: "green",
            showLabel: true,
            labelText: `设定温度${targetTemp}℃`,
            labelOffsetX: -260,
            labelAlign: "right",
            labelFontSize: 12,
            labelBgOpacity: 0.2,
          },
        ];
      } else {
        tempArr = [
          {
            value: targetTemp,
            lineColor: "green",
            showLabel: true,
            labelText: `设定温度${targetTemp}℃`,
            labelOffsetX: -260,
            labelAlign: "right",
            labelFontSize: 12,
            labelBgOpacity: 0.2,
          },
        ];
      }

      this.tempOpts.extra.markLine.data = tempArr;
    },
  },
};
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
}
</style>
