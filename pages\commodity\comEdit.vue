<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="商品编辑"></u-navbar>

		<swiper class="card-swiper round-dot" previous-margin="1rpx" :indicator-dots="false" :circular="true"
			:autoplay="true" interval="5000" duration="500" @change="cardSwiper" indicator-color="#ffffff"
			indicator-active-color="#ffffff" style="margin-top: 24rpx;">
			<swiper-item v-for="(item,index) in swiperList" :key="index" :class="cardCur == index ? 'cur':''">
				<view class="swiper-item shadow-shop" style="border-radius: 20rpx 20rpx 22rpx 22rpx;">
					<image :src="item" mode="aspectFill"></image>
				</view>
			</swiper-item>
		</swiper>
		<view class="content">
			<view class="xy-card">
				<view class="title">
					商品信息
				</view>

				<view class="list">
					<view class="xy-list-item">
						<view>商品名称</view>
						<view>{{comDetail.goodsName}}</view>
					</view>
					<!-- <view class="xy-list-item">
						<view>可用机型</view>
						<view>{{comDetail.name}}</view>
					</view> -->
					<view class="xy-list-item">
						<view>商品条码</view>
						<view style="white-space: normal;word-break: break-all;">{{comDetail.goodsBarcode}}</view>
					</view>
					<view class="xy-list-item">
						<view>商品尺寸</view>
						<view>{{comDetail.capacity}}</view>
					</view>
					<view class="xy-list-item">
						<view>所属分类</view>
						<view>{{comDetail.categoryCodeName||'其他'}}</view>
					</view>
				</view>
			</view>

			<view class="xy-card">
				<view class="title">
					复购率
				</view>
				<view class="list">
					<view class="list-item" v-for="(item,index) in progressData" :key="item.name">
						<view class="name">
							{{item.name}}
						</view>
						<u-line-progress :percentage="item.num" activeColor="#2C6FF3"></u-line-progress>
					</view>
				</view>
			</view>

			<view class="xy-card">
				<view class="title">
					商品设置
				</view>
				<view class="list">
					<u--form labelPosition="left" :model="form" :rules="rules" ref="form" errorType="toast">
						<u-form-item required labelWidth="90" label="进货价(元)" prop="priceCost" borderBottom ref="item1">
							<u--input type="digit" v-model="form.priceCost" border="none"></u--input>
						</u-form-item>
						<u-form-item required labelWidth="90" label="零售价(元)" prop="price" borderBottom ref="item1">
							<u--input type="digit" v-model="form.price" border="none"></u--input>
						</u-form-item>
						<!-- <u-form-item required labelWidth="90" label="单机容量" prop="capacity" borderBottom ref="item1">
							<u--input type="number" v-model="form.capacity" border="none"></u--input>
						</u-form-item> -->
						<u-form-item required labelWidth="90" label="库存预警" prop="warning" borderBottom ref="item1">
							<u--input type="number" v-model="form.warning" border="none"></u--input>
						</u-form-item>
						<u-form-item required labelWidth="90" label="保质期天数" prop="warning" borderBottom ref="item1">
							<u--input type="number" v-model="form.expirationDays" border="none"></u--input>
						</u-form-item>
						<u-form-item required labelWidth="90" label="上架状态" prop="warning" ref="item1">
							<u-switch v-model="form.status"></u-switch>
						</u-form-item>

					</u--form>
				</view>
			</view>
		</view>
		<view class="btn safe-bottom">
			<view class="btn safe-bottom" v-if="checkPermi(['comEdit'])">
				<xbutton round="88rpx" delay="1500" size="large" @click='submit'>保存</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		searchObj,
		update
	} from "@/api/commodity/mercGoods.js"

	import {
		goodsDrawRePayPer
	} from "@/api/commodity/goods.js"

	export default {
		data() {
			return {
				cardCur: 0,
				form: {
					price: undefined,
					warning: undefined,
					capacity: undefined,
					priceCost: undefined,
					expirationDays: undefined,
					status: true //上架状态
				},
				rules: {
					'price': {
						type: 'number',
						required: true,
						message: '请输入商品价格~',
						trigger: ['blur', 'change']
					},
					'warning': {
						type: 'number',
						required: true,
						message: '请输入预警值~',
						trigger: ['blur', 'change']
					},
					// 'capacity': {
					// 	type: 'number',
					// 	required: true,
					// 	message: '请输入容量~',
					// 	trigger: ['blur', 'change']
					// },
					'priceCost': {
						type: 'number',
						required: true,
						message: '请输入商品进货价~',
						trigger: ['blur', 'change']
					},
				},
				show: false,
				actions: [{
						name: '全部',
					},
					{
						name: '饮料',
					},
					{
						name: '槟榔',
					},
				],

				radio: '',
				switchVal: false,

				comDetail: null, //商品详情
				id: null,
				progressData: [{
						name: '1次占比',
						num: 0,
					},
					{
						name: '2次占比',
						num: 0,
					},
					{
						name: '3-5次占比',
						num: 0,
					},
					{
						name: '6-10次占比',
						num: 0,
					},
					{
						name: '10次以上占比',
						num: 0,
					}
				]
			};
		},

		computed: {
			swiperList() {
				let imgArr = []
				if (this.comDetail?.imgUrls) {
					imgArr = this.comDetail.imgUrls.indexOf('@@') != -1 ? this.comDetail.imgUrls.split('@@') : this
						.comDetail.imgUrls.split('|')
				}
				return imgArr
			}
		},

		onLoad(o) {
			this.id = o.id;
			this.getDetail(o.id)
			this.getRePlayer(o.id)
		},

		methods: {
			getDetail(id) {
				searchObj({
					id: id
				}).then(res => {
					this.comDetail = res.data
					this.form = {
						"price": Number(res.data.price) / 100,
						"warning": res.data.warning,
						"capacity": res.data.capacity,
						"priceCost": Number(res.data.priceCost) / 100,
						"status": res.data.status == '1' ? true : false,
						"expirationDays": res.data.expirationDays
					}
				})
			},

			getRePlayer(id) {
				goodsDrawRePayPer({
					goodsId: id
				}).then(res => {
					if (res.data) {
						let i = 0
						for (let key in res.data) {
							this.progressData[i].num = res.data[key]
							i++
						}
					}
				})
			},

			cardSwiper(e) {
				this.cardCur = e.detail.current
			},
			// 下拉框点击事件
			showSelect(type) {
				if (type == 'class') {
					this.actions = [{
							name: '全部',
						},
						{
							name: '饮料',
						},
						{
							name: '槟榔',
						}
					]
				} else {
					this.actions = [{
							name: '大瓶',
						},
						{
							name: '中瓶',
						},
						{
							name: '小瓶',
						}
					]
				}
				this.show = true;
			},

			//表单提交
			submit() {
				this.$refs.form.validate().then(res => {
					this.form.id = this.id;
					let params = {
						id: this.id,
						price: this.$xy.delMoneyL(this.form.price),
						warning: this.form.warning,
						capacity: this.form.capacity,
						priceCost: this.$xy.delMoneyL(this.form.priceCost),
						status: this.form.status ? '1' : '0',
						expirationDays: this.form.expirationDays
					}
					update(params).then(res => {
						this.$modal.msg('保存成功~')
						setTimeout(() => {
							uni.$emit('refreshData');
							this.$tab.navigateBack()
						}, 1000)
					})
				}).catch(errors => {})
			}
		},
		onReady() {
			setTimeout(() => {
				this.$refs.form.setRules(this.rules)
			}, 500)
		},
	};
</script>

<style scoped lang="scss">
	.container {

		.card-swiper {
			height: 350rpx !important;
		}

		.card-swiper swiper-item {
			width: 260upx !important;
			left: 245upx;
			box-sizing: border-box;
			padding: 0upx 15upx 50upx 15upx;
			overflow: initial;
			/* margin: 100rpx 0; */
		}

		.card-swiper swiper-item .swiper-item {
			width: 100%;
			display: block;
			height: 100%;
			border-radius: 6upx;
			transform: scale(0.7);
			transition: all 0.2s ease-in 0s;
			overflow: hidden;
		}

		.card-swiper swiper-item.cur .swiper-item {
			transform: none;
			transition: all 0.2s ease-in 0s;
		}

		.content {
			padding: 24rpx 24rpx 100rpx;

			.example-img {
				padding: 24rpx 24rpx 12rpx;
				display: flex;
				flex-flow: row nowrap;
				justify-content: space-between;

				>image {
					width: 100rpx;
				}
			}

			.img-upload {
				padding: 24rpx 24rpx 12rpx;
			}

			.xy-card {
				margin-bottom: 24rpx;
			}

			.title {
				font-size: 32rpx;
				font-weight: bold;
				line-height: 50rpx;
				position: relative;
				padding-left: 20rpx;

				&::after {
					content: '';
					width: 4rpx;
					height: 24rpx;
					background-color: #00aaff;
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
				}
			}

			.xy-list-item {
				display: flex;
				flex-flow: row nowrap;
				justify-content: flex-start;
				line-height: 80rpx;

				&:not(:last-child) {
					border-bottom: 1upx solid #eee;
				}

				>view:nth-child(1) {
					width: 180rpx;
				}

				>view:nth-child(2) {
					width: 500rpx;
					color: #666;
					font-size: 24rpx;
				}
			}

			.list {
				padding-left: 20rpx;
				padding-top: 10rpx;

				.list-item {
					position: relative;
					padding-left: 170rpx;
					padding: 16rpx 0 16rpx 180rpx;

					.name {
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
			z-index: 999;
		}
	}
</style>