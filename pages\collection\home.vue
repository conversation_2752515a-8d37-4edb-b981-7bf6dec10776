<template>
	<view class="goods-management">
		<!-- 顶部导航栏 -->
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="商品数据"></u-navbar>

		<!-- 使用u-tabs组件替换原来的tab-bar -->
		<!-- 		<u-tabs :list="tabsList" :current="currentTab" @change="tabsChange" lineColor="#2979ff" :lineWidth="20"
			:is-scroll="false"></u-tabs> -->

		<!-- 类别选择栏 -->
		<view class="category-bar">
			<view v-for="(item, index) in categories" :key="index" class="category-item"
				:class="{ active: currentCategory === index }" @click="switchCategory(index)">
				{{ item }}
			</view>
		</view>

		<!-- 商品列表 -->
		<scroll-view scroll-y class="goods-list" @scrolltolower="loadMoreData">
			<view class="goods-grid" v-if="goodsList.length > 0">
				<view class="goods-item" v-for="(item, index) in goodsList" :key="index"
					@click="toggleSelectItem(item)">
					<view class="goods-checkbox" v-if="currentCategory==0">
						<u-checkbox-group v-model="item.checkboxValue">
							<u-checkbox :name="item.id" shape="circle" activeColor="#2979ff">
							</u-checkbox>
						</u-checkbox-group>
					</view>
					<view class="goods-image flex justify-center">
						<image :src="item.mainImg || '/static/images/default-goods.png'" mode="aspectFit">
						</image>
					</view>
					<view class="goods-info">
						<view class="goods-name">{{ item.name }}</view>
						<view class="goods-price" v-if="item.salePrice">
							<text>售价: {{ $xy.delMoney(item.salePrice) }}元</text>
						</view>
						<view class="goods-spec" v-if="item.specs">
							<text>规格: {{ item.specs}}</text>
						</view>
						<view class="goods-status flex" v-if="currentCategory==3">
							<u-tag :text="getStatusText(item.status)" plain size="mini"
								:type="getStatusClass(item.status)"></u-tag>
						</view>
						<view class="goods-remark" v-if="item.rejectCause">
							<text>{{ item.rejectCause }}不好用</text>
						</view>
					</view>
					<!-- 删除按钮阻止冒泡 -->
					<!-- <view class="goods-action" @click.stop>
						<view class="delete-btn" @click="deleteGoods(item)">删除</view>
					</view> -->
				</view>

				<view class="flex align-center justify-center" style="padding:24rpx 24rpx 130rpx;width:100%;">
					<u-loadmore v-if="goodsList.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<!-- 底部按钮区 -->
		<view class="bottom-bar" v-if="currentCategory==0||currentCategory==2">
			<xbutton size="large" bgColor="#2979ff" round="88rpx" color="#fff" @click="addNewGoods"
				v-if="currentCategory==0">
				新增商品</xbutton>
			<xbutton size="large" bgColor="#2979ff" round="88rpx" color="#fff" @click="submitSelected"><text
					v-if="currentCategory==0">
					提交学习
				</text><text v-if="currentCategory==2">
					提交采集
				</text></xbutton>
		</view>
	</view>
</template>

<script>
	import {
		getOkGoodsPageData,
		waitGatherProduct,
		studyingProduct,
		getSkuList,
		commitTraining,
		auditProductList,
		waitGatherProductNew,
		uploadInfo,
		goOnUpload
	} from "@/api/collection.js";

	export default {
		data() {
			return {
				// u-tabs组件需要的格式
				tabsList: [{
						name: "商品管理"
					},
					{
						name: "商品排行"
					},
					{
						name: "商品库存"
					},
				],
				currentTab: 0, // 默认选中"商品管理"
				categories: ["可学习", "学习中", "待采集", "采集审核"],
				currentCategory: 0, // 默认选中"可学习"
				goodsList: [],
				loading: false, // 是否正在加载数据
				page: 1, // 当前页码
				pageSize: 10, // 每页条数
				loadmoreStatus: 'loadmore',
				modelName: '', // 添加模型名称字段
			};
		},
		onLoad() {
			// 加载商品数据
			this.loadGoodsList();

			uni.$on("refresh", () => {
				this.loadGoodsList();
			});
		},
		methods: {
			// 修改为使用u-tabs的change事件
			tabsChange(e) {
				const index = e.index;
				this.currentTab = index;
				// 根据切换的标签加载不同数据
				if (index === 1) {
					uni.navigateTo({
						url: "/pages/goods/ranking/index",
					});
				} else if (index === 2) {
					uni.navigateTo({
						url: "/pages/goods/inventory/index",
					});
				} else {
					// 当前页面，无需跳转
					this.resetAndLoadData();
				}
			},

			// 切换分类
			switchCategory(index) {
				this.currentCategory = index;
				// 根据类别筛选商品
				this.resetAndLoadData();
			},

			// 重置数据并重新加载
			resetAndLoadData() {
				this.page = 1;
				this.goodsList = [];
				this.loadmoreStatus == "loadmore"
				this.loadGoodsList();
			},

			// 加载更多数据
			loadMoreData() {
				if (this.loadmoreStatus == "loadmore" && !this.loading) {
					this.page++;
					this.loadGoodsList();
				}
			},

			// 加载商品列表数据
			loadGoodsList() {
				if (this.loading) return;

				this.loading = true;

				// 准备请求参数
				let params = {
					page: this.page,
					pageSize: this.pageSize,
				};

				// 根据当前选中的分类调用不同的API
				let apiPromise;
				if (this.currentCategory == 0) {
					// 可学习
					params = Object.assign(params, {
						gatherStatus: "training"
					})
					apiPromise = getOkGoodsPageData(params);
				} else if (this.currentCategory === 1) {
					// 学习中
					apiPromise = studyingProduct({});
				} else if (this.currentCategory === 2) {
					// 待采集
					apiPromise = waitGatherProductNew(params);
				} else {
					// 采集审核
					apiPromise = auditProductList({});
				}

				// 发起API请求
				apiPromise
					.then((res) => {
						if (res.code === 200) {
							if (this.currentCategory === 1 || this.currentCategory === 3) {
								this.loadmoreStatus = "nomore"
								// if (res.data.length < 10) {
								// 	this.loadmoreStatus = "nomore"
								// } else {
								// 	this.loadmoreStatus = "loadmore"
								// }
								// 待采集
								// 待采集商品列表API返回的是数组
								const newList = res.data || [];
								// 为数据添加selected属性用于选择
								newList.forEach((item) => {
									item.selected = false;
									item.checkboxValue = [];
								});
								this.goodsList = newList;
								return
							}
							if (this.currentCategory === 0 || this.currentCategory == 2) {
								// 可学习||待采集
								const pageData = res.data?.pageData || [];
								if (pageData.length < 10) {
									this.loadmoreStatus = "nomore"
								} else {
									this.loadmoreStatus = "loadmore"
								}
								// 为数据添加selected属性用于选择
								pageData.forEach((item) => {
									item.selected = false;
									item.checkboxValue = [];
								});
								this.goodsList = [...this.goodsList, ...pageData];
							}
						} else {
							uni.showToast({
								title: res.msg || "加载失败",
								icon: "none",
							});
						}
					})
					.finally(() => {
						this.loading = false;
					});
			},

			// 获取状态对应的样式类
			getStatusClass(status) {
				if (status === "pass") return "success";
				if (status === "reject") return "error";
				if (status === "wait") return "warning";
				return "primary";
			},

			// 获取状态对应的文本
			getStatusText(status) {
				if (status === "pass") return "已通过";
				if (status === "reject") return "已驳回";
				if (status === "wait") return "待审核";
				return '审核中';
			},

			// 切换单个商品的选择状态
			toggleSelectItem(item) {
				// 如果当前没有选中，则添加ID到选中数组
				if (!item.checkboxValue.includes(item.id)) {
					item.checkboxValue = [item.id];
					item.selected = true;
				} else {
					// 如果已经选中，则清空选中数组
					item.checkboxValue = [];
					item.selected = false;
				}
			},

			// 删除商品
			deleteGoods(item) {
				uni.showModal({
					title: "提示",
					content: "确定要删除该商品吗？",
					success: (res) => {
						if (res.confirm) {
							// 删除商品逻辑
							this.goodsList = this.goodsList.filter(
								(goods) => goods.id !== item.id
							);
							uni.showToast({
								title: "删除成功",
								icon: "success",
							});
						}
					},
				});
			},

			// 新增商品
			addNewGoods() {
				// 跳转到新增商品页面
				uni.navigateTo({
					url: "/pages/collection/comInfo",
				});
			},

			// 提交
			async submitSelected() {
				// 提交学习逻辑
				if (this.currentCategory == 0) {
					const selectedItems = this.goodsList.filter((item) => item.selected);

					if (selectedItems.length === 0) {
						uni.showToast({
							title: "请选择商品",
							icon: "none",
						});
						return;
					}

					// 弹出输入模型名称的弹框
					uni.showModal({
						title: '请输入模型名称',
						editable: true,
						placeholderText: '请输入模型名称',
						success: (res) => {
							if (res.confirm) {
								// 获取输入的模型名称
								const modelName = res.content;

								if (!modelName || modelName.trim() === '') {
									uni.showToast({
										title: '模型名称不能为空',
										icon: 'none'
									});
									return;
								}

								// 保存模型名称
								this.modelName = modelName;

								// 确认提交学习
								const selectedIds = selectedItems.map(item => item.id);

								commitTraining({
									modelName: this.modelName,
									productIds: selectedIds
								}).then(res => {
									if (res.code === 200) {
										if (res.data) {
											this.$modal.msg('提交成功');
											setTimeout(() => {
												// 刷新列表
												this.currentCategory = 1
												this.resetAndLoadData();
											}, 1000);
										} else {
											this.$modal.msg('提交失败');
										}
									}
								})
							}
						}
					});
				}

				// 提交采集逻辑
				if (this.currentCategory == 2) {
					try {
						// 获取采集上传信息
						let isAllUploadedRes = await this.isAllUploaded()
						if (!isAllUploadedRes) { //未上传完毕
							let confirmRes = await this.$modal.confirm('有未上传采集视频，是否继续上传？')
							if (confirmRes.confirm) {
								//继续上传
								let goOnUploadRes = await goOnUpload({})
								this.$tab.navigateTo(`/pages/collection/collectionList`);
								return
							}
						}
					} catch (error) {
						console.log(error)
					}

					//扫码机器
					let scanRes = await this.$xy.scanDevice()
					let deviceId = scanRes.deviceId || scanRes.terminalId;
					this.$tab.navigateTo(`/pages/collection/wifiConnect?deviceId=${deviceId}`);
				}
			},

			//是否所有采集上传完毕
			isAllUploaded() {
				// 判断是否所有采集上传完毕
				return new Promise((resolve, reject) => {
					uploadInfo({}).then(res => {
						if (res.code == 200) {
							let data = res.data;
							let bflag = true;
							if (data && data.length > 0) {
								if (data.some(i => i.status !== "success")) {
									bflag = false;
								}
							}
							resolve(bflag)
						} else {
							reject(res)
						}
					})
				})
			}
		},
	};
</script>

<style lang="scss">
	.goods-management {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
		position: relative;

		.category-bar {
			display: flex;
			padding: 0 15px;
			background-color: #ffffff;
			margin-bottom: 10px;
			height: 90rpx;

			.category-item {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
				color: #333;
				position: relative;

				&.active {
					color: #4177fb;
					font-weight: 500;

					&::after {
						content: "";
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 40rpx;
						height: 4rpx;
						background-color: #4177fb;
					}
				}
			}
		}

		.goods-list {
			flex: 1;
			padding: 0 20rpx;
			box-sizing: border-box;
			overflow-y: auto;
			-webkit-overflow-scrolling: touch;

			.goods-grid {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
			}

			.goods-item {
				width: 345rpx;
				margin-bottom: 20rpx;
				background-color: #ffffff;
				border-radius: 8px;
				overflow: hidden;
				position: relative;
				box-shadow: 0px 0px 10px 0px rgba(174, 201, 255, 0.2);

				// 添加点击效果
				&:active {
					opacity: 0.8;
				}

				.goods-checkbox {
					position: absolute;
					top: 10px;
					left: 10px;
					z-index: 10;
					pointer-events: none; // 防止checkbox区域影响点击
				}

				.goods-image {
					width: 100%;
					height: 160rpx;
					overflow: hidden;

					image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
				}

				.goods-info {
					padding: 10px;

					.goods-name {
						font-size: 26rpx;
						color: #333;
						margin-bottom: 10rpx;
						line-height: 1.4;
						// overflow: hidden;
						// text-overflow: ellipsis;
						// display: -webkit-box;
						// -webkit-line-clamp: 1;
						// -webkit-box-orient: vertical;
					}

					.goods-price,
					.goods-spec {
						font-size: 24rpx;
						color: #666;
						margin-bottom: 2px;
					}

					.goods-status {
						font-size: 24rpx;
						margin-top: 5px;
					}

					.goods-remark {
						font-size: 22rpx;
						color: #ff3b30;
						margin-top: 5px;
					}
				}

				.goods-action {
					padding: 5px 10px 10px;
					display: flex;
					justify-content: flex-end;

					.delete-btn {
						color: #2979ff;
						font-size: 24rpx;
					}
				}
			}
		}

		.empty {
			padding-top: 40%;
		}

		.bottom-bar {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			background-color: #ffffff;
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;
		}
	}
</style>