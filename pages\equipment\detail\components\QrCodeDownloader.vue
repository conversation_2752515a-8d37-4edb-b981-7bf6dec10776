<template>
  <view>
    <xbutton
      size="mini"
      style="margin-right: 12rpx"
      bgColor="#fff"
      borderColor="#2C6FF3"
      color="#2C6FF3"
      @click="downloadQr"
      >下载柜门二维码
    </xbutton>

    <xpopup
      :show="qrcodeShow"
      mode="center"
      @close="qrcodeClose"
      :showBtn="false">
      <view class="qrcode-content flex flex-direction align-center">
        <view class="canvas-box">
          <u--image
            width="400rpx"
            height="400rpx"
            :src="qrCodeImg[doorIndex]"
            mode="aspectFit"
            :lazy-load="true"
            showMenuByLongpress></u--image>
        </view>
        <view
          class="save-qrcode flex align-center"
          v-if="qrCodeImg && qrCodeImg.length > 1">
          <view
            v-for="(item, index) in doors"
            :key="index"
            :class="[doorIndex == index ? 'door-show' : '']"
            @click="doorTab(index)">
            {{ item }}
          </view>
        </view>
        <view class="qr-down"> 长按图片转发或保存二维码 </view>
      </view>
    </xpopup>
  </view>
</template>

<script>
import { getQrCode } from "@/api/device/device.js";

export default {
  name: "QrCodeDownloader",
  props: {
    deviceId: {
      type: [String, Number],
      required: true,
    },
    showButton: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      qrcodeShow: false,
      qrCodeImg: null,
      doorIndex: 0,
      doors: ["左门", "右门"],
    };
  },
  methods: {
    // 下载柜门二维码
    downloadQr() {
      this.qrcodeShow = true;
      this.drawQr();
    },

    // 绘制二维码
    drawQr() {
      getQrCode({
        deviceId: this.deviceId,
      }).then((res) => {
        this.qrCodeImg = res.data;
      });
    },

    // 关闭二维码弹窗
    qrcodeClose() {
      this.qrcodeShow = false;
    },

    // 门切换
    doorTab(index) {
      this.doorIndex = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.qrcode-content {
  padding: 24rpx;

  .save-qrcode {
    margin-top: 24rpx;

    view {
      width: 80rpx;
      line-height: 40rpx;
      color: #2c6ff3;
      text-align: center;
      border: 1rpx solid #2c6ff3;

      &.door-show {
        color: #fff;
        background-color: #2c6ff3;
      }
    }
  }

  .qr-down {
    color: #2c6ff3;
    font-size: 26rpx;
    text-align: center;
    margin-bottom: 20rpx;
    margin-top: 20rpx;
  }
}
</style>
