<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="奖品设置"></u-navbar>
		<view class="content">
			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view class="top-wrap">
					<view class="tips">
						提示：奖品列表概率之和必须为100%
					</view>
					<view class="top flex justify-between align-center">
						<view class="top-left">
							总概率：<text style="color:red;">{{totalPercentage}}%</text>
						</view>
						<xbutton round='88rpx' @click="add">
							增加奖品
						</xbutton>
					</view>
				</view>
				<view v-if="list.length>0" class="content">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="list-wrap">
							<view class="image">
								<u--image radius='14rpx' width="120rpx" height="120rpx"
									@click="$xy.previewImg(item.image)"
									:src="item.image?item.image:'https://static.xynetweb.com/sysFile/defaultgoods.png'"
									mode="aspectFit" :lazy-load="true">
								</u--image>
							</view>
							<view class="list-content">
								<view class="name">{{item.name}}</view>
								<view class="item-row">
									<text class="item-label">奖项：</text><text
										class="item-value">{{item.awardsName}}</text>
								</view>
								<view class="item-row">
									<text class="item-label">奖品数量：</text><text
										class="item-value">{{item.limitNum||'-'}}个</text>
								</view>
								<view class="item-row">
									<text class="item-label">单人最高中奖数量：</text><text
										class="item-value">{{item.limitWinNum||'-'}}个</text>
								</view>
								<view class="probability">
									<view>概率</view>
									<view>{{item.percentage}}%</view>
								</view>
								<view class="set flex justify-end">
									<view style="margin-right: 12rpx;">
										<xbutton round='88rpx' width="114rpx" bgColor="#fff" color="#2C6FF3"
											borderColor="#2C6FF3" @click="del(item)">
											删除
										</xbutton>
									</view>
									<view>
										<xbutton width="114rpx" round="88rpx"
											@click="$tab.navigateTo(`/pages/lottery/addPrize?lotteryId=${id}&prizeId=${item.id}`)">
											修改</xbutton>
									</view>
								</view>
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

			<view class="btn">
				<xbutton size="large" @click="save">
					保存
				</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		prizeList,
		prizeDel
	} from "@/api/lottery.js"
	export default {
		data() {
			return {
				id: null,
				list: [],
				loadmoreStatus: 'loadmore',
				fullHeight: 0,
				totalPercentage: 0
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)
			this.id = o.id
			this.search()

			uni.$on('updatePrize', data => {
				this.search()
			})
		},

		methods: {
			del(item) {
				let _this = this
				this.$modal.oldConfirm(`是否确定删除？`).then(res => {
					prizeDel({
						"lotteryId": this.id,
						"prizeId": item.id
					}).then(res => {
						_this.$modal.msg('成功~')
						setTimeout(() => {
							this.search()
						}, 500)
					})
				})
			},

			//获取订单列表
			getpage() {
				prizeList({
					lotteryId: this.id
				}).then(res => {
					let data = res.data;
					let totalPercentage = 0
					for (let i = 0; i < data.length; i++) {
						totalPercentage += data[i].percentage
					}
					this.totalPercentage = totalPercentage
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = data
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			add() {
				this.$tab.navigateTo(`/pages/lottery/addPrize?lotteryId=${this.id}`)
			},

			save() {
				uni.navigateBack({
					delta: 2,
					success: () => {
						uni.$emit('updateList', {})
					}
				})
			}
		},

		onUnload() {
			uni.$off('updatePrize')
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;

		.top-wrap {
			padding: 20rpx;

			.tips {
				line-height: 40rpx;
				color: red;
			}

			.top {
				padding: 10rpx 0;
				margin-top: 10rpx;
			}
		}

		.content {
			overflow: hidden;

			.menu-item {
				text-align: center;
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-top: 24rpx;

				>image {
					width: 57rpx;
					height: 57rpx;
				}

				>view {
					color: #333;
					font-size: 24rpx;
					line-height: 24rpx;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 16rpx;
				}
			}

			.empty {
				padding-top: 40%;
			}

			.martop {
				margin-top: 20rpx;
			}

			.tab-wrap {
				padding: 12rpx 0;

				.tab {
					// width: 40%;
					width: 70%;
				}
			}

			.tab-list {
				width: 100%;
				background-color: #fff;
				padding: 24rpx 26rpx 0;

				.tab-item {
					padding: 0 20rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					line-height: 62rpx;
					margin-bottom: 14rpx;
					text-align: center;
					margin-right: 12rpx;

					&.tab-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}
			}

			.marleft {
				margin-left: 10rpx;
			}

			.scrollview {
				.content {
					padding-bottom: 90rpx;
					overflow: hidden;
				}
			}

			.list-wrap {
				margin: 13rpx 13rpx 0;
				padding: 24rpx 30rpx 40rpx;
				border-radius: 14rpx;
				background-color: #fff;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

				.image {
					float: left;
				}

				.list-content {
					padding-left: 140rpx;
					position: relative;

					.name {
						font-size: 30rpx;
						font-weight: bold;
					}

					.item-row {
						font-size: 26rpx;
						margin-top: 16rpx;
					}

					.probability {
						position: absolute;
						right: 20rpx;
						top: 50rpx;
						text-align: center;

						>view:nth-child(2) {
							font-size: 40rpx;
							font-weight: bold;
							margin-top: 10rpx;
						}
					}

					.set {
						margin-top: 20rpx;
					}
				}
			}

			.btn {
				width: 724rpx;
				position: fixed;
				left: 13rpx;
				bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
				border-radius: 88rpx;
				overflow: hidden;

				.btn-wrap {
					text-align: center;
					font-size: 36rpx;
					font-weight: 500;
				}
			}
		}
	}
</style>