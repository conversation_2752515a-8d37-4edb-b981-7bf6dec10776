<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="会员管理"></u-navbar>

		<view class="tab-wrap">
			<view class="tab">
				<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
					:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
					lineColor="#2C6FF3">
				</u-tabs>
			</view>
		</view>

		<view class="search">
			<u-search animation placeholder="请输入手机号码" :clearabled="true" v-model="keyword" :showAction="false"
				@search="search"></u-search>
		</view>

		<view class="flex align-center justify-between screen-container" v-if="current==0">
			<view class="flex align-center">
				<view>用户总数：{{total}}</view>

				<view class="show-zero flex justify-end align-center">
					<view>黑名单：</view>
					<view>
						<u-switch activeColor="#2C6FF3" size="14" v-model="showBlack"
							@change="showBlackChange"></u-switch>
					</view>
				</view>
			</view>

			<view>
				<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' @tap='sendCoupon'>赠送优惠券
				</xbutton>
			</view>
		</view>

		<view class="flex align-center justify-between screen-container" v-else-if="current==1">
			<view class="total-list">
				<text>会员总数:{{total}}</text><text v-if="countData&&countData.length>0">，</text>
				<block v-for="(item,index) in countData" :key="item.mealId">
					<text>{{item.mealName}}会员数:{{item.count}}</text>
					<text v-if="index!==countData.length-1">，</text>
				</block>
			</view>
			<view class="flex align-center justify-end" style="width:230rpx;" @tap="screen">
				<view>筛选</view>
				<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png" mode="widthFix">
				</image>
			</view>
		</view>

		<view class="flex align-center justify-between screen-container" v-else>
			<view class="total-list flex">
				<view>总数:{{balanceTotal.num||'-'}}</view>
				<view class="total-item">充值金额:￥{{$xy.delMoney(balanceTotal.balance)||'-'}}</view>
				<view>赠送金额:￥{{$xy.delMoney(balanceTotal.balanceGive)||'-'}}</view>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view v-if="list.length>0" style="overflow: hidden;">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="equipment-container" v-if="current==0">
						<view class="flex align-center justify-between">
							<view class="title">
								{{item.tel||'-'}}
							</view>
						</view>

						<view class="flex">
							<view class="order-detail-item" style="width:50%;">
								<view>总消费金额：</view>￥{{$xy.delMoney(item.payMoney)}}
							</view>
							<view class="order-detail-item" style="width:50%;">
								<view>订单笔数：</view>{{item.size}}
							</view>
						</view>

						<view class="flex">
							<view class="order-detail-item" style="width:50%;">
								<view>黑名单：</view>{{item.isBlacklist?'是':'否'}}
							</view>
							<view class="order-detail-item" style="width:50%;">
								<view>积分：</view>{{item.points}}
							</view>
						</view>

						<view class="order-detail-item">
							<view>最后购物时间：</view>{{item.lastOrderTime||'-'}}
						</view>
						<view class="order-detail-item">
							<view>最后购物设备：</view>{{item.lastDeviceName||item.lastDeviceId||'-'}}
						</view>
					</view>

					<view class="equipment-container" v-else-if="current==1">
						<view class="flex align-center justify-between">
							<view class="title">
								{{item.tel||'-'}}
							</view>
						</view>

						<view class="order-detail-item">
							<view>当前套餐：</view>{{item.mealName}}
						</view>

						<view class="order-detail-item">
							<view>折扣：</view>{{Number(item.discount)*10||'-'}}折
						</view>

						<view class="order-detail-item" v-if="item.channelTypeName">
							<view>渠道来源：</view>{{item.channelTypeName}}
						</view>

						<view class="order-detail-item" v-if="item.deviceId">
							<view>来源设备：</view>{{item.deviceName||item.deviceId}}
						</view>

						<view class="order-detail-item">
							<view>订购时间：</view>{{item.createTime||'-'}}
						</view>

						<view class="order-detail-item">
							<view>到期时间：</view>{{item.expiresTime||'-'}}
						</view>
						<view class="order-detail-item">
							<view>总消费金额：</view>￥{{$xy.delMoney(item.payMoney)}}
						</view>
						<view class="order-detail-item">
							<view>总订单笔数：</view>{{item.size||'-'}}
						</view>

						<view class="order-detail-item"
							v-if="$xy.delMoney(item.payMoney)==0&&item.status==4&&!item.orderGoods">
							<view>零元单提示：</view>
							<view style="color: red;">未拿商品</view>
						</view>


						<view class="flex justify-end martop">

							<view class="marleft24">
								<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' @tap='record(item)'>套餐记录
								</xbutton>
							</view>
							<view class="marleft24">
								<xbutton size='medium' round='25rpx' padding='0rpx 20rpx' @tap='orderDetail(item)'>商品订单
								</xbutton>
							</view>
						</view>
					</view>

					<view class="equipment-container" v-else>
						<view class="flex align-center justify-between">
							<view class="title">
								手机号：{{item.tel||'-'}}
							</view>
						</view>

						<view class="order-detail-item">
							<view>剩余总金额：</view>￥{{ ($xy.delMoney(item.balance + item.balanceGive))}}
						</view>

						<view class="order-detail-item">
							<view>剩余充值金额：</view>￥{{$xy.delMoney(item.balance)}}
						</view>
						<view class="order-detail-item">
							<view>剩余赠送金额：</view>￥{{$xy.delMoney(item.balanceGive)}}
						</view>
						<view class="order-detail-item">
							<view>最后一次充值设备：</view>{{item.lastRechargeDeviceName||'-'}}
						</view>
						<view class="order-detail-item">
							<view>最后一次充值时间：</view>{{item.lastRechargeTime||'-'}}
						</view>

						<view class="flex justify-end martop">
							<view class="marleft24">
								<xbutton padding='0rpx 20rpx' bgColor="#F4F8FF" borderColor="#F4F8FF" color="#2C6FF3"
									@tap='recDetail(item)'>充值记录
								</xbutton>
							</view>
							<view class="marleft24">
								<xbutton padding='0rpx 20rpx' @tap='restDetail(item)'>余额明细
								</xbutton>
							</view>
						</view>
					</view>
				</block>
				<view class="load-more" style="padding:24rpx;">
					<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
				</view>
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>

		<xpopup :show="screenShow" @close="close" @confirm="sure" :showBtn="true" clear="清空" @clearClick="clear"
			title="筛选" zIndex="10000">
			<view class="popup-container">
				<view class='martop' @click="chooseDevice">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.deviceNames"
						suffixIconStyle="color: #909399" placeholder="设备选择" border="surround"></u--input>
				</view>
				<view class='martop' @click="actionsheetChange('type')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.mealIdName"
						suffixIconStyle="color: #909399" placeholder="套餐类型" border="surround"></u--input>
				</view>
				<view class='martop' @click="actionsheetChange('channel')">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.channelName"
						suffixIconStyle="color: #909399" placeholder="推广渠道" border="surround"></u--input>
				</view>
				<view class='martop' @click="chooseTime">
					<u--input clearable readonly suffixIcon="arrow-down" v-model="showQuery.time"
						suffixIconStyle="color: #909399" placeholder="时间区间" border="surround"></u--input>
				</view>
			</view>
		</xpopup>

		<!-- 设备选择弹框 -->
		<xpopup :show="deviceShow" @close="deviceClose" @confirm="deviceConfirm" :showBtn="true" title="设备选择">
			<view class="popup-container">
				<view class="search">
					<u-search animation placeholder="输入设备号/设备名称" :clearabled="true" v-model="searchKey"
						:showAction="false" @search="getDeviceList"></u-search>
				</view>

				<scroll-view style="height: 600rpx;" scroll-y scroll-with-animation>
					<view class='device-scroll'>
						<u-checkbox-group @change="deviceChange" activeColor="#2C6FF3">
							<u-checkbox :customStyle="{marginBottom: '14px',marginRight: '24px',width:'300rpx'}"
								v-for="(item, index) in deviceList" :key="item.deviceId"
								:label="item.deviceName||item.deviceId" :name="item.deviceId">
							</u-checkbox>
						</u-checkbox-group>
					</view>
				</scroll-view>
			</view>
		</xpopup>

		<u-action-sheet :show="actionSheetShow" :actions="actions" :title="title" @close="actionSheetShow = false"
			@select="actionsheetSelect($event)"></u-action-sheet>

		<u-calendar :show="calendarShow" allowSameDay closeOnClickOverlay @close="calendarShow=false"
			:monthNum="calendarMonthNum" mode="range" color="#2C6FF3" @confirm="calendarConfirm"></u-calendar>
	</view>
</template>

<script>
	import {
		viperPage,
		countTotalPage,
		userPage,
		listByMercId,
		balanceTotal
	} from "@/api/vip.js"

	import {
		simpleDeviceSearchPage
	} from '@/api/replenishment/replenishment.js'

	import {
		channelPage,
	} from "@/api/channel.js"

	export default {
		data() {
			return {
				keyword: null,
				list: [],
				orderCount: {},
				loadmoreStatus: 'loadmore',
				isEmpty: false,
				page: 1, //当前分页
				size: 10, //分页数据条数
				mode: 'single',
				screenShow: false,
				actionSheetShow: false,
				actions: [],
				title: '',

				pickerType: null,

				fullHeight: 0,

				tabList: [{
						name: '全部用户'
					},
					{
						name: 'vip会员'
					},
					{
						name: '储值会员'
					},
				],
				current: 0,
				showBlack: false,

				countData: [],

				meal: {
					id: null,
					name: null
				},
				total: 0,

				deviceShow: false,
				searchKey: '',
				deviceList: [],

				searchQuery: {
					deviceIdList: [], //设备集合
					mealId: '',
					channelTypeName: '',
					beginCreateTime: '',
					endCreateTime: ''
				},

				showQuery: {
					mealIdName: '',
					deviceNames: '',
					channelName: '',
					time: ''
				},

				balanceTotal: {
					"num": 0,
					"balance": 0,
					"balanceGive": 0
				},

				typeActions: [],
				channelActions: [],

				calendarShow: false,
				calendarMonthNum: 10,
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			this.search()
			this.plusTotal()
		},

		methods: {
			/**
			 * @param {Object} 
			 * 会员统计数据
			 */
			plusTotal() {
				countTotalPage({}).then(res => {
					let data = res.data;
					this.countData = data
				})
			},

			/**
			 * 套餐筛选
			 */
			screenList() {
				listByMercId({}).then(res => {
					let data = res.data;
					let actions = data.map(i => ({
						name: i.mealName,
						type: i.id
					}))
					this.typeActions = [{
						name: '全部',
						type: ''
					}, ...actions]
				})
			},

			/**
			 * 套餐筛选
			 */
			channelList() {
				channelPage({
					page: {
						current: 1,
						size: 1000
					}
				}).then(res => {
					let data = res.data.records
					let actions = []
					if (data.length > 0) {
						actions = data.map(i => {
							return {
								name: i.name,
								type: i.id
							}
						})
					}
					this.channelActions = [{
						name: '全部',
						type: ''
					}, ...actions]
				})
			},

			tabClick(e) {
				this.current = e.index
				this.reset()
				this.getpage()
				if (e.index == 2) {
					this.storeTotal()
				}
			},

			actionsheetSelect(e) {
				if (this.title == '套餐类型') {
					this.searchQuery.mealId = e.type
					this.showQuery.mealIdName = e.name
				} else {
					this.searchQuery.channelTypeName = e.name
					this.showQuery.channelName = e.name
				}
			},


			//是否展示黑名单用户
			showBlackChange() {
				this.search()
			},

			//点击筛选
			screen() {
				this.screenShow = true
				this.screenList()
				this.channelList()
			},

			close() {
				this.screenShow = false
			},

			sure() {
				this.reset()
				this.getpage()
				this.screenShow = false;
			},

			storeTotal() {
				balanceTotal({}).then(res => {
					this.balanceTotal = res.data
				})
			},

			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
				}
				if (this.current == 0 || this.current == 2) {
					let obj = {
						isBlacklistSelect: this.current == 0 ? this.showBlack : false,
						tel: this.keyword || null,
						isBalance: this.current == 2
					}
					Object.assign(params, obj)
					userPage(params).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.total = res.data.total
						this.list = this.list.concat(data)
					})
				} else if (this.current == 1) {
					let obj = {
						tel: this.keyword || null,
						...this.searchQuery
					}
					Object.assign(params, obj)
					if (params.channelTypeName == '全部') {
						params.channelTypeName = ''
					}
					viperPage(params).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.loadmoreStatus = "nomore"
						} else {
							this.loadmoreStatus = "loadmore"
						}
						this.total = res.data.total
						this.list = this.list.concat(data)
					})
				}
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			orderDetail(item) {
				this.$tab.navigateTo('/pages/order/userInfo?id=' + item.memberId)
			},

			pointsDetail(item) {
				this.$tab.navigateTo('/pages/integral/userIntegralRecord?id=' + item.memberId)
			},

			record(item) {
				this.$tab.navigateTo('/pages/vip/viperRec?memberId=' + item.memberId)
			},

			/**
			 * @param {Object} item
			 * 充值记录
			 */
			recDetail(item) {
				this.$tab.navigateTo('/pages/rechargeAct/recOrderList?memberId=' + item.memberId)
			},

			/**
			 * @param {Object} item
			 * 余额明细
			 */
			restDetail(item) {
				this.$tab.navigateTo(`/pages/rechargeAct/restList?tel=${item.tel}&id=${item.memberId}`)
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			chooseDevice() {
				this.deviceShow = true
				this.getDeviceList()
			},

			/**
			 * 设备列表
			 */
			getDeviceList() {
				simpleDeviceSearchPage({
					page: {
						size: -1
					},
					searchKey: this.searchKey
				}).then(res => {
					this.deviceList = res.data.records
				})
			},

			deviceChange(e) {
				console.log(e)
				this.searchQuery.deviceIdList = e
				let deviceNames = []
				for (let deviceId of e) {
					for (let item of this.deviceList) {
						if (deviceId == item.deviceId) {
							deviceNames.push(item.deviceName)
						}
					}
				}
				this.showQuery.deviceNames = deviceNames.join(',')
			},

			deviceClose() {
				this.deviceShow = false
				this.searchQuery.deviceIdList = []
			},

			deviceConfirm() {
				this.deviceShow = false
			},

			actionsheetChange(type) {
				this.actionSheetShow = true
				switch (type) {
					case 'type':
						this.title = "套餐类型"
						this.actions = this.typeActions
						break;
					case 'channel':
						this.title = "推广渠道"
						this.actions = this.channelActions
						break;
					default:
						break;
				}
			},

			chooseTime() {
				this.calendarShow = true
			},

			calendarConfirm(e) {
				this.searchQuery.beginCreateTime = e[0];
				this.searchQuery.endCreateTime = e.at(-1)
				this.showQuery.time = `${e[0]}~${e.at(-1)}`
				this.calendarShow = false
			},

			clear() {
				this.searchQuery = {
					deviceIdList: [], //设备集合
					mealId: '',
					channelTypeName: '',
					beginCreateTime: '',
					endCreateTime: ''
				}
				this.showQuery = {
					mealIdName: '',
					deviceNames: '',
					channelName: '',
					time: ''
				}

				this.screenShow = false
				this.search()
			},

			sendCoupon() {
				this.$tab.navigateTo('/pages/vip/sendCoupon')
			},
		}
	}
</script>
<style scoped lang="scss">
	::v-deep .u-checkbox-group {
		flex-flow: row wrap !important;
		justify-content: space-between !important;
	}

	::v-deep .u-checkbox {
		min-width: 260rpx !important;
	}

	::v-deep .u-checkbox__icon-wrap {
		margin-left: 20rpx !important;
	}

	.container {
		.empty {
			padding-top: 40%;
		}

		.martop {
			margin-top: 20rpx;
		}

		.search {
			padding: 24rpx 13rpx;
			background-color: #fff;
		}

		.show-zero {
			background-color: #fff;
			color: #777;
			margin-right: 18rpx;
			margin-left: 20rpx;
		}

		.tab-wrap {
			background-color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.screen-container {
			background-color: #fff;
			padding: 10rpx 13rpx 20rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-left: 12rpx;
			}

			.total-list {
				>view {
					display: inline-block;
				}

				.total-item {
					padding: 0 20rpx;
				}
			}
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 20rpx 24rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;

				>view {
					display: inline-block;
					width: 300rpx;
				}
			}
		}

		.popup-container {
			padding: 20rpx;
		}
	}
</style>