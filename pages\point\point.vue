<template>
	<view class="container">
		<u-navbar titleStyle="fontSize:36rpx;" :autoBack="true" bgColor="#fff" :placeholder="true"
			title="增加点位"></u-navbar>
		<view style="background-color: #fff;">
			<view class="flex align-center  box">
				<view class="menu-name">点位总数({{totalNum.placeNum}})</view>
				<view class="menu-name">区域总数({{totalNum.regionNum}})</view>
			</view>

			<!-- 			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabChange"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view> -->

			<view class="search">
				<u-search animation placeholder="请输入点位名称" bgColor="#F6F8FB" :clearabled="true" v-model="keyword"
					:showAction="false" @search="search"></u-search>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view class="content">
				<block v-for="(item,index) in list" :key="item.id">
					<view class="xy-card" @click.stop="pointClick(item)">
						<view class="title flex justify-between">
							<view class="flex name">
								{{item.placeName}}
							</view>
							<view class="flex status">
								{{item.deviceNum||'0'}}台设备
							</view>
						</view>

						<view class="t-content flex flex-direction">
							<view class="c-item flex">
								<view>管理员：</view>
								<view>{{item.adminName||'-'}}</view>
								<view style="padding-left:24rpx;color:#2C6FF3;" @click.stop="call(item.adminTel)">
									{{item.adminTel||''}}
								</view>
							</view>
							<view class="c-item flex">
								<view>区域/线路：</view>
								<view>{{item.regionName||'-'}}</view>
							</view>
							<view class="c-item flex">
								<view>场景：</view>
								<view>{{item.sceneNames||'-'}}</view>
							</view>
							<view class="c-item flex">
								<view>标签：</view>
								<view>{{item.tagNames||'-'}}</view>
							</view>
							<view class="c-item flex">
								<view>详细地址：</view>
								<view>{{item.address||'-'}}</view>
							</view>
						</view>

						<view class="edit flex justify-end">
							<xbutton width="144rpx" bgColor="#fff" color="red" borderColor="red" round="14rpx"
								@click.stop.native="del(item)" style="margin-right: 24rpx;" v-if="checkPermi(['point:del'])">
								删除
							</xbutton>
							<xbutton width="144rpx" bgColor="#fff" color="#2C6FF3" borderColor="#2C6FF3" round="14rpx"
								@click.stop.native="edit(item)" v-if="checkPermi(['point:edit'])">
								详情/编辑
							</xbutton>
						</view>
					</view>
				</block>

				<u-loadmore :status="status" v-if="list.length>=1" />
			</view>
			<view class="empty" v-if="list.length==0">
				<u-empty text="没有点位!"></u-empty>
			</view>
		</scroll-view>

		<view class="btn" v-if="checkPermi(['point:add'])">
			<xbutton size="large" @click="add">
				新增点位
			</xbutton>
		</view>

		<!-- 新增点位 -->
		<xpopup :show="pointEditShow" @close="close" @confirm="submit" showBtn :title="title">
			<view class="popup-content edit-point">
				<view class="pop-item flex align-center">
					<view class="pop-label">
						<text style="color: red;">*</text>点位名称：
					</view>
					<view class="pop-content">
						<u-input v-model="form.placeName" placeholder="请输入点位名称" border="none">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						<text style="color: red;">*</text>区域：
					</view>
					<view class="pop-content" @click="lotusAddressData.visible=true">
						<u-input v-model="form.regionName" placeholder="请选择区域" disabled disabledColor="#fff"
							border="none" suffixIcon="arrow-right" suffixIconStyle="color: #555555"
							:suffixIconStyle="{fontSize:24}">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						<text style="color: red;">*</text>详细地址：
					</view>
					<view class="pop-content">
						<u-input v-model="form.address" placeholder="请输入详细地址" border="none">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						地图位置：
					</view>
					<view class="pop-content" @click.native="getLocation">
						<u-input v-model="form.pos" placeholder="请选择地图位置" disabled disabledColor="#fff" border="none"
							suffixIcon="map-fill" suffixIconStyle="color: #555555">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						<text style="color: red;">*</text>场景：
					</view>
					<view class="pop-content" @click="chooseScene">
						<u-input v-model="form.sceneNames" placeholder="请选择场景" disabled disabledColor="#fff"
							border="none" suffixIcon="arrow-right" suffixIconStyle="color: #555555"
							:suffixIconStyle="{fontSize:24}">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						标签：
					</view>
					<view class="pop-content">
						<u-input v-model="form.tagNames" placeholder="多个标签逗号隔开" border="none">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center" @click="chooseEmployee(0)">
					<view class="pop-label">
						管理员：
					</view>
					<view class="pop-content">
						<u-input v-model="form.adminName" placeholder="请选择管理员" disabled disabledColor="#fff"
							border="none" suffixIcon="arrow-right" suffixIconStyle="color: #555555"
							:suffixIconStyle="{fontSize:24}">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						管理员电话：
					</view>
					<view class="pop-content">
						<u-input v-model="form.adminTel" placeholder="请输入管理员电话" border="none">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center" @click="chooseEmployee(1)">
					<view class="pop-label">
						补货员：
					</view>
					<view class="pop-content">
						<u-input v-model="form.supplyName" placeholder="请选择补货员" disabled disabledColor="#fff"
							border="none" suffixIcon="arrow-right" suffixIconStyle="color: #555555"
							:suffixIconStyle="{fontSize:24}">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						补货员电话：
					</view>
					<view class="pop-content">
						<u-input v-model="form.supplyTel" placeholder="请输入补货员电话" border="none">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center" @click="chooseEmployee(2)">
					<view class="pop-label">
						客服：
					</view>
					<view class="pop-content">
						<u-input v-model="form.serviceName" placeholder="请选择补货员" disabled disabledColor="#fff"
							border="none" suffixIcon="arrow-right" suffixIconStyle="color: #555555"
							:suffixIconStyle="{fontSize:24}">
						</u-input>
					</view>
				</view>

				<view class="pop-item flex align-center">
					<view class="pop-label">
						客服电话：
					</view>
					<view class="pop-content">
						<u-input v-model="form.serviceTel" placeholder="请输入客服电话" border="none">
						</u-input>
					</view>
				</view>
			</view>
		</xpopup>

		<!-- 级联地址 -->
		<lotus-address v-on:choseVal="addressConfirm" @close="addressClose" :lotusAddressData="lotusAddressData">
		</lotus-address>

		<!-- 场景选择 -->
		<xtree ref="tkitree" :multiple="true" :range="sceneTree" :selectParent="true" rangeKey="name"
			confirmColor="#2C6FF3" @confirm="sceneSubmit"></xtree>
	</view>
</template>

<script>
	import lotusAddress from "./components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";

	import {
		pointPage,
		pointSave,
		pointDel,
		sceneTree,
		pointCount
	} from "@/api/point/point"
	export default {
		components: {
			lotusAddress
		},
		data() {
			return {
				page: 1,
				size: 10,
				status: 'loadmore',
				current: 0,
				tabList: [{
						name: '点位列表',
						value: ''
					},
					{
						name: '区域列表',
						value: 1
					}
				],
				list: [],
				keyword: '',
				fullHeight: 0,

				pointEditShow: false,

				title: '新增点位',
				lotusAddressData: {
					visible: false,
					provinceName: '',
					cityName: '',
					townName: '',
				},

				form: {
					placeName: '',
					regionName: '',
					address: '',
					pos: '',
					lat: '',
					lon: '',
					sceneNames: '',
					tagNames: '',
					adminName: '',
					adminId: '',
					adminTel: '',
					supplyName: '',
					supplyId: '',
					supplyTel: '',
					serviceName: '',
					serviceTel: '',
					serviceId:'',
				},

				sceneTree: [],
				type: null,
				totalNum: {
					regionNum: 0,
					lineNum: 0,
					placeNum: 0
				}
			}
		},

		onPullDownRefresh() {
			this.reset()
			if (this.current == 0) {
				this.getList1().then(res => {
					uni.stopPullDownRefresh();
				})
			} else {
				this.getList2().then(res => {
					uni.stopPullDownRefresh();
				})
			}

		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			if (o.type) {
				this.type = o.type
			}

			uni.$on('chooseEmp', res => {
				console.log(res)
				this.chooseEmpBack(res)
			})

			this.getTotalNum()
			this.search()
		},

		methods: {
			getTotalNum() {
				pointCount().then(res => {
					this.totalNum = res.data
				})

			},
			chooseEmpBack(e) {
				switch (Number(e.type)) {
					case 0: //默认带出补货和客服管理员
						this.form.adminName = e.name;
						this.form.adminTel = e.tel;
						this.form.adminId = e.id
						this.form.supplyName = this.form.supplyName ? this.form.supplyName : e.name;
						this.form.supplyId = this.form.supplyId ? this.form.supplyId : e.id;
						this.form.supplyTel = this.form.supplyTel ? this.form.supplyTel : e.tel;
						this.form.serviceName = this.form.serviceName ? this.form.serviceName : e.name;
						this.form.serviceTel = this.form.serviceTel ? this.form.serviceTel : e.tel;
						this.form.serviceId = this.form.serviceId ? this.form.serviceId : e.id;
						break;
					case 1:
						this.form.supplyName = e.name;
						this.form.supplyTel = e.tel;
						this.form.supplyId = e.id;
						break;
					case 2:
						this.form.serviceName = e.name;
						this.form.serviceTel = e.tel;
						this.form.serviceId = e.id;
						break;
					default:
						break;
				}
				this.$forceUpdate()
			},

			tabChange(e) {
				this.current = e.index
				this.reset()
				this.getList()
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.list = [];
			},

			getList() {
				return new Promise((resolve, reject) => {
					pointPage({
						page: {
							current: this.page,
							size: this.size
						},
						search: this.keyword,
						searchDeviceCount: true
					}).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.status = "nomore"
						} else {
							this.status = "loadmore"
						}
						this.list = this.list.concat(data)
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			pointClick(item) {
				console.log(item)
				if (this.type == 1) {
					uni.$emit('selectPoint', item)
					this.$tab.navigateBack()
				}
			},

			search() {
				this.reset()
				this.getList()
			},

			edit(item) {
				this.title = '详情/编辑'
				this.getSceneTree()
				this.form = JSON.parse(JSON.stringify(item))
				this.form.pos = item.lat ? item.lat + ',' + item.lon : ''
				this.pointEditShow = true
			},

			del(item) {
				this.$modal.oldConfirm(`确定删除${item.placeName}点位？`).then(res => {
					pointDel({
						id: item.id
					}).then(res => {
						this.search()
						setTimeout(() => {
							this.$modal.showToast('删除成功~')
						}, 500)
					})
				})
			},

			resetForm() {
				this.form = {
					placeName: '',
					regionName: '',
					address: '',
					pos: '',
					lat: '',
					lon: '',
					sceneNames: '',
					tagNames: '',
					adminName: '',
					adminId: '',
					adminTel: '',
					supplyName: '',
					supplyId: '',
					supplyTel: '',
					serviceName: '',
					serviceTel: '',
					serviceId:'',
				}
			},

			//触底加载更多
			scrolltolower() {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			close() {
				this.pointEditShow = false
			},

			submit() {
				if (this.checkParams(this.form)) return
				let params = {
					id: this.form.id,
					placeName: this.form.placeName,
					regionName: this.form.regionName,
					address: this.form.address,
					lat: this.form.lat,
					lon: this.form.lon,
					sceneNames: this.form.sceneNames,
					tagNames: this.form.tagNames,
					adminName: this.form.adminName,
					adminTel: this.form.adminTel,
					adminId: this.form.adminId,
					supplyName: this.form.supplyName,
					supplyTel: this.form.supplyTel,
					supplyId: this.form.supplyId,
					serviceName: this.form.serviceName,
					serviceTel: this.form.serviceTel,
					serviceId: this.form.serviceId
				}
				pointSave(params).then(res => {
					this.pointEditShow = false
					this.search()
					setTimeout(() => {
						this.$modal.showToast('成功~')
					}, 500)
				})

			},

			checkParams(params) {
				if (!params.placeName) {
					this.$modal.msg('点位名称不能为空~')
					return true
				}
				if (!params.regionName) {
					this.$modal.msg('区域不能为空不能为空~')
					return true
				}
				if (!params.address) {
					this.$modal.msg('详细地址不能为空~')
					return true
				}
				return false
			},

			//级联地址选择确认
			addressConfirm(res) {
				if (res.id === null) {
					this.$modal.msg('请选择区域!');
					return
				}
				let data = res.province
				if (res.city) {
					data += `/${res.city}`
				}
				if (res.town) {
					data += `/${res.town}`
				}
				this.form.regionName = data
				this.addressClose() //关闭弹框
			},

			// 级联地址取消
			addressClose() {
				this.lotusAddressData.visible = false;
			},

			getLocation() {
				uni.chooseLocation({
					success: (res) => {
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						this.form.lat = res.latitude;
						this.form.lon = res.longitude;
						this.form.pos = res.latitude + ',' + res.longitude
						getApp().globalData.isOnShow = false
					}
				})
			},

			getSceneTree() {
				sceneTree({}).then(res => {
					this.sceneTree = res.data
				})
			},

			chooseScene() {
				this.$refs.tkitree._show();
			},

			sceneSubmit(e) {
				if (e.length > 0) {
					let sceneNames = e.map(i => i.name)
					this.form.sceneNames = sceneNames.join(',')
				}
			},

			add() {
				this.getSceneTree()
				this.resetForm()
				this.pointEditShow = true
			},

			chooseEmployee(type) {
				this.$tab.navigateTo(`/pages/point/employee?type=${type}`)
			},


			call(tell) {
				uni.makePhoneCall({
					phoneNumber: tell
				})
			},
		},

		onUnload() {
			uni.$off('chooseEmp')
		}
	}
</script>

<style scoped lang="scss">
	.container {

		// padding-top: 120rpx;
		image {
			width: 50rpx;
			height: 50rpx;
		}

		.box {
			padding: 38rpx 12rpx 14rpx;
			background: #fff;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;
			line-height: 62rpx;

			>view {
				background: #F7F7F7;
				border-radius: 10rpx;
				margin-right: 20rpx;
				padding: 0 24rpx;
			}
		}

		.tips {
			width: 724rpx;
			padding: 10rpx 24rpx;
			font-size: 28rpx;
			line-height: 40rpx;
			font-weight: 500;
			color: #333333;
			background: #F0FFFC;
			border: 2rpx dashed #61D8BE;
			border-radius: 14rpx;
			margin-left: 13rpx;
		}

		.search {
			padding: 0rpx 13rpx 24rpx;
			background-color: #fff;
			position: relative;
			margin-top: 14rpx;

			.scan-icon {
				position: absolute;
				right: 36rpx;
				top: 50%;
				transform: translateY(-50%);
				z-index: 2;
			}
		}

		.scan {
			width: 100%;
			position: fixed;
			bottom: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			background-color: #fff;
			box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);

			.scan-icon-bot {
				width: 60px;
			}

			>view:nth-child(2) {
				font-size: 24rpx;
				color: #999;
			}
		}

		.tab-wrap {
			background-color: #fff;
			padding: 10rpx 0 20rpx;

			.tab {
				width: 60%;
				margin-left: -12rpx;
			}
		}

		.content {
			margin: 0 13rpx;
			padding-bottom: 90rpx;
			overflow: hidden;

			.xy-card {
				margin-top: 20rpx;
				padding: 32rpx;
			}
		}

		.title {
			line-height: 50rpx;

			.name {
				font-size: 32rpx;
				font-weight: bold;
			}

			.status {
				font-size: 28rpx;
			}
		}

		.t-content {
			font-size: 28rpx;
			color: #555555;
			line-height: 40rpx;
			margin-top: 10rpx;

			.c-item {
				margin-top: 10rpx;

				>view:nth-child(1) {
					width: 200rpx;
				}
			}
		}

		.edit {
			margin-top: 20rpx;
		}

		.empty {
			margin-top: 50rpx;
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;

				>image {
					width: 42rpx;
					height: 42rpx;
					margin-right: 20rpx;
				}
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			height: 850rpx;

			.pop-item {
				line-height: 66rpx;
				font-size: 28rpx;
				color: #555;

				.pop-label {
					width: 220rpx;
				}

				.pop-content {
					width: 480rpx;
				}
			}
		}
	}
</style>