<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="通知"></u-navbar>
		<view class="content">

			<scroll-view @scrolltolower="loadMore" class="scrollview" :scroll-with-animation="true" scroll-y
				lower-threshold="100" :style="{height:fullHeight}">
				<view class="list">
						<view class="comm-main">
							<view class="item-head flex justify-between">
								<view class="title">
									{{detail.title||'无'}}
								</view>
							</view>
							<view class="item-body">
								<view class="item-body-sec">
									<view style="width:150rpx;">创建时间：</view><view>{{detail.createTime||'无'}}</view>
								</view>
								<view class="item-body-sec">
									<view style="width:150rpx;">通知内容：</view><view>{{detail.content||'无'}}</view>
								</view>
							</view>
						</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		msgDetail
	} from "@/api/message.js"
	export default {
		data() {
			return {
				fullHeight: 0,
				id:null,
				detail:{}
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview',0)
			
			this.id=o.id
			this.msgDetail()
		},

		methods: {
			//消息中心
			msgDetail() {
				msgDetail({
					id:this.id
				}).then(res => {
					this.detail=res.data
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.content {

			.list {
				width: 100%;
				padding: 0rpx 13rpx 12rpx;
				padding-bottom: calc(110rpx + env(safe-area-inset-bottom) / 2);
				overflow: hidden;

				.comm-img {
					width: 130rpx;
					height: 130rpx;
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: space-around;

					image {
						width: 130rpx;
						height: 130rpx;
					}
				}

				.comm-main {
					padding: 20rpx 30rpx;
					background-color: #fff;
					border-radius: 12rpx;
					margin-top: 12rpx;
					box-sizing: border-box;
					color: #999;
					line-height: 60rpx;
					position: relative;

					.item-head {
						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}
					
						.status {
							font-size: 28rpx;
						}
						
						.read-status{
							width:20rpx;
							height:20rpx;
							border-radius: 20rpx;
							background-color:red;
						}
					}
					
					.item-body {
						margin-top: 22rpx;
						color: #333;
					
						.item-body-sec {
							line-height: 44rpx;
							overflow: hidden;
							
							>view:nth-child(1) {
								float:left;
							}
							>view:nth-child(2){
								padding-left: 150rpx;
								word-break: break-all;
							}
						}
					}
				}
			}
		}
	}

	.empty {
		padding-top: 40%;
	}
</style>