<template>
	<u-upload sizeType="original" :fileList="fileList" :previewFullImage="true" @afterRead="afterRead" @delete="deletePic" multiple
		accept="image" :maxCount="maxCount">
		<view class="upload-fix flex justify-around align-center">
			<view class="flex">
				<u-icon name="camera" color="#cacaca" size="24"></u-icon>
			</view>
		</view>
	</u-upload>
</template>

<script>
	import {
		ossInfo
	} from '@/api/oss.js'
	import {
		getRandomNum
	} from '@/utils/common.js'
	import imgUpload from '@/utils/upload.js'
	export default {
		data() {
			return {
				uploadData: { //oss返回数据
					policy: undefined,
					Signature: undefined,
					OSSAccessKeyId: undefined,
					uploadImgUrl: undefined,
					dir: undefined,
					key: undefined
				},
				fileList: []
			}
		},
		props: {
			value: {
				type: Array,
				require: true,
				default () {
					return []
				}
			},

			maxCount: {
				type: Number,
				require: false,
				default: 4
			},
		},

		watch: {
			value: {
				handler(newVal, oldVal) {
					let newList = newVal.map(i => {
						return {
							url: i
						}
					})
					this.fileList = newList
				},
				immediate: true,
				deep: true
			}
		},

		methods: {
			oss() {
				return new Promise((resolve, reject) => {
					ossInfo().then(response => {
						this.uploadData.policy = response.data.policy;
						this.uploadData.Signature = response.data.signature;
						this.uploadData.OSSAccessKeyId = response.data.accessid;
						this.uploadData.uploadImgUrl = response.data.domain;
						this.uploadData.dir = response.data.dir;
						resolve(response)
					}).catch(err => {
						reject(err)
					})
				})
			},
			// 删除图片
			deletePic(event) {
				this.fileList.splice(event.index, 1);
				let newList = this.fileList.map(i => {
					return i.url
				})
				this.$emit('input', newList)
			},
			
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.fileList.length
				lists.map((item) => {
					this.fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})

				await this.oss() //获取oss数据

				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i])
					let item = this.fileList[fileListLen]
					this.fileList.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '成功',
						url: result
					}))
					fileListLen++
				}

				let newList = this.fileList.map(i => {
					return i.url
				})
				this.$emit('input', newList)
			},
			uploadFilePromise(file) {
				//组装上传数据
				let timestamp = new Date().getTime()
				let randomNum = getRandomNum(10000, 99999)
				let imgType = file.url.substr(file.url.indexOf('.'))
				this.uploadData.key = `${this.uploadData.dir}${timestamp}${randomNum}${imgType}`
				let params = {
					policy: this.uploadData.policy,
					Signature: this.uploadData.Signature,
					OSSAccessKeyId: this.uploadData.OSSAccessKeyId,
					key: this.uploadData.key
				}

				return new Promise((resolve, reject) => {
					imgUpload({
						url: this.uploadData.uploadImgUrl,
						header: {
							isToken: true
						},
						formData: params,
						filePath: file.url
					}).then((res) => {
						console.log('res',res)
						let url = this.uploadData.uploadImgUrl + '/' + this.uploadData
							.key //图片路径需要前端组装
						console.log('图片路径', url)
						resolve(url)
					}).catch(err => {
						reject(err)
					})
				})
			},
		}
	}
</script>

<style lang="scss">
.upload-fix{
	width:160rpx;
	height:160rpx;
	border: 1rpx dashed #dddddd;
	border-radius: 12rpx;
}
</style>
