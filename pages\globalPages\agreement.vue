<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="入驻协议"></u-navbar>
		<view class="content safe-bottom">
			<pre v-html="content" v-if="content!=''">
			</pre>
			<view class="empty" v-else>
				<u-empty mode="list" text="没有任何内容!"></u-empty>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		agreement,
		agreementPage
	} from "@/api/system/user.js"
	export default {
		data() {
			return {
				content: ''
			}
		},

		onLoad() {
			this.detail()
		},

		methods: {
			agreementList() {
				agreement({
					page: {
						current: 1,
						size: 100
					},
					orders: [{
						asc: false,
						column: "create_time"
					}]
				}).then(res => {
					if (res.data && res.data.content) {
						this.content = res.data.content
					} else {
						this.content = ''
					}
				})
			},

			detail() {
				agreement({
					type: 3
				}).then(res => {
					this.content = res.data.content
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;
		background-color: #fff;

		.content {
			padding: 30rpx;
			min-height: 100vh;

			.empty {
				padding-top: 40%;
			}
		}
	}
</style>