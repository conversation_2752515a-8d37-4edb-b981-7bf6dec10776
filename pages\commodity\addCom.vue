<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="商品建模"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" :rules="rules" ref="form" errorType="toast" labelWidth="auto">
				<!-- <u-form-item label="类型" borderBottom ref="item2">
					<u-radio-group v-model="form.type">
						<u-radio :customStyle="{marginRight: '16rpx'}" label="标准商品" name="1">
						</u-radio>
						<u-radio :customStyle="{marginRight: '16rpx'}" label="非标商品" name="2">
						</u-radio>
					</u-radio-group>
				</u-form-item> -->
				<u-form-item required label="名称" prop="goodsName" borderBottom ref="item1">
					<u--input v-model="form.goodsName" placeholder="输入商品名称,如:百事可乐厅330ml" border="none"></u--input>
				</u-form-item>
				<view class="bar-code">
					<u-form-item required label="条码" prop="barcode" borderBottom ref="item1">
						<u--input v-model="form.barcode" placeholder="输入商品条码,如:123456789" border="none"></u--input>
					</u-form-item>
					<view class="scan-icon" @click.stop="scan">
						<u-icon name="scan" size="22" color="#909399"></u-icon>
					</view>
				</view>
				<u-form-item required label="类目" prop="categoryName" borderBottom @click="chooseCategoty" ref="item1">
					<u--input v-model="form.categoryName" disabled placeholder="请选择商品类目" disabledColor="#ffffff"
						border="none"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item required label="品牌" prop="brandName" borderBottom ref="item1">
					<u--input v-model="form.brandName" placeholder="输入商品品牌,如:百事可乐" border="none"></u--input>
				</u-form-item>
				<!-- 	<u-form-item required label="规格" prop="unitName" borderBottom ref="item1">
					<u--input v-model="form.unitName" placeholder="请输入计量单位" border="none"></u--input>
				</u-form-item> -->
				<u-form-item required label="规格" borderBottom @click="showSelect('unitName')" ref="item1">
					<u--input v-model="form.unitName" disabled placeholder="请选择商品规格" disabledColor="#ffffff"
						border="none">
					</u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="销售价" borderBottom ref="item1">
					<u--input v-model="form.price" type="digit" placeholder="请输入销售价" border="none"></u--input>
				</u-form-item>
				<u-form-item label="成本价" borderBottom ref="item1">
					<u--input v-model="form.priceCost" type="digit" placeholder="请输入成本价" border="none"></u--input>
				</u-form-item>

				<view class="img-tips">
					注意事项：<br />
					1、商品图片必须完成，请参考示例图片拍照上传。<br />
					2、香烟及其他危险物品、非法物品不支持建模。<br />
					3、新品建模的审核时效为1-3个工作日。
				</view>

				<u-row customStyle="margin-top: 10px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="69码示例">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/barcode.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/barcode.png"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="imgBarcode" labelWidth="350"
									label="商品69码">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgBarcode||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgBarcode')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgBarcode" :data-src="form.imgs.imgBarcode"
								@click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="封面示例">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgPositive.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgPositive.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="cover" labelWidth="350" label="封面图片"
									ref="item1">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx" :src="form.cover||imgBg"
											mode="aspectFit" :lazy-lord="true" @click="imgClick('cover')"></u--image>
									</view>
								</u-form-item>
								<view class="tips">
									小程序商品陈列展示用
								</view>
							</view>
							<view class="prev" v-if="form.cover" :data-src="form.cover" @click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgPositive.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgPositive.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="imgPositive" labelWidth="80"
									label="正面图片" ref="item1">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgPositive||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgPositive')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgPositive" :data-src="form.imgs.imgPositive"
								@click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgBack.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgBack.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="imgBack" labelWidth="80" label="背面图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgBack||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgBack')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgBack" :data-src="form.imgs.imgBack" @click="preview">
								预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgLeft.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgLeft.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="imgLeft" labelWidth="80" label="左面图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgLeft||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgLeft')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgLeft" :data-src="form.imgs.imgLeft" @click="preview">
								预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgRight.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgRight.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="imgRight" labelWidth="80" label="右面图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgRight||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgRight')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgRight" :data-src="form.imgs.imgRight"
								@click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgTop.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgTop.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="imgTop" labelWidth="80" label="顶部图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgTop||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgTop')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgTop" :data-src="form.imgs.imgTop" @click="preview">预览
							</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgBottom.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgBottom.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<u-form-item required labelPosition="top" prop="imgBottom" labelWidth="80" label="底部图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgBottom||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgBottom')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgBottom" :data-src="form.imgs.imgBottom"
								@click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgPositiveBias.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgPositiveBias.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<!-- <u-form-item required labelPosition="top" prop="imgPositiveBias" labelWidth="80"
									label="正斜图片"> -->
								<u-form-item labelPosition="top" labelWidth="80" label="正斜图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgPositiveBias||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgPositiveBias')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgPositiveBias" :data-src="form.imgs.imgPositiveBias"
								@click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgLeftBias.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgLeftBias.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<!-- <u-form-item required labelPosition="top" prop="imgLeftBias" labelWidth="80"
									label="左斜图片"> -->
								<u-form-item labelPosition="top" labelWidth="80" label="左斜图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgLeftBias||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgLeftBias')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgLeftBias" :data-src="form.imgs.imgLeftBias"
								@click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgRightBias.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgRightBias.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img">
								<!-- <u-form-item required labelPosition="top" prop="imgRightBias" labelWidth="80"
									label="右斜图片"> -->
								<u-form-item labelPosition="top" labelWidth="80" label="右斜图片">
									<view class="img-upload">
										<u--image radius="4" width="160rpx" height="160rpx"
											:src="form.imgs.imgRightBias||imgBg" mode="aspectFit" :lazy-lord="true"
											@click="imgClick('imgRightBias')"></u--image>
									</view>
								</u-form-item>
							</view>
							<view class="prev" v-if="form.imgs.imgRightBias" :data-src="form.imgs.imgRightBias"
								@click="preview">预览</view>
						</view>
					</u-col>
				</u-row>

				<u-row customStyle="margin-top: 20px;">
					<u-col span="5">
						<u-form-item labelPosition="top" labelWidth="120" label="商品图片(示例)">
							<view class="example-img">
								<image @click="preview"
									data-src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgRight.png"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/imgRight.png?x-oss-process=image/resize,m_fill,h_300,w_300"
									mode="widthFix"></image>
							</view>
						</u-form-item>
					</u-col>
					<u-col span="7">
						<view class="form-item flex align-center">
							<view class="form-item-img" style="width:400rpx;">
								<u-form-item labelPosition="top" labelWidth="80" label="其他图片">
									<view class="img-upload">
										<ximgUpload :maxCount="4" v-model="form.imgs.others" />
									</view>
								</u-form-item>
							</view>
							<view class="prev prev-else" v-if="form.imgs.others&&form.imgs.others.length>0"
								:data-src="form.imgs.others" @click="preview">预览
							</view>
						</view>
					</u-col>
				</u-row>


			</u--form>
			<u-picker :show="show" :columns="actions" :closeOnClickOverlay="true" keyName="name" @close="show=false"
				@confirm="actionSelect" @cancel="show = false">
			</u-picker>
		</view>

		<view class="btn safe-bottom" v-if="checkStatus=='1'">
			<xbutton delay="1500" bgColor="#5ac725" throttleTime="1500" size="large" width="702rpx" round="130rpx">审核通过
			</xbutton>
		</view>
		<view class="btn safe-bottom" v-else-if="checkStatus=='-1'">
			<xbutton delay="1500" throttleTime="1500" size="large" width="702rpx" round="130rpx" @click="submit">重新提交
			</xbutton>
		</view>
		<view class="btn safe-bottom" v-else-if="checkStatus=='0'">
			<xbutton delay="1500" bgColor="#f9ae3d" throttleTime="1500" size="large" width="702rpx" round="130rpx">审核中
			</xbutton>
		</view>
		<view class="btn safe-bottom" v-else>
			<xbutton delay="1500" throttleTime="1500" size="large" width="702rpx" round="130rpx" @click="submit">提交审核
			</xbutton>
		</view>

		<!-- 弹框 -->
		<xpopup :show="popShow" @close="popClose" :showBtn="false" title="选择类目">
			<!-- 类目选择 -->
			<scroll-view style="height: 600rpx;" scroll-y scroll-with-animation>
				<view class="popup-content">
					<tki-tree style="width:100%;" :range="categoryOptions" :foldAll="false" rangeKey="name"
						idKey="categoryCode" buttonName="选中" @btnClick="categorySubmit">
					</tki-tree>
				</view>
			</scroll-view>
		</xpopup>

		<ImgUploadCut @success="imgSuccess" ref="uploadImg" />
	</view>
</template>

<script>
	import ImgUploadCut from "./components/imgUploadCut"
	import {
		save,
		update,
		searchObj,
		categoryTree,
		listIdName
	} from '@/api/commodity/goodsMode.js'
	//验证规则
	let rulesObj = {
		type: 'string',
		required: true,
		message: '必填项',
		trigger: ['change']
	}

	export default {
		components: {
			ImgUploadCut
		},
		data() {
			return {
				form: {
					goodsName: undefined, //商品名称
					barcode: undefined, //条形码
					brandName: undefined, //品牌
					categoryName: undefined, //分类
					unitName: undefined, //规格
					cover: undefined, //封面图片
					price: undefined, //销售价
					priceCost: undefined, //成本价
					imgs: {
						imgBarcode: undefined, //69码图片
						imgPositive: undefined, //正面
						imgBack: undefined, //背面
						imgLeft: undefined, //左边
						imgRight: undefined, //右边
						imgTop: undefined, //顶部
						imgBottom: undefined, //底部图片
						imgPositiveBias: undefined, //正斜图片
						imgLeftBias: undefined, //左斜图片
						imgRightBias: undefined, //右斜图片
						others: []
					}

				},
				rules: {
					'unitName': {
						type: 'string',
						required: true,
						message: '请选择商品规格~',
						trigger: ['change']
					},
					'goodsName': {
						type: 'string',
						required: true,
						message: '请填入商品名称~',
						trigger: ['change']
					},
					'barcode': {
						type: 'string',
						required: true,
						message: '请填入商品条码~',
						trigger: ['change']
					},
					'brandName': {
						type: 'string',
						required: true,
						message: '请填入商品品牌~',
						trigger: ['change']
					},
					'categoryName': {
						type: 'string',
						required: true,
						message: '请选择商品类目~',
						trigger: ['change']
					},
					'cover': {
						validator: (rule, value, callback) => {
							if (!this.form.cover || this.form.cover.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传封面图片~',
						trigger: ['change', 'blur']
					},
					'imgBarcode': {
						validator: (rule, value, callback) => {
							if (!this.form.cover || this.form.cover.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传69码图片~',
						trigger: ['change', 'blur']
					},
					'imgPositive': {
						validator: (rule, value, callback) => {
							if (!this.form.imgs.imgPositive || this.form.imgs.imgPositive.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传正面图片~',
						trigger: ['change', 'blur']
					},
					'imgBack': {
						validator: (rule, value, callback) => {
							if (!this.form.imgs.imgBack || this.form.imgs.imgBack.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传背面图片~',
						trigger: ['change', 'blur']
					},
					'imgLeft': {
						validator: (rule, value, callback) => {
							if (!this.form.imgs.imgLeft || this.form.imgs.imgLeft.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传左面图片~',
						trigger: ['change', 'blur']
					},
					'imgRight': {
						validator: (rule, value, callback) => {
							if (!this.form.imgs.imgRight || this.form.imgs.imgRight.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传右面图片~',
						trigger: ['change', 'blur']
					},
					'imgTop': {
						validator: (rule, value, callback) => {
							if (!this.form.imgs.imgTop || this.form.imgs.imgTop.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传顶部图片~',
						trigger: ['change', 'blur']
					},
					'imgBottom': {
						validator: (rule, value, callback) => {
							if (!this.form.imgs.imgBottom || this.form.imgs.imgBottom.length == 0) {
								return false
							} else {
								return true
							}
						},
						message: '请上传底部图片~',
						trigger: ['change', 'blur']
					},
					// 'imgPositiveBias': {
					// 	validator: (rule, value, callback) => {
					// 		if (!this.form.imgs.imgPositiveBias || this.form.imgs.imgPositiveBias.length == 0) {
					// 			return false
					// 		} else {
					// 			return true
					// 		}
					// 	},
					// 	message: '请上传正斜图片~',
					// 	trigger: ['change', 'blur']
					// },
					// 'imgLeftBias': {
					// 	validator: (rule, value, callback) => {
					// 		if (!this.form.imgs.imgLeftBias || this.form.imgs.imgLeftBias.length == 0) {
					// 			return false
					// 		} else {
					// 			return true
					// 		}
					// 	},
					// 	message: '请上传左斜图片~',
					// 	trigger: ['change', 'blur']
					// },
					// 'imgRightBias': {
					// 	validator: (rule, value, callback) => {
					// 		if (!this.form.imgs.imgRightBias || this.form.imgs.imgRightBias.length == 0) {
					// 			return false
					// 		} else {
					// 			return true
					// 		}
					// 	},
					// 	message: '请上传右斜图片~',
					// 	trigger: ['change', 'blur']
					// }
				},
				imgBg: 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/commodity/upload-bg.png',
				show: false,
				selectType: 'categoryCode',
				actions: [{
						name: '全部',
					},
					{
						name: '饮料',
					},
					{
						name: '槟榔',
					},
				],

				radio: '',
				switchVal: false,

				categoryOptions: [], //类目options
				unitOptions: [], //规格options
				title: null, //picker标题

				popShow: false,
				type: 'add', //add添加、edit编辑
				checkStatus: null,
				imgType: null,
			};
		},

		onLoad(o) {
			this.getCategory()
			this.getUnit()
			if (o.id) { //编辑
				this.type = 'edit'
				this.getDetail(o.id)
			} else { //新增暂存数据反显
				if (uni.getStorageSync('goodsAddModal')) {
					this.form = JSON.parse(uni.getStorageSync('goodsAddModal'))
				}
			}
		},

		watch: {
			form: {
				handler(newVal, oldVal) {
					console.log('暂存建模内容', newVal)
					if (this.type != 'edit') {
						uni.setStorageSync('goodsAddModal', JSON.stringify(newVal))
					}
				},
				deep: true
			}
		},

		methods: {
			//获取详情
			getDetail(id) {
				searchObj({
					id: id
				}).then(res => {
					let data = res.data;
					if (data) {
						this.checkStatus = data.status; //审核状态
						// 组装反显参数
						let newData = {
							id: data.id,
							goodsName: data.goodsName, //商品名称
							barcode: data.barcode, //条形码
							brandName: data.brandName, //品牌
							categoryCode: data.categoryCode, //分类
							categoryName: data.categoryName, //分类名称
							unitName: data.unitName, //规格
							cover: data.cover, //封面图片
							price: this.$xy.delMoney(data.price), //销售价
							priceCost: this.$xy.delMoney(data.priceCost), //成本价
							imgs: {
								imgBarcode: undefined, //69码图片
								imgPositive: undefined, //正面
								imgBack: undefined, //背面
								imgLeft: undefined, //左边
								imgRight: undefined, //右边
								imgTop: undefined, //顶部
								imgBottom: undefined, //底部图片
								imgPositiveBias: undefined, //正斜图片
								imgLeftBias: undefined, //左斜图片
								imgRightBias: undefined, //右斜图片
								others: []
							}
						}

						data.imgs.forEach(i => {
							if (i.type == 'barcode') {
								newData.imgs.imgBarcode = i.imgUrl
							}
							if (i.type == 'top') {
								newData.imgs.imgTop = i.imgUrl
							}
							if (i.type == 'back') {
								newData.imgs.imgBack = i.imgUrl
							}
							if (i.type == 'left') {
								newData.imgs.imgLeft = i.imgUrl
							}
							if (i.type == 'positive') {
								newData.imgs.imgPositive = i.imgUrl
							}
							if (i.type == 'right') {
								newData.imgs.imgRight = i.imgUrl
							}
							if (i.type == 'bottom') {
								newData.imgs.imgBottom = i.imgUrl
							}
							if (i.type == 'positive_bias') {
								newData.imgs.imgPositiveBias = i.imgUrl
							}
							if (i.type == 'left_bias') {
								newData.imgs.imgLeftBias = i.imgUrl
							}
							if (i.type == 'right_bias') {
								newData.imgs.imgRightBias = i.imgUrl
							}
							if (i.type == 'others') {
								newData.imgs.others.push(i.imgUrl)
							}

						})

						this.form = JSON.parse(JSON.stringify(newData))
					}
				})
			},

			fixImgSize(url) {
				return url + '?x-oss-process=image/resize,m_fill,h_300,w_300'
			},

			imgClick(type) {
				this.imgType = type;
				let obj = {
					selWidth: '600upx',
					selHeight: '632upx',
					inner: false,
					canRotate: false,
				}

				// switch (type) {
				// 	case 'cover': //240*228
				// 		obj = {
				// 			selWidth: '600upx',
				// 			selHeight: '632upx',
				// 			inner: false,
				// 			canRotate: false,
				// 		}
				// 		break;
				// 	default:
				// 		break;
				// }
				this.$refs.uploadImg.fChooseImg(obj)
				getApp().globalData.isOnShow = false
			},

			imgSuccess(url) {
				console.log('图片路径', url)
				switch (this.imgType) {
					case 'imgBarcode':
						this.form.imgs.imgBarcode = url
						break;
					case 'cover':
						this.form.cover = url
						break;
					case 'imgPositive':
						this.form.imgs.imgPositive = url
						break;
					case 'imgBack':
						this.form.imgs.imgBack = url
						break;
					case 'imgLeft':
						this.form.imgs.imgLeft = url
						break;
					case 'imgRight':
						this.form.imgs.imgRight = url
						break;
					case 'imgTop':
						this.form.imgs.imgTop = url
						break;
					case 'imgBottom':
						this.form.imgs.imgBottom = url
						break;
					case 'imgPositiveBias':
						this.form.imgs.imgPositiveBias = url
						break;
					case 'imgLeftBias':
						this.form.imgs.imgLeftBias = url
						break;
					case 'imgRightBias':
						this.form.imgs.imgRightBias = url
						break;
					default:
						break;
				}
				this.form = JSON.parse(JSON.stringify(this.form))
			},

			//扫码
			scan() {
				uni.scanCode({
					success: (res) => {
						this.form.barcode = res.result;
					}
				});
			},

			// 获取商品类目
			getCategory() {
				categoryTree({}).then(res => {
					this.categoryOptions = res.data;
				})
			},

			chooseCategoty() {
				this.popShow = true
			},

			popClose() {
				this.popShow = false
			},

			categorySubmit(item) {
				this.form.categoryCode = item.id;
				this.form.categoryName = item.name;
				this.popClose()
			},

			//获取规格
			getUnit() {
				listIdName().then(res => {
					let data = res.data;
					let newData = data.map(i => {
						return {
							id: i.id,
							name: i.unitName
						}
					})
					this.unitOptions = newData;
				})
			},

			// 显示下拉框
			showSelect(type) {
				this.selectType = type;
				if (type == 'categoryCode') {
					this.title = '请选择类目';
					this.actions = [this.categoryOptions];
				} else {
					this.title = '请选择规格';
					this.actions = [this.unitOptions];
				}
				this.show = true;
			},

			// 下拉选择
			actionSelect(e) {
				console.log(e)
				if (this.selectType === 'categoryCode') { // 分类
					this.form.categoryCode = e.value[0].name;
					this.$refs.form.validateField('categoryCode');
				} else { //规格
					this.form.unitName = e.value[0].name;
					this.$refs.form.validateField('unitName');
				}
				this.show = false
			},

			//表单提交
			submit() {
				console.log(this.form)
				this.$refs.form.validate().then(res => {
					let params = {
						barcode: this.form.barcode,
						brandName: this.form.brandName,
						categoryCode: this.form.categoryCode,
						goodsName: this.form.goodsName,
						type: this.form.type,
						unitName: this.form.unitName,
						cover: this.form.cover,
						price: this.$xy.delMoneyL(this.form.price), //销售价
						priceCost: this.$xy.delMoneyL(this.form.priceCost), //成本价
						imgs: {
							imgBarcode: this.form.imgs.imgBarcode,
							imgPositive: this.form.imgs.imgPositive, //正面
							imgBack: this.form.imgs.imgBack, //背面
							imgLeft: this.form.imgs.imgLeft, //左边
							imgRight: this.form.imgs.imgRight, //右边
							imgTop: this.form.imgs.imgTop, //顶部
							imgBottom: this.form.imgs.imgBottom, //底部图片
							imgPositiveBias: this.form.imgs.imgPositiveBias, //正斜图片
							imgLeftBias: this.form.imgs.imgLeftBias, //左斜图片
							imgRightBias: this.form.imgs.imgRightBias, //右斜图片
							others: this.form.imgs.others
						}
					}
					if (this.form.id) {
						params.id = this.form.id
						update(params).then(res => {
							this.$modal.msg('修改成功~')
							setTimeout(() => {
								this.$tab.navigateTo('/pages/commodity/auditList')
							}, 1000)
						}).catch(err => {

						})
					} else {
						save(params).then(res => {
							uni.setStorageSync('goodsAddModal', '') //清空临时存储
							this.$modal.msg('新建成功~')
							setTimeout(() => {
								this.$tab.navigateTo('/pages/commodity/auditList')
							}, 1000)
						}).catch(err => {

						})
					}
				}).catch(errors => {
					console.log(errors)
				})
			},

			// 预览图片
			preview(event) {
				console.log(event)
				let data = event.target.dataset.src
				let currentUrl = null
				if (typeof data == 'object') {
					currentUrl = data
				} else {
					currentUrl = [data]
				}
				uni.previewImage({
					urls: currentUrl // 需要预览的图片http链接列表
				})
				getApp().globalData.isOnShow = false
			}
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},
	};
</script>

<style scoped lang="scss">
	.container {
		height: 100vh;
		background-color: #fff;

		.content {
			padding: 24rpx 36rpx 360rpx;
			background-color: #fff;

			.fail-msg {
				color: red;
				font-size: 28rpx;
				line-height: 32rpx;
				margin-top: 12rpx;
				margin-bottom: 24rpx;
				width: 726rpx;
				margin-left: -24rpx;
				background-color: skyblue;
				padding: 24rpx 12rpx;
				box-sizing: border-box;
				border-radius: 12rpx;
			}

			.example-img {
				height: 212rpx;
				padding: 24rpx 0 12rpx;
				display: flex;
				flex-flow: row nowrap;
				justify-content: flex-start;

				>image {
					width: 160rpx;
					margin-right: 66rpx;
					border-radius: 4rpx;
				}
			}

			.img-tips {
				color: red;
				margin-top: 30rpx;
				line-height: 44rpx;
			}

			.img-upload {
				height: 212rpx;
				padding: 24rpx 0 12rpx;
			}

			.bar-code {
				position: relative;

				.scan-icon {
					position: absolute;
					right: 0;
					top: 50%;
					transform: translateY(-50%);
					z-index: 9999;
				}
			}

			.form-item {
				position: relative;

				.form-item-img {
					width: 200rpx;
				}

				.prev {
					font-size: 24rpx;
					color: #2C6FF3;
					text-decoration: underline;
					font-style: italic;
					padding: 24rpx;
					position: absolute;
					right: 100rpx;
					top: 42%;

					&.prev-else {
						right: -30rpx;
					}
				}

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
			z-index: 999;

			.cu-btn {
				width: 100%;
				background-color: #2C6FF3;
				color: #fff;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>