<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="积分兑换商品设置"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" :rules="rules" ref="form" errorType="toast" labelWidth="120">
			<!-- 	<view class="section-wrap explain">
					<view>设置说明</view>
					<view>1.说明商品兑换商品兑换积分兑换；</view>
					<view>2.说明积分兑换商品设置.</view>
				</view> -->

				<view class="section-wrap" style="padding:0 13rpx 24rpx;">
					<view class="spe-list">
						<view class="spe-head flex justify-between align-center" style="line-height: 104rpx;">
							<view class="title">特价设置</view>
							<view>
								<xbutton width="134rpx" round="14rpx" @click="chooseCom">增加商品</xbutton>
							</view>
						</view>
						<view class="spe-body" v-if="form.goods_list&&form.goods_list.length>0">
							<block v-for="(item,index) in form.goods_list" :key="item.goods_id">
								<view class="com-item flex align-center">
									<view class="com-img">
										<u--image radius="4" width="124rpx" height="150rpx" :src="item.goods_cover"
											mode="aspectFit" :lazy-lord="true"></u--image>
									</view>
									<view class="com-content">
										<view class="com-name">
											{{item.goods_name}}
										</view>
										<view class="com-old-price">
											原价：¥{{$xy.delMoney(item.goods_price)}}
										</view>
										<view class="com-num-spe">
											<view class="com-num flex align-center">
												<view>所需积分：</view>
												<view class="com-input flex align-center justify-center">
													<u--input v-model="item.need_points" placeholder="请输入"
														border="none"></u--input>
												</view>
												<view>积分</view>
											</view>
											<view class="com-spe flex align-center" style="margin-top: 20rpx;">
												<view>所需金额：</view>
												<view class="com-input flex align-center justify-center">
													<u--input v-model="item.need_money"
														@blur="priceSet($event,item.goods_price)" type="digit"
														placeholder="请输入" border="none"></u--input>
												</view>
												<view>元</view>
											</view>
										</view>
									</view>

									<view class="close-btn" @click="delCom(index)">
										<u-icon name="close" color="#333" size="16"></u-icon>
									</view>
								</view>
							</block>
						</view>
						<view class="spe-body" v-else style="text-align: center;line-height: 100rpx;">
							请选择商品！
						</view>
					</view>
				</view>
			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				保存
			</xbutton>
		</view>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		addGoodsList,
		goodsList,
		delGoods
	} from "@/api/integral.js"
	//验证规则
	let rulesObj = {
		type: 'string',
		required: true,
		message: '必填项',
		trigger: ['change']
	}

	export default {
		data() {
			return {
				form: {
					goods_list: [],
				},

				rules: {
					'unitName': {
						type: 'string',
						required: true,
						message: '请选择商品规格~',
						trigger: ['change']
					},
				},
			}
		},

		onLoad(o) {
			this.getDetail()

			uni.$on('updateCom', res => {
				if (res && res.length > 0) {
					for (var i = 0; i < this.form.goods_list.length; i++) {
						let item = this.form.goods_list[i]
						if (res.findIndex(j => j.goodsId == item.goods_id) == -1) {
							this.form.goods_list
							this.form.goods_list.splice(i, 1)
						}
					}
					for (var i = 0; i < res.length; i++) {
						let item = res[i]
						if (this.form.goods_list.findIndex(i => i.goods_id == item.goodsId) == -1) {
							this.form.goods_list.push({
								goods_id: item.goodsId,
								goods_name: item.goodsName,
								goods_price: item.price,
								goods_cover: item.cover,
								need_points: '',
								need_money: ''
							})
						}
					}
				}
			})
		},

		methods: {
			getDetail() {
				goodsList({}).then(res => {
					let data = res.data
					let goods_list = []
					if (data?.length > 0) {
						for (let key in data) {
							let item = data[key]
							goods_list.push({
								id: item.id,
								goods_id: item.goods_id,
								goods_name: item.goods_name,
								goods_price: item.goods_price,
								goods_cover: item.goods_cover,
								need_points: item.need_points,
								need_money: this.$xy.delMoney(item.need_money)
							})
						}
					}
					this.form = {
						goods_list: goods_list
					}
				})
			},

			chooseCom() {
				let data = this.form.goods_list.map(i => ({
					goodsId: i.goods_id,
					goodsName: i.goods_name,
					cover: i.cover
				}))
				let goodsList = JSON.stringify(data)
				uni.setStorageSync('integralSto', goodsList)
				this.$tab.navigateTo('/pages/integral/chooseGoods')
			},

			delCom(index) {
				this.$modal.oldConfirm('确认删除该商品？').then(res => {
					if (this.form.goods_list[index].id) { //已有商品删除
						delGoods({
							id: this.form.goods_list[index].id
						}).then(res => {
							this.form.goods_list.splice(index, 1)
							this.$forceUpdate()
							this.$modal.msg('删除成功~')
						}).catch(err => {

						})
					} else { //新增未保存删除
						this.form.goods_list.splice(index, 1)
						this.$forceUpdate()
						this.$modal.msg('删除成功~')
					}
				}).catch(err => {

				})
			},

			priceSet(value, oldPrice) {
				if (value != null) {
					if (value <= 0 || value * 100 >= oldPrice) {
						this.$modal.msg('特价价格需低于原价，特价不能为0')
					}
				}
			},

			//表单提交
			submit() {
				console.log(this.form)
				if (this.checkParams(this.form)) return
				this.$refs.form.validate().then(res => {
					let goods_list = this.form.goods_list.map(item => ({
						id: item.id ?? '',
						goods_id: item.goods_id,
						goods_name: item.goods_name,
						goods_price: item.goods_price,
						goods_cover: item.goods_cover,
						need_points: item.need_points,
						need_money: this.$xy.delMoneyL(item.need_money)
					}))
					addGoodsList({
						goods_list: goods_list
					}).then(res => {
						this.$modal.msg('成功~')
						setTimeout(()=>{
							uni.navigateBack()
						},500)
					}).catch(err => {

					})
				}).catch(errors => {
					console.log(errors)
				})
			},

			checkParams(params) {
				let bflag = false
				for (let item of params.goods_list) {
					if (item.need_money != 0) {
						if (item.need_money * 100 > item.goods_price) {
							this.$modal.msg('所需金额需低于原价')
							bflag = true
							break
						}
					}
				}
				return bflag
			},
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx 30rpx;
				background-color: #fff;
				margin-bottom: 24rpx;
			}

			.explain {
				padding: 30rpx 43rpx;

				view {
					font-size: 24rpx;
					color: #333333;
					line-height: 40rpx;

					&:nth-child(1) {
						font-size: 28rpx;
						color: #333333;
						margin-bottom: 10rpx;
					}
				}
			}

			.input-text {
				text-align: right !important;
			}

			.tab-wrap {
				padding: 12rpx 0;
			}

			.choose-list {
				width: 418rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.mj-content {

				.edit {
					color: #2C6FF3;
					text-decoration: underline;

					&.del {
						color: red;
					}
				}

				.mj-head {
					line-height: 80rpx;
				}

				.mj-body {
					.mj-item {
						+.mj-item {
							margin-top: 24rpx;
						}

						.left-input {
							border: 1px solid #d6d7d9;
							border-radius: 8rpx;
							padding: 0 13rpx;
							line-height: 68rpx;
							width: 230rpx;

							>text {
								margin-right: 12rpx;
							}

							&.zk-left-input {
								width: 300rpx;
							}
						}

						.right-switch {
							>text {
								margin-right: 12rpx;
							}
						}

					}
				}
			}

			.form-item {
				position: relative;

				.tips {
					position: absolute;
					right: 24rpx;
					top: 24rpx;
					color: red;
					font-size: 24rpx;
				}
			}
		}

		.spe-list {
			.spe-head {
				padding: 0 17rpx;

				.title {
					font-size: 32rpx;
					font-weight: 800;
					color: #333333;
				}
			}

			.spe-body {
				.com-item {
					padding: 25rpx 25rpx 30rpx;
					box-shadow: 0 6px 18px #1d293924;
					position: relative;

					+.com-item {
						margin-top: 20rpx;
					}

					background: #FFFFFF;
					border-radius: 14rpx;
					box-shadow: #333333;

					.com-img {
						margin-right: 26rpx;
					}

					.com-content {
						.com-name {
							font-size: 28rpx;
							font-weight: 800;
							color: #333333;
							width: 460rpx;
						}

						.com-old-price {
							font-size: 26rpx;
							color: #555555;
							margin-top: 30rpx;
						}

						.com-num-spe {
							font-size: 26rpx;
							margin-top: 20rpx;

							.com-num,
							.com-spe {
								.com-input {
									width: 139rpx;
									height: 68rpx;
									border: 1px solid #D8D8D8;
									border-radius: 8rpx;
									padding: 0 12rpx;
									margin: 0 20rpx;
								}
							}
						}
					}

					.close-btn {
						position: absolute;
						right: 0;
						top: 0;
						padding: 24rpx;
					}
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 99;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.popup-content {
			padding: 36rpx 24rpx;
			display: flex;
			flex-flow: row nowrap;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>