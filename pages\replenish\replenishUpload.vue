<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="图片上传"></u-navbar>
		<view class="content">
			<ximgUpload :capture="['camera']" v-model="imgs" />
			
			<!-- <view class="tips">最多上传四张照片！</view> -->
			<view class="btn safe-bottom">
				<xbutton size="large" round="82rpx" @click="upload">上传</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		imgSave
	} from "@/api/replenishment/replenishment.js"
	export default {
		data() {
			return {
				imgs: [],
				deviceId: null
			}
		},

		onLoad(o) {
			this.deviceId = o.deviceId
		},

		methods: {
			//上传
			upload() {
				if (this.imgs.length == 0) {
					this.$modal.msg('请选择或拍摄照片~')
					return
				}
				imgSave({
					imgsUrlList: this.imgs,
					deviceId: this.deviceId,
					mercId: 0
				}).then(res => {
					this.$modal.msg('上传成功~')
					setTimeout(() => {
						uni.$emit('refreshData')
						this.$tab.navigateBack()
					}, 1000)
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		overflow: hidden;

		.content {
			padding: 24rpx;
			
			.tips{
				font-size: 24rpx;
				line-height: 80rpx;
				color: red;
			}

			.btn {
				width: 100%;
				position: fixed;
				left: 0;
				padding: 0 24rpx;
				bottom: $iphone-safe-bottom;
			}
		}
	}
</style>