import request from '@/utils/request'
/**
 * @param {Object} data
 * 添加套餐
 */
export function save(data) {
	return request({
		url: '/merc/miniMercPlusMeal/save',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 更新套餐
 */
export function update(data) {
	return request({
		url: '/merc/miniMercPlusMeal/update',
		method: 'post',
		data: data
	})
}



/**
 * @param {Object} data
 * 套餐列表
 */
export function page(data) {
	return request({
		url: '/merc/miniMercPlusMeal/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 激活首页信息
 */
export function objs(data) {
	return request({
		url: '/merc/MiniMercPlusConfig/obj',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 更新图片
 */
export function updateImgs(data) {
	return request({
		url: '/merc/MiniMercPlusConfig/updateImg',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 套餐内容
 */
export function vipMsg(data) {
	return request({
		url: '/merc/miniMercPlusMeal/obj',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 会员分页查询
 */
export function viperPage(data) {
	return request({
		url: '/merc/MiniMercPlusMember/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 会员统计
 */
export function countTotalPage(data) {
	return request({
		url: '/merc/MiniMercPlusMember/countTotalPage',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 非会员分页查询
 */
export function userPage(data) {
	return request({
		url: '/merc/mini/member/pageJoin',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 更新设备
 */
export function updateDevice(data) {
	return request({
		url: '/merc/MiniMercPlusConfig/updateDevice',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 套餐购买记录
 */
export function buyPlusRec(data) {
	return request({
		url: '/order/orders-plus-member/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 套餐列表
 */
export function listByMercId(data) {
	return request({
		url: '/merc/miniMercPlusMeal/listByMercId',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 会员套餐购买记录
 */
export function orderPlusMember(data) {
	return request({
		url: '/order/order-plus-member-mini/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 储值套餐统计
 */
export function balanceTotal(data) {
	return request({
		url: '/merc/mini/member/balanceTotal',
		method: 'post',
		data: data
	})
}


