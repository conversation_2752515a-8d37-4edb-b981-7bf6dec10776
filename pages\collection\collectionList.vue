<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff"
			:placeholder="true" title="已采集商品">
		</u-navbar>

		<view class="content">
			<!-- 商品列表 -->
			<view class="goods-list" v-if="collectedGoods.length > 0">
				<view class="goods-item" v-for="(item, index) in collectedGoods" :key="index">
					<view class="goods-image-container">
						<image class="goods-image" :src="item.image || '/static/images/default-goods.png'"
							mode="aspectFill"></image>
					</view>
					<view class="goods-info">
						<text class="goods-name">{{item.productInfo}}</text>
						<!-- 右上角状态标签 -->
						<view class="status-tag">
							<u-tag :text="getStatusText(item.status)" plain size="mini"
								:type="getStatusClass(item.status)"></u-tag>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-else>
				<u-empty mode="data" text="暂无采集商品"></u-empty>
			</view>
		</view>

		<!-- 固定在底部的操作按钮 -->
		<view class="bottom-bar">
			<button class="btn-primary" @click="backHome">返回首页</button>
		</view>
	</view>
</template>

<script>
	import {
		uploadInfo,
		deleteBatchNo
	} from "@/api/collection.js";
	export default {
		data() {
			return {
				collectedGoods: [], // 已采集的商品列表
				loading: false,
			};
		},

		async onLoad(o) {
			await this.loadCollectedGoods();

			if (this.collectedGoods.length > 0) {
				//全部上传完毕，则删除批次号
				if (this.collectedGoods.every(item => item.status === 'success')) {
					this.deleteBatchNoFun()
				}
			}
		},

		methods: {
			/**
			 * 加载已采集的商品列表
			 */
			loadCollectedGoods() {
				return new Promise((resolve, reject) => {
					this.loading = true;
					uploadInfo({}).then(res => {
						if (res.code === 200) {
							this.collectedGoods = res.data || [];
						}
						resolve(res);
					}).catch(err => {
						console.error('获取商品列表失败', err);
						reject(err);
					}).finally(() => {
						this.loading = false;
					});
				})
			},

			//清除采集批次标记
			deleteBatchNoFun() {
				return new Promise((resolve, reject) => {
					deleteBatchNo({}).then(res => {
						reject(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			/**
			 * 返回首页
			 */
			backHome() {
				this.$tab.navigateTo('/pages/globalPages/home');
			},
			
			// 获取状态对应的样式类
			getStatusClass(status) {
				if (status === "success") return "success";
				if (status === "fail") return "error";
				if (status === "wait") return "primary";
				return "primary";
			},
			
			// 获取状态对应的文本
			getStatusText(status) {
				if (status === "success") return "已通过";
				if (status === "fail") return "失败";
				if (status === "wait") return "上传中";
				return '上传中';
			},
		},
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #fff;
		position: relative;
		padding: 0 20rpx;

		.content {
			flex: 1;
			padding: 20rpx 0;
			padding-bottom: calc(200rpx + env(safe-area-inset-bottom));

			// 商品列表
			.goods-list {
				.goods-item {
					display: flex;
					padding: 20rpx;
					margin-bottom: 20rpx;
					background-color: #fff;
					border-radius: 12rpx;
					box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
					position: relative;

					.goods-image-container {
						width: 160rpx;
						height: 160rpx;
						margin-right: 20rpx;
						flex-shrink: 0;

						.goods-image {
							width: 100%;
							height: 100%;
							border-radius: 8rpx;
							background-color: #f5f5f5;
						}
					}

					.goods-info {
						flex: 1;
						display: flex;
						flex-direction: column;
						justify-content: center;
						position: relative;

						.goods-name {
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
							margin-bottom: 10rpx;
							padding-right: 80rpx; // 为状态标签留出空间
						}

						// 右上角状态标签
						.status-tag {
							position: absolute;
							top: -10rpx;
							right: 0;
						}
					}
				}
			}

			// 空状态
			.empty-state {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 100rpx 0;
			}
		}

		.bottom-bar {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			background-color: #ffffff;
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.btn-primary {
				height: 88rpx;
				background-color: #4177fb;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0;
			}
		}
	}
</style>