<template>
  <view class="container">
    <u-navbar
      leftIconColor="#fff"
      titleStyle="color:#fff;fontSize:36rpx;"
      :autoBack="true"
      bgColor="#2C6FF3"
      :placeholder="true"
      title="优惠券管理"></u-navbar>
    <view class="content">
      <!-- 	<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view> -->
      <!-- 
			<view class="tab-list flex flex-wrap flex-start">
				<block v-for="(item,index) in typeList" :key="item.name">
					<view :class="[type==item.value?'tab-item tab-show':'tab-item']"
						@click="menuTab(item.value)">
						{{item.name}}
					</view>
				</block>
			</view> -->

      <view class="flex justify-between" style="padding: 20rpx 20rpx 0 0">
        <xhelpPopup guideId="YH0001" />

        <view class="flex align-center justify-center" @click="screen">
          <view style="font-size: 28rpx; color: #333333">筛选</view>
          <image
            src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/screen.png"
            style="width: 32rpx; height: 32rpx; margin: 8rpx 0 0 12rpx"
            mode="widthFix"></image>
        </view>
      </view>

      <scroll-view
        class="scrollview"
        :scroll-with-animation="true"
        scroll-y
        lower-threshold="100"
        @scrolltolower="scrolltolower"
        :style="{ height: fullHeight }">
        <view v-if="list.length > 0" class="content">
          <block v-for="(item, index) in list" :key="item.id">
            <view class="equipment-container">
              <view class="item-head flex justify-between align-center">
                <view class="name">{{ item.name }}</view>
                <view class="status" style="color: #2c6ff3">{{
                  item.type == "1" ? "折扣券" : "满减券"
                }}</view>
              </view>
              <view class="item-body">
                <view class="att-list">
                  <text class="att-name">优惠：</text>
                  <text class="att-val" v-if="item.type == '1'"
                    >{{ item.discount }}折</text
                  ><text class="att-val" v-else-if="item.type == '2'"
                    >满{{ $xy.delMoney(item.limit) }}减{{
                      $xy.delMoney(item.price)
                    }}</text
                  >
                </view>
                <!-- <view class="att-list">
									<text class="att-name">使用门槛：</text><text
										class="att-val">￥{{$xy.delMoney(item.useMinPrice)}}</text>
								</view> -->
                <view class="att-list" v-if="item.validType == '2'">
                  <text class="att-name">使用期限：</text
                  ><text class="att-val" v-if="item.validStartDate">{{
                    item.validStartDate + "~" + item.validEndDate
                  }}</text
                  ><text v-else>无</text>
                </view>

                <view class="att-list" v-else>
                  <text class="att-name">有效天数：</text
                  ><text class="att-val">{{ item.validDay }}天</text>
                </view>

                <view class="att-list">
                  <text class="att-name">发放类型：</text
                  ><text class="att-val">{{ item.sendType || "-" }}</text>
                </view>

                <view class="att-list">
                  <text class="att-name">设备：</text
                  ><text class="att-val">{{
                    item.deviceIds ? "部分设备" : "全部设备"
                  }}</text>
                </view>
                <view class="att-list">
                  <text class="att-name">商品：</text
                  ><text class="att-val">{{
                    item.goodsIds ? "部分商品" : "全部商品"
                  }}</text>
                </view>
                <view class="flex justify-between use-status">
                  <view>
                    <text class="att-name">未使用：</text
                    ><text class="att-val" style="color: red">{{
                      item.numNotUsed
                    }}</text>
                  </view>
                  <view>
                    <text class="att-name">已使用：</text
                    ><text class="att-val" style="color: green">{{
                      item.numUsed
                    }}</text>
                  </view>
                  <view>
                    <text class="att-name">已取消：</text
                    ><text class="att-val" style="color: #555500">{{
                      item.numCancel
                    }}</text>
                  </view>
                </view>
              </view>

              <view class="item-foot flex justify-end align-center">
                <view class="foot-right flex">
                  <!-- <view style="margin-right: 12rpx;">
										<xbutton width="124rpx" round="14rpx" @click="editBtn(item)">赠送会员</xbutton>
									</view> -->
                  <view style="margin-right: 12rpx">
                    <xbutton
                      width="124rpx"
                      round="14rpx"
                      @click="handelSendCoupon(item)"
                      >赠券
                    </xbutton>
                  </view>
                  <view style="margin-right: 12rpx">
                    <xbutton
                      width="124rpx"
                      round="14rpx"
                      v-if="item.sendType == '扫码领取'"
                      @click="showQrcode(item)"
                      >二维码</xbutton
                    >
                  </view>
                  <view>
                    <xbutton width="124rpx" round="14rpx" @click="editBtn(item)"
                      >编辑</xbutton
                    >
                  </view>
                </view>
              </view>
            </view>
          </block>
          <view class="load-more" style="padding: 24rpx">
            <u-loadmore v-if="list.length > 0" :status="loadmoreStatus" />
          </view>
        </view>
        <view v-else class="empty">
          <u-empty mode="data" text="数据为空"></u-empty>
        </view>
      </scroll-view>

      <view class="btn">
        <xbutton size="large" @click="add"> 添加优惠券 </xbutton>
      </view>
    </view>

    <xpopup
      :show="screenShow"
      @close="screenShow = false"
      @confirm="sure"
      :showBtn="true"
      clear="清空"
      @clearClick="clear"
      title="筛选"
      zIndex="10000">
      <view class="popup-container">
        <view class="margin-top-sm">
          <u--input
            placeholder="请输入优惠券名称"
            v-model="name"
            border="surround"></u--input>
        </view>
        <view class="margin-top-sm" @click="actionClick('优惠券类型')">
          <u--input
            clearable
            readonly
            suffixIcon="arrow-down"
            v-model="couponTypeName"
            border="surround"
            suffixIconStyle="color: #909399"
            placeholder="请选择优惠券类型">
          </u--input>
        </view>
        <view class="margin-top-sm" @click="actionClick('发放类型')">
          <u--input
            clearable
            readonly
            suffixIcon="arrow-down"
            v-model="sendTypeName"
            border="surround"
            suffixIconStyle="color: #909399"
            placeholder="请选择发放类型">
          </u--input>
        </view>
      </view>
    </xpopup>

    <!-- 赠券弹框 -->
    <xpopup
      :show="sendCouponPopShow"
      @close="sendCouponPopShow = false"
      @confirm="sendCouponPopSubmit"
      :showBtn="true"
      clear="清空"
      @clearClick="sendCouponPopClear"
      title="赠券"
      zIndex="10000">
      <view class="popup-container">
        <view class="margin-bottom">
          <u-radio-group
            v-model="sendCouponType"
            placement="row"
            @change="sendCouponTypeChange">
            <u-radio
              :customStyle="{ marginRight: '16rpx' }"
              v-for="(item, index) in sendCouponTypeList"
              :key="index"
              :name="item.value"
              :label="item.name"></u-radio>
          </u-radio-group>
        </view>

        <view class="margin-top-sm" v-if="sendCouponType === 'phone'">
          <u--input
            placeholder="请输入手机号"
            v-model="sendCouponPhone"
            border="surround"
            maxlength="11"
            type="number"></u--input>
        </view>

        <view class="margin-top-sm" v-if="sendCouponType === 'memberId'">
          <u--input
            placeholder="请输入会员ID"
            v-model="sendCouponMemberId"
            border="surround"
            type="text"></u--input>
        </view>
        <!-- 				
				<view class='margin-top-sm'>
					<u--input placeholder="请输入赠送数量" v-model="sendCouponNum" border="surround" type="number"></u--input>
				</view> -->
      </view>
    </xpopup>

    <view class="canvas-box">
      <canvas style="width: 185px; height: 185px" canvas-id="myQrcode"></canvas>
    </view>

    <!-- 二维码弹框 -->
    <xpopup
      :show="qrcodeShow"
      round="12"
      @close="qrcodeClose"
      :safeAreaInsetBottom="false"
      :showBtn="false"
      mode="center">
      <view class="qrcode-content">
        <view class="qrcode">
          <image
            style="width: 185px; height: 185px"
            :src="qrImg"
            mode="widthFix"
            :show-menu-by-longpress="true"></image>
        </view>
        <view class="tips flex flex-start">
          <view class="tips-before">提示：</view>
          <view class="tips-content"
            >请长按图片或截屏转发给他人扫码领取优惠券！</view
          >
        </view>
      </view>
    </xpopup>

    <u-action-sheet
      :show="actionsShow"
      :actions="actions"
      :title="actionTitle"
      @close="actionsShow = false"
      @select="actionsSelect($event)"></u-action-sheet>
  </view>
</template>

<script>
import { couponPage, mercSendByMemberId, mercSendByTel } from "@/api/coupon.js";
import getDict from "@/utils/getDict.js";
import drawQrcode from "@/static/js/weapp.qrcode.esm.js";
import config from "../../config";

export default {
  data() {
    return {
      list: [],
      loadmoreStatus: "loadmore",
      page: 1, //当前分页
      size: 10, //分页数据条数
      fullHeight: 0,

      tabList: [
        {
          name: "全部",
        },
        {
          name: "折扣",
        },
        {
          name: "满减",
        },
      ],
      current: 0,
      status: "进行中",
      type: "cx",
      typeList: [
        {
          name: "促销",
          value: "cx",
        },
        {
          name: "特价",
          value: "tj",
        },
      ],
      screenShow: false,
      actionsShow: false,
      actionTitle: null,
      sendAction: [],
      couponAction: [],
      actions: [],
      couponType: null,
      couponTypeName: null,
      sendType: null,
      sendTypeName: null,
      name: null,

      couponId: null,
      sendCouponPopShow: false,
      sendCouponType: "phone", // 默认使用手机号赠券
      sendCouponTypeList: [
        {
          name: "手机号",
          value: "phone",
        },
        {
          name: "会员ID",
          value: "memberId",
        },
      ],
      sendCouponPhone: "", // 赠券手机号
      sendCouponMemberId: "", // 赠券会员ID
      sendCouponNum: "1", // 赠券数量，默认为1
      qrcodeShow: false,
      qrcodeUrl: "",
      qrImg: null,
    };
  },
  async onLoad(o) {
    let _this = this;
    _this.fullHeight = await _this.$xy.scrollHeight(_this, ".scrollview", 0);

    await getDict(["coupon_send_type", "coupon_type"]).then((res) => {
      let sendAction = [];
      let couponAction = [];
      for (var i = 0; i < res.length; i++) {
        let item = res[i];
        let obj = {
          code: item.code,
          name: item.msg,
        };
        if (item.paterCode == "coupon_send_type") {
          sendAction.push(obj);
        }
        if (item.paterCode == "coupon_type") {
          couponAction.push(obj);
        }
      }
      this.sendAction = [
        {
          code: "",
          name: "全部",
        },
        ...sendAction,
      ];
      this.couponAction = [
        {
          code: "",
          name: "全部",
        },
        ...couponAction,
      ];
    });
    this.search();

    uni.$on("refresh", (res) => {
      this.search();
    });
  },

  onShow() {
    uni.setStorageSync("coupon", ""); //清空临时存储
  },

  methods: {
    screen() {
      this.screenShow = true;
    },

    actionClick(type) {
      this.actionsShow = true;
      this.actionTitle = type;
      if (type == "优惠券类型") {
        this.actions = this.couponAction;
      } else if (type == "发放类型") {
        this.actions = this.sendAction;
      }
    },

    actionsSelect(e) {
      if (this.actionTitle == "优惠券类型") {
        this.couponTypeName = e.name;
        this.couponType = e.code;
      } else if (this.actionTitle == "发放类型") {
        this.sendTypeName = e.name;
        this.sendType = e.code;
      }
    },

    sure() {
      this.screenShow = false;
      this.search();
    },

    clear() {
      this.couponTypeName = null;
      this.couponType = null;
      this.sendTypeName = null;
      this.sendType = null;
      this.name = null;
    },

    menuTab(type) {
      this.type = type;
      this.search();
    },

    switchBtn(item) {
      let _this = this;
      let status = item.enable_status == "启用" ? "禁用" : "启用";
      this.$modal.oldConfirm(`是否确定${status}活动`).then((res) => {
        proStatus({
          id: item.id,
          enable_status: status,
        }).then((res) => {
          _this.$modal.msg("成功~");
          item.enable_status = status;
          _this.$forceUpdate();
        });
      });
    },

    /**
     * @param {Object} item
     * 赠券
     */
    handelSendCoupon(item) {
      this.couponId = item.id;
      this.sendCouponPopShow = true;
      // 重置赠券表单
      // this.sendCouponType = 'phone';
      // this.sendCouponPhone = '';
      // this.sendCouponMemberId = '';
      // this.sendCouponNum = '1';
    },

    /**
     * 赠券类型切换
     */
    sendCouponTypeChange(e) {
      this.sendCouponType = e;
      // 切换类型时清空输入框
      if (e === "phone") {
        this.sendCouponMemberId = "";
      } else {
        this.sendCouponPhone = "";
      }
    },

    /**
     * 赠券表单提交
     */
    async sendCouponPopSubmit() {
      if (this.sendCouponType === "phone" && !this.sendCouponPhone) {
        this.$modal.msg("请输入手机号");
        return;
      }

      if (this.sendCouponType === "memberId" && !this.sendCouponMemberId) {
        this.$modal.msg("请输入会员ID");
        return;
      }

      // if(!this.sendCouponNum || Number(this.sendCouponNum) <= 0) {
      // 	this.$modal.msg('请输入正确的赠送数量');
      // 	return;
      // }

      // 构建请求参数
      let params = {
        couponIdList: [this.couponId],
        // num: Number(this.sendCouponNum)
      };

      let sendPromise;
      if (this.sendCouponType === "phone") {
        params = Object.assign(params, {
          tel: this.sendCouponPhone,
        });
        sendPromise = mercSendByTel(params);
      } else {
        params = Object.assign(params, {
          memberId: this.sendCouponMemberId,
        });
        sendPromise = mercSendByMemberId(params);
      }

      let sendCouponRes = await sendPromise;
      this.$modal.msg(sendCouponRes.msg);
      this.sendCouponPopShow = false;
    },

    /**
     * 清空赠券表单
     */
    sendCouponPopClear() {
      this.sendCouponPhone = "";
      this.sendCouponMemberId = "";
      this.sendCouponNum = "1";
    },

    editBtn(item) {
      uni.setStorageSync("coupon", ""); //清空临时存储
      this.$tab.navigateTo(`/pages/coupon/add?id=${item.id}`);
    },

    tabClick(e) {
      console.log(e);
      this.current = e.index;
      this.status = e.name;
      this.reset();
      this.getpage();
    },

    getpage() {
      couponPage({
        page: {
          current: this.page,
          size: this.size,
        },
        type: this.current == 0 ? "" : this.current,
        name: this.name,
        type: this.couponType,
        sendType: this.sendType,
      }).then((res) => {
        let data = res.data.records;
        if (data.length < 10) {
          this.loadmoreStatus = "nomore";
        } else {
          this.loadmoreStatus = "loadmore";
        }
        if (data && data.length > 0) {
          for (let item of data) {
            item.scheme = JSON.parse(item.scheme);
            if (item.type == "1") {
              //折扣
              item.discount = Number(item.scheme.scalar) * 10;
            } else if (item.type == "2") {
              //满减
              item.limit = item.scheme[0]?.limit;
              item.price = item.scheme[0]?.price;
              item.is_loop = item.scheme[0]?.is_loop;
            }
            item.sendType = this.sendAction.find(
              (i) => i.code == item.sendType
            )?.name;
          }
        }
        this.list = this.list.concat(data);
      });
    },

    // 搜索
    search() {
      this.reset();
      this.getpage();
    },

    // 重置数据
    reset() {
      this.loadmoreStatus == "loadmore";
      this.page = 1;
      this.size = 10;
      this.list = [];
    },

    // 触底加载
    scrolltolower() {
      if (this.loadmoreStatus == "nomore") return;
      this.page++;
      this.getpage();
    },

    add() {
      this.$tab.navigateTo("/pages/coupon/add");
    },

    del(item) {
      this.$modal
        .oldConfirm(`是否确定删除【${item.name}】优惠券`)
        .then((res) => {
          proStatus({
            id: item.id,
            enable_status: status,
          }).then((res) => {
            _this.$modal.msg("成功~");
            item.enable_status = status;
            _this.$forceUpdate();
          });
        });
    },

    showQrcode(item) {
      this.qrcodeShow = true;
      this.drawQrcode(item.id);
    },

    qrcodeClose() {
      this.qrcodeShow = false;
    },

    //绘制二维码
    drawQrcode(id) {
      let url = `${config.baseUrl}?couponId=${id}&type=3`;
      console.log(url);
      drawQrcode({
        width: 185,
        height: 185,
        canvasId: "myQrcode",
        text: url,
      });

      // 绘制结束,生成本地图片链接
      setTimeout(() => {
        this.saveImg();
      }, 1000);
    },

    //二维码保存转发
    saveImg() {
      console.log("转换图片");
      uni.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: 185,
        height: 185,
        destWidth: 185, //画布宽高*dpr 以iphone6为准
        destHeight: 185,
        canvasId: "myQrcode",
        success: (res) => {
          //console.log(res.tempFilePath) //生成的临时图片路径
          this.qrImg = res.tempFilePath;
          console.log("临时地址======" + this.qrImg);
        },
      });
    },
  },

  onUnload() {
    uni.$off("refresh");
  },
};
</script>

<style scoped lang="scss">
.container {
  font-size: 28rpx;
  color: #333;

  .content {
    overflow: hidden;

    .menu-item {
      text-align: center;
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 24rpx;

      > image {
        width: 57rpx;
        height: 57rpx;
      }

      > view {
        color: #333;
        font-size: 24rpx;
        line-height: 24rpx;
        font-size: 28rpx;
        line-height: 28rpx;
        margin-top: 16rpx;
      }
    }

    .empty {
      padding-top: 40%;
    }

    .martop {
      margin-top: 20rpx;
    }

    .tab-wrap {
      padding: 12rpx 0;
    }

    .tab-list {
      width: 100%;
      background-color: #fff;
      padding: 24rpx 26rpx 0;

      .tab-item {
        padding: 0 20rpx;
        height: 62rpx;
        background: #f7f7f7;
        border-radius: 10rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #777777;
        line-height: 62rpx;
        margin-bottom: 14rpx;
        text-align: center;
        margin-right: 12rpx;

        &.tab-show {
          background: #f4f8ff;
          color: #2c6ff3;
        }
      }
    }

    .marleft {
      margin-left: 10rpx;
    }

    .scrollview {
      .content {
        padding-bottom: 90rpx;
        overflow: hidden;
      }
    }

    .equipment-container {
      margin: 13rpx 13rpx 0;
      padding: 24rpx 30rpx 40rpx;
      border-radius: 14rpx;
      background-color: #fff;
      box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

      .item-head {
        padding-bottom: 10rpx;

        .name {
          font-size: 32rpx;
          font-weight: 800;
          color: #333333;
        }

        .status {
        }
      }

      .item-body {
        background: #f6f7fa;
        border-radius: 14rpx;
        padding: 30rpx;
        font-size: 28rpx;
        color: #333333;
        margin-top: 20rpx;

        .att-list {
          line-height: 60rpx;

          .att-name {
            display: inline-block;
            width: 180rpx;
          }
        }

        .use-status {
          line-height: 60rpx;
        }
      }

      .item-foot {
        margin-top: 18rpx;

        .foot-left {
          font-size: 22rpx;
          color: #555555;
        }
      }
    }

    .btn {
      width: 724rpx;
      position: fixed;
      left: 13rpx;
      bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
      border-radius: 88rpx;
      overflow: hidden;

      .btn-wrap {
        text-align: center;
        font-size: 36rpx;
        font-weight: 500;
      }
    }
  }

  .popup-container {
    padding: 20rpx;

    .margin-bottom {
      margin-bottom: 20rpx;
    }

    .margin-top-sm {
      margin-top: 20rpx;
    }

    .qrcode-box {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40rpx 0;
    }

    .qrcode-tips {
      text-align: center;
      color: #666;
      font-size: 28rpx;
      padding-bottom: 40rpx;
    }
  }

  .canvas-box {
    position: absolute;
    top: 0;
    left: -2000rpx;
  }

  .qrcode-content {
    width: 616rpx;
    border-radius: 18rpx;
    padding: 50rpx 110rpx;
    text-align: center;

    .qrcode {
      width: 370rpx;
      height: 370rpx;
      margin-top: 43rpx;
      position: relative;
      left: 50%;
      transform: translateX(-50%);
    }

    .tips {
      font-size: 22rpx;
      font-weight: 500;
      color: #999999;
      line-height: 30rpx;
      text-align: left;
      margin-bottom: 12rpx;
      margin-top: 12rpx;

      .tips-before {
        width: 110rpx;
      }

      .tips-content {
        width: 390rpx;
      }
    }
  }
}
</style>
