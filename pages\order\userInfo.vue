<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="会员信息"></u-navbar>
		<view class="content">
			<view class="info-box">
				<view class="flex justify-start">
					<view class="flex justify-start">
						<view class="name">会员id：</view>
						<view>{{info.memberId||'-'}}</view>
					</view>
					<view style="margin-left: 24rpx;" v-if="info.memberId" @click="copy(info.memberId)">
						<text class="under-line-text">复制</text>
					</view>
				</view>
				<view class="flex justify-start">
					<view class="name">昵称：</view>
					<view>{{info.member.wechatNickname||info.member.alipayNickname||'-'}}</view>
				</view>
				<view class="flex justify-start phone">
					<view class="flex justify-start">
						<view class="name">手机：</view>
						<view>{{info.member.tel||'-'}}</view>
					</view>
					<view class="phone-num" @click="call(info.member.tel)">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/order/phone.png"
							mode="widthFix"></image>拨打
					</view>
				</view>
			</view>

			<view class="search">
				<u-search animation placeholder="请输入订单编号" :clearabled="true" v-model="keyword" :showAction="false"
					@search="search" @clear="clearSearch"></u-search>
			</view>

			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view v-if="orderList.length>0">
					<block v-for="(item,index) in orderList" :key="item.id">
						<view class="equipment-container" @tap="details(item)">
							<view class="flex align-center justify-between">
								<view class="title" v-if="item.deviceName">
									{{item.deviceName}}<text>({{item.deviceId}})</text>
								</view>
								<view class="title" v-else>{{item.deviceId}}</view>
								<view>
									<u-icon name="arrow-right" size="14"></u-icon>
								</view>
							</view>

							<view class="order-detail-item">
								<view>订单号：</view>{{item.id}}
							</view>

							<view class="order-detail-item">
								<view>支付方式：</view>{{$xy.getPayType(item.payType)}}
							</view>

							<view class="order-detail-item">
								<view>设备类型：</view>{{item.deviceTypeName||'-'}}
							</view>

							<view class="order-detail-item">
								<view>时间：</view>{{item.createTime}}
							</view>

							<view class="order-detail-item"
								v-if="$xy.delMoney(item.payMoney)==0&&item.status==4&&!item.orderGoods">
								<view>零元单提示：</view>
								<view style="color: red;">未拿商品</view>
							</view>

							<view class="goods-container sb-box" v-for="(item1,index1) in item.orderGoods"
								:key="item1.id">
								<view class="flex align-center justify-center image-container">
									<u--image radius="4" width="130rpx" height="130rpx" :src="item1.goodsImgUrl"
										mode="aspectFit" :lazy-lord="true"></u--image>
								</view>
								<view class="details-container">
									<view class="flex align-center goods-name-num justify-between">
										<view class="goods-name">{{item1.goodsName}}</view>
										<view class="goods-num">×{{item1.totalNumber}}</view>
									</view>
									<view class="gp-item">
										单价：￥{{(Number(item1.totalMoney)/Number(item1.totalNumber))/100}}
									</view>
									<view class="gp-item" v-if="item1.refundMoney&&item1.refundMoney!=0">
										已退款：￥{{$xy.delMoney(item1.refundMoney)}}
									</view>
									<view class="goods-price flex">
										<!-- <view class="gp-item">
											应收：￥{{(Number(item1.totalMoney)-Number(item1.discountMoney))/100}}
										</view> -->
										<!-- <view class="gp-item">
											实收：￥{{item1.totalMoney-item1.discountMoney}}
										</view> -->
									</view>
									<!-- <view class="good-act">优惠活动：无</view> -->
								</view>
							</view>
							<view v-if="current==0" class="martop" style="text-align: right;">
								共计{{Number(item.goodsNumber)}}件，实付款<text
									style="color: red;font-weight: bold;">￥{{$xy.delMoney(item.orderTotalMoney)}}</text>
							</view>
							<view v-else class="martop" style="text-align: right;">
								共计{{Number(item.goodsNumber)}}件，应付款<text
									style="color: red;font-weight: bold;">￥{{$xy.delMoney(item.orderTotalMoney)}}</text>
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="orderList.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>
		</view>

		<view class="btn safe-bottom">
			<xbutton size="large" round="80rpx" @click="block">{{info.isBlacklist?'解除拉黑':'拉黑'}}</xbutton>
		</view>



		<xpopup :show="show" @close="close" @confirm="submit" :showBtn="true" title="操作">
			<!-- 拉黑 -->
			<view class="pop-content restart">
				<view v-if="!info.isBlacklist">
					<view>
						是否确定拉黑该用户?
					</view>
					<view style="margin-top: 24rpx;">
						<u--textarea v-model="banReason" placeholder="请输入拉黑原因"></u--textarea>
					</view>
				</view>
				<view v-else>
					是否确定解除拉黑?
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		userInfo,
		setBlacklist,
		removeBlackList,
		page
	} from '@/api/order/order.js'
	export default {
		data() {
			return {
				id: null,
				info: {},
				show: false,
				fullHeight: 0,

				tabList: [{
						name: '全部'
					},
					{
						name: '支付中'
					},
					{
						name: '补单中'
					},
				],
				current: 0,

				page: 1, //当前分页
				size: 10, //分页数据条数
				loadmoreStatus: 'loadmore',
				orderList: [],
				keyword: null,
				payStatus: '',
				status: '',
				supplement: false,
				banReason: ''
			}
		},

		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')

			this.id = o.id;
			this.getInfo()

			this.search()
		},

		methods: {
			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			clearSearch() {
				this.keyword = null
				this.search()
			},

			tabClick(e) {
				this.current = e.index
				switch (this.current) {
					case 0:
						this.payStatus = ''
						this.status = ''
						this.supplement = false
						break;
					case 1:
						// this.payStatus = 8
						this.status = 7
						this.supplement = false
						break;
					case 2:
						this.payStatus = ''
						this.status = ''
						this.supplement = true
						break;
					default:
						break;
				}
				this.search()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.orderList = [];
			},

			//获取订单列表
			getpage() {
				let params = {
					page: {
						current: this.page,
						size: this.size
					},
					id: this.keyword,
					memberId: this.id,
					status: this.status,
					supplement: this.supplement
				}
				page(params).then(res => {
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.orderList = this.orderList.concat(data)
				})
			},

			getInfo() {
				userInfo({
					memberId: this.id
				}).then(res => {
					this.info = res.data
				})
			},

			copy(text) {
				uni.setClipboardData({
					data: text.toString(),
					success: (data) => {
						uni.showToast({
							title: '复制成功'
						})
					},
					fail: function(err) {

					},
					complete: function(res) {
						console.log(res)
					}
				})
			},

			block() {
				this.show = true
			},

			// 关闭弹框
			close(e) {
				this.show = false
			},

			// 弹框确定
			submit() {
				if (this.info.isBlacklist) { //已拉黑，解除
					removeBlackList({
						memberId: this.id
					}).then(res => {
						this.$modal.msg('解除成功~')
						this.close()
						setTimeout(() => {
							this.getInfo()
						}, 1000)
					}).catch(err => {
						this.close()
					})
				} else { //拉黑
					setBlacklist({
						memberId: this.id,
						banReason: this.banReason
					}).then(res => {
						this.$modal.msg('拉黑成功~')
						this.close()
						setTimeout(() => {
							this.getInfo()
						}, 1000)
					}).catch(err => {
						this.close()
					})
				}
			},

			details(item) {
				this.$tab.navigateTo(
					`/pages/order/orderDetails?id=${item.id}`)
			},

			call(tell) {
				uni.makePhoneCall({
					phoneNumber: tell
				})
			},

			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},
		}
	}
</script>

<style lang="scss">
	.container {
		.content {
			overflow: hidden;

			.empty {
				padding-top: 40%;
			}

			.martop {
				margin-top: 20rpx;
			}

			.info-box {
				background-color: #fff;
				padding: 10rpx 20rpx;
				line-height: 60rpx;

				.name {
					width: 120rpx;
					font-size: 28rpx;
					font-weight: bold;
					color: #333;
				}

				.under-line-text {
					font-size: 26rpx !important;
					font-weight: 500;
					font-style: italic;
					text-decoration: underline;
					color: #2C6FF3 !important;
					margin-left: 24rpx;
					background-color: #fff !important;
				}
			}

			.phone {
				line-height: 36rpx;

				text {
					display: inline-block;
					background-color: #5b5b5b;
					color: #fff;
					font-size: 22rpx;
					padding: 0 12rpx;
					border-radius: 6rpx;
					margin-left: 12rpx;
					line-height: 36rpx;
				}
			}

			.phone-num {
				display: inline-block;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #2C6FF3;
				padding: 0 16rpx;
				line-height: 38rpx;
				background: #F4F8FF;
				border-radius: 8rpx;
				margin-left: 24rpx;

				>image {
					width: 20rpx;
					height: 20rpx;
					margin-right: 13rpx;
				}
			}
		}

		.search {
			padding: 12rpx 13rpx;
			background-color: #fff;
		}

		.tab-wrap {
			background-color: #fff;

			.tab {
				// width: 40%;
			}
		}

		.scrollview {
			overflow: hidden;
		}

		.equipment-container {
			margin: 13rpx 13rpx 0;
			padding: 12rpx 20rpx 24rpx;
			border-radius: 14rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

			.sb-box {
				padding: 24rpx 18rpx;
				background-color: #f5f8fb;
				border-radius: 8rpx;
				margin-top: 12rpx;
			}

			.title {
				height: 60rpx;
				line-height: 60rpx;
				font-size: 32rpx;
				font-weight: bold;
				color: #333;

				>text {
					font-size: 24rpx;
					color: #333;
				}
			}

			.goods-container {
				min-height: 154rpx;
				margin-top: 14rpx;
				padding: 12rpx 12rpx 12rpx 164rpx;
				box-sizing: border-box;
				position: relative;

				.image-container {
					height: 130rpx;
					width: 130rpx;
					position: absolute;
					left: 12rpx;
					top: 50%;
					transform: translateY(-50%);
				}

				.details-container {
					position: relative;
					padding: 12rpx;

					.goods-name-num {
						.goods-name {
							font-size: 26rpx;
							color: #333;
							font-weight: bold;
						}

						.goods-num {
							font-size: 26rpx;
							color: red;
							margin-right: 40rpx;
							font-weight: bold;
						}
					}

					.goods-price {
						font-size: 28rpx;
					}

					.good-act {
						margin-top: 12rpx;
					}

					.gp-item {
						width: 50%;
						margin-top: 12rpx;
					}

					.goodf-act {
						font-size: 28rpx;
						color: #333;
						margin-top: 12rpx;
					}

					.goods-btn {
						margin-top: 12rpx;
					}

					.refund {
						position: absolute;
						right: 0;
						top: 0;
						background-color: red;
						color: #fff;
						border-radius: 4rpx;
						padding: 0 12rpx;
						line-height: 40rpx;
						font-size: 24rpx;
					}
				}

			}

			.order-detail-item {
				font-size: 28rpx;
				margin-top: 12rpx;
				color: #777;

				>view {
					display: inline-block;
					width: 170rpx;
				}
			}
		}

		.btn {
			width: 100%;
			position: fixed;
			left: 0;
			bottom: 24rpx;
			padding: 0 24rpx;
		}

		.pop-content {
			padding: 24rpx;
		}
	}
</style>