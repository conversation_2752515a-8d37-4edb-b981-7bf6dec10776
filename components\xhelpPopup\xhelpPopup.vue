<template>
	<view class="help-popup">
		<view class="title-wrap" v-if="title">
			<view class="btn-title" @click="show=true">
				{{title}}
				<view class="icon"><u-icon name="question-circle" size="16"></u-icon></view>
			</view>
		</view>
		<u-popup :show="show" @close="close" :round="round" :overlayStyle="{zIndex:zIndex-5}" :zIndex="zIndex"
			:safeAreaInsetBottom="safeAreaInsetBottom&&mode=='bottom'" @open="open" :mode="mode">
			<view class="pop-wrap">
				<view class="title">
					{{title}}
				</view>
				<view class="content">
					<scroll-view scroll-y style="height:100%;">
						<rich-text :nodes="content"></rich-text>
					</scroll-view>
				</view>
				<view class="popup-btn">
					<xbutton width="200rpx" size="large" bgColor="#fff" color="#2C6FF3" borderColor="#2C6FF3"
						@click="close">关闭</xbutton>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		obj
	} from '@/api/guide.js'
	export default {
		data() {
			return {
				show: false,
				title: '',
				content: ''
			}
		},
		props: {
			guideId: {
				require: true,
				default: ''
			},
			mode: {
				type: String,
				require: false,
				default: 'center'
			},
			showBtn: {
				type: Boolean,
				require: false,
				default: true
			},
			safeAreaInsetBottom: {
				type: Boolean,
				require: false,
				default: true
			},
			round: {
				type: String,
				require: false,
				default: '12rpx'
			},
			zIndex: {
				type: Number,
				require: false,
				default: 20075
			},
		},

		async created() {
			await this.getData()
			let count = uni.getStorageSync(`help-${this.guideId}`) || 0
			//count计数，如果大于3，则不再显示
			if (this.content && count < 3) {
				this.show = true
				count++
				uni.setStorageSync(`help-${this.guideId}`, count)
			}
		},

		methods: {
			close() {
				this.show = false
			},
			open() {
				this.show = true
			},
			getData() {
				return new Promise((resolve, reject) => {
					obj({
						guideId: this.guideId
					}).then(res => {
						let data = res.data;
						this.title = data?.guideTitle
						this.content = data?.guideContent
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.help-popup {
		// background-color: #fff;
	}

	.title-wrap {
		padding: 10rpx 20rpx;
	}

	.btn-title {
		display: inline-block;
		line-height: 40rpx;
		padding: 0 50rpx 0 20rpx;
		background-color: orange;
		border-radius: 6rpx;
		position: relative;

		.icon {
			position: absolute;
			right: 10rpx;
			top: 44%;
			transform: translateY(-50%);
		}
	}

	.pop-wrap {
		width: 690rpx;
		box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);
	}

	.popup-clear {
		padding: 24rpx;
		color: #2C6FF3;
	}

	.popup-btn {
		display: flex;
		flex-flow: row nowrap;
		justify-content: space-around;
		width: 100%;
		padding: 50rpx 24rpx 24rpx;

		.cu-btn {
			background-color: #2C6FF3;
			color: #fff;
			width: 200rpx;
		}

		.cu-btn1 {
			background-color: green;
		}

	}


	.title {
		font-size: 32rpx;
		font-weight: bold;

		padding: 24rpx;
		text-align: center;
	}

	.content {
		height: 580rpx;
		padding: 0 20rpx;
	}
</style>