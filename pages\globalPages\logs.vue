<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" bgColor="#2C6FF3" :placeholder="true">
			<view class="u-nav-slot flex align-center" slot="left">
				<view class="nav-back flex align-center" @click="$tab.navigateBack()">
					<u-icon name="arrow-left" size="22" color="#fff"></u-icon>
					<view class="nav-title">接口日志</view>
				</view>
				<view class="clear-img flex align-center justify-around" @click="clearLogs">
					<image
						src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/clear-notice.png"
						mode="widthFix"></image>
				</view>
			</view>
		</u-navbar>
		<view class="version">
			版本号：{{version||'暂无版本号'}}
		</view>
		<scroll-view scroll-y="true" scroll-with-animation="true" lower-threshold="100">
			<view class="logs-wrap" v-if="logs&&logs.length>0">
				<view class="log-item" v-for="(item,index) in logs" :key="index">
					<view class="logs">
						<view class="name">页面路由</view>
						<view class="val">{{item.route}}</view>
					</view>
					<view class="logs">
						<view class="name">请求路径</view>
						<view :class="[isErr(item.res)?'red val':'green val']" @click="copy(item.url)">{{item.url}}
						</view>
					</view>
					<view class="logs">
						<view class="name">请求时间</view>
						<view class="val">{{item.startTime}}</view>
					</view>
					<view class="logs">
						<view class="name">响应时间</view>
						<view :class="[item.endTime>600?'red val':'val']">{{item.endTime}}毫秒</view>
					</view>
					<view class="logs">
						<view class="name">请求参数</view>
						<view class="val" @click="copy(item.params)">{{item.params}}</view>
					</view>
					<view class="logs">
						<view class="name">token</view>
						<view class="val" @click="copy(item.token)">{{item.token}}</view>
					</view>
					<view class="logs">
						<view class="name">返回结果</view>
						<view class="val response" @click="copy(item.res)">{{item.res}}</view>
					</view>
				</view>
			</view>
			<view v-else style="padding-top: 40%;">
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				logs: [],
				version: ''
			}
		},
		onLoad(o) {
			this.version = uni.getAccountInfoSync().miniProgram.version
			console.log(this.version)
			if (uni.getStorageSync('logs')) {
				this.logs = JSON.parse(uni.getStorageSync('logs'))
			}
		},
		methods: {
			isErr(url) {
				return url.indexOf(`"code":200`) == -1
			},

			copy(text) {
				uni.setClipboardData({
					data: text,
					success: (data) => {
						uni.showToast({
							title: '复制成功'
						})
					},
					fail: function(err) {
						console.log(err)
					},
					complete: function(res) {
						console.log(res)
					}
				})
			},

			clearLogs() {
				this.logs = []
				uni.setStorageSync('logs', '')
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		padding: 24rpx;
		line-height: 50rpx;

		.u-nav-slot {
			.nav-title {
				font-size: 32rpx;
				color: #fff;
			}

			.clear-img {
				width: 50rpx;
				height: 50rpx;
				border-radius: 50rpx;
				background-color: rgba(0, 0, 0, 0.2);
				margin-left: 44rpx;

				>image {
					width: 36rpx;
					height: 36rpx;
				}
			}
		}

		.text {
			color: #2C6FF3;
		}

		.log-item {
			width: 702rpx;
			margin-bottom: 50rpx;

			.logs {
				overflow: hidden;
				width: 100%;
			}

			.name {
				float: left;
				margin-right: 24rpx;
				width: 20%;
				color: gray;
			}

			.val {
				float: left;
				word-wrap: break-word;
				word-break: break-all;
				width: 76%;

				&.red {
					color: red;
				}

				&.green {
					color: green;
				}
			}

			.response {
				max-height: 880rpx;
				overflow: hidden;
			}
		}
	}
</style>