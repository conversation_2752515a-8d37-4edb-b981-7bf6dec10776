<template>
	<view class="container">
		<u-navbar titleStyle="fontSize:36rpx;" :autoBack="true" bgColor="#fff" :placeholder="true"
			title="补货管理"></u-navbar>
		<view style="background-color: #fff;">
			<xhelpPopup guideId="BH0001" />
			<view class="flex align-center  box"
				v-if="checkPermi(['repMan:repRecord','repMan:physicalRecord','repMan:invSearch'])">
				<!-- <view class="flex flex-direction align-center" @tap="startReplenishment()">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/bh.png" mode="widthFix"></image>
						<view class="">开始补货</view>
					</view> -->
				<!-- <view class="flex flex-direction align-center"  @tap="commodityList()">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/qd.png" mode="widthFix"></image>
						<view class="">商品清单</view>
					</view> -->
				<view class="flex justify-between align-center" @tap="replenishmentRecord"
					v-if="checkPermi(['repMan:repRecord'])">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/jl.png"
						mode="widthFix"></image>
					<!-- <image src="../../static/favicon.ico" mode=""></image> -->
					<view class="menu-name">补货记录</view>
				</view>
				<view class="flex justify-between align-center" @tap="physicalRecord"
					v-if="checkPermi(['repMan:physicalRecord'])">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/pd-mini.png"
						mode="widthFix"></image>
					<view class="menu-name">盘点记录</view>
				</view>
				<view class="flex justify-between align-center" @tap='inventoryManagement'
					v-if="checkPermi(['repMan:invSearch'])">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/kc.png"
						mode="widthFix"></image>
					<!-- <image src="../../static/favicon.ico" mode=""></image> -->
					<view class="menu-name">库存管理</view>
				</view>
			</view>

			<!-- <view class="tips">
					补货说明补货说明补货说明
				</view> -->

			<!-- 设备列表 -->
			<view class="search">
				<u-search animation placeholder="请输入设备名称" bgColor="#F6F8FB" :clearabled="true" v-model="keyword"
					:showAction="false" @search="search"></u-search>
				<!-- 	<view class="scan-icon" @click="scan">
						<u-icon name="scan" size="22" color="#909399"></u-icon>
					</view> -->
			</view>

			<!-- 	<view class="tab-list">
					<u-tabs :list="tabList" @click="tabChange" :current="current" :scrollable="false" bgColor="#fff"
						lineColor="#2C6FF3"></u-tabs>
				</view> -->

			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabChange"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
			@scrolltolower="scrolltolower" :style="{height:fullHeight}">
			<view class="content">
				<view class="xy-card" v-for="(item,index) in list" :key="item.deviceId" @click="replenish(item)">
					<view class="title flex justify-between">
						<view class="flex justify-between">
							<view>
								<view class="device-name" v-if="item.deviceName">
									<view class="name">
										{{item.deviceName}}
									</view>
									<view class="deviceId">{{item.deviceId}}</view>
								</view>
								<view class="device-name" v-else>
									{{item.deviceId}}
								</view>
							</view>
							<view class="flex justify-end">
								<view style="margin-left: 22rpx; margin-top: 8rpx;" v-if="item.deviceTypeName">
									<view class="busy-state on">{{item.deviceTypeName}}</view>
								</view>
								<view style="margin-left: 22rpx; margin-top: 8rpx;">
									<view v-if="item.busyState==1" class="busy-state on">运营中</view>
									<view v-else class="busy-state off">已停运</view>
								</view>
							</view>
						</view>

						<view class="flex status">
							<view style="margin-left: 24rpx;color: #FF0000;" v-if="item.goodsIdStockOutCount>0">
								缺货
							</view>
							<view style="margin-left: 24rpx;color: green;" v-else>
								正常
							</view>
						</view>
					</view>

					<view class="t-content flex flex-direction" v-if="current==0">
						<view class="c-item-t">
							<view class="c-item">
								<view>剩余库存：</view>
								<view>{{item.stockSum||0}}</view>
							</view>
							<view class="c-item">
								<view>补后库存：</view>
								<view>{{item.fillCountSum||0}}</view>
							</view>
						</view>
						<view class="c-item-t">
							<view class="c-item">
								<view>在售商品种类：</view>
								<view>{{item.goodsIdCount||0}}</view>
							</view>
							<view class="c-item" v-if="item.goodsIdStockOutCount>0">
								<view>缺货商品种类：</view>
								<view style="color: red;font-weight: bold;">{{item.goodsIdStockOutCount||0}}</view>
							</view>
						</view>
					</view>

					<view class="t-content flex flex-direction" v-else>
						<view class="c-item-t">
							<view class="c-item">
								<view>剩余库存：</view>
								<view>{{item.stockSum||0}}</view>
							</view>
							<view class="c-item">
								<view>补后库存：</view>
								<view>{{item.fillCountSum||0}}</view>
							</view>
						</view>
						<view class="c-item-t">
							<view class="c-item">
								<view>缺货商品种类：</view>
								<view style="color: red;font-weight: bold;">{{item.goodsIdStockOutCount||0}}</view>
							</view>
						</view>
					</view>
				</view>

				<u-loadmore :status="status" v-if="list.length>=1" />
			</view>
			<view class="empty" v-if="list.length==0">
				<u-empty text="没有设备!"></u-empty>
			</view>
		</scroll-view>

		<!-- <view class="scan" @click="startReplenishment">
				<view class="scan-icon-bot">
					<u-icon name="scan" color="#2C6FF3" size="60"></u-icon>
				</view>
				<view>扫码补货</view>
			</view> -->

		<view class="btn" v-if="checkPermi(['repMan:scan'])">
			<xbutton size="large" @click="startReplenishment">
				<view class="btn-wrap flex align-center">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep-scan.png"
						mode=""></image>
					扫码补货
				</view>
			</xbutton>
		</view>
	</view>
</template>

<script>
	import {
		deviceDetail,
		searchPage,
		isMerc
	} from '@/api/device/device.js'

	import {
		deviceStockList,
		deviceStockOutList,
		devicePart
	} from '@/api/replenishment/replenishment.js'
	export default {
		data() {
			return {
				page: 1,
				size: 10,

				status: 'loadmore',

				current: 0,
				tabList: [{
						name: '全部',
						value: ''
					},
					{
						name: '缺货',
						value: 1
					}
				],

				list: [],

				keyword: '',

				fullHeight: 0
			}
		},

		async onLoad() {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview')
			this.search()
		},

		methods: {
			async startReplenishment() {
				try {
					let scanRes = await this.$xy.scanDevice()
					let deviceId = scanRes.deviceId || scanRes.terminalId;
					let door = scanRes.door || 0

					let getDeviceDetailRes = await this.getDeviceDetailFun(deviceId)
					let data = getDeviceDetailRes.data.records;
					if (data && data.length > 0) {
						let deviceDetail = data[0]
						let url = ''
						if (deviceDetail.deviceType == 6 || deviceDetail.deviceType == 7) {
							let isOpenGravity = await this.isOpenGravity(deviceId) //是否开启重力识别
							url = isOpenGravity ? '/pages/replenish/replenishmentGra' :
								'/pages/replenish/replenishmentHomePage'
						} else {
							url = '/pages/replenish/replenishmentHomePage'
						}
						this.$tab.navigateTo(
							`${url}?id=${deviceId}&deviceType=${deviceDetail.deviceType}`
						)
					} else {
						this.$modal.msg('设备不存在！')
					}
				} catch (err) {
					this.$modal.msg(err)
				}
			},

			hasDevice(id) {
				return new Promise((resolve, reject) => {
					isMerc({
						deviceId: id
					}).then(res => {
						console.log(res)
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			commodityList() {
				this.$tab.navigateTo('../commodity/commoditylist')
			},
			replenishmentRecord() {
				this.$tab.navigateTo('/pages/replenish/repRecord')
			},
			physicalRecord() {
				this.$tab.navigateTo('/pages/replenish/physicalRecord')
			},
			inventoryManagement() {
				this.$tab.navigateTo('/pages/replenish/invSearch')
			},

			tabChange(e) {
				this.current = e.index
				this.reset()
				this.getList()
			},

			getList() {
				if (this.current == 0) {
					this.getList1()
				} else {
					this.getList2()
				}
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.list = [];
			},

			getList1() {
				return new Promise((resolve, reject) => {
					deviceStockList({
						page: {
							current: this.page,
							size: this.size
						},
						keyword: this.keyword,
					}).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.status = "nomore"
						} else {
							this.status = "loadmore"
						}
						this.list = this.list.concat(data)
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			getList2() {
				return new Promise((resolve, reject) => {
					deviceStockOutList({
						page: {
							current: this.page,
							size: this.size
						},
						keyword: this.keyword,
					}).then(res => {
						let data = res.data.records;
						if (data.length < 10) {
							this.status = "nomore"
						} else {
							this.status = "loadmore"
						}
						this.list = this.list.concat(data)
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},

			search() {
				this.reset()
				this.getList()
			},

			async replenish(item) {
				if (this.checkPermi(['repMan:replenishmentHomePage'])) {
					let url = ''
					if (item.deviceType == 6 || item.deviceType == 7) {
						let isOpenGravity = await this.isOpenGravity(item.deviceId) //是否开启重力识别
						url = isOpenGravity ? '/pages/replenish/replenishmentGra' :
							'/pages/replenish/replenishmentHomePage'
					} else {
						url = '/pages/replenish/replenishmentHomePage'
					}
					this.$tab.navigateTo(
						`${url}?id=${item.deviceId}&deviceType=${item.deviceType}`
					)
				} else {
					this.$modal.msg('暂无权限~')
				}
			},

			isOpenGravity(deviceId) {
				return new Promise((resolve, reject) => {
					devicePart({
						deviceId: deviceId,
						code: 'es'
					}).then(res => {
						let data = res.data
						if (data && data.length > 0) {
							//value:{is:true,type:1}
							let value = JSON.parse(data[0].value)
							resolve(value.is)
						} else {
							resolve(false)
						}
					}).catch(err => {
						reject(err)
					})
				})
			},

			//触底加载更多
			scrolltolower() {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},

			getDeviceDetailFun(deviceId) {
				return new Promise((resolve, reject) => {
					deviceStockList({
						page: {
							current: 1,
							size: 1
						},
						keyword: deviceId,
					}).then(res => {
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.container {

		// padding-top: 120rpx;
		image {
			width: 50rpx;
			height: 50rpx;
		}

		.box {
			padding: 20rpx 12rpx 14rpx;
			background: #fff;
			font-size: 28rpx;
			font-weight: 500;
			color: #777777;
			line-height: 62rpx;

			>view {
				background: #F7F7F7;
				border-radius: 10rpx;
				margin-right: 20rpx;
				padding: 0 12rpx;
			}

			image {
				width: 34rpx;
				height: 34rpx;
				margin-right: 15rpx;
			}
		}

		.tips {
			width: 724rpx;
			padding: 10rpx 24rpx;
			font-size: 28rpx;
			line-height: 40rpx;
			font-weight: 500;
			color: #333333;
			background: #F0FFFC;
			border: 2rpx dashed #61D8BE;
			border-radius: 14rpx;
			margin-left: 13rpx;
		}

		.search {
			padding: 0rpx 13rpx 24rpx;
			background-color: #fff;
			position: relative;
			margin-top: 14rpx;

			.scan-icon {
				position: absolute;
				right: 36rpx;
				top: 50%;
				transform: translateY(-50%);
				z-index: 2;
			}
		}

		.scan {
			width: 100%;
			position: fixed;
			bottom: 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			background-color: #fff;
			box-shadow: 0 2rpx 10rpx 0 rgba(0, 0, 0, 0.1);

			.scan-icon-bot {
				width: 60px;
			}

			>view:nth-child(2) {
				font-size: 24rpx;
				color: #999;
			}
		}

		.tab-wrap {
			background-color: #fff;

			.tab {
				width: 40%;
				margin-left: -12rpx;
			}
		}

		.content {
			margin: 0 13rpx;
			padding-bottom: 90rpx;
			overflow: hidden;

			.xy-card {
				margin-top: 20rpx;
				padding: 32rpx;
			}
		}

		.title {
			font-size: 28rpx;
			font-weight: bold;
			line-height: 50rpx;

			.device-name {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
				width: 220rpx;

				.name {
					width: 220rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.deviceId {
					font-size: 24rpx;
					color: #666;
					font-weight: normal;
				}
			}
		}

		.t-content {
			margin-top: 12rpx;

			.c-item-t {
				display: flex;
				justify-content: flex-start;
				margin-top: 12rpx;

				.c-item {
					width: 50%;
					display: flex;
					justify-content: flex-start;
					align-items: flex-end;
				}
			}
		}

		.busy-state {
			font-size: 24rpx;
			padding: 0 16rpx;
			line-height: 38rpx;
			background: #F4F8FF;
			border-radius: 8rpx;
			text-align: center;
			font-weight: normal;
			max-width: 180rpx;

			&.on {
				color: #2C6FF3;
			}

			&.off {
				color: #666;
			}
		}

		.status {
			font-weight: normal;
		}

		.empty {
			margin-top: 50rpx;
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;

				>image {
					width: 42rpx;
					height: 42rpx;
					margin-right: 20rpx;
				}
			}
		}
	}
</style>