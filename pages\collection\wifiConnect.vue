<template>
	<view class="container">
		<!-- 顶部导航栏：显示页面标题和返回按钮 -->
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff"
			:placeholder="true" title="WiFi连接">
		</u-navbar>

		<!-- 表单区域：仅在第二步时显示 -->
		<view class="form-container" v-if="step == 2">
			<!-- WiFi连接类型选择：下拉选择或手动输入 -->
			<view class="form-item">
				<text class="label required">WiFi连接类型:</text>
				<view class="input-wrapper">
					<u-radio-group v-model="connectType" placement="row">
						<u-radio :customStyle="{ marginRight: '40rpx' }" label="下拉选择" name="select">
						</u-radio>
						<u-radio label="手动输入" name="manual"> </u-radio>
					</u-radio-group>
				</view>
			</view>

			<!-- WiFi名称输入/选择区域 -->
			<view class="form-item">
				<text class="label required">WiFi名称:</text>
				<view class="input-wrapper">
					<!-- 当选择"下拉选择"模式时显示 -->
					<template v-if="connectType === 'select'">
						<u-cell-group :border="false">
							<u-cell :titleStyle="{
                  color: acount ? '#333' : '#999', // 有值时显示黑色，无值时显示灰色
                  fontSize: '28rpx',
                }" isLink :border="false" :title="acount || '请选择WiFi'" @click="getWifiList"> <!-- 点击触发获取WiFi列表方法 -->
							</u-cell>
						</u-cell-group>
					</template>
					<!-- 当选择"手动输入"模式时显示 -->
					<u-input v-else v-model="acount" placeholder="请输入WiFi名称">
					</u-input>
				</view>
			</view>

			<!-- WiFi密码输入区域 -->
			<view class="form-item">
				<text class="label required">WiFi密码:</text>
				<view class="input-wrapper">
					<u-input v-model="password" type="password" placeholder="请输入WiFi密码">
					</u-input>
				</view>
			</view>
		</view>

		<!-- 固定在底部的操作按钮 -->
		<view class="fixed-bottom-bar">
			<button class="save-btn" @click="submitForm">
				{{ step == 1 ? "重置设备wifi" : "连接" }} <!-- 根据当前步骤显示不同按钮文字 -->
			</button>
		</view>

		<!-- WiFi选择弹窗：用于显示可用的WiFi列表 -->
		<u-picker :show="showWifiSelect" :columns="[wifiList]" @confirm="onWifiSelect" @cancel="showWifiSelect = false">
			<!-- 取消选择时关闭弹窗 -->
		</u-picker>

		<!-- 连接wifi中 -->
		<u-popup :show="connectShow" mode="center" @close="closePop" round="10" :safe-area-inset-bottom="false">
			<view class="pop-content" v-if="connectStatus==0">
				<view class="p-title">重置wifi中...</view>
				<view class="p-body">
					<view class="p-tips">
						请耐心等待"wifi重置成功"
					</view>
					<view class="p-icon flex justify-center">
						<u-loading-icon size="32"></u-loading-icon>
					</view>
				</view>
			</view>

			<view class="pop-content" v-if="connectStatus==1">
				<view class="p-title">重置wifi中...</view>
				<view class="p-body">
					<view class="p-tips">
						wifi重置成功
					</view>
					<view class="p-icon flex justify-center">
						<u-loading-icon size="32"></u-loading-icon>
					</view>
				</view>
			</view>

			<view class="pop-content" v-if="connectStatus==2">
				<view class="p-title">正在连接WiFi中...</view>
				<view class="p-body">
					<view class="p-tips">
						请耐心等待语音播报"WiFi连接成功"
					</view>
					<view class="p-icon flex justify-center">
						<u-loading-icon size="32"></u-loading-icon>
					</view>
				</view>
			</view>

			<view class="pop-content" v-if="connectStatus==3">
				<view class="p-title">WiFi连接成功,等待门开锁</view>
				<view class="p-body">
					<view class="p-tips">
						请耐心等待"开门成功"
					</view>
					<view class="p-icon flex justify-center">
						<u-loading-icon size="32"></u-loading-icon>
					</view>
				</view>
			</view>

			<view class="pop-content" v-if="connectStatus==4">
				<view class="p-title">连接成功</view>
				<view class="p-body">
					<view class="p-tips">
						柜门已打开，请拉门采集数据
					</view>
				</view>
				<view class="p-btn">
					<xbutton @click="openDoorSure">确定</xbutton>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		connectWifi,
		gatherOpenDoor,
		doorStatus,
		connectWifiResult,
		queryDeviceNetwork,
		openGatherMode
	} from "@/api/collection.js"; // 导入WiFi连接API

	export default {
		data() {
			return {
				step: 1, // 当前步骤：1-重置设备WiFi，2-连接WiFi
				deviceId: null, // 设备ID，从路由参数获取
				connectType: "select", // WiFi连接类型：select-下拉选择，manual-手动输入
				acount: "", // WiFi名称/账号
				password: "", // WiFi密码
				showWifiSelect: false, // 是否显示WiFi选择弹窗
				wifiList: [], // 可用的WiFi列表
				connectShow: false, // 连接wifi中弹窗
				connectStatus: 0, // WiFi连接状态
				timer: null, // 定时器
			};
		},

		// 页面加载时执行
		onLoad(o) {
			this.deviceId = o.deviceId; // 获取路由参数中的设备ID
			// 初始化微信小程序WiFi模块
			wx.startWifi({
				success: () => {
					console.log("WiFi模块初始化成功");
				},
				fail: (err) => {
					console.error("WiFi模块初始化失败", err);
				},
			});
		},

		onUnload() {
			clearInterval(this.timer)
		},

		methods: {
			// WiFi选择确认处理：从弹窗选择WiFi后的回调
			onWifiSelect(e) {
				const selected = e.value[0]; // 获取选中的WiFi信息
				this.acount = selected.text; // 设置WiFi名称
				this.showWifiSelect = false; // 关闭选择弹窗
			},

			// 获取可用的WiFi列表
			async getWifiList() {
				try {
					let wifiApiRes = await this.wifiApiFun();
					if (wifiApiRes && wifiApiRes.length) {
						// 格式化WiFi列表数据，用于选择器显示
						this.wifiList = wifiApiRes.map((wifi) => ({
							text: wifi.SSID, // 显示的WiFi名称
							value: wifi.BSSID, // WiFi的唯一标识符
						}));
						this.showWifiSelect = true; // 显示WiFi选择弹窗
					} else {
						// 未找到可用WiFi时显示提示
						this.$modal.msg('未找到可用WiFi')
					}
				} catch (error) {
					this.$modal.msg('请检查手机wifi是否打开')
					console.log(error)
				}
			},

			//微信附近wifi获取api
			wifiApiFun() {
				return new Promise((resolve, reject) => {
					uni.showLoading({
						title: "搜索WiFi中...", // 显示加载提示
					});

					// 调用微信小程序API获取WiFi列表
					wx.getWifiList({
						success: (res1) => {
							console.log('res1', res1);
							// 监听获取到WiFi列表事件
							wx.onGetWifiList((res) => {
								console.log('res', res);
								uni.hideLoading(); // 隐藏加载提示

								// 对WiFi列表按SSID进行去重处理
								const uniqueWifiMap = new Map();
								res.wifiList.forEach(wifi => {
									if (wifi.SSID && !uniqueWifiMap.has(wifi.SSID)) {
										uniqueWifiMap.set(wifi.SSID, wifi);
									}
								});

								// 转换为数组
								const uniqueWifiList = Array.from(uniqueWifiMap.values());
								resolve(uniqueWifiList);
							});
						},
						fail: (err) => {
							// 获取WiFi列表失败处理
							uni.hideLoading();
							this.$modal.msg('获取WiFi列表失败')
							console.error("获取WiFi列表失败", err);
							reject(err)
						},
					});
				})
			},

			// 表单提交处理
			async submitForm() {
				try {

					// 第一步：重置设备WiFi
					if (this.step == 1) {
						//重置中
						this.connectStatus = 0

						//先开启采集
						let openGatherModeRes = await openGatherMode({
							deviceId: this.deviceId
						});
						if (!openGatherModeRes?.data) return this.$modal.msg('重置失败，请重试')
						this.connectShow = true //显示连接wifi中弹窗
						setTimeout(() => {
							this.$modal.msg('重置成功')
							this.connectShow = false
							this.connectStatus = 1 //重置成功
							// TODO: 实现重置设备WiFi的逻辑
							this.step = 2; // 进入第二步
						}, 15000)
					}
					// 第二步：连接WiFi
					else if (this.step == 2) {
						// 表单验证：确保WiFi名称已填写
						if (!this.acount) {
							uni.showToast({
								title: "请输入WiFi名称",
								icon: "none",
							});
							return;
						}
						// 表单验证：确保WiFi密码已填写
						if (!this.password) {
							uni.showToast({
								title: "请输入WiFi密码",
								icon: "none",
							});
							return;
						}

						this.connectShow = true //显示连接wifi中弹窗

						// 开始连接wifi
						this.connectStatus = 2 //连接中

						let connectWifiRes = await connectWifi({
							identifier: this.deviceId, // 设备ID
							acount: this.acount, // WiFi名称/账号
							password: this.password, // WiFi密码
						})
						if (!connectWifiRes?.data) return this.$modal.msg('wifi连接失败，请重试')

						let wifiStatusRes = await this.wifiStatus(); // 查询wifi连接状态
						this.connectStatus = 3 //连接成功

						let netStatusRes = await queryDeviceNetwork({
							identifier: this.deviceId, // 设备ID
						}); // 查询设备网络状态
						if (!netStatusRes.data) return this.$modal.msg('设备网络异常，请重试')

						let openDoorStatus = await gatherOpenDoor({
							identifier: this.deviceId
						});
						if (!openDoorStatus.data) return this.$modal.msg('开门失败，请重试')
						this.connectStatus = 4 //开门成功
					}
				} catch (err) {
					console.log(err)
					this.connectShow = false
				}
			},

			closePop() {
				if (this.timer) clearInterval(this.timer)
				this.connectShow = false
			},

			async openDoorSure() {
				try {
					let doorStatusRes = await doorStatus({
						identifier: this.deviceId
					});
					if (doorStatusRes?.data == 'close') return this.$modal.msg('请先打开柜门!')

					this.$tab.navigateTo("/pages/collection/chooseGoods?deviceId=" + this.deviceId);
				} catch (error) {
					console.log(error)
				}
			},

			//查询wifi连接状态
			wifiStatus() {
				return new Promise((resolve, reject) => {
					try {
						let num = 0
						this.timer = setInterval(async () => {
							num++
							if (num > 20) {
								clearInterval(this.timer)
								this.$modal.msg('连接超时，请重试')
								reject('连接超时')
								return
							}

							try {
								let res = await connectWifiResult({
									identifier: this.deviceId, // 设备ID
								})

								if (res.code == 200) {
									if (res.data == 'online') {
										//连接成功，退出轮询
										clearInterval(this.timer)
										resolve(res)
									} else if (res.data == 'offline') {
										// 离线状态，继续轮询
										console.log('WiFi当前离线，继续等待连接...')
									} else {
										// 其他状态，继续轮询
										console.log('WiFi状态:', res.data, '继续等待连接...')
									}
								}
							} catch (apiError) {
								// API请求异常，记录错误但继续轮询
								console.error('API请求异常:', apiError)
							}
						}, 2000)
					} catch (error) {
						if (this.timer) clearInterval(this.timer)
						this.connectShow = false
						reject(error)
					}
				})
			},
		},
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
		position: relative;

		.form-container {
			padding: 20rpx;
			background-color: #fff;
			margin-top: 20rpx;

			.form-item {
				display: flex;
				align-items: center;
				min-height: 88rpx;
				border-bottom: 1rpx solid #f5f5f5;

				&:last-child {
					border-bottom: none;
				}

				.label {
					width: 200rpx;
					font-size: 28rpx;
					color: #333;

					&.required::before {
						content: "*"; // 必填项前显示星号
						color: #ff3b30;
						margin-right: 4rpx;
					}
				}

				.input-wrapper {
					flex: 1;
					display: flex;
					align-items: center;

					:deep(.u-cell) {
						flex: 1;
						background-color: transparent;

						// 移除cell的内边距
						.u-cell__body {
							padding: 0;
						}

						// 调整箭头图标位置
						.u-cell__right-icon-wrap {
							margin-right: 0;
						}
					}

					.u-input {
						flex: 1;
					}
				}
			}
		}

		.fixed-bottom-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); // 适配底部安全区域
			background-color: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.save-btn {
				height: 88rpx;
				background-color: #4177fb;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0;
			}
		}

		.pop-content {
			width: 620rpx;
			padding: 40rpx;
			text-align: center;

			.p-title {
				font-size: 34rpx;
				font-weight: bold;
				padding-bottom: 20rpx;
			}

			.p-tips {
				line-height: 50rpx;
			}

			.p-icon {
				margin-top: 30rpx;
			}

			.p-btn {
				margin-top: 100rpx;
			}
		}
	}
</style>