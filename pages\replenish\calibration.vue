<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="电子秤校准"></u-navbar>
		<view class="content safe-bottom">
			<view class="tab-title">层架电子秤类型:</view>
			<view class="tab-box flex align-center justify-around">
				<view v-for="(item,index) in types" :key="item" :class="[item.value==current?'type typeshow':'type']"
					@click="typeClick(item)">
					{{item.name}}
				</view>
			</view>

			<view class="input-box">
				<view class="section1">
					<view class="flex justify-between">
						<view class="input-item flex align-center">
							<view class="input-title">层架号</view>
							<u--input v-model="cjVal" type="number" placeholder="请输入"></u--input>
						</view>
						<view class="input-item flex align-center">
							<view class="input-title">货道号</view>
							<u--input v-model="hdVal" type="number" placeholder="请输入"></u--input>
						</view>
					</view>
					<view class="input-item flex align-center">
						<view class="input-title">发砝码</view>
						<u-input v-model="fmVal" placeholder="请输入">
							<template slot="suffix">
								<text>kg</text>
							</template>
						</u-input>
					</view>

					<view class="tip">
						提示: 可用1kg, 2kg, 5kg砝码。
					</view>
				</view>
			</view>

			<view class="step-box" style="margin-top: 30rpx;">
				<view v-for="(item,index) in steps" :key="item">{{item}}</view>
			</view>

			<view class="btns flex justify-around">
				<xbutton round='88rpx' bgColor="#fff" borderColor="#2C6FF3" color="#2C6FF3" width="160rpx"
					@click="btnClick('clean')">清零</xbutton>
				<xbutton round='88rpx' bgColor="#fff" borderColor="#2C6FF3" color="#2C6FF3" width="160rpx"
					@click="btnClick('set')">校准</xbutton>
				<xbutton round='88rpx' bgColor="#fff" borderColor="#2C6FF3" color="#2C6FF3" width="160rpx"
					@click="btnClick('autoset')">一键校准</xbutton>
			</view>

			<view class="step-title">一键校准过程语音提示</view>

			<view class="step-box">
				<view v-for="(item,index) in tips" :key="item">{{item}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		sendCommand
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				types: [{
					name: '单电子秤',
					value: 1
				}, {
					name: '双电子秤',
					value: 2
				}, {
					name: '四电子秤',
					value: 4
				}],
				current: '1',
				steps: [
					"1）将层架置空（不要放任何东西）",
					'2）输入层架号（如果对单个货道校准还要输入货道号）。',
					'3）执行清零指令。',
					'4）置入相应法码到电层架上。',
					'5）执行电子称校准指令。'
				],
				tips: [
					'1）提示置空层架',
					'2)  提示货道号清零指令执行结果。',
					"3）提示在倒计时20完成时间内置入法码。",
					'4）计数完成后，提示发送校准指令。',
					'5）提示指令校准指令执行结果。',
					'6）提示校准后重新查询重量是否正确。',
					'7）提示校准结果成功或失败。'
				],
				cjVal: '',
				hdVal: '',
				fmVal: '',
				id: null,
				aisleNo: null
			}
		},
		onLoad(o) {
			this.id = o.id
			this.aisleNo = o.aisleNo
		},
		methods: {
			typeClick(item) {
				if (this.current == item.value) return
				this.current = item.value
			},

			btnClick(type) {
				let typeName = '清零'
				switch (type) {
					case 'clean':
						typeName = '清零'
						break;
					case 'set':
						typeName = '校准'
						break;
					case 'autoset':
						typeName = '一键校准'
						break;
					default:
						break;
				}
				this.$modal.oldConfirm(`是否确认${typeName}?`).then(res => {
					this.save(type)
				}).catch(err => {

				})
			},

			async save(type) {
				let templet = {}
				await this.getDict('mqtt_cmd_templet_task', 'weight').then(res => {
					templet = JSON.parse(res[0].value);
					console.log(templet)
					templet.data.layerhdcount = this.current;
					templet.data.task = type;
					templet.data.layer = this.cjVal;
					templet.data.hdh = this.hdVal;
					templet.data.weight = this.fmVal * 1000;
				});
				sendCommand([{
					deviceId: this.id,
					templet: templet
				}]).then(res => {
					this.$modal.showToast('成功~')
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 24rpx;

		.section1 {
			padding: 20rpx;
		}

		.tab-title {
			font-weight: bold;
			line-height: 50rpx;
		}

		.tab-box {
			margin-top: 20rpx;

			.type {
				width: 160rpx;
				line-height: 60rpx;
				border: 1px solid #2C6FF3;
				color: #2C6FF3;
				border-radius: 60rpx;
				text-align: center;
				margin-right: 24rpx;

				&.typeshow {
					background-color: #2C6FF3;
					color: #fff;
				}
			}
		}

		.input-box {
			margin-top: 40rpx;
			box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 1px 0px, rgba(0, 0, 0, 0.15) 0px 4px 8px 0px;
			border-radius: 12rpx;
			padding: 20rpx 0;

			.input-item {
				width: 48%;
				margin-bottom: 20rpx;

				.input-title {
					margin-right: 12rpx;
					width: 100rpx;
				}
			}
		}

		.tip {
			color: red;
			font-size: 24rpx;
		}

		.step-box {
			margin-top: 20rpx;
			padding: 20rpx;
			border-radius: 12rpx;
			background-color: #ffc9a2;
			line-height: 50rpx;
			font-size: 26rpx;
		}

		.btns {
			margin-top: 40rpx;
		}

		.step-title {
			font-weight: bold;
			line-height: 50rpx;
			margin-top: 40rpx;
		}
	}
</style>