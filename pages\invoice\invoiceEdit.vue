<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="开具发票"></u-navbar>
		<view class="content">
			<u--form labelPosition="left" :model="form" ref="form" errorType="toast" labelWidth="120">
				<view class="section-wrap">
					<u-form-item label="抬头类型" borderBottom>
						<u-radio-group v-model="type" disabled placement="row" @change="actionRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="企业单位" name="1">
							</u-radio>
							<u-radio label="个人/非企业单位" name="2">
							</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="公司名称" :borderBottom="type==1">
						<view class="flex align-center">
							<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.companyName"
								placeholder="请输入" border="none" clearable @focus="showList=false"
								@blur="showList=true"></u-input>
							<view @click.stop="chooseComp" style="padding:0 20rpx;">
								<u-icon slot="right" size="22" name="list" v-if="showList"></u-icon>
							</view>
						</view>
					</u-form-item>

					<u-form-item label="公司税号" borderBottom v-if="type==1">
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.taxIdNumber"
							placeholder="请输入" border="none" clearable></u-input>
					</u-form-item>

					<u-form-item label="公司地址" borderBottom v-if="type==1">
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.address"
							placeholder="请输入" border="none" clearable></u-input>
					</u-form-item>

					<u-form-item label="公司电话" borderBottom v-if="type==1">
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.tel" placeholder="请输入"
							border="none" clearable></u-input>
					</u-form-item>

					<u-form-item label="开户行" borderBottom v-if="type==1">
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.bank" placeholder="请输入"
							border="none" clearable></u-input>
					</u-form-item>

					<u-form-item label="开户行账号" v-if="type==1">
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.accountWithBank"
							placeholder="请输入" border="none" clearable></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="联系人" borderBottom>
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.contactName"
							placeholder="请输入" border="none" clearable></u-input>
					</u-form-item>
					<u-form-item label="电话" borderBottom>
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.contactTel"
							placeholder="请输入" border="none" clearable></u-input>
					</u-form-item>
					<u-form-item label="地址">
						<u-input inputAlign="left" disabled disabledColor="#fff" v-model="form.contactAddress"
							placeholder="请输入" border="none" clearable></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<u-form-item label="发票类型" borderBottom>
						<u-radio-group v-model="form.invoiceType" placement="row" @change="actionTypeRadio">
							<u-radio :customStyle="{marginRight: '40rpx'}" label="专票" name="1">
							</u-radio>
							<u-radio label="普票" name="2">
							</u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="发票金额">
						<u-input inputAlign="left" color="red" disabled :value="unitTotal" placeholder="请输入"
							border="none" clearable></u-input>
					</u-form-item>
				</view>

				<view class="section-wrap">
					<view @click="selectAction('费用类型')">
						<u-form-item label="费用类型" borderBottom>
							<u-input inputAlign="left" disabled disabledColor="#fff" :value="form.feeType"
								placeholder="请选择" border="none" clearable></u-input>
						</u-form-item>
					</view>
					<view @click="selectAction('开票类型')">
						<u-form-item label="开票类型">
							<u-input inputAlign="left" disabled disabledColor="#fff" :value="form.invoiceCategoryName"
								placeholder="请选择" border="none" clearable></u-input>
						</u-form-item>
					</view>
				</view>

				<block v-if="form.invoiceCategory">
					<view class="section-wrap" v-if="form.invoiceCategory=='1'">
						<u-form-item label="发票接收邮箱">
							<u-input inputAlign="left" disabledColor="#fff" v-model="form.email" placeholder="请输入"
								border="none" clearable></u-input>
						</u-form-item>
					</view>
					
					<view class="section-wrap" v-else>
						<view @click="selectAction('快递选择')">
							<u-form-item label="快递选择" borderBottom>
								<u-input inputAlign="left" disabled disabledColor="#fff" :value="form.expressDeliveryName"
									placeholder="请选择" border="none" clearable></u-input>
							</u-form-item>
						</view>
					
						<view @click="selectAction('支付方式')">
							<u-form-item label="支付方式">
								<u-input inputAlign="left" disabled disabledColor="#fff" :value="form.payTypeName"
									placeholder="请选择" border="none" clearable></u-input>
							</u-form-item>
						</view>
					
						<view style="color:red;line-height: 50rpx;font-size: 24rpx;">注：发票运费需自理！</view>
					</view>
				</block>

				<!-- <view class="section-wrap total-money flex justify-between align-center">
					<view>发票内容</view>
					<view>{{notes}}</view>
				</view>
				<view class="section-wrap total-money flex justify-between align-center" style="margin-top: 0;">
					<view>总金额</view>
					<view>￥{{$xy.delMoney(totalMoney)}}</view>
				</view> -->

			</u--form>
		</view>
		<view class="btn">
			<xbutton size="large" @click="submit">
				提交
			</xbutton>
		</view>


		<xpopup :show="show" @close="close" @confirm="submit" :showBtn="false" title="抬头选择">
			<!-- 抬头选择 -->
			<view class="pop-content">
				<scroll-view scroll-y class="list-wrap" v-if="list&&list.length>0" style="height:600rpx;">
					<block v-for="(item,index) in list" :key="item.titleId">
						<view class="list-item flex justify-between align-center" @click="selectTitle(item)">
							<view class="msg">
								<view class="name">
									{{item.companyName}}
								</view>
								<view class="code" v-if="item.type==1">
									{{item.taxIdNumber}}
								</view>
							</view>
							<!-- <view class="select-img">
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
									mode="widthFix" v-show="item.defaultTitle"></image>
								<image
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
									mode="widthFix" v-show="!item.defaultTitle"></image>
							</view> -->
						</view>
					</block>
				</scroll-view>
				<view class="empty" v-else>
					请先添加常用发票抬头~
				</view>

				<view class="pop-btn safe-bottom">
					<xbutton size="large" round="88rpx" @click="add">
						添加常用发票抬头
					</xbutton>
				</view>
			</view>
		</xpopup>

		<u-action-sheet :show="actionShow" :actions="action" :title="actionTitle" @cancle="actionShow=false"
			@close="actionShow = false" @select="actionSelect($event)"></u-action-sheet>
	</view>
</template>

<script>
	import getDict from "@/utils/getDict.js"
	import {
		apply,
		titleList
	} from '@/api/invoice.js'

	export default {
		data() {
			return {
				form: {
					"companyName": "",
					"taxIdNumber": "",
					"accountWithBank": "",
					"address": "",
					"tel": "",
					"contactName": "",
					"contactTel": "",
					"contactAddress": "",
					"bank": "",
					"contactName": "",
					"contactTel": "",
					"contactAddress": "",
					"titleId": "",
					"invoiceType": "1",
					"feeType": "",
					"expressDeliveryType": "",
					"expressDeliveryName": "",
					"payType": "",
					"payTypeName": "",
					"invoiceCategory": "",
					"invoiceCategoryName": "",
					"email": ""
				},

				show: false,
				list: [],

				totalMoney: 0,
				type: '1',
				showList: true,
				orderIds: null,
				notes: null,
				feeTypeAction: [],
				expressAction: [],
				payTypeAction: [],
				categoryAction: [],
				action: [],
				actionTitle: '',
				actionShow: false,
			}
		},

		computed: {
			unitTotal() {
				return '￥' + this.$xy.delMoney(this.totalMoney)
			},
		},

		onLoad(o) {
			this.notes = o.notes
			this.orderIds = o.ids
			this.totalMoney = o.totalMoney
			this.getTitles()

			getDict(['invoice_fee_type', 'express_delivery_type', 'express_delivery_pay_type', 'invoice_category']).then(
				res => {
					let feeTypeAction = [];
					let expressAction = [];
					let payTypeAction = [];
					let categoryAction = []

					for (var i = 0; i < res.length; i++) {
						let item = res[i]
						if (item.paterCode == 'invoice_fee_type') {
							feeTypeAction.push({
								type: item.value,
								name: item.msg
							})
						} else if (item.paterCode == 'express_delivery_type') {
							expressAction.push({
								type: item.value,
								name: item.msg
							})
						} else if (item.paterCode == 'express_delivery_pay_type') {
							payTypeAction.push({
								type: item.value,
								name: item.msg
							})
						} else {
							categoryAction.push({
								type: item.value,
								name: item.msg
							})
						}
					}

					this.feeTypeAction = feeTypeAction
					this.expressAction = expressAction
					this.payTypeAction = payTypeAction
					this.categoryAction = categoryAction
					//设置默认支付方式
					this.form.payType = payTypeAction[0].type;
					this.form.payTypeName = payTypeAction[0].name;

				})

			uni.$on('refreshData', res => {
				this.getTitles()
			})
		},

		methods: {
			getTitles() {
				titleList({}).then(res => {
					this.list = res.data
					if (this.list && this.list.length > 0) {
						//设置默认抬头
						let item = this.list.find(i => i.defaultTitle)
						if (item) {
							this.selectTitle(item)
							this.show = false
						} else {
							this.show = true
						}
					} else {
						this.show = true
					}
				})
			},
			chooseComp() {
				this.show = true
			},

			close() {
				if (this.list && this.list.length > 0 && this.form.titleId) {
					this.show = false
				} else {
					this.$modal.msg('请先选择/设置抬头信息~')
				}
			},

			add() {
				if (this.list?.length > 0) {
					this.$tab.navigateTo('/pages/invoice/titleList')
				} else {
					this.$tab.navigateTo('/pages/invoice/editTitle')
				}
			},

			actionRadio(e) {
				this.type = e
			},

			actionTypeRadio(e) {
				this.form.invoiceType = e
			},

			/**
			 * @param {Object} item
			 * 选择抬头
			 */
			selectTitle(item) {
				this.type = item.type.toString()
				this.form = {
					"companyName": item.companyName,
					"taxIdNumber": item.taxIdNumber,
					"accountWithBank": item.accountWithBank,
					"address": item.address,
					"tel": item.tel,
					"contactName": item.contactName,
					"contactTel": item.contactTel,
					"contactAddress": item.contactAddress,
					"bank": item.bank,
					"contactName": item.contactName,
					"contactTel": item.contactTel,
					"contactAddress": item.contactAddress,
					"titleId": item.titleId,
					"invoiceType": "1"
				}
				this.show = false
			},

			//表单提交
			submit() {
				let params = {
					"invoiceType": this.form.invoiceType,
					"orderId": this.orderIds,
					"titleId": this.form.titleId,
					"money": this.totalMoney,
					"feeRemark": this.notes,
					"feeType": this.form.feeType,
					"expressDeliveryType": this.form.expressDeliveryType,
					"payType": this.form.payType,
					"invoiceCategory": this.form.invoiceCategory,
					"email": this.form.email
				}
				console.log(params)
				apply(params).then(res => {
					this.$modal.showToast('申请成功~')
					setTimeout(() => {
						uni.$emit('update')
						this.$tab.navigateBack()
					}, 1500)
				}).catch(err => {

				})
			},

			// 下拉选择
			selectAction(type) {
				this.actionTitle = type
				switch (type) {
					case '费用类型':
						this.action = this.feeTypeAction
						break;
					case '开票类型':
						this.action = this.categoryAction
						break;
					case '快递选择':
						this.action = this.expressAction
						break;
					case '支付方式':
						this.action = this.payTypeAction
						break;

					default:
						break;
				}
				console.log(this.action)
				this.actionShow = true
			},

			actionSelect(e) {
				switch (this.actionTitle) {
					case '费用类型':
						this.form.feeType = e.name
						break;
					case '开票类型':
						this.form.invoiceCategoryName = e.name
						this.form.invoiceCategory = e.type
						break;
					case '快递选择':
						this.form.expressDeliveryName = e.name
						this.form.expressDeliveryType = e.type
						break;
					case '支付方式':
						this.form.payTypeName = e.name
						this.form.payType = e.type
						break;

					default:
						break;
				}
				this.actionShow = false
			},
		},
		onUnload() {
			uni.$off('refreshData')
		}
	};
</script>

<style scoped lang="scss">
	::v-deep .u-form-item__body {
		padding: 30rpx 0 !important;
	}

	.container {
		.content {
			padding-bottom: 200rpx;

			.section-wrap {
				padding: 0 30rpx;
				background-color: #fff;
				margin-top: 24rpx;
			}

			.total-money {
				padding: 30rpx;

				>view:nth-child(2) {
					color: red;
				}
			}
		}

		.btn {
			width: 724rpx;
			position: fixed;
			left: 13rpx;
			bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
			border-radius: 88rpx;
			overflow: hidden;
			z-index: 9;

			.btn-wrap {
				text-align: center;
				font-size: 36rpx;
				font-weight: 500;
			}
		}

		.pop-content {
			padding: 12rpx 0 160rpx;
			min-height: 700rpx;
			position: relative;

			.list-wrap {

				.list-item {
					border-top: 1rpx solid #e8e8e8;
					padding: 16rpx 24rpx;
					height: 120rpx;

					&:nth-last-child(1) {
						border-bottom: 1rpx solid #e8e8e8;
					}

					.msg {
						.name {
							font-size: 30rpx;
							font-weight: bold;
							line-height: 50rpx;
						}

						.code {
							font-size: 26rpx;
							line-height: 50rpx;
						}
					}

					.select-img {
						image {
							width: 34rpx;
							height: 34rpx;
						}
					}
				}
			}

			.empty {
				text-align: center;
				padding-top: 20%;
			}

			.pop-btn {
				position: absolute;
				width: 702rpx;
				bottom: 0;
				left: 24rpx;
			}
		}
	}
</style>