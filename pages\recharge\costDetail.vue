<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="费用明细"></u-navbar>
		<view class="content">
			<block v-for="(item,index) in list" :key="item.deviceId">
				<view class="xy-card section">
					<view class="flex justify-between">
						<view class="flex" style="font-size: 32rpx; line-height: 68rpx;margin-bottom: 12rpx;">
							<view class="row-item flex align-center"
								style="font-size: 32rpx; line-height: 68rpx;font-weight: bold;">
								<view v-if="item.deviceName&&item.deviceName!=item.deviceId" class="device-name">
									{{item.deviceName}}<text style="font-size: 26rpx;">（{{item.deviceId}}）</text>
								</view>
								<view v-else class="device-name">
									{{item.deviceId}}
								</view>
							</view>
							<!-- <view v-if="item.busyState==1" class="busy-state on">在线</view>
							<view v-else class="busy-state off">离线</view> -->
						</view>
						<view class="card-pac-wrap">
							<xbutton width="130rpx" @click="costDetail(item,1)">卡包<u-badge absolute :offset="[-8,-8]" numberType="overflow" bgColor="#E60012" max="99" :value="item.algorithmChargingSize"></u-badge></xbutton>
						</view>
					</view>
					<view class="flex align-center justify-between">
						<view>
							<view class="row-item flex align-center">
								<view class="row-name">
									机器类型：
								</view>
								<view class="row-val">
									动态视觉柜
								</view>
							</view>
							
							<!-- <view class="row-item flex align-center">
								<view class="row-name">
									欠费金额：
								</view>
								<view class="row-val" style="color: red;">
									￥{{$xy.delMoney(item.arrearageBalance)}}
								</view>
							</view> -->
						</view>
						<view style="text-align: center;font-size: 28rpx;line-height: 50rpx;">
							<view><text style="color: red;">剩余次数</text>/卡包次数</view>
							<view><text style="color: red;">{{item.unusedSize}}</text>/{{item.unusedSize+item.makeSize}}</view>
						</view>
					</view>

					<view class="btns flex justify-between martop align-center">
						<view class="btn-item flex align-center" @click="costDetail(item,2)">
							<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/glf.png"
								mode="widthFix"></image>
							<text>管理费明细</text>
						</view>
						<view class="line"></view>
						<view class="btn-item flex align-center" @click="costDetail(item,3)">
							<image
								src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/llkxf.png"
								mode="widthFix"></image>
							<text>流量卡续费明细</text>
						</view>
						<view class="line"></view>
						<view class="btn-item flex align-center" @click="costDetail(item,4)">
							<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/sfkf.png"
								mode="widthFix"></image>
							<text>算法扣费明细</text>
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		algorithmChargingDevice 
	} from "@/api/recharge.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: [],
				deviceIds: [],
				page: 1,
				size: 10,
				loadmoreStatus: 'loadmore',
			}
		},
		onLoad(o) {
			this.search()
		},
		methods: {
			// 搜索
			search() {
				this.reset()
				this.getList()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			getList() {
				algorithmChargingDevice({
					page: {
						current: this.page,
						size: this.size
					}
				}).then(res => {
					console.log(res.data)
					let data = res.data.records;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			costDetail(item, type) {
				let url = ''
				let bflag = false
				switch (type) {
					case 1: //卡包
						bflag = this.checkPermis(['costDetail:costPack'])
						url = '/pages/recharge/costPackDetail'
						break
					case 2: //管理费
						bflag = this.checkPermis(['costDetail:costMan'])
						url = '/pages/recharge/costDevDetail'
						break
					case 3: //流量卡
						bflag = this.checkPermis(['costDetail:costSim'])
						url = '/pages/recharge/costSimDetail'
						break
					case 4: //算法
						bflag = this.checkPermis(['costDetail:costAlgo'])
						url = '/pages/recharge/costAlgoDetail'
						break
					default:
						break
				}
				if (!bflag) return
				this.$tab.navigateTo(`${url}?deviceId=${item.deviceId}`)
			}
		},
		
		onReachBottom(){
			if (this.loadmoreStatus == 'nomore') return
			this.page++
			this.getList()
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.martop {
			margin-top: 24rpx;
		}

		.marleft24 {
			margin-left: 24rpx;
		}

		.content {
			padding: 0 13rpx;
			overflow: hidden;

			.search {
				margin-top: 30rpx;
			}

			.section {
				margin-top: 20rpx;
				padding: 24rpx 20rpx 24rpx 28rpx;

				.busy-state {
					font-size: 24rpx;
					height: 38rpx;
					padding: 0 22rpx;
					line-height: 38rpx;
					background: #F4F8FF;
					border-radius: 8rpx;
					text-align: center;
					font-weight: normal;
					margin-top: 14rpx;
					margin-left: 24rpx;

					&.on {
						color: #2C6FF3;
					}

					&.off {
						color: #666;
					}
				}

				.row-item {
					font-size: 28rpx;
					color: #555555;
					line-height: 56rpx;

					.device-name {
						width: 500rpx;
					}

					.row-name {
						min-width: 140rpx;
					}

					.row-val {}
				}

				.btns {
					border-top: 1rpx solid #f0f0f0;
					padding-top: 26rpx;
					padding: 26rpx 8rpx 10rpx;
					margin-top: 40rpx;

					.btn-item {


						>image {
							width: 30rpx;
							height: 30rpx;
							margin-right: 8rpx;
							margin-top: 4rpx;
						}
					}

					.line {
						width: 1rpx;
						height: 22rpx;
						background-color: #CCCCCC;
					}
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}
	}
</style>