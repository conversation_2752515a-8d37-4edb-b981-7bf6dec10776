<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="选择设备"></u-navbar>
		<view class="content">
			<view class="search">
				<u-search animation placeholder="请输入设备名称/id/管理员" bgColor="#F6F8FB" :clearabled="true"
					v-model="searchKey" :showAction="false" @search="search"></u-search>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				:style="{height:fullHeight}">
				<view class="list" v-if="list&&list.length>0">
					<block v-for="(item,index) in list" :key="item.adminName">
						<view class="xy-card section">
							<view class="flex align-center section-top" @click="lineSelect(item)">
								<view class="select-line-img">
									<image
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
										mode="widthFix" v-show="item.checked"></image>
									<image
										src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
										mode="widthFix" v-show="!item.checked"></image>
								</view>
								<view class="line-name">
									{{item.adminName}}({{item.deviceNum}}台)
								</view>
							</view>
							<block v-for="(item1,index1) in item.deviceInfos" :key="item1.deviceId">
								<view class="device-item flex" @click="select(item1,item)">
									<view class="select-img">
										<image
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
											mode="widthFix" v-show="item1.checked"></image>
										<image
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
											mode="widthFix" v-show="!item1.checked"></image>
									</view>
									<view class="device-content">
										<view class="device-name" v-if="item1.deviceName">
											{{item1.deviceName}}<text>({{item1.deviceId}})</text>
										</view>
										<view class="device-name" v-else>
											{{item1.deviceId}}
										</view>
										<view class="device-man">
											设备类型：{{item1.deviceTypeName}}
										</view>
									</view>
								</view>
							</block>
						</view>
					</block>
				</view>
				<view class="empty" v-else>
					<u-empty></u-empty>
				</view>
			</scroll-view>
		</view>

		<view class="btn flex justify-between align-center">
			<view class="all-select flex align-center" @click="selectAll">
				<view class="select-line-img">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-sel.png"
						mode="widthFix" v-show="allChecked"></image>
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/report/circle-nosel.png"
						mode="widthFix" v-show="!allChecked"></image>
				</view>
				<view>
					全选
				</view>
			</view>
			<view class="sure-btn">
				<xbutton width="180rpx" size="large" round="50rpx" @click="sure">选择设备</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		groupByAdminName,
	} from "@/api/device/device.js"
	import {
		saveGoodsDevice,
	} from "@/api/commoditylist/commoditylist.js"
	export default {
		data() {
			return {
				searchKey: null,
				list: null,
				type: null,
				fullHeight: 0,
				allChecked: false,
				ids: [],
				deviceList: [],
				modelId: null,
				algorithmId: null
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 70)
			this.modelId = o.modelId
			this.algorithmId = o.algorithmId
			this.search()
		},
		methods: {
			search() {
				this.getList()
			},

			getList(id) {
				groupByAdminName({
					searchKey: this.searchKey,
					algorithmId: this.algorithmId
				}).then(res => {
					let data = res.data;
					for (let i = 0; i < data.length; i++) {
						let par = data[i];
						par.checked = false
						for (let j = 0; j < par.deviceInfos.length; j++) {
							let child = par.deviceInfos[j]
							child.checked = false
						}
					}
					this.list = data;
				})
			},

			lineSelect(item) {
				for (let i = 0; i < item.deviceInfos.length; i++) {
					let device = item.deviceInfos[i]
					device.checked = !item.checked
				}
				item.checked = this.isAllChecked(item.deviceInfos)
				this.allChecked = this.isAllChecked(this.list)
				this.$forceUpdate()
			},

			select(item1, item) {
				item1.checked = !item1.checked
				item.checked = this.isAllChecked(item.deviceInfos)
				this.allChecked = this.isAllChecked(this.list)
				this.$forceUpdate()
			},

			selectAll() {
				let status = this.allChecked
				for (var i = 0; i < this.list.length; i++) {
					let item = this.list[i]
					console.log(item)
					if (item.checked == status) {
						this.lineSelect(item)
					}
				}
				this.$forceUpdate()
			},

			isAllChecked(list) {
				if (list && list.length > 0) {
					return list.every(i => i.checked)
				} else {
					return false
				}
			},

			sure() {
				let deviceIds = []
				for (let i = 0; i < this.list.length; i++) {
					let par = this.list[i];
					for (let j = 0; j < par.deviceInfos.length; j++) {
						let child = par.deviceInfos[j]
						if (child.checked) {
							deviceIds.push(child.deviceId)
						}
					}
				}
				saveGoodsDevice({
					"deviceIds": deviceIds, //设备信息id
					// "mercId": 0, //商户ID
					"modelId": this.modelId //商品清单ID
				}).then(res => {
					this.$modal.msg('应用成功')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000)
				})

			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;

		.content {
			padding: 0 13rpx;
			overflow: hidden;


			.search {
				margin-top: 30rpx;
			}

			.section {
				margin-top: 30rpx;

				.section-top {
					height: 62rpx;
					margin-bottom: 20rpx;
				}

				.select-line-img {
					margin-right: 18rpx;
					margin-top: 14rpx;

					>image {
						width: 34rpx;
						height: 34rpx;
					}
				}

				.line-name {
					font-size: 32rpx;
					font-weight: bold;
				}

				.device-item {
					background: #F6F7FA;
					border-radius: 14rpx;
					margin-top: 10rpx;
					padding: 24rpx 18rpx;

					.select-img {
						margin-right: 18rpx;
						margin-top: 6rpx;

						>image {
							width: 34rpx;
							height: 34rpx;
						}
					}

					.device-content {
						.device-name {
							>text {
								font-size: 26rpx;
							}
						}

						.device-man {
							font-size: 24rpx;
							color: #555;
							line-height: 40rpx;
							margin-top: 18rpx;
						}
					}
				}
			}
		}

		.empty {
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}

		.btn {
			background-color: #fff;
			padding: 24rpx;
			position: fixed;
			width: 100%;
			bottom: 0;
			left: 0;

			.all-select {
				padding: 20rpx 0;

				.select-line-img {
					width: 34rpx;
					height: 34rpx;
					margin-right: 28rpx;

					>image {
						width: 34rpx;
						height: 34rpx;
					}
				}
			}
		}
	}
</style>