<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" :title="typeName"></u-navbar>
		<view class="content">
			<view class="btn flex justify-end align-center">
				<!-- <view @click="chooseMerc">{{merc}}</view> -->
				<xbutton padding="15rpx 40rpx" @click="$tab.navigateTo(`/pages/report/createReport?type=${type}`)">新增
				</xbutton>
			</view>

			<scroll-view @scrolltolower="loadMore" class="scrollview" :scroll-with-animation="true" scroll-y
				lower-threshold="100" :style="{height:fullHeight}">

				<view class="list-warp" v-if="list&&list.length>0">
					<block v-for="(item, index) in list" :key="item.id">
						<view class="item-wrap xy-card">
							<view class="item-head flex justify-between">
								<view class="title">
									{{item.name}}
								</view>
								<view class="status">
									<view class="com-status" style="color: #bdbdbd;" v-if="item.status==1">生成中</view>
									<view class="com-status" style="color: #00aa00;" v-if="item.status==2">生成完成</view>
									<view class="com-status" style="color: #FF1E1E;" v-if="item.status==3">生成超时</view>
								</view>
							</view>
							<view class="item-body">
								<view class="item-body-sec">
									<view>报表类型：</view>{{typeName}}
								</view>
								<view class="item-body-sec">
									<view>创建时间：</view>{{item.createTime}}
								</view>
							</view>
							<view class="item-foot flex justify-end" v-if="item.status==2">
								<!-- <xbutton padding="15rpx 40rpx" bgColor="#F4F8FF" color="#2C6FF3"
									@click="downLoad(item.fileUrl)">下载</xbutton> -->
								<view class="view flex align-center justify-end">
									<xbutton padding="15rpx 40rpx" delay="2000" bgColor="#F4F8FF" color="#2C6FF3"
										@click="creatAgain(item)">重新生成</xbutton>
									<xbutton padding="15rpx 40rpx" delay="2000" bgColor="#F4F8FF" color="#2C6FF3" style="margin-left: 24rpx;"
										@click="downLoad(item.fileUrl)">下载</xbutton>
									<xbutton padding="15rpx 40rpx" bgColor="#fff" borderColor="#2C6FF3" color="#2C6FF3"
										style="margin-left: 24rpx;" @click="shareUrl(item.fileUrl)">
										分享
									</xbutton>
									<!-- <xbutton padding="15rpx 40rpx">发送</xbutton> -->
								</view>
							</view>
						</view>
					</block>
				</view>

				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

		</view>

		<!-- 区域选择弹框 -->
		<xpopup :show="shareShow" @close="shareShow=false" :showBtn="false" mode="center">
			<view class="popup-content">
				<view class="txt txt1">
					{{fileUrl}}
				</view>
				<view class="txt txt2">
					文件路径已复制，请前往微信粘贴！
				</view>
				<view slot="botton" class="button flex">
					<view @click="shareShow=false">
						不分享了
					</view>
					<navigator target="miniProgram" open-type="exit">去微信粘贴</navigator>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		page,
		save
	} from '@/api/report.js'
	export default {
		data() {
			return {
				status: 'loadmore',
				list: [],
				page: 1,
				size: 10,
				type: null,
				typeName: null,

				fullHeight: 0,
				shareShow: false,
				fileUrl: null
			}
		},
		async onLoad(o) {
			this.type = o.type
			this.typeName = o.typeName
			
			let _this = this;
			_this.fullHeight= await _this.$xy.scrollHeight(_this,'.scrollview')
		},
		
		onShow(){
			this.reset()
			this.getList()
		},
		
		methods: {
			getList() {
				page({
					type: this.type,
					page: {
						current: this.page,
						size: this.size
					},
				}).then(res => {
					let data = res.data.records;
					for (var i = 0; i < data.length; i++) {
						let item = data[i]
						switch (item.status) {
							case 1:
								item.statusName = '生成中'
								break

							case 2:
								item.statusName = '生成完成'
								break
							case 3:
								item.statusName = '生成超时'
								break
						}
					}
					if (data.length < 10) {
						this.status = "nomore"
					} else {
						this.status = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			reset() {
				this.status == 'loadmore'
				this.page = 1;
				this.list = [];
			},

			//上拉加载
			loadMore(e) {
				if (this.status == 'nomore') return
				this.page++
				this.getList()
			},
			
			creatAgain(item){
				let params = {
					"deviceIds": item.deviceIds?JSON.parse(item.deviceIds):[],
					"goodsIds": item.goodsIds?JSON.parse(item.goodsIds):[],
					"mercId": item.mercId,
					"name": item.name,
					"reportBeginTime": item.reportBeginTime.split(' ')[0],
					"reportEndTime": item.reportEndTime.split(' ')[0],
					"type": item.type
				}
				save(params).then(res => {
					this.$modal.showToast('报表生成中！')
					setTimeout(() => {
						this.reset()
						this.getList()
					}, 1500)
				})
			},

			downLoad(url) {
				this.$xy.exportDoc(url)
			},

			shareUrl(url) {
				this.shareShow = true
				this.fileUrl = url
				uni.setClipboardData({
					data: url,
					success: (data) => {
						console.log('复制成功')
					},
					fail: function(err) {
						console.log(err)
					},
					complete: function(res) {
						console.log(res)
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #555;

		.content {
			overflow: hidden;
			padding: 0 13rpx;

			.btn {
				margin-top: 20rpx;
				padding-bottom: 24rpx;
			}

			.list-warp {
				margin-top: 10rpx;

				.item-wrap {
					+.item-wrap {
						margin-top: 20rpx;
					}

					.item-head {
						.title {
							font-size: 32rpx;
							color: #333;
							font-weight: bold;
						}

						.status {
							font-size: 28rpx;
						}
					}

					.item-body {
						margin-top: 22rpx;

						.item-body-sec {
							line-height: 44rpx;

							>view {
								display: inline-block;
								min-width: 100rpx;
							}
						}
					}

					.item-foot {
						margin-top: 16rpx;

						.view {
							width: 500rpx;
						}
					}
				}
			}

			.empty {
				padding-top: 40%;
			}
		}

		.popup-content {
			width: 600rpx;
			border-radius: 12rpx;
			overflow: hidden;
			.txt{
				padding:24rpx;
				text-align: center;
				word-break:break-all;
				line-height: 60rpx;
			}
			.txt1{
				padding:24rpx;
				text-align: left;
				word-break:break-all;
			}
			.button{
				text-align: center;
				line-height: 80rpx;
				border-top:1rpx solid #dfdfdf;
				>view{
					flex:1;
					border-right: 1rpx solid #dfdfdf;
				}
				>navigator{
					flex:1;
					color: #2C6FF3;
				}
			}
		}
	}
</style>