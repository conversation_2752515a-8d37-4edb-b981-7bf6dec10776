<template>
	<view class="container">
		<!-- 个人信息 -->
		<view class="user">
			<view class="nav-bar">
				<u-navbar bgColor="rgba(0,0,0,0)" :autoBack="false" :placeholder="true">
					<view slot="left" style="color:#fff;font-size: 36rpx;">
						我的
					</view>
				</u-navbar>
			</view>
			<view class="user-content flex align-center flex-start">
				<view class="head-img">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/head-exam.png"
						mode="widthFix">
					</image>
				</view>
				<view class="user-msg">
					<view class="user-item">
						{{info.name}}<text>{{$xy.delPhone(info.tel)||'-'}}</text>
					</view>
					<view class="user-item dept">
						部门：{{item.deptNames&&info.deptNames!=[]?info.deptNames:info.mercName}}
					</view>
					<view class="user-item role">
						角色：{{info.roleNames}}
					</view>
				</view>


				<!-- <view class="share" @share="share">
					<view class="share-image">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/fx.png"
							mode="widthFix">
						</image>
					</view>
					<button open-type="share"></button>
				</view> -->

				<view class="login-out" @click="setting" v-if="checkPermi(['account:setting'])">
					设置
				</view>
			</view>
		</view>

		<!-- 用户信息 -->
		<!-- <view class="menu menu-userinfo">
			<view class="u-content">
				<view class="u-item flex align-center flex-direction">
					<view class="u-name">
						{{info.mercName}}
					</view>
					<view class="u-val">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/sh.png"
							mode="widthFix"></image>
						商户
					</view>
				</view>
				<view class="u-item">
					<view class="u-name">
						{{info.roleNames}}
					</view>
					<view class="u-val">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/js.png"
							mode="widthFix"></image>
						角色
					</view>
				</view>
				<view class="u-item">
					<view class="u-name">
						{{info.tel}}
					</view>
					<view class="u-val">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/dh.png"
							mode="widthFix"></image>
						电话
					</view>
				</view>
			</view>
		</view> -->

		<!-- <view class="menu">
			<view class="menu-title">
				系统参数设置
			</view>
			<view class="s-content">
				<view class="s-item">
					自动处理未上架订单：<text>是</text>
				</view>
				<view class="s-item">
					开启预定模式：<text>是</text>
				</view>
				<view class="s-item">
					是否强制盘点：<text>是</text>
				</view>
			</view>

			<xbutton class="edit" size="mini" @click="btnClick('编辑')">修改</xbutton>
		</view> -->

		<!--团队角色管理 -->
		<view class="menu convenient" v-if="roleManageMenu.length>0">
			<!--<view class="menu-title">
				团队角色管理
			</view> -->
			<view v-if="roleManageMenu.length>0" class="flex flex-wrap justify-start">
				<view class="menu-item" v-for="(item,index) in roleManageMenu" :key="item.id" @click="menuClick(item)">
					<view class="image">
						<u-image width="74rpx" height="74rpx" :src="item.miniMenuIcon" mode="widthFix"
							:lazy-load="true"></u-image>
					</view>
					<view>{{item.name}}</view>
				</view>
			</view>
			<view class="empty" v-else>
				<u-empty mode="permission"></u-empty>
			</view>
		</view>

		<!--费用管理 -->
		<view class="menu convenient" v-if="costManageMenu.length>0">
			<view v-if="costManageMenu.length>0" class="flex flex-wrap justify-start">
				<view class="menu-item" v-for="(item,index) in costManageMenu" :key="item.id" @click="menuClick(item)">
					<view class="image">
						<u-image width="74rpx" height="74rpx" :src="item.miniMenuIcon" mode="widthFix"
							:lazy-load="true"></u-image>
					</view>
					<view>{{item.name}}</view>
					<view class="notice-tips flex justify-around align-center">
						<u-badge numberType="overflow" bgColor="#E60012" max="99" :value="item.badge"
							v-if="item.name!='算法充值'"></u-badge>
						<view class="notice-rest" v-if="item.name=='算法充值'&&item.badge=='余额不足'">{{item.badge}}</view>
					</view>
				</view>
			</view>
			<view class="empty" v-else>
				<u-empty mode="permission"></u-empty>
			</view>
		</view>

		<!--应用管理 -->
		<view class="menu convenient" v-if="elseMenu.length>0">
			<!-- 	<view class="menu-title">
				费用/提现管理
			</view> -->
			<view v-if="elseMenu.length>0" class="flex flex-wrap justify-start">
				<view class="menu-item" v-for="(item,index) in elseMenu" :key="item.id" @click="menuClick(item)">
					<view class="image">
						<u-image width="74rpx" height="74rpx" :src="item.miniMenuIcon" mode="widthFix"
							:lazy-load="true"></u-image>
					</view>
					<view>{{item.name}}</view>
					<view class="notice-tips flex justify-around align-center" v-if="item.name=='通知'&&noticeNum>0">
						<u-badge numberType="overflow" bgColor="#E60012" max="99" :value="noticeNum"></u-badge>
					</view>
				</view>
			</view>
			<view class="empty" v-else>
				<u-empty mode="permission"></u-empty>
			</view>
		</view>

		<view class="version" @click="searchLog">
			版本号：v {{version||'0.0.1'}}
		</view>

		<!-- <view class="chang-password" @click="changePwd">
			修改密码
		</view> -->

		<xpopup :show="qrcodeShow" mode="center" @close="qrcodeClose" :showBtn="false">
			<view class="qrcode-content flex flex-direction align-center">
				<view class="qrcode-img">
					<u-image width="400rpx" height="400rpx"
						src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/miniQrcode.jpg"
						mode="widthFix" :lazy-load="true" showMenuByLongpress>
					</u-image>
				</view>
				<view class="save-qrcode" slot="botton">
					<xbutton @click="save">保存二维码</xbutton>
				</view>
			</view>
		</xpopup>

		<xpopup :show="pwdShow" @close="pwdClose" @confirm="pwdSubmit" :showBtn="true" title="修改密码">
			<view class="pwd-popup-content flex align-center">
				<view>新密码：</view>
				<view>
					<u--input placeholder="请输入新密码" type="password" border="surround" v-model="newpassword"></u--input>
				</view>
			</view>
		</xpopup>
	</view>
</template>

<script>
	import {
		removeToken
	} from '@/utils/auth'
	import {
		userInfo,
		updateUserInfo,
		arrearageCount
	} from "@/api/system/user.js"
	import storage from '@/utils/storage'
	import constant from '@/utils/constant'
	import {
		unRead
	} from "@/api/message.js"

	export default {
		data() {
			return {
				version: '0.0.1',
				tabArr: [{
						name: '今日',
						id: 0
					},
					{
						name: '本月',
						id: 1
					}
				],
				current: 0,
				info: {
					mercName: "",
					name: "sunny",
					roleNames: "",
					tel: ""
				},
				timer: null,
				logNum: 0,

				qrcodeShow: false,

				newpassword: '',
				pwdShow: false,

				roleManageMenu: null,
				costManageMenu: null,
				elseMenu: null,

				noticeNum: 0
			}
		},

		computed: {
			//用户名
			name() {
				return this.$store.state.user.name
			},
		},

		onShareAppMessage() {
			let arrName = ''
			switch (uni.getAccountInfoSync().miniProgram.appId) {
				case 'wxe875ab8368fdbe57':
					appName = '小白商家助手'
					break;
				case 'wxde6edfb4b2c1e7db':
					appName = '喵星人商家助手'
					break;
			}
			return {
				title: appName,
				path: '/pages/login',
				imageUrl: 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/merc_qrcode.jpg',
				success: function(res) {
					// 转发成功之后的回调
					if (res.errMsg == 'shareAppMessage:ok') {

					}
				},
				fail: function() {
					// 转发失败之后的回调
					if (res.errMsg == 'shareAppMessage:fail cancel') {
						// 用户取消转发
					} else if (res.errMsg == 'shareAppMessage:fail') {
						// 转发失败，其中 detail message 为详细失败信息
					}
				},
			}
		},

		created() {
			//版本号
			this.version = uni.getAccountInfoSync().miniProgram.version
			//团队角色管理
			this.roleManageMenu = this.getMenu('我的', '团队角色管理')
			//费用管理
			this.costManageMenu = this.getMenu('我的', '费用管理')
			//应用管理
			this.elseMenu = this.getMenu('我的', '应用管理')

			// //更新未读通知条数
			// uni.$on('refreshData', res => {
			// 	this.getNotice()
			// })
		},

		methods: {
			/**
			 * 更新所有数据
			 */
			refreshData() {
				//用户信息
				this.getUserInfo()
				//费用角标
				this.getRecBadge()
				//通知角标
				this.getNotice()
			},

			searchLog() {
				this.logNum++
				if (this.timer) {
					if (this.logNum > 6) {
						this.logNum = 0
						clearTimeout(this.timer)
						this.timer = null
						this.$tab.navigateTo('/pages/globalPages/logs')
					}
				} else {
					this.timer = setTimeout(() => {
						this.logNum = 0
						clearTimeout(this.timer)
						this.timer = null
					}, 2000)
				}

			},

			getNotice() {
				unRead().then(res => {
					this.noticeNum = res.data
				})
			},

			getUserInfo() {
				userInfo().then(res => {
					if (res.code == 200) {
						this.info = res.data
					} else {
						this.info = null
					}
				})
			},

			getMenu(menu1, menu2) {
				let menu = []
				let permissions_menu = this.$store.state.permission.permissions_menu
				if (permissions_menu && permissions_menu !=
					'[]') {
					let allMenu = JSON.parse(permissions_menu);
					if (allMenu.find(i => i.name == menu1) && allMenu.find(i => i.name == menu1).children) {
						let homeMenu = allMenu.find(i => i.name == menu1).children;
						if (homeMenu.find(i => i.name == menu2) && homeMenu.find(i => i.name == menu2).children) {
							menu = homeMenu.find(i => i.name == menu2).children
						}
					}
				}
				return menu
			},

			btnClick(e) {

			},

			menuClick(e) {
				if (e.route.indexOf('/') != -1) {
					this.$tab.navigateTo(`/pages/${e.route}`)
				} else if (e.name == '小程序二维码') {
					this.qrcodeShow = true;
				} else if (e.name == '客服') {
					this.phone('88888888')
					// this.$modal.msg('小程序暂未开通客服！')
				} else {
					this.$modal.msg('功能开发中，尽请期待~')
				}
			},

			loginOut() {
				this.$store.dispatch('LogOut').then(res => {
					this.$tab.reLaunch('/pages/login')
				})
			},

			qrcodeClose() {
				this.qrcodeShow = false;
			},

			save() {
				let imgUrl = 'https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/miniQrcode.jpg'
				wx.getSetting({
					success: function(res) {
						if (!res.authSetting['scope.writePhotosAlbum']) { //判断是否开启相册权限
							uni.authorize({
								scope: 'scope.writePhotosAlbum',
								success: function(res) {
									uni.downloadFile({
										url: imgUrl,
										success: function(res) {
											uni.saveImageToPhotosAlbum({
												filePath: res.tempFilePath,
												success: function(res) {
													wx.showToast({
														title: '保存成功',
													})
												}
											})
										}
									})
								},
								fail(res) {
									uni.showModal({
										title: '提示',
										content: '您未开启保存图片到相册的权限，请点击确定去开启权限！',
										success(res) {
											if (res.confirm) {
												uni.openSetting()
											}
										}
									})
								}
							})
						} else {
							uni.downloadFile({
								url: imgUrl,
								success: function(res) {
									uni.saveImageToPhotosAlbum({
										filePath: res.tempFilePath,
										success: function(res) {
											wx.showToast({
												title: '保存成功',
											})
										}
									})
								}
							})

						}
					},
					fail(res) {}
				})
			},

			//拨打电话
			phone(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				})
			},

			setting() {
				this.$tab.navigateTo('/pages/globalPages/setting')
			},

			changePwd() {
				this.pwdShow = true
			},

			pwdSubmit() {
				if (this.newpassword) {
					updateUserInfo({
						password: this.newpassword
					}).then(res => {
						this.$modal.showToast('修改成功~')
					})
					this.pwdClose()
				} else {
					this.$modal.msg('请输入新密码！')
				}
			},

			pwdClose() {
				this.pwdShow = false
			},

			getRecBadge() {
				return new Promise((resolve, reject) => {
					arrearageCount({}).then(res => {
						let data = res.data;
						for (var i = 0; i < this.costManageMenu.length; i++) {
							let item = this.costManageMenu[i];
							switch (item.name) {
								case '算法充值':
									if (data && data.mercBalance && data.mercBalanceThre) {
										item.badge = data.mercBalance - data.mercBalanceThre < 0 ? '余额不足' :
											null
									} else {
										item.badge = null
									}
									break
								case '设备管理费':
									if (data && (data.deviceChargingBeTimeoutCout || data
											.deviceChargingTimeoutCout)) {
										item.badge = Number(data.deviceChargingBeTimeoutCout) + Number(data
											.deviceChargingTimeoutCout)
									} else {
										item.badge = null
									}
									break
								case '流量卡续费':
									if (data && (data.deviceSimBeTimeoutCout || data
											.deviceSimTimeoutCout)) {
										item.badge = Number(data.deviceSimBeTimeoutCout) + Number(data
											.deviceSimTimeoutCout)
									} else {
										item.badge = null
									}
									break
								default:
									break;
							}
						}
						this.costManageMenu = JSON.parse(JSON.stringify(this.costManageMenu))
						resolve(data)
					}).catch(err => {
						reject(err)
					})
				})
			},
		},

		// onUnload() {
		// 	uni.$off('refreshData')
		// }
	}
</script>

<style lang="scss" scoped>
	.container {
		padding-bottom: 100rpx;

		// 个人信息
		.user {
			background: #2C6FF3 url('https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/globalPages/user-bg.png') no-repeat left top;
			background-size: 100%;
			padding-bottom: 45rpx;
			position: relative;
			width: 100%;

			.user-content {
				margin-top: 34rpx;
				width: 100%;

				.head-img {
					width: 120rpx;
					height: 120rpx;
					border-radius: 120rpx;
					margin-left: 26rpx;
				}

				.user-msg {
					.user-item {
						font-size: 28rpx;
						line-height: 48rpx;
						color: #fff;
						margin-left: 33rpx;
						
						>text{
							margin-left: 12rpx;
						}

						&.dept,
						&.role {
							font-size: 24rpx;
							line-height: 38rpx;
						}
					}
				}

				.share {
					width: 30rpx;
					height: 30rpx;
					position: relative;
					margin-left: 20rpx;

					.share-image {
						width: 30rpx;
						height: 30rpx;
						position: absolute;
						left: 0;
						top: 0;

						>image {
							width: 30rpx;
							height: 30rpx;
						}
					}

					button {
						width: 40rpx;
						height: 40rpx;
						position: absolute;
						left: 0;
						top: 0;
						opacity: 0;
					}
				}

				.login-out {
					width: 140rpx;
					line-height: 52rpx;
					height: 52rpx;
					text-align: center;
					font-size: 24rpx;
					color: #2C6FF3;
					background-color: #fff;
					position: absolute;
					right: 0;
					top: 205rpx;
					z-index: 99;
					border-top-left-radius: 52rpx;
					border-bottom-left-radius: 52rpx;
				}
			}
		}


		// 常用菜单
		.menu {
			width: 724rpx;
			margin-left: 14rpx;
			background: #FFFFFF;
			box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);
			border-radius: 14rpx;
			margin-top: 24rpx;
			position: relative;
			padding: 20rpx 36rpx 0;

			&.menu-userinfo {
				padding: 36rpx;
				margin-top: -102rpx;
			}

			.menu-title {
				font-size: 30rpx;
				font-weight: 800;
				color: #000000;
				position: absolute;
				left: 28rpx;
				top: 28rpx;
			}

			.u-content {
				display: flex;
				flex-flow: row nowrap;
				justify-content: space-around;
				text-align: center;

				.u-item {
					line-height: 50rpx;

					.u-name {
						font-weight: bold;
						font-size: 30rpx;
					}

					.u-val {
						font-size: 28rpx;
						color: #666;
						position: relative;
						padding-left: 30rpx;
						display: inline-block;

						>image {
							width: 21rpx;
							height: 21rpx;
							position: absolute;
							left: 0;
							top: 50%;
							transform: translateY(-50%);
						}
					}
				}
			}

			.cu-btn {
				padding: 0 12rpx;
				font-size: 22rpx;
				height: 40rpx;
				line-height: 40rpx;
				background-color: #2C6FF3;
				color: #fff;

			}

			.edit {
				position: absolute;
				right: 28rpx;
				top: 28rpx;
			}

			.s-content {
				.s-item {
					line-height: 50rpx;

					>text {
						color: red;
					}
				}
			}

			.menu-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 25%;
				position: relative;
				padding-bottom: 40rpx;

				>.image {
					width: 74rpx;
					height: 74rpx;
				}

				>view {
					color: #333333;
					font-size: 26rpx;
					line-height: 26rpx;
					margin-top: 16rpx;
					text-align: center;
				}

				.notice-tips {
					padding: 10rpx;
					text-align: center;
					position: absolute;
					right: 0;
					top: 0;
					z-index: 999;
					margin-top: -10rpx;

					.notice-rest {
						padding: 4rpx 10rpx;
						background-color: #E60012;
						font-size: 22rpx;
						color: #fff;
						border-radius: 200rpx;
					}
				}
			}
		}

		.qrcode-content {
			padding: 24rpx;

			.qrcode-img {
				width: 400rpx;
				height: 400rpx;
			}

			.save-qrcode {
				margin-top: 24rpx;
			}
		}

		.version {
			font-size: 22rpx;
			color: #b1b1b1;
			margin-top: 100rpx;
			text-align: center;
		}

		.chang-password {
			text-align: center;
			line-height: 60rpx;
			color: #2C6FF3;
			text-decoration: underline;
		}

		.pwd-popup-content {
			padding: 24rpx;

			>view:nth-child(1) {
				width: 160rpx;
			}

			>view:nth-child(2) {
				width: 100%;
			}
		}
	}
</style>