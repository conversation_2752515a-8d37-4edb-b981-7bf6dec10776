// 积分
import request from '@/utils/request'
import {
	downLoadReq
} from '@/utils/request.js'
/**
 * @param {Object} data
 * 添加套餐
 */
export function configSave(data) {
	return request({
		url: '/act/home/<USER>/configSave',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 积分设置详情
 */
export function configObj(data) {
	return request({
		url: '/act/home/<USER>/configObj',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 商品积分购买设置
 */
export function addGoodsList(data) {
	return request({
		url: '/act/home/<USER>/addGoodsList',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 商品积分购买设置反显
 */
export function goodsList(data) {
	return request({
		url: '/act/home/<USER>/goodsList',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 删除商品
 */
export function delGoods(data) {
	return request({
		url: '/act/home/<USER>/del',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 积分类型
 */
export function integralTypeList(data) {
	return request({
		url: '/act/home/<USER>/memberPointsHistoryTypeList',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 积分记录
 */
export function recordList(data) {
	return request({
		url: '/merc/mini/memberPointsHistory/page',
		method: 'post',
		data: data
	})
}

/**
 * @param {Object} data
 * 导出全部
 */
export function exportMember(data, name) {
	return downLoadReq({
		'url': '/merc/mini/member/exportMember',
		'method': 'post',
		'data': data,
		'name': name
	})
}

/**
 * @param {Object} data
 * 导出明细
 */
export function exportHistory(data, name) {
	return downLoadReq({
		'url': '/merc/mini/memberPointsHistory/exportHistory',
		'method': 'post',
		'data': data,
		'name': name
	})
}