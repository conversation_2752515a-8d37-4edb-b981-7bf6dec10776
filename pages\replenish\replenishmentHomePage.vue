<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="补货首页"></u-navbar>

		<view class="machine-box">
			机器编号：{{deviceId}}
		</view>

		<view class="flex justify-between align-center">
			<!-- 帮助 -->
			<xhelpPopup guideId="BH0002" />
		</view>

		<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y :style="{'height':fullHeight}"
			lower-threshold="100" @scrolltolower="scrolltolower">
			<view class="top-wrap">
				<view class="tab-wrap">
					<view class="tab">
						<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
							:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="tabsIndex"
							@click="tabschange" lineColor="#2C6FF3">
						</u-tabs>
					</view>
				</view>

				<view class="search-container">
					<u-search animation placeholder="商品搜索" :clearabled="false" v-model="keyword" :showAction="false"
						@search="search"></u-search>
					<!-- 	<view class="scan-icon" @click="scan">
						<u-icon name="scan" size="22" color="#909399"></u-icon>
					</view> -->

					<view class="search-history flex flex-wrap flex-start" v-if="historyList.length>0">
						<view class="history-tips">最近搜索</view>
						<view class="history-item" v-for="(item,index) in historyList" :key="index"
							@click="searchFast(item)">
							{{item}}
						</view>
					</view>
				</view>

				<view class="btns flex justify-between">
					<view class="btn-left flex">
						<view class="icon-btn" style="margin-right: 12rpx;" v-if="checkPermi(['repMan:addCom'])">
							<!-- <xbutton width="184rpx" round="14rpx" @click="addgoods"
								icon="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_add.png">
								添加商品</xbutton> -->
							<xbutton width="134rpx" round="14rpx" @click="addgoods">添加商品</xbutton>
						</view>
						<view class="icon-btn" style="margin-right: 12rpx;" v-if="checkPermi(['repMan:importList'])">
							<xbutton width="134rpx" round="14rpx" @click="addlist"
								>导入清单</xbutton>
						</view>
						<view class="icon-btn" style="margin-right: 12rpx;" v-if="checkPermi(['repMan:savePhysical'])">
							<xbutton width="134rpx" bgColor="#fff" color="#2C6FF3" borderColor="#2C6FF3" round="14rpx"
								@click="physical">保存盘点</xbutton>
						</view>
						<view class="icon-btn" v-if="checkPermi(['repMan:autoFull'])">
							<xbutton width="134rpx" bgColor="#fff" color="#2C6FF3" borderColor="#2C6FF3" round="14rpx"
								@click="allFull">一键满货</xbutton>
						</view>
					</view>
					<view class="btn-right" v-if="checkPermi(['repMan:replenishSubmit'])">
						<xbutton width="94rpx" round="14rpx" @click="keep">提交</xbutton>
					</view>
				</view>
			</view>
			<view v-if="goodsList&&goodsList.length>0" class="scroll-content">
				<view class="goods-box" v-for="(item,index) in goodsList" :key="item.id">
					<view class="flex flex-sub align-center">
						<view class="image">
							<u--image radius='14rpx' width="120rpx" height="120rpx" @click="$xy.previewImg(item.cover)"
								:src="item.cover?item.cover:'https://static.xynetweb.com/sysFile/defaultgoods.png'"
								mode="aspectFit" :lazy-load="true">
							</u--image>
						</view>
						<view class="flex flex-sub flex-direction justify-around goods-content">
							<view class="goods-name-price flex justify-between align-start">
								<view class="goods-name">
									{{item.name}}
								</view>
								<view class="goods-price" v-if="item.newPrice==null">
									￥{{$xy.delMoney(item.price)}}
								</view>
								<view class="goods-price flex" v-else>
									<view style="color: #666;text-decoration: line-through;">
										￥{{$xy.delMoney(item.price)}}
									</view>
									<view>
										￥{{$xy.delMoney(item.newPrice)}}
									</view>
								</view>
							</view>

							<!-- 	<view class="goods-stock flex">
								<view>商品ID：{{item.goodsId}}</view>
							</view>

							<view class="goods-stock flex">
								<view>算法SKUID：{{item.skuId}}</view>
							</view> -->

							<view class="flex align-center justify-between" style="margin-top: 30rpx;">
								<view class="flex">
									<view>
										<view class="goods-stock flex" v-if="item.newCapacity">
											<view>容量：<text
													style="text-decoration: line-through;">{{item.capacity}}</text>
											</view>
											<view style="margin-left: 12rpx;color: red;">
												新：{{item.newCapacity}}</view>
										</view>
										<view class="goods-stock flex" v-else>
											<view>容量：{{item.capacity}}</view>
										</view>
									</view>

									<view style="margin-left: 24rpx;">
										<view class="goods-stock flex" v-if="item.newStock==0">
											<view>库存：{{item.stock}}</view>
										</view>
										<view class="goods-stock flex" v-else>
											<view>库存：<text style="text-decoration: line-through;">{{item.stock}}</text>
											</view>
											<view style="margin-left: 12rpx;color: red;" v-if="item.stock>0">
												新：{{Number(item.stock)+Number(item.newStock)}}</view>
											<view style="margin-left: 12rpx;color: red;" v-else>
												新：{{Number(item.newStock)}}</view>
										</view>
									</view>
								</view>

								<view class="flex align-center">
									<view v-if="item.newStock==0&&checkPermi(['repMan:changeStock'])">
										<xbutton round="14rpx" @click="stockFix(item)">库存纠正</xbutton>
									</view>
									<view style="margin-left: 20rpx;" v-if="checkPermi(['repMan:full'])">
										<xbutton round="14rpx" @click="fullItem(item)">满货</xbutton>
									</view>
								</view>
							</view>
							
							<!-- <block v-if="item.producedDate">
								<view class="goods-time flex" v-if="item.newProducedDate">
									<view>生产日期：<text
											style="text-decoration: line-through;">{{item.producedDate}}</text>
									</view>
									<view style="margin-left: 12rpx;color: red;">
										新：{{item.newProducedDate}}</view>
								</view>
								<view class="goods-time flex" v-else>
									<view>生产日期：{{item.producedDate}}</view>
								</view>
							</block>
							
							<view class="goods-time" v-if="item.expirationDate">过期时间：{{item.expirationDate}}</view> -->

							<!-- <view class="btn-input flex align-center flex-start" v-if="deviceInfo.deviceType!=5"> -->
							<block>
								<view class="btn-input flex align-center flex-start">
									<view class="btn-input-left">补货：</view>
									<view class="btn-input-right flex align-center">
										<image
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_sub.png"
											mode="widthFix" @click="sub(item)"></image>
										<input type="doit" v-model="item.newStock">
										<image
											src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_sup.png"
											mode="widthFix" @click="sup(item)"></image>
									</view>
								</view>
								<!-- <view style="height:20rpx;" v-if="deviceInfo.deviceType==5"></view> -->
								<image class="edit-goods" @click.stop="editGoods(item,index)"
									src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_edit.png"
									mode="widthFix"></image>
							</block>


						</view>
					</view>

					<view class="edit-box" v-if="item.show">
						<view v-if="checkPermi(['repMan:changePrice'])" @click.stop="changePrice(item)">改价</view>
						<view v-if="checkPermi(['repMan:changeCapacity'])" @click.stop="changeCapacity(item)">容量</view>
						<view v-if="checkPermi(['repMan:delCom'])" @click.stop="del(item)">删除</view>
						<view v-if="checkPermi(['repMan:stockRec'])" @click.stop="stockRecord(item)">库存记录</view>
						<!-- <view>满货</view> -->
					</view>
					<view class="bg" v-if="editIndex!=null" @click.stop="editHidden"></view>
				</view>
				<u-loadmore :status="loadmoreStatus" />
			</view>
			<view v-else class='empty'>
				<u-empty mode="data" text="数据为空"></u-empty>
			</view>

		</scroll-view>

		<view class="flex align-center justify-around bottom-btn">
			<view class="bottom-view" @tap="search">
				<image class="bottom-image"
					src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_upd.png"
					mode="widthFix"></image>
				<view class="bottom-text">刷新</view>
			</view>
			<view class="bottom-view" @tap="scanCodeOpenDor" v-if="checkPermi(['repMan:openDoor'])">
				<image class="bottom-image"
					src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_open.png"
					mode="widthFix"></image>
				<view class="bottom-text">一键开柜</view>
			</view>
			<view class="bottom-view" @tap="cper" v-if="checkPermi(['repMan:repRec'])">
				<image class="bottom-image"
					src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/rep_home_rec.png"
					mode="widthFix">
				</image>
				<view class="bottom-text">补货记录</view>
			</view>
			<view class="bottom-view" @tap="repImg" v-if="checkPermi(['repMan:repImg'])">
				<image class="bottom-image"
					src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/replenish/repimg.png"
					mode="widthFix">
				</image>
				<view class="bottom-text">补货图片</view>
				<u-badge absolute :offset="[-2,38]" is-dot bgColor="#Esss60012"></u-badge>
			</view>
		</view>

		<xpopup :show="editPriceShow" @close="editPriceClose" @confirm="priceSet" :showBtn="true" :title="editTitle">
			<view class="pop-content">
				<u--input placeholder="请输入内容" type="digit" border="surround" v-model="editPrice"
					v-if="editTitle=='修改价格'"></u--input>
				<u--input placeholder="请输入内容" type="digit" border="surround" v-model="editCapacity" v-if="editTitle=='修改容量'"></u--input>
				<u--input @focus="timeShow=true" placeholder="请输入选择生产日期" type="digit" border="surround" v-model="editProducedDate" v-if="editTitle=='修改生产日期'"></u--input>
			</view>
		</xpopup>

		<xpopup :show="backShow" round="12" @close="backClose" :safeAreaInsetBottom="false" :showBtn="false"
			@open="backOpen" mode="center">
			<scroll-view style="height: 500rpx;" scroll-y="true" scroll-with-animation="true" lower-threshold="100">
				<view class="back-pop-content">
					<view class="flex align-center">
						<view>名称</view>
						<view>类型</view>
						<view>旧值</view>
						<view></view>
						<view>新值</view>
					</view>
					<block v-for="(item,index) in changeList" :key="item.goodsName">
						<view class="back-price flex align-center" v-if="item.type=='price'">
							<view>{{item.goodsName}}</view>
							<view>价格:</view>
							<view>{{$xy.delMoney(item.oldPrice)}}</view>
							<view></view>
							<view>{{$xy.delMoney(item.newPrice)}}</view>
						</view>
						<view class="back-stock flex align-center" v-if="item.type=='stock'">
							<view>{{item.goodsName}}</view>
							<view>库存:</view>
							<view>{{item.oldStock}}</view>
							<view>→</view>
							<view>{{item.newStock}}</view>
						</view>
						<view class="back-capacity flex align-center" v-if="item.type=='capacity'">
							<view>{{item.goodsName}}</view>
							<view>容量:</view>
							<view>{{item.oldCapacity}}</view>
							<view>→</view>
							<view>{{item.newCapacity}}</view>
						</view>
						<view class="back-producedDate flex align-center" v-if="item.type=='producedDate'">
							<view>{{item.goodsName}}</view>
							<view>生产日期:</view>
							<view>{{item.oldProducedDate}}</view>
							<view>→</view>
							<view>{{item.newProducedDate}}</view>
						</view>
					</block>
				</view>
			</scroll-view>
		</xpopup>

		<!-- 库存纠正 -->
		<xpopup :show="stockFixShow" @close="stockFixClose" @confirm="stockFixSubmit" mode="center" :showBtn="true"
			title="库存纠正">
			<view class="stock-fix-content">
				<view class="stock-fix-tips">
					<text>提示:</text></br>
					<text>补货前商品库存不对时，请先修正后再补货
						1）补货前的库存数据可查询分析。
						2）保证补货数据的正确性，便于做进销存数据统计分析。</text>
				</view>
				<u--input placeholder="请输入纠正后库存" type="number" border="surround" v-model="stockFixNum"></u--input>
			</view>
		</xpopup>

		<!-- 选择左右门 -->
		<u-modal :show="doorShow" @confirm="rightDoor" :showCancelButton="true" confirmText="右门" cancelText="左门"
			confirmColor="#2C6FF3" cancelColor="#2C6FF3" @cancel="leftDoor" title="提示" content="请选择打开左门还是右门~">
		</u-modal>

		<!-- 确认商品 -->
		<xpopup :show="refShow" @close="refClose" mode="center" :showBtn="false" title="商品确认">
			<view class="stock-fix-content">
				<view class="tips">
					提示：平台商品库升级，请仔细核对实际补货商品是否为此商品？
				</view>
				<view class="flex flex-direction align-center" style="text-align: center;margin-top: 12rpx;">
					<u--image radius='14rpx' width="240rpx" height="240rpx"
						@click="$xy.previewImg(goodsItem.pgoodsInfo.cover)"
						:src="goodsItem.pgoodsInfo.cover?goodsItem.pgoodsInfo.cover:'https://static.xynetweb.com/sysFile/defaultgoods.png'"
						mode="aspectFit" :lazy-load="true">
					</u--image>
					<view style="margin-top: 20rpx;">{{goodsItem.pgoodsInfo.name}}</view>
				</view>

				<view class="popup-btn">
					<xbutton width="200rpx" round="88rpx" size="large" bgColor="#dfdfdf" color="#333"
						borderColor="#dfdfdf" @click="refSubmit(false)">否</xbutton>
					<xbutton delay="1500" round="88rpx" width="200rpx" size="large" @click="refSubmit(true)">是</xbutton>
				</view>
			</view>
		</xpopup>
		
		<u-datetime-picker :show="timeShow" :closeOnClickOverlay="true" @close="timeShow=false" @confirm="timeSubmit"
			@cancel="timeShow=false" v-model="timeStamp" mode="date"></u-datetime-picker>
	</view>
</template>

<script>
	import {
		supplyPage,
		delGoods,
		save,
		saveByOpenDoor,
		check,
		create,
		stockFixed,
		saveBySnapShoot,
		goodsRefSet
	} from "@/api/replenishment/replenishment.js"

	import {
		saveKeyWord
	} from '@/utils/common.js'
	export default {
		data() {
			return {
				goodsList: [],
				page: 1, //当前分页
				size: 10, //分页数据条数
				tabsIndex: 0,
				addGoodsShow: false,
				active: '',
				deviceId: null,
				door: 0,
				name: '', //商品名称/条形码
				doorShow: false,

				workNo: '',
				fullHeight: 0,
				historyList: [],
				keyword: '',

				tabList: [{
						name: '全部',
						badge: {
							value: 0
						}
					},
					{
						name: '缺货',
						badge: {
							value: 0,
						}
					},
					{
						name: '售罄',
						badge: {
							value: 0,
						}
					}
				],

				editIndex: null,
				editPriceShow: false,
				editPrice: null,
				editGoodsIndex: null,
				changeList: [], //提交反显信息

				backShow: false,
				deviceInfo: {},
				editTitle: '修改价格',
				editCapacity: null,
				
				editProducedDate:null,

				stockFixShow: false,
				stockFixNum: '',
				stockFixItem: null,

				tempData: [],
				loadmoreStatus: 'loadmore',

				helpShow: true,

				helpTxt: '<p style="color:red;">我是一个帮助</p>',
				isScan: false,
				isAlis: false,
				refShow: false,
				goodsItem: {},
				
				timeShow:false,
				timeStamp: new Date(), //时间picker显示时间
			}
		},
		async onLoad(o) {
			if (uni.getStorageSync('goods')) {
				this.historyList = JSON.parse(uni.getStorageSync('goods'))
			}
			this.deviceId = o.id;
			if (o.door != undefined) {
				this.door = o.door
				this.isScan = true
			} else {
				this.isScan = false
			}

			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 50)
		},

		onShow() {
			this.reset()
			this.gethomePage()
		},

		methods: {
			search(val) {
				if (val) {
					this.historyList = saveKeyWord('goods', this.keyword)
					console.log(this.historyList)
				}
				this.reset()
				this.gethomePage()
			},

			searchFast(e) {
				this.keyword = e
				this.search()
			},

			scanQRCode() {
				uni.scanCode({
					needResult: 1, //默认为0，扫描结果由微信处理，1则直接返回扫描结果，
					scanType: ["qrCode", "barCode"], //// 可以指定扫二维码还是一维码，默认二者都有
					success: (res) => {
						var result = res.resultStr; //当needResult 为 1 时，扫码返回的结果  获取扫码信息
						this.name = result
						this.gethomePage()
					},
				});
			},

			tabschange(e) {
				this.tabsIndex = e.index
				this.reset()
				this.gethomePage()
			},

			addgoods() {
				this.$tab.navigateTo('/pages/equipment/addCom?id=' + this.deviceId)
			},

			addlist() {
				this.$tab.navigateTo('/pages/commodity/commoditylist?id=' + this.deviceId)
			},

			//补货记录
			cper() {
				this.$tab.navigateTo('/pages/replenish/repRecord?deviceId=' + this.deviceId)
			},

			repImg() {
				this.$tab.navigateTo('/pages/replenish/replenishImg?deviceId=' + this.deviceId)
			},

			// 单个商品满货
			fullItem(item) {
				item.newStock = item.stock > 0 ? item.capacity - item.stock : item.capacity
				this.$forceUpdate()
				this.$modal.showToast('已经满货~')
			},

			//一键满货
			allFull() {
				uni.showModal({
					title: '提示',
					content: '是否确认一键满货',
					success: res => {
						if (res.confirm) {
							for (var i = 0; i < this.goodsList.length; i++) {
								let goods = this.goodsList[i];
								goods.newStock = goods.stock > 0 ? goods.capacity - goods.stock : goods
									.capacity
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},

			/**
			 * 一键开柜
			 *
			 * 扫码获取机器号并且打开柜门
			 */
			scanCodeOpenDor() {
				// 扫码确认是当前设备
				this.$xy.scanDevice().then(res => {
					let deviceId = res.deviceId || res.terminalId;
					this.isAlis = res.terminalId ? true : false;
					this.door = res.door ?? 0
					console.log(deviceId, this.door)
					if (this.deviceId == deviceId) { //是当前设备
						this.confirmOpen()
					} else { //不是当前设备
						this.$modal.msg('当前设备与扫码设备不一致，请确认！')
					}
				}).catch(err => {
					console.log('err', err)
				})
			},

			// 一键开柜
			// openCabinet() {
			// 	if (this.deviceInfo.deviceType == 2 || this.deviceInfo.deviceType == 4) { //双门
			// 		if (!this.isScan) { //未确定打开哪一扇门，进行选择
			// 			this.doorShow = true
			// 			return
			// 		}
			// 	}
			// 	this.confirmOpen()
			// },

			confirmOpen() {
				let msg = this.isAlis ? '请确认柜门二维码仍旧显示在小屏幕上！！！' : '是否确认一键开柜?'
				uni.showModal({
					title: '提示',
					content: msg,
					success: res => {
						if (res.confirm) {
							this.openDoor()
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},

			/**
			 * 开门逻辑
			 */
			openDoor() {
				saveByOpenDoor({
					"door": this.door ?? 0, //0左门 1右门
					"deviceId": this.deviceId
				}).then(res => {
					this.workNo = res.data
					this.checkWx()
				})
			},

			/**
			 * 选择开那一扇门
			 */
			rightDoor(e) {
				this.doorShow = false
				this.door = 1
				this.openDoor()
			},
			leftDoor() {
				this.doorShow = false
				this.door = 0
				this.openDoor()
			},

			//效验活动
			checkWx() {
				check({
					"deviceId": this.deviceId,
					"deviceType": this.isAlis ? 2 : 1, //支付宝柜子2，其他1
					"door": this.door ?? 0, //0左门 1右门
					"workType": 2
				}).then(res => {
					this.createOrder(res.data)
				})
			},
			//创建活动
			createOrder(e) {
				create({
					"deviceId": this.deviceId,
					"deviceType": this.isAlis ? 2 : 1, //支付宝柜子2，其他1
					"payQueryOrderId": "",
					"payType": '',
					"sysType": 1,
					"workNo": this.workNo,
					"workType": 2,
					"openSark": {
						"door": this.door ?? 0, //0左门 1右门
					},
				}).then(res => {
					uni.showLoading({
						title: '开门中...'
					});

					setTimeout(function() {
						uni.showToast({
							title: '开门成功',
							duration: 2000
						});
						uni.hideLoading();
					}, 2000);
				}).catch(err => {
					uni.showToast({
						title: JSON.stringify(res),
						duration: 10000
					});
				})
			},

			/**
			 * 盘点
			 */
			physical() {
				var goods = [] //收集提交改动商品参数
				var changeList = [] //收集提交改动商品清单，做提醒结果用
				this.goodsList.forEach(item => {
					if (item.newPrice != null || item.newStock != 0 || item.newCapacity != null) {
						goods.push({
							"deviceGoodsId": item.id,
							"oldStock": item.stock,
							"newStock": !(Number(item.stock) < 0) ? Number(item.newStock) + Number(item
								.stock) : Number(item.newStock),
							"oldPrice": item.price,
							"newPrice": item.newPrice,
							"oldCapacity": item.oldCapacity,
							"newCapacity": item.newCapacity,
							"oldWarning": item.warning,
							"newWarning": item.warning,
							"goodsId": item.goodsId,
							"goodsName": item.name
						})

						if (item.newPrice != null) { //改价商品
							changeList.push({
								goodsName: item.name,
								type: 'price',
								oldPrice: item.price,
								newPrice: item.newPrice
							})
						}
						if (item.newStock != 0) { //改库存商品
							changeList.push({
								goodsName: item.name,
								type: 'stock',
								oldStock: item.stock,
								newStock: !(Number(item.stock) < 0) ? Number(item.newStock) + Number(item
									.stock) : Number(item.newStock)
							})
						}
						if (item.newCapacity != null) { //改容量商品
							changeList.push({
								goodsName: item.name,
								type: 'capacity',
								oldCapacity: item.capacity,
								newCapacity: item.newCapacity
							})
						}
					}
				})

				this.changeList = JSON.parse(JSON.stringify(changeList))

				if (goods.length == 0) {
					uni.showModal({
						title: '提示',
						content: '请确保当前库存与实际相符再提交盘点！',
						success: res => {
							if (res.confirm) {
								saveBySnapShoot({
									deviceId: this.deviceId
								}).then(res => {
									// this.backOpen()
									// this.clearStorage()
									// this.gethomePage()
									this.$modal.showToast('盘点成功~')
								}).catch(err => {
									uni.showModal({
										title: '提示',
										content: err,
										success: res => {
											if (res.confirm) {
												console.log('确定')
											}
										}
									})
								})
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				} else {
					uni.showModal({
						title: '提示',
						content: '请先提交更改的库存后再进行盘点！',
						success: res => {
							if (res.confirm) {
								// save({
								// 	id: this.workNo ? this.workNo : '',
								// 	deviceId: this.deviceId,
								// 	goods: goods
								// }).then(res => {
								// 	this.backOpen()
								// 	this.clearStorage()
								// 	this.gethomePage()
								// }).catch(err => {
								// 	uni.showModal({
								// 		title: '提示',
								// 		content: err,
								// 		success: res => {
								// 			if (res.confirm) {
								// 				console.log('确定')
								// 			}
								// 		}
								// 	})
								// })
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}

			},

			//保存
			keep() {
				var goods = [] //收集提交改动商品参数
				var changeList = [] //收集提交改动商品清单，做提醒结果用
				var hasZeroStock = false //是否存在零库存商品，存在则提醒用户保存会删除零库存商品
				this.goodsList.forEach(item => {
					// if (!(Number(item.newStock) > 0) && !((Number(item.stock) + Number(item.newStock)) >
					// 		0)) { //存在零库存商品
					// 	hasZeroStock = true
					// }
					if (item.newPrice != null || item.newStock != 0 || item.newCapacity != null||item.newProducedDate) {
						let tempData = {
							"deviceGoodsId": item.id,
							"oldStock": item.stock,
							"newStock": !(Number(item.stock) < 0) ? Number(item.newStock) + Number(item
								.stock) : Number(item.newStock), //
							"oldPrice": item.price,
							"newPrice": item.newPrice,
							"oldCapacity": item.oldCapacity,
							"newCapacity": item.newCapacity,
							"oldWarning": item.warning,
							"newWarning": item.warning,
							"oldProducedDate":item.producedDate,
							"newProducedDate":item.newProducedDate,
							"goodsId": item.goodsId,
							"goodsName": item.name,
							"producedDate":item.newProducedDate,
						}
						goods.push(tempData)

						if (item.newPrice != null) { //改价商品
							changeList.push({
								goodsName: item.name,
								type: 'price',
								oldPrice: item.price,
								newPrice: item.newPrice
							})
						}
						if (item.newStock != 0) { //改库存商品
							changeList.push({
								goodsName: item.name,
								type: 'stock',
								oldStock: item.stock,
								newStock: !(Number(item.stock) < 0) ? Number(item.newStock) + Number(item
									.stock) : Number(item.newStock)
							})
						}
						if (item.newCapacity != null) { //改容量商品
							changeList.push({
								goodsName: item.name,
								type: 'capacity',
								oldCapacity: item.capacity,
								newCapacity: item.newCapacity
							})
						}
						if (item.newProducedDate != null) { //改生产日期商品
							changeList.push({
								goodsName: item.name,
								type: 'producedDate',
								oldProducedDate: item.producedDate,
								newProducedDate: item.newProducedDate
							})
						}
					}
				})

				this.changeList = JSON.parse(JSON.stringify(changeList))

				if (goods.length == 0) {
					this.$modal.msg('您未对商品做任何改动~')
					return
				}
				
				this.$modal.confirm(hasZeroStock ? '库存为零的商品，保存后会被删除，确认保存？' : '是否确认保存').then(res => {
					this.saveFun(goods)
				})
			},

			saveFun(goods) {
				save({
					id: this.workNo ? this.workNo : '',
					deviceId: this.deviceId,
					goods: goods
				}).then(res => {
					this.backOpen()
					this.clearStorage()
					this.reset()
					this.gethomePage()
				}).catch(err => {
					uni.showModal({
						title: '提示',
						content: err,
						success: res => {
							if (res.confirm) {
								console.log('确定')
							}
						}
					})
				})
			},

			// 保存成功,清空缓存
			clearStorage() {
				uni.setStorageSync(`replenish:${this.deviceId}`, '')
			},

			backClose() {
				this.backShow = false
			},

			//回显保存结果
			backOpen() {
				this.backShow = true
			},

			//删除
			del(val) {
				this.editHidden()
				let _this = this
				uni.showModal({
					title: '提示',
					content: '是否确认删除',
					success: function(res) {
						if (res.confirm) {
							delGoods({
								"deviceId": _this.deviceId,
								"goodsIds": [val.goodsId]
							}).then(res => {
								_this.reset()
								_this.gethomePage()
								setTimeout(() => {
									_this.$modal.showToast('删除成功！')
								}, 500)
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});

			},

			/**
			 * 库存记录
			 */
			stockRecord(item) {
				var today = new Date();
				let startTime = uni.$u.timeFormat(today.setDate(today.getDate() - 7), 'yyyy-mm-dd');
				let endTime = uni.$u.timeFormat(today, 'yyyy-mm-dd');
				this.$tab.navigateTo(
					`/pages/replenish/stockRecord?deviceId=${this.deviceId}&goodsId=${item.goodsId}&goodsName=${item.name}&startTime=${startTime}&endTime=${endTime}`
				)
			},

			gethomePage() {
				supplyPage({
					page: {
						size: -1
					},
					search: this.keyword,
					deviceId: this.deviceId,
					type: this.tabsIndex + 1, //类型
					isRefresh: true
				}).then(res => {
					let deviceInfo = res.data.deviceInfo || ''
					let data = res.data.page?.records || []
					let sto = uni.getStorageSync(`replenish:${this.deviceId}`)
					for (var i = 0; i < data.length; i++) {
						data[i].oldCapacity = data[i].capacity
						data[i].show = false
						data[i].newPrice = null
						data[i].newStock = 0

						// 暂存补货数据
						if (sto && sto != '') {
							let replenishList = JSON.parse(sto);
							replenishList.forEach(j => {
								if (data[i].goodsId === j.goodsId) {
									data[i].newPrice = j.newPrice
									data[i].newStock = j.newStock
								}
							})
						}
					}
					this.deviceInfo = deviceInfo
					this.tempData = data;
					this.setgGoodsData()

					let tabListTemp = [{
							name: '全部',
							badge: {
								value: 0,
							}
						},
						{
							name: '缺货',
							badge: {
								value: 0,
							}
						},
						{
							name: '售罄',
							badge: {
								value: 0,
							}
						}
					]
					tabListTemp[this.tabsIndex].badge.value = data.length
					this.tabList = JSON.parse(JSON.stringify(tabListTemp))
				})
			},

			// 触底加载
			scrolltolower() {
				console.log(this.page)
				console.log(this.loadmoreStatus)
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.setgGoodsData()
			},

			//模拟数据懒加载
			setgGoodsData() {
				if (this.tempData.slice((this.page - 1) * 10, this.page * 10).length < 10) {
					this.loadmoreStatus = 'nomore'
				}
				this.goodsList = this.goodsList.concat(this.tempData.slice((this.page - 1) * 10, this.page * 10))
				console.log(this.goodsList)
			},

			// 重置数据
			reset() {
				this.loadmoreStatus = 'loadmore'
				this.page = 1;
				this.goodsList = [];
			},

			editGoods(item, index) {
				item.show = true
				this.editIndex = index
				this.editGoodsIndex = index
			},

			editHidden() {
				if (this.editIndex != null) {
					this.goodsList[this.editIndex].show = false
					this.editIndex = null
				}
			},

			sub(item) {
				if (item.stock >= 0) {
					if (Number(item.stock) + Number(item.newStock) == 0) {
						this.$modal.msg('这个商品没库存啦~')
						return
					}
				} else {
					if (Number(item.newStock) <= 0) {
						this.$modal.msg('这个商品没库存啦~')
						return
					}
				}

				item.newStock--
				this.goodsChange()
			},

			sup(item) {
				if (item.needRef) {
					this.goodsItem = item
					this.refShow = true
					return
				}
				item.newStock++
				this.goodsChange()
			},

			refClose() {
				this.refShow = false
			},

			refSubmit(e) {
				goodsRefSet({
					"deviceId": this.deviceId,
					"goodsId": this.goodsItem.goodsId,
					"pubGoodsId": this.goodsItem.pgoodsInfo.id,
					"skuId": this.goodsItem.skuId,
					"isMatch": e
				}).then(res => {
					this.$modal.msg('确认成功')
					this.goodsItem.needRef = false
				})
				this.refShow = false
			},

			// 库存调整,设置缓存
			goodsChange(item) {
				//设置缓存
				let newListSto = []
				this.goodsList.forEach(i => {
					if (i.newPrice != null || i.newStock != 0) {
						let obj = {
							goodsId: i.goodsId,
							newPrice: i.newPrice,
							newStock: i.newStock,
							newProducedDate:i.newProducedDate
						}
						newListSto.push(obj)
					}
				})

				uni.setStorageSync(`replenish:${this.deviceId}`, JSON.stringify(newListSto))
			},

			changePrice(item) {
				this.editTitle = '修改价格'
				this.editPriceShow = true;
				this.editHidden()
			},

			changeCapacity() {
				this.editTitle = '修改容量'
				this.editPriceShow = true;
				this.editHidden()
			},
			
			changeProducedDate() {
				this.editTitle = '修改生产日期'
				this.editPriceShow = true;
				this.editHidden()
			},
			

			editPriceClose() {
				this.editPriceShow = false;
			},

			priceSet() {
				if (this.editTitle == '修改价格') {
					if (this.editPrice == null) {
						this.$modal.msg('商品价格不能为空！')
						return
					}
					let reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
					if (!reg.test(this.editPrice)) {
						uni.showToast({
							icon: 'none',
							title: '价格最多保留两位小数！'
						})
						return;
					}
					this.editPriceClose()
					this.goodsList[this.editGoodsIndex].newPrice = this.$xy.delMoneyL(this.editPrice)
					this.goodsChange() //设置缓存

					this.editPrice = null
					this.editGoodsIndex = null
				} else if(this.editTitle == '修改容量') { //修改容量
					if (this.editCapacity == null) {
						this.$modal.msg('商品容量不能为空！')
						return
					}
					let reg = /^([0-9]*)$/
					if (!reg.test(this.editCapacity)) {
						uni.showToast({
							icon: 'none',
							title: '容量必须为整数！'
						})
						return;
					}
					this.editPriceClose()
					this.goodsList[this.editGoodsIndex].newCapacity = this.editCapacity
					this.goodsChange() //设置缓存

					this.newCapacity = null
					this.editGoodsIndex = null
				}else{
					if (this.editProducedDate == null) {
						this.$modal.msg('生产日期不能为空！')
						return
					}
					this.editPriceClose()
					this.goodsList[this.editGoodsIndex].newProducedDate = this.editProducedDate
					this.goodsChange() //设置缓存
					
					this.newCapacity = null
					this.editGoodsIndex = null
				}
			},
			
			// 时间选择
			timeSubmit(e) {
				this.editProducedDate = uni.$u.timeFormat(e.value, 'yyyy-mm-dd')
				this.timeShow = false
			},

			/**
			 * 库存纠正
			 */
			stockFix(item) {
				// this.$modal.msg('功能开发中！')
				// return
				this.stockFixShow = true
				this.stockFixItem = item
			},

			stockFixClose() {
				this.stockFixShow = false
			},

			stockFixSubmit() {
				if (!this.stockFixNum) {
					this.$modal.msg('请输入纠正后库存~')
					return
				}
				this.stockFixShow = false
				this.$modal.confirm('是否确认修正库存？').then(res => {
					let params = {
						deviceId: this.deviceId,
						goods: [{
							"deviceGoodsId": this.stockFixItem.id,
							"oldStock": this.stockFixItem.stock,
							"newStock": Number(this.stockFixNum),
							"goodsId": this.stockFixItem.goodsId,
							"goodsName": this.stockFixItem.name
						}]
					}
					stockFixed(params).then(res => {
						this.stockFixNum = ''
						this.reset()
						this.gethomePage()
						setTimeout(() => {
							this.$modal.showToast('修正成功~')
						}, 1000)
					})
				})
			},

			helpClose() {
				this.helpShow = false
			}
		}
	}
</script>
<style>
	page {
		background-color: #eee;
	}
</style>
<style scoped lang="scss">
	.container {
		height: 100vh;
		background-color: #F4F4F4;
		position: static;

		.machine-box {
			width: 750rpx;
			height: 74rpx;
			font-size: 32rpx;
			line-height: 58rpx;
			text-align: center;
			background: #2C6FF3;
			color: #fff;
			border-radius: 0px 0px 50rpx 50rpx;
		}

		.top-wrap {
			background: linear-gradient(0deg, #D7D7D7, #FFFFFF);
		}

		.tab-wrap {
			background-color: #fff;
			padding-left: 30rpx;

			.tab {
				width: 80%;
			}
		}

		.search-container {
			padding: 26rpx 13rpx 26rpx;
			background-color: #fff;
			position: relative;

			.scan-icon {
				position: absolute;
				right: 36rpx;
				top: 38rpx;
				z-index: 2;
			}

			.search-history {
				.history-tips {
					font-size: 24rpx;
					color: #333333;
					line-height: 40rpx;
					margin-right: 10rpx;
					margin-top: 24rpx;
				}

				.history-item {
					margin-right: 10rpx;
					padding: 0 13rpx;
					background: #F6F8FB;
					color: #333;
					font-size: 24rpx;
					line-height: 40rpx;
					border-radius: 40rpx;
					margin-top: 24rpx;
				}
			}
		}

		.btns {
			background-color: #fff;
			padding: 20rpx 13rpx 20rpx;
		}

		.empty {
			margin-top: 120rpx
		}

		.active {
			background-color: #2C6FF3;
			color: #fff;
		}

		.marleft {
			margin-left: 10rpx;
		}

		.martop {
			margin-top: 20rpx;
		}

		.scrollview {
			// padding-top: 10rpx;
			position: relative;
		}

		.goods-box {
			width: 724rpx;
			margin-left: 13rpx;
			background-color: #fff;
			box-shadow: 0px 0px 10px 0px rgba(174, 201, 255, 0.2);
			border-radius: 14rpx;
			padding: 28rpx 12rpx 22rpx;
			position: relative;

			.edit-box {
				width: 180rpx;
				background-color: #fff;
				font-size: 30rpx;
				text-align: center;
				line-height: 80rpx;
				padding: 10rpx 0;
				box-shadow: 0px 0px 10px 0px rgba(174, 201, 255, 0.2);
				position: absolute;
				right: 20rpx;
				bottom: -120rpx;
				z-index: 99999;
			}

			&+.goods-box {
				margin-top: 10rpx;
			}

			.image {
				margin-right: 18rpx;
				height: 120rpx;
				width: 120rpx;
			}

			.goods-content {
				position: relative;

				.goods-name-price {
					.goods-name {
						font-size: 28rpx;
						font-weight: 800;
						color: #333333;
						line-height: 34rpx;
					}

					.goods-price {
						font-size: 32rpx;
						line-height: 32rpx;
						font-weight: 800;
						color: #FF0000;
						margin-right: 10rpx;
					}
				}

				.goods-stock {
					font-size: 26rpx;
					font-weight: 500;
					color: #555555;
					line-height: 26rpx;

					.stock-fix {
						margin-left: 24rpx;
						color: #2C6FF3;
						text-decoration: underline;
						font-style: italic;
					}
				}
				
				.goods-time{
					font-size: 26rpx;
					font-weight: 500;
					color: #555555;
					line-height: 26rpx;
					margin-top: 20rpx;
				}

				.btn-input {
					margin-top: 20rpx;

					.btn-input-left {
						font-size: 26rpx;
					}

					.btn-input-right {
						justify-content: flex-end;
						margin-left: 30rpx;

						>image {
							width: 56rpx;
							height: 56rpx;
						}

						>input {
							width: 100rpx;
							height: 56rpx;
							line-height: 56rpx;
							border: 2rpx solid #2C6FF3;
							border-radius: 10rpx;
							text-align: center;
							margin: 0 30rpx;
							padding: 0 12rpx;
						}
					}
				}

				.edit-goods {
					width: 56rpx;
					height: 56rpx;
					position: absolute;
					right: 10rpx;
					bottom: 0;

				}
			}
		}

		.pop-content {
			padding: 24rpx;
		}

		.bottom-btn {
			width: 100%;
			position: fixed;
			bottom: 0;
			right: 0;
			left: 0;
			background-color: #fff;
			height: calc(98rpx + env(safe-area-inset-bottom) / 2);
			padding-bottom: calc(env(safe-area-inset-bottom) / 2);
			box-shadow: 0px -2px 10px 0px rgba(174, 201, 255, 0.2);
			z-index: 999;
		}

		.scroll-content {
			padding-bottom: calc(160rpx + env(safe-area-inset-bottom) / 2);
			margin-top: 10rpx;
		}

		.bottom-view {
			display: flex;
			flex-direction: column;
			flex: 1;
			text-align: center;
			align-items: center;
			position: relative;
		}

		.bottom-image {
			height: 48rpx;
			width: 48rpx;
		}

		.bottom-text {
			font-size: 22rpx;
			line-height: 22rpx;
			margin-top: 8rpx;
		}

		.save {
			position: fixed;
			right: 15rpx;
			bottom: 120rpx;
			width: 140rpx;
			height: 140rpx;
			border-radius: 50%;
			background-color: #f6d232;

			image {
				width: 80rpx;
			}
		}

		.bg {
			position: fixed;
			width: 100%;
			height: 100vh;
			z-index: 99998;
			left: 0;
			top: 0;
		}

		.back-pop-content {
			width: 700rpx;
			padding: 24rpx;
			line-height: 50rpx;

			>view {
				font-size: 30rpx;

				>view {
					display: inline-block;
				}

				>view:nth-child(1) {
					width: 270rpx;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				>view:nth-child(2) {
					width: 100rpx;
					margin-left: 24rpx;
				}

				>view:nth-child(3) {
					margin-left: 24rpx;
					margin-right: 24rpx;
					width: 100rpx;
				}

				>view:nth-child(4) {
					width: 50rpx;
				}

				>view:nth-child(5) {
					margin-left: 24rpx;
					width: 100rpx;
				}
			}

			.back-price {

				>view:nth-child(2) {
					color: #2C6FF3;
				}

				>view:nth-child(3) {
					color: #38fb58;
				}

				>view:nth-child(5) {
					color: #ef9840;
				}
			}

			.back-stock {
				>view:nth-child(2) {
					color: #ff67bd;
				}

				>view:nth-child(3) {
					color: #38fb58;
				}

				>view:nth-child(5) {
					color: #ef9840;
				}
			}

			.back-capacity {
				>view:nth-child(2) {
					color: #ff5500;
				}
				
				>view:nth-child(3) {
					color: #38fb58;
				}
				
				>view:nth-child(5) {
					color: #ef9840;
				}
			}
			
			.back-producedDate {
				>view:nth-child(2) {
					color: #00aa7f;
				}
			
				>view:nth-child(3) {
					color: #38fb58;
				}
			
				>view:nth-child(5) {
					color: #ef9840;
				}
			}
		}

		.stock-fix-content {
			width: 690rpx;
			padding: 0 30rpx;
			
			.stock-fix-tips {
				margin-bottom: 24rpx;
				line-height: 46rpx;

				>text:nth-child(1) {
					color: #FF0000;
					font-weight: bold;
				}

				>text:nth-child(2) {
					color: #666;
					line-height: 38rpx;
				}
			}
			
			.tips{
				color:red;
			}

			.popup-btn {
				display: flex;
				flex-flow: row nowrap;
				justify-content: space-around;
				width: 100%;
				padding: 50rpx 24rpx 24rpx;

				.cu-btn {
					background-color: #2C6FF3;
					color: #fff;
					width: 200rpx;
				}

				.cu-btn1 {
					background-color: green;
				}
			}
		}
	}
</style>