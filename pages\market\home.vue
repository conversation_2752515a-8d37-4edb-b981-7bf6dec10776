<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="营销管理"></u-navbar>
		<view class="content">

			<xhelpPopup guideId="YX0001" />
			<!-- 			<view class="flex">
				<view class="menu-item" @click="menuTab('cx')" v-if="checkPermi(['market:promotion'])">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/home/<USER>"
						mode=""></image>
					<view>促销活动</view>
				</view>
				<view class="menu-item" @click="menuTab('tj')" v-if="checkPermi(['market:special'])">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/home/<USER>"
						mode=""></image>
					<view>特价活动</view>
				</view>
				<view class="menu-item" @click="menuTab('yh')" v-if="checkPermi(['market:discount'])">
					<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/home/<USER>"
						mode=""></image>
					<view>优惠券</view>
				</view>
			</view> -->

			<view class="tab-wrap">
				<view class="tab">
					<u-tabs :list="tabList" :activeStyle="{color: '#333',fontWeight: 'bold',fontSize:'36rpx'}"
						:inactiveStyle="{fontSize:'32rpx'}" :scrollable="false" :current="current" @click="tabClick"
						lineColor="#2C6FF3">
					</u-tabs>
				</view>
			</view>

			<view class="tab-list flex flex-wrap flex-start">
				<block v-for="(item,index) in typeList" :key="item.name">
					<view :class="[type==item.value?'tab-item tab-show':'tab-item']" @click="menuTab(item.value)">
						{{item.name}}
					</view>
				</block>
			</view>

			<scroll-view class="scrollview" :scroll-with-animation="true" scroll-y lower-threshold="100"
				@scrolltolower="scrolltolower" :style="{height:fullHeight}">
				<view v-if="list.length>0" class="content">
					<block v-for="(item,index) in list" :key="item.id">
						<view class="equipment-container">
							<view class="item-head">
								{{item.name}}<text style="font-size: 26rpx;">（{{item.type}}）</text>
							</view>
							<block v-if="item.type=='满减'">
								<view class="item-body flex" v-for="(item1,index) in item.price" :key="index">
									<view>满￥{{$xy.delMoney(item1.limit)}}</view>
									<view>减：¥{{$xy.delMoney(item1.price)}}</view>
									<view>优先级：{{item.weight}}</view>
								</view>
							</block>
							<block v-if="item.type=='折扣'">
								<view class="item-body flex">
									<view>{{item.price.scalar*10}}折</view>
									<view>优先级：{{item.weight}}</view>
								</view>
							</block>
							<block v-if="item.type=='特价'">
								<view class="item-body flex">
									<view>特价</view>
									<view>优先级：{{item.weight}}</view>
								</view>
							</block>

							<view class="item-foot flex justify-between align-center">
								<view class="foot-left">
									{{item.start_date}}至{{item.end_date}}
								</view>
								<view class="foot-right flex">
									<!-- <view style="margin-right: 12rpx;">
										<xbutton width="114rpx" bgColor="#fff" color="#2C6FF3" borderColor="#2C6FF3"
											round="14rpx" @click="preview(item)">
											预览
										</xbutton>
									</view> -->
									<view style="margin-right: 12rpx;"  v-if="checkPermi(['market:status'])">
										<xbutton width="114rpx" bgColor="#fff" color="#2C6FF3" borderColor="#2C6FF3"
											round="14rpx" @click="switchBtn(item)">
											{{item.enable_status=='启用'?'禁用':'启用'}}
										</xbutton>
									</view>
									<view v-if="checkPermi(['market:edit'])">
										<xbutton width="114rpx" round="14rpx" @click="editBtn(item)">编辑</xbutton>
									</view>
								</view>
							</view>
						</view>
					</block>
					<view class="load-more" style="padding:24rpx;">
						<u-loadmore v-if="list.length>0" :status="loadmoreStatus" />
					</view>
				</view>
				<view v-else class='empty'>
					<u-empty mode="data" text="数据为空"></u-empty>
				</view>
			</scroll-view>

			<view class="btn" v-if="checkPermi(['market:add'])">
				<xbutton size="large" @click="addMarket">
					添加活动
				</xbutton>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		proPage,
		proStatus,
		sendStatus
	} from "@/api/market/promotion.js"
	export default {
		data() {
			return {
				list: [],
				loadmoreStatus: 'loadmore',
				page: 1, //当前分页
				size: 10, //分页数据条数
				fullHeight: 0,

				tabList: [{
						name: '进行中'
					},
					{
						name: '未开始'
					},
					{
						name: '已结束'
					},
				],
				current: 0,
				status: '进行中',
				type: 'cx',
				typeList: [{
						name: '促销',
						value: 'cx'
					},
					{
						name: '特价',
						value: 'tj'
					},
					{
						name: '买赠',
						value: 'mz'
					}
				]
			}
		},
		async onLoad(o) {
			let _this = this;
			_this.fullHeight = await _this.$xy.scrollHeight(_this, '.scrollview', 0)
		},

		onShow() {
			uni.setStorageSync('market', '') //清空临时存储
			this.search()
		},

		methods: {
			menuTab(type) {
				this.type = type
				this.search()
			},

			switchBtn(item) {
				let _this = this
				let status = item.enable_status == '启用' ? '禁用' : '启用'
				this.$modal.oldConfirm(`是否确定${status}活动`).then(res => {
					if(this.type=='mz'){
						sendStatus({
							id: item.id,
							enable_status: status
						}).then(res => {
							_this.$modal.msg('成功~')
							item.enable_status = status
							_this.$forceUpdate()
						
						})
					}else{
						proStatus({
							id: item.id,
							enable_status: status
						}).then(res => {
							_this.$modal.msg('成功~')
							item.enable_status = status
							_this.$forceUpdate()
						
						})
					}
				})
			},

			editBtn(item) {
				let url = ''
				console.log(this.type)
				switch (this.type) {
					case 'cx':
						url = `/pages/market/addPro?id=${item.id}`
						break;
					case 'tj':
						url = `/pages/market/addSpe?id=${item.id}`
						break;
					case 'mz':
						url = `/pages/market/addSend?id=${item.id}`
						break;
					default:
						break;
				}
				this.$tab.navigateTo(url)
			},

			tabClick(e) {
				console.log(e)
				this.current = e.index
				this.status = e.name
				this.reset()
				this.getpage()
			},

			//获取订单列表
			getpage() {
				let type = null
				if (this.type == 'cx') {
					type = ['满减', '折扣']
				} else if (this.type == 'tj') {
					type = '特价'
				} else {
					type = '买赠'
				}
				proPage({
					page_index: this.page,
					page_size: this.size,
					status: this.status,
					type: type
				}).then(res => {
					let data = res.data.data;
					if (data.length < 10) {
						this.loadmoreStatus = "nomore"
					} else {
						this.loadmoreStatus = "loadmore"
					}
					this.list = this.list.concat(data)
				})
			},

			// 搜索
			search() {
				this.reset()
				this.getpage()
			},

			// 重置数据
			reset() {
				this.loadmoreStatus == 'loadmore'
				this.page = 1;
				this.size = 10;
				this.list = [];
			},

			// 触底加载
			scrolltolower() {
				if (this.loadmoreStatus == 'nomore') return
				this.page++
				this.getpage()
			},

			addMarket() {
				let url = ''
				switch (this.type) {
					case 'cx':
						url = '/pages/market/addPro'
						break;
					case 'tj':
						url = '/pages/market/addSpe'
						break;
					case 'mz':
						url = `/pages/market/addSend`
						break;
					default:
						break;
				}
				this.$tab.navigateTo(url)
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;

		.content {
			overflow: hidden;

			.menu-item {
				text-align: center;
				width: 25%;
				display: flex;
				flex-direction: column;
				align-items: center;
				margin-top: 24rpx;

				>image {
					width: 57rpx;
					height: 57rpx;
				}

				>view {
					color: #333;
					font-size: 24rpx;
					line-height: 24rpx;
					font-size: 28rpx;
					line-height: 28rpx;
					margin-top: 16rpx;
				}
			}

			.empty {
				padding-top: 40%;
			}

			.martop {
				margin-top: 20rpx;
			}

			.tab-wrap {
				padding: 12rpx 0;

				.tab {
					// width: 40%;
					width: 70%;
				}
			}

			.tab-list {
				width: 100%;
				background-color: #fff;
				padding: 24rpx 26rpx 0;

				.tab-item {
					padding: 0 20rpx;
					height: 62rpx;
					background: #F7F7F7;
					border-radius: 10rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: #777777;
					line-height: 62rpx;
					margin-bottom: 14rpx;
					text-align: center;
					margin-right: 12rpx;

					&.tab-show {
						background: #F4F8FF;
						color: #2C6FF3;
					}
				}
			}

			.marleft {
				margin-left: 10rpx;
			}

			.scrollview {
				.content {
					padding-bottom: 90rpx;
					overflow: hidden;
				}
			}

			.equipment-container {
				margin: 13rpx 13rpx 0;
				padding: 24rpx 30rpx 40rpx;
				border-radius: 14rpx;
				background-color: #fff;
				box-shadow: 0px 0px 10rpx 0px rgba(174, 201, 255, 0.2);

				.item-head {
					font-size: 32rpx;
					font-weight: 800;
					color: #333333;
					padding-bottom: 10rpx;
				}

				.item-body {
					background: #F6F7FA;
					border-radius: 14rpx;
					padding: 30rpx;
					font-size: 28rpx;
					font-weight: 800;
					color: #333333;
					margin-top: 20rpx;

					>view:nth-child(1) {
						padding-right: 30rpx;
					}

					>view:nth-child(2) {
						padding-left: 30rpx;
						padding-right: 30rpx;
						position: relative;

						&::before {
							content: '';
							width: 1rpx;
							height: 20rpx;
							background: #999999;
							position: absolute;
							left: 0;
							top: 50%;
							transform: translateY(-50%);
						}
					}

					>view:nth-child(3) {
						padding-left: 30rpx;
						position: relative;

						&::before {
							content: '';
							width: 1rpx;
							height: 20rpx;
							background: #999999;
							position: absolute;
							left: 0;
							top: 50%;
							transform: translateY(-50%);
						}
					}
				}

				.item-foot {
					margin-top: 18rpx;

					.foot-left {
						font-size: 22rpx;
						color: #555555;
					}
				}
			}

			.btn {
				width: 724rpx;
				position: fixed;
				left: 13rpx;
				bottom: calc(24rpx + env(safe-area-inset-bottom) / 2);
				border-radius: 88rpx;
				overflow: hidden;

				.btn-wrap {
					text-align: center;
					font-size: 36rpx;
					font-weight: 500;
				}
			}
		}
	}
</style>