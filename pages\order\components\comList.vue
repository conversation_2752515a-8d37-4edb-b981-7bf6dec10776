<template>
	<view class="wrap">
		<view class="u-menu-wrap">
			<scroll-view lower-threshold="150" :style="{height:height}" scroll-y class="right-box-full"
				@scrolltolower="lowerBottom">
				<view class="scroll-content">
					<view class="com-list" v-if="commList.length>0">
						<view v-if="menuShow" class="menu-list flex flex-wrap">
							<block v-for="(item, index) in commList" :key="item.id">
								<view class="menu-item" @click.stop="commItemClick(item)">
									<view class="menu-img flex justify-around">
										<u--image radius="4" width="130rpx" height="130rpx" :src="item.cover"
											mode="aspectFit" :lazy-lord="true"></u--image>
									</view>
									<view class="menu-name">
										{{item.name}}
									</view>
								</view>
							</block>
							<u-loadmore :status="status" v-if="commList.length>=1" />
						</view>
						<view class="item-container" v-else>
							<block v-for="(item, index) in commList" :key="item.id">
								<view class="thumb-box" @click.stop="commItemClick(item)">
									<view class="check-content">
										<view class="comm-img" @click.stop="$xy.previewImg(item.cover)">
											<u--image radius="4" width="130rpx" height="130rpx" :src="item.cover"
												mode="aspectFit" :lazy-lord="true"></u--image>
										</view>
										<view class="comm-main">
											<view>
												{{item.name}}
											</view>
											<view v-if="showId">
												商品id：{{item.showId}}
											</view>
											<view style="height: auto;word-break:break-all;line-height: 40rpx;">
												条形码：{{item.barcode}}
											</view>
											<!-- <view>
											商品类型：{{item.categoryName}}
										</view> -->
											<view v-if="item.weight">
												重量：<text>{{(Number(item.weight)/1000)}}</text>g
											</view>
											<view v-if="item.price!=null">
												价格：<text>￥{{item.price}}</text>
											</view>
										</view>
									</view>
								</view>
							</block>
							<u-loadmore :status="status" v-if="commList.length>=1" />
						</view>
					</view>
					<view class="empty" v-else>
						<u-empty></u-empty>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword: '',
				checkedList: [], //当前类目选中商品

				delPopShow: false, //删除弹框
				delTitle: undefined, //删除或清空提示
				selectList: [], //已选中商品
			}
		},
		props: {
			menuShow: {
				type: Boolean,
				require: false,
				default: true
			},
			height: {
				type: String,
				require: false,
				default: 'auto'
			},

			commList: {
				type: Array,
				require: true,
				default () {
					return []
				}
			},

			status: {
				type: String,
				require: false,
				default: 'loadmore'
			},
		},

		methods: {
			// 商品点击
			commItemClick(e) {
				this.$emit('comClick', e)
			},

			lowerBottom() {
				this.$emit('lowerBottom')
			},
		}
	}
</script>

<style lang="scss" scoped>
	.scroll-content {
		padding: 16rpx 16rpx 0;
	}

	.com-list {
		background-color: #fff;
		border-radius: 8rpx;
		padding:12rpx;
		
		.menu-list{
			margin:0;
			.menu-item{
				width:25%;
				height: 180rpx;
				.menu-img{
					width: 100%;
					height: 130rpx;
				}
				.menu-name{
					font-size: 24rpx;
					text-align: center;
					width: 100%;
					padding:0 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}
		}
	}

	.item-title {
		font-size: 30rpx;
		color: $u-main-color;
		font-weight: bold;
		margin: 10rpx 0;
	}

	.item-menu-name {
		font-weight: normal;
		font-size: 24rpx;
		color: $u-main-color;
	}

	.item-container {
		display: flex;
		flex-direction: column;
		flex-wrap: wrap;
	}

	.empty {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.item-menu-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 100rpx;
	}

	.thumb-box {
		display: flex;
		flex-flow: row nowrap;
		padding: 12rpx 0;
		align-items: center;

		&:not(:last-child) {
			border-bottom: 1rpx solid #f4f4f4;
			margin-bottom: 10rpx;
		}
	}
	
	.check-content {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding-left: 12rpx;
	
		.comm-img {
			width: 130rpx;
			height: 130rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-around;
			position: relative;
	
			image {
				width: 100%;
			}
	
			.ali-goods {
				width: 40rpx;
				height: 40rpx;
				position: absolute;
				left: 0;
				top: 0;
	
				>image {
					width: 40rpx;
					height: 40rpx;
				}
	
			}
		}
	
		.comm-main {
			box-sizing: border-box;
			padding-left: 18rpx;
			color: #999;
	
			>view {
				padding: 4rpx 0;
			}
	
			>view:nth-child(1) {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
			}
	
			>view:nth-child(2) {
				font-size: 24rpx;
				width: 380rpx;
			}
	
			>view:nth-child(3) {
				font-size: 24rpx;
			}
	
			>view:nth-child(4) {
				font-size: 24rpx;
	
				text {
					font-weight: bold;
					color: red;
				}
			}
		}
	}
</style>