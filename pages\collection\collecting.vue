<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<u-navbar leftIconColor="#333" titleStyle="color:#333;fontSize:36rpx;" :autoBack="true" bgColor="#fff"
			:placeholder="true" title="开始采集">
		</u-navbar>

		<!-- 进度显示区域 -->
		<view class="progress-container">

			<!-- 状态提示 -->
			<view class="status-text">
				<text>商品数据录入中...</text>
			</view>

			<!-- 状态列表 -->
			<view class="status-list">
				<view class="status-item" v-for="(item, index) in statusList" :key="index">
					<block v-if="item.status == 0">
						<u-loading-icon size="16"></u-loading-icon>
						<text class="status-item-text">正在{{ item.text }}...</text>
					</block>
					<block v-if="item.status == 1">
						<u-icon name="checkmark" size="16"></u-icon>
						<text class="status-item-text">{{ item.text }}成功</text>
					</block>
					<block v-if="item.status == 2">
						<u-icon name="close" size="16"></u-icon>
						<text class="status-item-text">{{ item.text }}失败</text>
					</block>
				</view>
			</view>
		</view>

		<!-- 固定在底部的操作按钮 -->
		<view class="fixed-bottom-bar">
			<button class="save-btn" @click="submitForm">
				结束采集
			</button>
		</view>
	</view>
</template>

<script>
	import {
		confirmStart,
		endOne,
		gatherError
	} from "@/api/collection.js";
	export default {
		data() {
			return {
				goodsId: "", // 商品ID
				isReady: false, // 是否准备完成
				statusList: [{
						text: "打开摄像头",
						status: 1
					},
					{
						text: "参数设置",
						status: 1
					},
				],
				deviceId: "",
				timer: null,
			};
		},

		onLoad(o) {
			this.goodsId = o.goodsId;
			this.deviceId = o.deviceId;
			// 页面加载后立即开始准备采集
			this.startGatherPreparation();
		},

		onUnload() {
			// 页面卸载时清除定时器
			clearInterval(this.timer);	
		},

		methods: {
			// 开始准备采集
			async startGatherPreparation() {
				// 调用采集准备接口
				let startGatherRes=await this.startGather();
				if (startGatherRes.code !== 200){
					this.$modal.msg('开始采集失败')
					setTimeout(() => {
						this.$tab.navigateBack()
					}, 1000);
					return
				}

				this.isReady = true;

				// 轮询查询是否发生采集错误
				this.timer = setInterval(() => {
					this.gatherErrorFun();
				},5000)
			},

			startGather(){
				return new Promise((resolve, reject) => {
					confirmStart({
						identifier: this.deviceId,
						id: this.goodsId
					}).then(res => {
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			// 提交表单，结束采集
			async submitForm() {
				if (!this.isReady) {
					uni.showToast({
						title: "请等待准备完成",
						icon: "none",
					});
					return;
				}
				
				// 关闭轮询
				clearInterval(this.timer)

				// 调用结束采集接口
				let endGatherRes=await this.endGather();
				if (endGatherRes.code !== 200) return this.$modal.msg('结束采集失败')

				// 跳转到结果页面
				this.$tab.navigateTo(`/pages/collection/result?goodsId=${this.goodsId}&deviceId=${this.deviceId}`);
			},

			endGather(){
				return new Promise((resolve, reject) => {
					endOne({
						identifier: this.deviceId,
					}).then(res => {
						resolve(res)
					}).catch(err => {
						reject(err)
					})
				})
			},

			//查询是否发生采集错误
			gatherErrorFun(){
				return new Promise((resolve, reject) => {
					gatherError({
						id: this.goodsId,	
					}).then(res => {
						resolve(res)
					}).catch(err => {
						reject(err)	
					})
				})
			},
		},
	};
</script>

<style lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #fff;
		position: relative;
		padding: 0 20rpx;

		// 进度显示区域
		.progress-container {
			width: 61.8%;
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;

			position: absolute;
			top: 35%;
			left: 50%;
			transform: translate(-50%, -35%);

			// 状态提示文字
			.status-text {
				font-size: 32rpx;
				color: #333;
				margin-bottom: 40rpx;
			}

			// 状态列表
			.status-list {
				width: 56%;

				.status-item {
					display: flex;
					align-items: center;
					margin-bottom: 20rpx;

					.status-item-text {
						font-size: 28rpx;
						color: #666;
						margin-left: 10rpx;
					}
				}
			}
		}

		.fixed-bottom-bar {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 20rpx 30rpx;
			padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
			background-color: #fff;
			box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
			z-index: 99;

			.save-btn {
				height: 88rpx;
				background-color: #4177fb;
				color: #fff;
				font-size: 32rpx;
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0;

				&[disabled] {
					background-color: #cccccc;
					color: #ffffff;
				}
			}
		}
	}
</style>