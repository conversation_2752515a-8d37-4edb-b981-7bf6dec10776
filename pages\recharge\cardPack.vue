<template>
	<view class="container">
		<u-navbar leftIconColor="#fff" titleStyle="color:#fff;fontSize:36rpx;" :autoBack="true" bgColor="#2C6FF3"
			:placeholder="true" title="卡包"></u-navbar>
		<view class="content">
			<block v-for="(item,index) in list" :key="item.placeLineId">
				<view class="xy-card section">
					<view class="content-top flex">
						<image src="https://cdn.ossfile.mxrvending.com/assets/xy_merc_mini/images/recharge/kb.png" mode="widthFix"></image>
						<view class="content-msg">
							<view class="id">
								券号：564564654
							</view>
							<view class="time">
								2023-06-29  18:08:11
							</view>
						</view>
						
						<view class="money">
							￥50
						</view>
					</view>
					
					<view class="btns flex justify-between martop align-center">
						可抵扣设备管理费
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		mercHomeList,
	} from "@/api/device/device.js"
	export default {
		data() {
			return {
				deviceId: null,
				list: null,
				deviceIds: [],
			}
		},
		onLoad(o) {
			this.search()
		},
		methods: {
			detail(){
				
			},
			
			search() {
				this.getList()
			},

			getList(id) {
				mercHomeList({
					memercId: id,
					deviceName: '',
					deviceId: this.deviceId
				}).then(res => {
					let data = res.data;
					if (this.deviceIds) {
						for (let i = 0; i < data.length; i++) {
							let par = data[i];
							par.checked = true
							for (let j = 0; j < par.deviceInfos.length; j++) {
								let child = par.deviceInfos[j]
								if (this.deviceIds.indexOf(child.deviceId.toString()) != -1) {
									child.checked = true
								} else {
									child.checked = false
									par.checked = false
								}
							}
						}
					}
					this.list = data;
				})
			},
		},
	}
</script>

<style scoped lang="scss">
	.container {
		font-size: 28rpx;
		color: #333;
		height: 100vh;
		
		.martop {
			margin-top: 24rpx;
		}
		
		.marleft24 {
			margin-left: 24rpx;
		}

		.content {
			padding: 0 13rpx;
			overflow: hidden;

			.search {
				margin-top: 30rpx;
			}

			.section {
				margin-top: 20rpx;
				padding:24rpx 20rpx 24rpx 28rpx;
				
				.content-top{
					position: relative;
					padding-top: 16rpx;
					overflow: hidden;
					>image{
						width:50rpx;
						height:35rpx;
						margin-right: 27rpx;
						margin-top: 6rpx;
					}
					.content-msg{
						.id{
							font-size: 28rpx;
							color: #333333;
						}
						.time{
							font-size: 22rpx;
							color: #999999;
							margin-top: 16rpx;
						}
					}
					.money{
						font-size: 48rpx;
						color: #FF0000;
						position: absolute;
						right:0;
						top:-10rpx;
					}
				}
				
				.btns{
					border-top: 1rpx solid #D8D8D8;
					padding-top: 26rpx;
					padding:20rpx 8rpx 10rpx;
					margin-top: 40rpx;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}

		.btn {
			position: fixed;
			left: 13rpx;
		}
	}
</style>