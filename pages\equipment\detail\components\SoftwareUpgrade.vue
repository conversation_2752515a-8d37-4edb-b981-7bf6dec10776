<template>
  <view>
    <!-- 升级弹框 -->
    <xpopup
      :show="show"
      @close="closeUpgradePopup"
      @confirm="confirmUpgrade"
      :showBtn="false"
      title="软件升级">
      <view class="popup-content upgrade-popup">
        <view class="upgrade-info">
          <view class="version-row">
            <text class="version-label">当前版本：</text>
            <text class="version-value">{{ version || "-" }}</text>
          </view>
          <view class="version-row">
            <text class="version-label">新版本：</text>
            <text class="version-value">{{ newVersion || "-" }}</text>
          </view>
          <view class="version-row">
            <text class="version-label">升级状态：</text>
            <text class="version-value">{{ upgradeStatus || "未开始" }}</text>
          </view>
          <view
            class="progress-container"
            v-if="upgradeStatus.includes('正在下载')">
            <view class="progress-bar">
              <view
                class="progress-inner"
                :style="{ width: downloadProgress + '%' }"></view>
            </view>
            <text class="progress-text">{{ downloadProgress }}%</text>
          </view>
        </view>

        <view class="upgrade-actions">
          <xbutton v-if="!downloadSn" @click="startDownload">
            下载新版本
          </xbutton>
          <xbutton
            v-else-if="upgradeStatus === '下载完成，点击升级'"
            @click="startUpgrade">
            立即安装
          </xbutton>
          <xbutton v-else-if="upgradeStatus.includes('正在')" disabled>
            {{ upgradeStatus }}
          </xbutton>
          <xbutton
            v-else-if="upgradeStatus === '升级命令已发送，请等待设备重启'"
            disabled>
            安装中...
          </xbutton>
          <xbutton
            v-else-if="
              upgradeStatus === '升级失败，请重试' ||
              upgradeStatus === '下载命令发送失败' ||
              upgradeStatus == '升级命令发送失败'
            "
            @click="startDownload">
            重新下载
          </xbutton>
        </view>
      </view>
    </xpopup>
  </view>
</template>

<script>
import {
  sendCommand,
  queryUpInfoTask,
  snByCmdAndResult,
} from "@/api/device/device.js";
export default {
  name: "SoftwareUpgrade",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    deviceId: {
      type: [Number, String],
      required: false,
    },
    deviceSn: {
      type: String,
      default: "",
    },
    currentVersion: {
      type: String,
      default: "",
    },
    newVersion: {
      type: String,
      default: "",
    },
    upgradeUrl: {
      type: String,
      default: "",
    },
    appId: {
      type: String,
      default: "",
    },
    appKey: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      upgradeStatus: "",
      downloadSn: null,
      checkingStatus: false,
      statusTimer: null,
      downloadProgress: 0,
    };
  },
  computed: {
    version() {
      return this.currentVersion.substring(
        this.currentVersion.indexOf(".") + 1
      );
    },
  },
  watch: {
    show(newVal) {
      if (!newVal) {
        this.stopCheckStatus();
      }
    },
  },
  methods: {
    // 关闭升级弹框
    closeUpgradePopup() {
      this.$emit("update:show", false);
      this.stopCheckStatus();
    },

    // 确认升级操作
    confirmUpgrade() {
      this.closeUpgradePopup();
    },

    // 开始下载新版本
    startDownload() {
      if (this.checkingStatus) return;
      this.downloadProgress = 0;
      this.sendDownloadCommand();
    },

    // 开始安装新版本
    startUpgrade() {
      this.sendUpgradeCommand();
    },

    getTemplet(type) {
      return new Promise((resolve, reject) => {
        this.getDict("mqtt_cmd_templet_task", type)
          .then((res) => {
            let templet = JSON.parse(res[0].value);
            resolve(templet);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },

    // 发送下载命令
    async sendDownloadCommand() {
      try {
        let templet = await this.getTemplet("appUpdate");
        templet.data.task = "update";
        templet.data.appId = this.appId;
        templet.data.appKey = this.appKey;
        templet.data.version = this.newVersion;
        templet.data.src = this.upgradeUrl;
        var params = [
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ];

        this.upgradeStatus = "正在下载...";
        this.downloadProgress = 0;

        const installRes = await sendCommand(params);
        if (installRes.code !== 200)
          return (this.upgradeStatus = "下载命令发送失败");
        this.downloadSn = installRes.data[0].v1;

        // 开始检查下载状态
        this.startCheckStatus();
      } catch (error) {
        console.error("发送下载命令失败", error);
        this.upgradeStatus = "下载命令发送失败";
      }
    },

    // 发送升级命令
    async sendUpgradeCommand() {
      try {
        this.upgradeStatus = "正在升级...";
        let templet = await this.getTemplet("file");
        templet.data.task = "install";
        templet.data.file = this.upgradeUrl;
        var params = [
          {
            deviceId: this.deviceId,
            templet: templet,
          },
        ];

        const updateRes = await sendCommand(params);
        if (updateRes.code !== 200)
          return (this.upgradeStatus = "升级命令发送失败");
        this.downloadSn = updateRes.data[0].v1;
        this.upgradeStatus = "升级命令已发送，请等待设备重启";
      } catch (error) {
        console.error("发送升级命令失败", error);
        this.upgradeStatus = "升级命令发送失败";
      }
    },

    // 开始检查下载状态
    startCheckStatus() {
      this.checkingStatus = true;
      this.checkDownloadStatus();

      // 设置定时器，每5秒检查一次状态
      this.statusTimer = setInterval(() => {
        this.checkDownloadStatus();
      }, 5000);
    },

    // 检查下载状态
    async checkDownloadStatus() {
      if (!this.downloadSn) return;

      try {
        const statusRes = await snByCmdAndResult({
          sn: this.downloadSn,
        });

        if (statusRes.code !== 200) {
          this.upgradeStatus = "升级包下载状态检测失败";
          this.stopCheckStatus();
          return;
        }
        let result = statusRes.data.result; //包含下载进度
        let progress = result?.progress;
        if (progress > 0 && progress < 100) {
          this.upgradeStatus = "正在下载...";
          this.downloadProgress = progress;
          return;
        }

        if (progress == 100) {
          this.upgradeStatus = "下载完成，点击升级";
          this.stopCheckStatus();
          return;
        }

        // this.upgradeStatus = "升级失败，请重试";
        // this.stopCheckStatus();
      } catch (error) {
        this.upgradeStatus = "升级失败，请重试";
        this.stopCheckStatus();
      }
    },

    // 停止检查状态
    stopCheckStatus() {
      this.downloadSn = "";
      this.upgradeStatus = "";
      this.checkingStatus = false;
      if (this.statusTimer) {
        clearInterval(this.statusTimer);
        this.statusTimer = null;
      }
    },
  },
  beforeDestroy() {
    this.stopCheckStatus();
  },
};
</script>

<style lang="scss" scoped>
// 为升级弹框添加样式
.upgrade-popup {
  padding: 30rpx;

  .upgrade-info {
    margin-bottom: 40rpx;

    .version-row {
      display: flex;
      margin-bottom: 20rpx;

      .version-label {
        width: 180rpx;
        color: #666;
      }

      .version-value {
        flex: 1;
        font-weight: bold;
      }
    }

    .progress-container {
      margin-top: 30rpx;

      .progress-bar {
        height: 20rpx;
        background-color: #f0f0f0;
        border-radius: 10rpx;
        overflow: hidden;

        .progress-inner {
          height: 100%;
          background-color: #2c6ff3;
        }
      }

      .progress-text {
        display: block;
        text-align: right;
        margin-top: 10rpx;
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .upgrade-actions {
    display: flex;
    justify-content: center;
  }
}
</style>
